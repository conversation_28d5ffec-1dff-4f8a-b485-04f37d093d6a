const configMap = require('./auto/config');
const PRIME_PLATFORM = process.env.PRIME_PLATFORM;
let PRIME_APP_ID = configMap[PRIME_PLATFORM].syAppid;

module.exports = {
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          globalVars: {
            'text-background-color': '#F2F2F2', // 文字的灰色背景
            'button-background-color': '#BABABA', // tab样式按钮的灰色背景
            'button-selected-color': '#EBF9F6', // tab样式的按钮选中后的背景色（浅绿色）
            'text-color': '#333333', // 文字的紫色（目前跟按钮是一个）
            'border-color': '#333333',
            'skeleton-color': '#f8f8f8'
          }
        }
      }
    },
    extract:
      process.env.NODE_ENV === 'production'
        ? {
            ignoreOrder: true
          }
        : false,
    sourceMap: false
  },
  configureWebpack: {
    optimization: {
      usedExports: true,
      sideEffects: true,
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 0,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        automaticNameDelimiter: '~',
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            name(module) {
              const packageName = module.context.match(
                /[\\/]node_modules[\\/](.*?)([\\/]|$)/
              )[1];
              return `npm.${packageName.replace('@', '')}`;
            }
          },
          common: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
            name: 'common'
          }
        }
      }
    }
  },
  chainWebpack: (config) => {
    config.plugin('define').tap((args) => {
      args[0].AUTH_DEBUG = JSON.stringify(false); // 不喜欢登录console提示的同学可以关闭这个选项
      args[0].__API_ENV_DEV_NUM__ = JSON.stringify(process.env.API_ENV_DEV_NUM);
      args[0].__API_ENV__ = JSON.stringify(process.env.API_ENV || 'prod');
      args[0].__MINI_PRIME_CHAIN_STORE__ = JSON.stringify(
        PRIME_PLATFORM === '__prime_chain_store__'
      );
      args[0].__PRIME_APPID__ = JSON.stringify(PRIME_APP_ID);
      // 添加日志大小限制配置
      args[0].__LOG_SIZE_LIMIT__ = JSON.stringify(2048); // 设置日志大小限制为2KB
      return args;
    });
    // 忽略第三方文件
    config.module.rule('js').exclude.add(/fp.min.js$/);

    // 图片压缩配置
    if (process.env.NODE_ENV === 'production') {
      config.module
        .rule('images')
        .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
        .use('image-webpack-loader')
        .loader('image-webpack-loader')
        .options({
          bypassOnDebug: true,
          mozjpeg: {
            progressive: true,
            quality: 80
          },
          optipng: {
            enabled: true
          },
          pngquant: {
            quality: [0.65, 0.9],
            speed: 4
          },
          gifsicle: {
            interlaced: false
          }
        });
    }

    // 压缩代码，减少Sentry错误日志大小
    if (process.env.NODE_ENV === 'production') {
      config.optimization.minimize(true);
      config.optimization.minimizer('terser').tap((args) => {
        const terserOptions = args[0].terserOptions;
        // 移除console日志，减少日志体积
        terserOptions.compress.drop_console = true;
        // 压缩错误消息
        terserOptions.compress.warnings = false;
        return args;
      });
    } else {
      // 开发环境可以不压缩
      config.optimization.minimize(false);
    }
  }
};
console.log('outputDir', process.env.UNI_OUTPUT_DIR);
console.log('编译平台', process.env.UNI_PLATFORM);
console.log('编译环境', process.env.API_ENV);
console.log('编译优享平台', process.env.PRIME_PLATFORM);
if (process.env.API_ENV === 'dev') {
  console.log('环境编号', process.env.API_ENV_DEV_NUM);
}
