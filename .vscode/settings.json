{"editor.formatOnSave": true, "editor.detectIndentation": false, "editor.insertSpaces": true, "editor.tabSize": 2, "vetur.format.defaultFormatter.js": "prettier-es<PERSON>", "vetur.format.defaultFormatter.html": "prettier", "editor.defaultFormatter": null, "eslint.format.enable": true, "prettier.embeddedLanguageFormatting": "off", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "javascript.format.enable": true, "eslint.alwaysShowStatus": true, "eslint.validate": ["javascript", "html"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.debug": true, "nuxt.isNuxtApp": false, "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "eslint.enable": true, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "notebook.formatOnSave.enabled": true, "vue3snippets.enable-compile-vue-file-on-did-save-code": true}