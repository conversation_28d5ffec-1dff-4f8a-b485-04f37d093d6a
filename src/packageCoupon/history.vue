<template>
  <div class="coupon-page">
    <div class="anchor">
      <div
        class="anchor-btn"
        :class="{ focus: type === item.type }"
        @click="onTypeChange(item)"
        v-for="item in anchorList"
        :key="item.type"
      >
        {{ item.text }}({{ item.count }})
      </div>
      <div
        class="anchor-slide"
        v-show="anchorVisible"
        :style="{
          transform: `translateX(${anchorLeft}px)`
        }"
      ></div>
    </div>
    <div class="coupon-list" v-if="fetching || list.length">
      <Card
        v-for="item in list"
        :cardData="item"
        :key="item.allowance_id || item.code_id"
        :showTypeTag="false"
        :scene="1"
      />
      <div class="history-tip" v-if="!fetching">仅展示90天内的优惠券记录</div>
    </div>
    <div class="coupon-empty" v-else>
      <img src="https://static.soyoung.com/sy-pre/empty-1685412600723.png" />
      <div>暂无历史{{ type === 'coupon' ? '红包' : '津贴' }}</div>
    </div>
    <PageLoading :visible="fetching" />
  </div>
</template>
<script>
import Card from '@/components/coupon/card.vue';
import { coupon_utils_formatCouponList } from '@/components/coupon/coupon-utils';

import {
  apiGetCoupon4CouponCenter,
  apiGetAllowance4CouponCenter
} from '@/api/coupon';
import { mapGetters } from 'vuex';

export default {
  pageTrackConfig: '',
  components: { Card },
  data() {
    return {
      anchorLeft: 0,
      type: 'coupon',
      anchorVisible: false,
      fetching: true,
      couponList: [],
      allowanceList: []
    };
  },
  computed: {
    ...mapGetters(['isLogin']),
    list() {
      return this.type === 'coupon' ? this.couponList : this.allowanceList;
    },
    anchorList() {
      return [
        { text: `红包`, type: 'coupon', count: this.couponList.length },
        { text: '津贴', type: 'allowance', count: this.allowanceList.length }
      ];
    },
    api() {
      return this.type === 'coupon'
        ? apiGetCoupon4CouponCenter
        : apiGetAllowance4CouponCenter;
    }
  },
  async onLoad() {
    if (!this.isLogin) {
      const isAuth = await this.$login().catch(() => null);
      if (!isAuth) return;
    }
    const res = await this.$queryNodes('.anchor .anchor-btn');
    this.$watch(
      'type',
      function (key) {
        const index = this.anchorList.findIndex((item) => item.type === key);
        const { left, width } = res[index];
        this.anchorLeft = left + width / 2;
      },
      { immediate: true }
    );
    this.eventChannel = this.getOpenerEventChannel();
    this.eventChannel.once(
      'coupon_center_history_coupon_data_pass',
      (options) => {
        const { type = 'coupon', couponList, allowanceList } = options;
        this.type = type;
        this.couponList = coupon_utils_formatCouponList('coupon')(
          couponList || []
        );
        this.allowanceList = coupon_utils_formatCouponList('allowance')(
          allowanceList || []
        );
        this.anchorVisible = true;
        this.fetching = false;
      }
    );
  },
  onUnload() {
    this.eventChannel = null;
  },
  methods: {
    async onTypeChange({ type }) {
      this.type = type;
      this.fetchData();
    },
    async fetchData() {
      this.fetching = true;
      const list = await this.api(2);
      if (this.type === 'coupon') {
        this.couponList = coupon_utils_formatCouponList('coupon')(list || []);
      } else {
        this.allowanceList = coupon_utils_formatCouponList('allowance')(
          list || []
        );
      }
      this.fetching = false;
    }
  }
};
</script>
<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.coupon-page {
  min-height: 100vh;
  background: #f6f6f6;
}
.anchor {
  .flex-center;
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  height: 60 * 2rpx;
  flex-basis: 60 * 2rpx;
  background-color: #fff;
  z-index: 6;
  .anchor-btn {
    flex: 1;
    position: relative;
    font-size: 18 * 2rpx;
    font-weight: 400;
    text-align: center;
  }
  > .focus {
    font-weight: 600;
  }
  .anchor-slide {
    position: absolute;
    bottom: 20rpx;
    left: -22rpx;
    height: 12rpx;
    width: 44rpx;
    background: url(https://static.soyoung.com/sy-design/3gcimprsb1n2m1717383404653.png)
      no-repeat center center transparent;
    background-size: contain;
    transition: transform 0.2s;
  }
}
.coupon-list {
  box-sizing: border-box;
  padding: 10rpx 30rpx 0;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
}
.coupon-empty {
  img {
    display: block;
    margin: 270rpx auto 24rpx;
    width: 35 * 2rpx;
    height: 35 * 2rpx;
  }
  div {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
}
.history-tip {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
  &::before,
  &::after {
    content: '';
    display: inline-block;
    margin: 4rpx;
    top: 50%;
    height: 1rpx;
    border-top: 1rpx solid #999999;
    width: 18rpx;
  }
}
</style>
