<template>
  <div class="coupon-page">
    <!-- <div class="anchor">
      <div
        class="anchor-btn"
        :class="{ focus: type === item.type }"
        @click="onTypeChange(item)"
        v-for="item in anchorList"
        :key="item.type"
      >
        {{ item.text }}({{ item.count }})
      </div>
      <div
        class="anchor-slide"
        v-show="anchorVisible"
        :style="{
          transform: `translateX(${anchorLeft}px)`
        }"
      ></div>
    </div> -->
    <div>
      <div v-if="type === 'coupon' && couponList.length" class="coupon-list">
        <CouponCard
          v-for="(item, index) in list"
          :cardData="item"
          :scene="1"
          :key="index"
          :valid="false"
        />
      </div>
      <div
        v-if="type === 'allowance' && allowanceList.length"
        class="coupon-list"
      >
        <CardAllowance
          v-for="(item, index) in list"
          :item="item"
          :valid="false"
          :key="index"
        />
      </div>
    </div>
    <div
      class="coupon-empty"
      v-if="!loading && type === 'coupon' && couponList.length === 0"
    >
      <div class="center">
        <img
          src="https://static.soyoung.com/sy-design/2hca7nc2ykif91728442434527.png"
        />
        <p>当前暂无红包</p>
      </div>
    </div>
    <div
      class="coupon-empty"
      v-if="!loading && type === 'allowance' && allowanceList.length === 0"
    >
      <div class="center">
        <img
          src="https://static.soyoung.com/sy-design/2hca7nc2ykif91728442434527.png"
        />
        <p>当前暂无津贴</p>
      </div>
    </div>
    <div
      class="history-tip"
      v-if="
        !loading &&
        ((type === 'coupon' && couponList.length) ||
          (type === 'allowance' && allowanceList.length))
      "
    >
      仅展示90天内的优惠券记录
    </div>
  </div>
</template>
<script>
import CouponCard from '@/components/coupon/card-eb.vue';
import CardAllowance from '@/components/coupon/card-allowance.vue';

export default {
  pageTrackConfig: 'sy_wxtuan_tuan_red_envelopes_page',
  components: { CouponCard, CardAllowance },
  data() {
    return {
      loading: false,
      type: 'coupon',
      couponList: [],
      allowanceList: [],
      anchorVisible: false,
      anchorLeft: 0,
      selected_code_id: 0,
      selected_allowance_id: 0,
      pid: '',
      code_id: ''
    };
  },
  computed: {
    // ...mapGetters(['isLogin'])
    scene() {
      if (this.pid) {
        return 3;
      } else {
        return 1;
      }
    },
    list() {
      return this.type === 'coupon' ? this.couponList : this.allowanceList;
    },
    anchorList() {
      return [
        { text: `红包`, type: 'coupon', count: this.couponList.length },
        { text: '津贴', type: 'allowance', count: this.allowanceList.length }
      ];
    }
  },

  methods: {
    async onTypeChange({ type }) {
      this.type = type;
      if (type === 'coupon') {
        await this.fetchCouponData();
      } else if (type === 'allowance') {
        await this.fetchAllowanceData();
      }
    },
    async fetchCouponData() {
      this.fetching = true;
      const res = await this.$request({
        url: '/syChainTrade/wxapp/coupon/couponPackage'
      });
      if (res.data.errorCode === 0) {
        this.couponList = res.data.responseData.unvalid;
      } else {
        uni.showToast({
          title: res.data.errorMsg
        });
      }
      this.fetching = false;
    },
    async fetchAllowanceData() {
      this.fetching = true;
      const res = await this.$request({
        url: '/syGroupBuy/chain/coupon/getAllowancePackage'
      });
      if (res.data.errorCode === 0) {
        this.allowanceList = res.data.responseData.unvalid;
      } else {
        uni.showToast({
          title: res.data.errorMsg
        });
      }

      this.fetching = false;
    }
  },
  async onLoad(opts) {
    const { type = 'coupon' } = opts;
    this.type = type;
    await this.fetchCouponData();
    await this.fetchAllowanceData();
    const res = await this.$queryNodes('.anchor .anchor-btn');
    this.$watch(
      'type',
      function (key) {
        const index = this.anchorList.findIndex((item) => item.type === key);
        const { left, width } = res[index];
        this.anchorLeft = left + width / 2;
        this.anchorVisible = true;
      },
      { immediate: true }
    );
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.coupon-page {
  box-sizing: border-box;
  min-height: 100vh;
  width: 100vw;
}
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.coupon-page {
  min-height: 100vh;
  // background: #f6f6f6;
}
.anchor {
  .flex-center;
  align-items: stretch;
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  height: 60 * 2rpx;
  flex-basis: 60 * 2rpx;
  background-color: #fff;
  z-index: 6;
  margin-bottom: 14rpx;
  .anchor-btn {
    flex: 1;
    position: relative;
    font-size: 14 * 2rpx;
    font-weight: 400;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  > .focus {
    font-weight: 600;
  }
  .anchor-slide {
    position: absolute;
    bottom: 20rpx;
    left: -22rpx;
    height: 2rpx;
    width: 44rpx;
    background-size: contain;
    transition: transform 0.2s;
    background-color: #333333;
  }
}
.coupon-list {
  box-sizing: border-box;
  padding: 40rpx 30rpx;
  background: #ffffff;
}
.coupon-empty {
  height: calc(100vh - 120rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    display: block;
    margin: 0 auto 24rpx;
    width: 35 * 2rpx;
    height: 35 * 2rpx;
  }
  div {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
}
.history-tip {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  padding: 40rpx 0;
  font-size: 24rpx;
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
}
</style>
