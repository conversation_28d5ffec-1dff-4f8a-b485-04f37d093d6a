<template>
  <scroll-view
    class="coupon-page"
    :scroll-y="true"
    :scroll-into-view="`coupon-${selected_code_id}`"
  >
    <!--<div style="height: 100vh"></div>-->
    <CouponCard
      v-for="(item, index) in couponList"
      :cardData="item"
      :scene="3"
      :checked="item.extend_info.check_yn"
      :key="index"
      :id="`coupon-${item.code_info.code_id}`"
      @afterChoose="afterChoose"
    />
    <div class="coupon-empty" v-if="!loading && couponList.length === 0">
      <div class="center">
        <img
          src="https://static.soyoung.com/sy-design/2hca7nc2ykif91728442434527.png"
        />
        <p>暂无可用红包</p>
      </div>
    </div>
    <!--<div style="height: 100vh"></div>-->
  </scroll-view>
</template>
<script>
import CouponCard from '@/components/coupon/card-eb.vue';
const eventBus = getApp().globalData.eventBus;

export default {
  pageTrackConfig: 'sy_wxtuan_tuan_red_envelopes_page',
  components: { CouponCard },
  data() {
    return {
      loading: true,
      couponList: [],
      selected_code_id: 0
    };
  },
  computed: {
    // ...mapGetters(['isLogin'])
  },
  methods: {
    afterChoose(item) {
      this.afterCouponChoose(item);
    },
    afterCouponChoose(item) {
      if (item.extend_info.check_yn === true) {
        uni.showModal({
          title: '温馨提示',
          content: '是否取消使用红包',
          showCancel: true,
          confirmText: '是',
          success: (res) => {
            if (res.confirm) {
              eventBus.$emit('coupon-select', {
                code_id: '',
                use_yn: 0
              });
              uni.showToast({
                title: `取消勾选${item.code_info.code_id}`,
                icon: 'none'
              });
              uni.navigateBack();
            }
          }
        });
      } else {
        eventBus.$emit('coupon-select', {
          code_id: item.code_info.code_id,
          use_yn: 1
        });
        uni.showToast({
          title: `勾选${item.code_info.code_id}`,
          icon: 'none'
        });
        uni.navigateBack();
      }
    }
  },
  async onLoad({ pid, code_id }) {
    this.loading = true;
    uni.showLoading({});
    const res = await this.$request({
      url: `/syGroupBuy/chain/coupon/getCouponPackage`,
      data: {
        pid,
        code_id
      }
    });
    if (res.data.errorCode === 0) {
      this.couponList = res.data.responseData.valid;
      this.loading = false;
      this.$nextTick(() => {
        this.selected_code_id = code_id;
      });
      uni.hideLoading();
    } else {
      uni.hideLoading();
      uni.showToast({
        title: res.data.errorMsg
      });
    }
  },
  onUnload() {
    this.eventChannel = null;
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.coupon-page {
  box-sizing: border-box;
  height: 100vh;
  width: 100vw;
  padding: 20rpx 30rpx 60rpx;
}
.coupon-empty {
  img {
    display: block;
    margin: 270rpx auto 24rpx;
    width: 35 * 2rpx;
    height: 35 * 2rpx;
  }
  div {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
}
</style>
