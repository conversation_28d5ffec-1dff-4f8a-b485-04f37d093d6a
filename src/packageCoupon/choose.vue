<template>
  <div class="coupon-page">
    <CouponCard
      v-for="(item, index) in list"
      :cardData="item"
      :scene="3"
      :checked="
        item.fe_type === 'coupon'
          ? selected_code_id === item.code_id
          : selected_allowance_id === item.allowance_id
      "
      :key="index"
      @afterChoose="afterChoose"
    />
    <PageLoading :visible="loading" />
  </div>
</template>
<script>
import { apiGetOrderAllowanceList, apiGetOrderCouponList } from '@/api/coupon';
import CouponCard from '@/components/coupon/card.vue';
import PageLoading from '@/components/pageLoading.vue';
// import { mapGetters } from 'vuex';

export default {
  pageTrackConfig: 'sy_wxtuan_tuan_red_envelopes_page',
  components: { CouponCard, PageLoading },
  data() {
    return {
      loading: false,
      list: [],
      selected_code_id: 0,
      selected_allowance_id: 0
    };
  },
  computed: {
    // ...mapGetters(['isLogin'])
  },
  methods: {
    afterChoose(item) {
      return item.fe_type === 'coupon'
        ? this.afterCouponChoose(item)
        : this.afterAllowanceChoose(item);
    },
    afterCouponChoose(item) {
      if (item.code_id === this.selected_code_id) {
        uni.showModal({
          title: '温馨提示',
          content: '是否取消使用红包',
          showCancel: true,
          confirmText: '是',
          success: (res) => {
            if (res.confirm) {
              this.eventChannel.emit('afterSelectCoupon', {
                code_id: '',
                use_yn: 0
              });
              uni.navigateBack();
            }
          }
        });
        return;
      }
      this.eventChannel.emit('afterSelectCoupon', {
        code_id: item.code_id,
        use_yn: 1
      });
      uni.navigateBack();
    },
    afterAllowanceChoose(item) {
      if (item.allowance_id === this.selected_allowance_id) {
        uni.showModal({
          title: '温馨提示',
          content: '是否取消使用津贴红包',
          showCancel: true,
          confirmText: '是',
          success: (res) => {
            if (res.confirm) {
              this.eventChannel.emit('afterSelectAllowance', {
                allowance_id: -1
              });
              uni.navigateBack();
            }
          }
        });
        return;
      }
      this.eventChannel.emit('afterSelectAllowance', {
        allowance_id: item.allowance_id
      });
      uni.navigateBack();
    },
    handleCouponList(list, exclude = '') {
      const result = [];
      let excludeList = exclude?.length > 0 ? exclude.split(',') : [];
      excludeList = excludeList.map((id) => Number(id));
      list.forEach((item) => {
        item.fe_type = 'coupon';
        item.code_id = Number(item.code_id);
        if (item.code_id === this.selected_code_id) {
          result.unshift(item);
        } else if (!excludeList.includes(item.code_id)) {
          result.push(item);
        }
      });
      return result;
    },
    handleAllowanceList(list) {
      const result = [];
      list.forEach((item) => {
        item.fe_type = 'allowance';
        item.allowance_id = Number(item.allowance_id);
        if (item.allowance_id === this.selected_allowance_id) {
          result.unshift(item);
        } else {
          result.push(item);
        }
      });
      return result;
    }
  },
  async onLoad() {
    this.eventChannel = this.getOpenerEventChannel();
    this.eventChannel.once('comfirmOrderChooseCoupon', async (params) => {
      const { code_id, exclude, pid, price } = params;
      this.selected_code_id = Number(code_id);
      this.loading = true;
      const list = await apiGetOrderCouponList(pid, price);
      this.list = this.handleCouponList(list, exclude);
      this.loading = false;
    });
    this.eventChannel.once('comfirmOrderChooseAllowance', async (params) => {
      const { allowance_id, price } = params;
      this.selected_allowance_id = Number(allowance_id);
      this.loading = true;
      const list = await apiGetOrderAllowanceList(allowance_id, price);
      this.list = this.handleAllowanceList(list);
      this.loading = false;
    });
  },
  onUnload() {
    this.eventChannel = null;
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.coupon-page {
  box-sizing: border-box;
  min-height: 100vh;
  width: 100vw;
  padding: 20rpx 30rpx;
}
</style>
