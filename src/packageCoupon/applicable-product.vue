// 适用商品
<template>
  <scroll-view class="product-page" :scroll-y="true" style="height: 100%">
    <template v-if="productList.length > 0">
      <ProductCard
        v-for="item in productList"
        :key="item.id"
        :product="item"
      ></ProductCard>
    </template>
    <div class="product-empty" v-if="!productList.length || !hasMore">
      <div class="center">
        <p>没有更多了</p>
      </div>
    </div>
  </scroll-view>
</template>
<script>
const eventBus = getApp().globalData.eventBus;
import ProductCard from '@/components/productCard/productCard.vue';

export default {
  components: {
    ProductCard
  },
  data() {
    return {
      productList: [],
      pageNum: 0,
      hasMore: false,
      couponId: '',
      userInfo: {}
    };
  },
  methods: {
    afterChoose(item) {
      this.afterCouponChoose(item);
    },
    afterCouponChoose(item) {
      if (item.extend_info.check_yn === true) {
        uni.showModal({
          title: '温馨提示',
          content: '是否取消使用红包',
          showCancel: true,
          confirmText: '是',
          success: (res) => {
            if (res.confirm) {
              eventBus.$emit('product-select', {
                code_id: '',
                use_yn: 0
              });
              uni.showToast({
                title: `取消勾选${item.code_info.code_id}`,
                icon: 'none'
              });
              uni.navigateBack();
            }
          }
        });
      } else {
        eventBus.$emit('product-select', {
          code_id: item.code_info.code_id,
          use_yn: 1
        });
        uni.showToast({
          title: `勾选${item.code_info.code_id}`,
          icon: 'none'
        });
        uni.navigateBack();
      }
    },
    async getList() {
      const data = {
        coupon_id: this.couponId,
        page: this.pageNum
      };
      if (this.userInfo.hospital?.city_id) {
        data.city_id = this.userInfo.hospital?.city_id;
        data.cityId = this.userInfo.hospital?.city_id;
      }
      const res = await this.$request({
        url: `/syChainTrade/wxapp/coupon/couponSkuList`,
        data
      });
      console.log('res--->', res.data);
      if (res.data.errorCode === 0) {
        this.hasMore = res.data.responseData.has_more;
        if (this.pageNum === 0) {
          this.productList = res.data.responseData.sku_list;
        } else {
          this.productList = this.productList.concat(
            res.data.responseData.sku_list
          );
        }
        uni.hideLoading();
      } else {
        uni.hideLoading();
        uni.showToast({
          title: res.data.errorMsg
        });
      }
    }
  },
  async onLoad({ id }) {
    this.couponId = id;
    this.userInfo = uni.getStorageSync('user_info') || {};
    this.getList();
  },
  onReachBottom() {
    if (this.hasMore) {
      this.pageNum++;
      this.getList();
    }
  },
  onUnload() {
    this.eventChannel = null;
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.product-page {
  box-sizing: border-box;
  height: 100vh;
  width: 100vw;
  padding: 20rpx 30rpx 60rpx;
  // .product-item {
  // }
}
.product-empty {
  img {
    display: block;
    margin: 270rpx auto 24rpx;
    width: 64 * 2rpx;
    height: 50 * 2rpx;
  }
  div {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
}
</style>
