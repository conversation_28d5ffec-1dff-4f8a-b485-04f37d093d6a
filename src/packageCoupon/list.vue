<template>
  <div class="page-wrap">
    <div class="nav">
      <div class="left">
        <div
          v-for="item in typeItems"
          :key="item.type"
          class="btn"
          :class="{
            selected: item.type === type
          }"
          @click="onTypeChange(item.type)"
        >
          {{ item.text }}
        </div>
      </div>
      <div class="right" @click="onRule">
        使用说明
        <i></i>
      </div>
    </div>
    <div v-if="type === 'coupon' && couponList.length" class="coupon-list">
      <CouponCard
        v-for="(item, index) in couponList"
        :cardData="item"
        :key="index"
        :valid="true"
        :show-go-use-btn="true"
      />
    </div>
    <div
      v-if="type === 'allowance' && allowanceList.length"
      class="coupon-list"
    >
      <CardAllowance
        v-for="(item, index) in allowanceList"
        :item="item"
        :valid="true"
        :show-go-use-btn="true"
        :key="index"
      />
    </div>
    <div
      class="coupon-empty"
      v-if="!fetching && type === 'coupon' && couponList.length === 0"
    >
      <div class="center">
        <img
          src="https://static.soyoung.com/sy-design/2hca7nc2ykif91728442434527.png"
        />
        <p>暂无可用红包</p>
      </div>
    </div>
    <div
      class="coupon-history"
      v-if="!fetching && type === 'coupon'"
      :class="{ whiteBg: couponList.length }"
      @click="on2history"
    >
      查看历史优惠券
    </div>
    <div
      class="coupon-empty"
      v-if="!fetching && type === 'allowance' && allowanceList.length === 0"
    >
      <div class="center">
        <img
          src="https://static.soyoung.com/sy-design/2hca7nc2ykif91728442434527.png"
        />
        <p>暂无可用津贴</p>
      </div>
    </div>
    <div
      class="coupon-history"
      v-if="!fetching && type === 'allowance'"
      :class="{ whiteBg: allowanceList.length }"
      @click="on2history"
    >
      查看历史津贴
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

import CouponCard from '@/components/coupon/card-eb.vue';
import CardAllowance from '@/components/coupon/card-allowance.vue';

export default {
  pageTrackConfig: 'sy_wxtuan_tuan_yhq_info_page',
  components: {
    CardAllowance,
    CouponCard
  },
  watch: {
    isLogin(val) {
      if (val) {
        this.fetchPageInfo();
      }
    }
  },
  async onLoad(opts = {}) {
    const { type = 'coupon' } = opts;
    console.log(type);
    this.type = type;
    uni.showLoading();
    if (type === 'coupon') {
      await this.fetchCouponData();
      await this.fetchAllowanceData();
    } else if (type === 'allowance') {
      this.type = 'allowance';
      await this.fetchAllowanceData();
      await this.fetchCouponData();
    }
    this.fetching = false;
  },
  data() {
    return {
      fetching: false,
      type: 'coupon',
      focusTab: 0,
      couponList: [],
      allowanceList: [],
      hasCouponHistory: false,
      hasAllowanceHistory: false,
      coupon_total: 0,
      allowance_num: 0
    };
  },
  computed: {
    ...mapGetters(['isLogin']),
    typeItems() {
      return [
        {
          type: 'coupon',
          text: `红包(${this.couponList.length})`
        },
        {
          type: 'allowance',
          text: `津贴(${this.allowanceList.length})`
        }
      ];
    },
    anchorList() {
      return [
        {
          text: '优惠券',
          count: this.coupon_total
        },
        {
          text: '会员积分',
          count: this.points_total
        }
      ];
    }
  },
  methods: {
    async fetchCouponData() {
      const res = await this.$request({
        url: '/syGroupBuy/chain/coupon/getCouponPackage',
        data: {
          pid: this.pid,
          code_id: this.code_id
        }
      }).catch((e) => {
        uni.hideLoading();
        uni.showToast({
          title: '请求出错' + e.message,
          icon: 'none'
        });
      });
      uni.hideLoading();
      if (res.data.errorCode === 0) {
        this.couponList = res.data.responseData.valid;
        this.hasCouponHistory = res.data.responseData.unvalid.length;
      } else {
        uni.showToast({
          title: res.data.errorMsg
        });
      }
    },
    async fetchAllowanceData() {
      this.fetching = true;
      const res = await this.$request({
        url: '/syGroupBuy/chain/coupon/getAllowancePackage',
        data: {
          pid: this.pid,
          code_id: this.code_id
        }
      }).catch((e) => {
        uni.hideLoading();
        uni.showToast({
          title: '请求出错' + e.message,
          icon: 'none'
        });
      });
      if (res.data.errorCode === 0) {
        this.allowanceList = res.data.responseData.valid;
        this.hasAllowanceHistory = res.data.responseData.unvalid.length;
      } else {
        uni.showToast({
          title: res.data.errorMsg
        });
      }
      uni.hideLoading();
    },
    onRule() {
      console.log('onRule');
      uni.navigateTo({
        url: `/pages/h5?url=${encodeURIComponent(
          'https://m.soyoung.com/tmwap24982'
        )}`
      });
    },
    on2history() {
      console.log('onHistory');
      uni.navigateTo({
        url: `/packageCoupon/history-eb?type=${this.type}`
      });
      this.$reportData({
        info: 'sy_wxtuan_tuan_yhq_info:his_bt_click',
        ext: {}
      });
    },
    onTypeChange(type) {
      this.type = type;
      // if (type === 'coupon') {
      //
      // } else {
      //
      // }
    }
  }
};
</script>
<style lang="less" scoped>
.page-wrap {
  background: #f8f8f6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.nav {
  position: sticky;
  top: 0;
  left: 0;
  box-sizing: border-box;
  padding: 0 30rpx;
  height: 47 * 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  z-index: 10;
  .left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .btn {
      margin-right: 30rpx;
      height: 27 * 2rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      padding: 0 30rpx;
      font-size: 24rpx;
      color: #555;
      font-weight: 400;
      background: #f3f3f3;
      border-radius: 27 * 2rpx / 2;

      &.selected {
        color: #00ab84;
        background: #e2f8f1;
        font-weight: 600;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .right {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 26rpx;
    color: #666666;
    font-weight: 400;

    i {
      margin-left: 8rpx;
      height: 26rpx;
      width: 26rpx;
      background: url(https://static.soyoung.com/sy-pre/info-1685081400731.png)
        no-repeat center center transparent;
      background-size: contain;
    }
  }
}

.coupon-list {
  box-sizing: border-box;
  padding: 20rpx 30rpx 0;
  background: #ffffff;
}

.coupon-empty {
  flex: 1;
  img {
    display: block;
    margin: 270rpx auto 24rpx;
    width: 35 * 2rpx;
    height: 35 * 2rpx;
  }
  div {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
}
.coupon-history {
  padding: 20rpx 0 40rpx;
  font-size: 24rpx;
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
  text-align: center;
  margin-bottom: 120rpx;
  &.whiteBg {
    background: #ffffff;
  }
  &::after {
    content: '';
    display: inline-block;
    vertical-align: -2%;
    width: 22rpx;
    height: 20rpx;
    background: url(https://static.soyoung.com/sy-pre/to-1685412600723.png)
      center center transparent;
    background-size: contain;
  }
}
</style>
