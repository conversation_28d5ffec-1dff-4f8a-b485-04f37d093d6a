// 某个城市下的机构列表
<template>
  <div class="hospital-list">
    <div
      class="item"
      v-for="item in list"
      :key="item.id"
      @click="handleCard(item)"
    >
      <cardTb :info="item"></cardTb>
    </div>
  </div>
</template>

<script>
import cardTb from './components/hospital-list/cardTb.vue';
import { getTenantOneCityList } from '@/api/hospital';
export default {
  name: 'hospital-city-list',
  components: {
    cardTb
  },
  data() {
    return {
      list: [],
      cityId: ''
    };
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_chain_store_tuan_city_hospital_list_page',
      ext: {
        city_id: this.cityId || ''
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_city_hospital_list_page',
      ext: {
        city_id: this.cityId || ''
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_city_hospital_list_page',
      ext: {
        city_id: this.cityId || ''
      }
    });
  },
  onLoad(query) {
    this.cityId = query.cityId || '';
    if (this.cityId) {
      this.getAllCityHospital();
    }
    if (query.cityName) {
      wx.setNavigationBarTitle({
        title: query.cityName || '北京' // 新标题
      });
    }
  },
  methods: {
    // 获取所有城市的门店列表
    async getAllCityHospital() {
      const { tenant_list } = await getTenantOneCityList({
        city_id: this.cityId || ''
      });
      this.list = tenant_list;
    },
    handleCard(item) {
      this.$reportData({
        info: 'sy_chain_store_tuan_city_hospital_list:city_card_click',
        ext: {
          hospital_id: item.soyoung_hospital_id
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.hospital-list {
  padding: 0 15px 30px;
  .item {
    margin-bottom: 20px;
  }
}
</style>
