<template>
  <page-meta :page-style="pageStyle">
    <PageLoading :visible="loading" />
    <div
      class="doctor-home-page"
      :class="{
        sketelon
      }"
    >
      <nav-bar
        :background="background"
        :title="title"
        :hasFixedHome="true"
        :hasBack="true"
        @onBack="navBack"
      ></nav-bar>
      <div class="doctor-info">
        <div class="doctor-info_image">
          <img
            class="image-cover"
            :style="{
              width: `${cover ? cover.w : doctorInfo.image.w}px`,
              height: `${cover ? cover.h : doctorInfo.image.h}px`
            }"
            :src="cover && cover.u ? cover.u : doctorInfo.image.u"
          />
          <div class="image-left">
            <img
              mode="widthFix"
              :src="
                doctorInfo.background.left_img.u ||
                'https://static.soyoung.com/sy-design/bg1734926073460.png'
              "
            />
          </div>
          <div
            class="image-right"
            :style="{ backgroundColor: doctorInfo.background.right_color }"
          ></div>
          <div class="image-right-info">
            <img
              class="right-top_tips"
              src="https://static.soyoung.com/sy-design/2hca9ziqfkuif1735185566940.png"
            />
            <div
              :class="['right-doctor_info', { 'active': infoTagsHeight != 0 }]"
              :style="{ marginTop: `${infoTagsHeight}px` }"
            >
              <span class="info-name_tag">
                <text class="name"
                  >{{ doctorInfo.info.name }}
                  <text class="line"></text>
                  <text class="tag">{{ doctorInfo.info.title_show }}</text>
                </text>
              </span>
              <div
                class="info-certificate"
                v-if="doctorInfo.info.certificate_number"
              >
                <div class="name">执业证书编号：</div>
                <div class="number">
                  {{ doctorInfo.info.certificate_number }}
                </div>
              </div>
              <div
                class="info-line"
                v-if="doctorInfo.info.tags && doctorInfo.info.tags.length > 0"
              ></div>
              <div
                class="info-tags"
                v-if="doctorInfo.info.tags && doctorInfo.info.tags.length > 0"
              >
                <div
                  class="tags-box"
                  v-for="(item, index) in doctorInfo.info.tags"
                  :key="index"
                  @click="onClickTag(item)"
                >
                  <div :class="['box-wrap', { 'active': item.tag_style == 1 }]">
                    {{ item.tag_name }}
                  </div>
                  <div class="box-tips">{{ item.service_str }}</div>
                  <div class="box-number">{{ item.service_num }}</div>
                </div>
              </div>
            </div>
            <div class="info-footer-description">
              <img
                src="https://static.soyoung.com/sy-design/logo1739505224303.png"
                alt=""
              />
              <!-- <div
                v-html="doctorInfo.background.right_desc_clinic"
                class="info-footer-description-p"
              ></div> -->
              <text class="info-footer-description-p">{{
                formattedText(doctorInfo.background.right_desc_clinic)
              }}</text>
            </div>
            <!-- TODO: SY-B-20250088需求更改 -->
            <!-- <img
              class="right-bottom_tips"
              src="https://static.soyoung.com/sy-design/aloki500w7841735185567165.png"
            /> -->
          </div>
        </div>
        <div
          class="doctor-info-extra"
          v-if="doctorInfo.description && doctorInfo.description.length > 0"
        >
          <Description
            v-for="(item, index) in doctorInfo.description"
            :key="index"
            :description="item"
            :showLine="index != doctorInfo.description.length - 1"
          ></Description>
        </div>
        <div style="height: 104rpx; width: 100vw"></div>
        <div class="doctor-info-share">
          <button open-type="share" @click="report">分享主页</button>
        </div>
      </div>
      <WeakNetwork
        @refresh="refresh"
        :path="['/syGroupBuy/chain/hospital/doctorInfo']"
        :visible.sync="isNetwork"
      />
      <DoctorTags v-model="showTagStatus" :tag_id="tag_id"></DoctorTags>
    </div>
  </page-meta>
</template>
<script>
import { getDoctorInfoApi } from '@/api/hospital';
import NavBar from '@/components/NavBar';
import PageLoading from '@/components/pageLoading.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import Description from '@/packageHospital/components/description.vue';
import DoctorTags from '@/packageHospital/components/doctorTags.vue';

export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_other_hospital_doctor_page_page',
      ext: {
        doctor_id: this.doctor_id
      }
    };
  },
  components: {
    NavBar,
    Description,
    PageLoading,
    WeakNetwork,
    DoctorTags
  },
  data() {
    return {
      loading: true,
      sketelon: true,
      doctor_id: 0,
      doctorInfo: null, // 医生信息
      background: 'rgba(255,255,255,0)',
      title: '',
      isNetwork: false,
      showTagStatus: false,
      infoTagsHeight: 0,
      tag_id: '',
      cover: null
    };
  },
  computed: {
    pageStyle() {
      return `overflow-y: ${this.showTagStatus ? 'hidden' : 'scroll'}`;
    }
  },
  onLoad(query) {
    this.eventChannel = this.getOpenerEventChannel();
    this.eventChannel.on('packageHospital_doctor_click', (data) => {
      if (data.cover) {
        this.cover = data.cover;
      }
    });
    this.$once('hook:beforeDestroy', () => {
      this.eventChannel.off('packageHospital_doctor_click');
    });
    this.doctor_id = query.doctor_id || '';
    this.tag_id = query.tag_id || '';
    if (this.tag_id) {
      this.showTagStatus = true;
    }
    this.getDoctorData();
  },
  // 分享给好友
  onShareAppMessage() {
    return {
      title: '新氧青春诊所',
      path: `/packageHospital/hospital-doctor?doctor_id=${this.doctor_id}`
      // imageUrl:
      //   'https://static.soyoung.com/sy-pre/sharehospital-1722327000635.jpeg'
    };
  },
  // 朋友圈分享
  onShareTimeline() {
    return {
      title: '新氧青春诊所',
      path: `/packageHospital/hospital-doctor?doctor_id=${this.doctor_id}`
      // imageUrl:
      //   'https://static.soyoung.com/sy-pre/sharehospital-1722327000635.jpeg'
    };
  },
  methods: {
    navBack() {
      uni.navigateBack();
    },
    async refresh() {
      await this.getDoctorData();
    },
    async getDoctorData() {
      this.loading = true;
      const responseData = await getDoctorInfoApi(this.doctor_id);
      this.loading = false;
      if (responseData) {
        this.isNetwork = false;
        this.doctorInfo = responseData;
        this.sketelon = false;
      } else {
        uni.showModal({
          title: '提示',
          content: '接口请求失败',
          confirmText: '重试',
          cancelText: '返回',
          success: (resp) => {
            if (resp.confirm) {
              this.getDoctorData();
            } else {
              uni.navigateBack();
            }
          }
        });
      }
      this.$nextTick(() => {
        this.getInfoTagsHeight();
      });
    },
    getInfoTagsHeight() {
      let tagBoxHeight = 0;
      const query = uni.createSelectorQuery();
      query
        .select('.right-doctor_info')
        .boundingClientRect((rect) => {
          tagBoxHeight = rect.height;
          this.infoTagsHeight = (495 - 172 - tagBoxHeight) / 4;
        })
        .exec();
    },
    onClickTag(item) {
      this.showTagStatus = true;
      this.tag_id = item.tag_id;
      this.$reportData({
        info: 'sy_chain_store_other_hospital_doctor_page:label_click',
        ext: {
          tag_id: item.tag_id,
          tag_name: item.tag_name
        }
      });
    },
    formattedText(val) {
      return val.replace(/↵/g, '\n');
    }
  },
  onPageScroll({ scrollTop }) {
    const show = scrollTop > 100 ? 1 : scrollTop / 100;
    this.background = `rgba(255,255,255,${show})`;
    this.title = show === 1 ? '医生主页' : '';
  }
};
</script>
<style lang="less" scoped>
.doctor-home-page {
  min-height: 100vh;
  background-color: #f2f2f2;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  @sketelonBackgroundColor: #f6f6f6;
  .doctor-info {
    .doctor-info_image {
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      align-items: center;
      height: 990rpx;
      width: 100vw;
      position: relative;
      overflow: hidden;
      .image-left {
        width: calc(100vw - 330rpx);
        height: 100%;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .image-right {
        width: 330rpx;
        height: 100%;
        background-color: #a9ea6a;
        box-sizing: border-box;
        padding-left: 40rpx;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        position: relative;
        z-index: 50;
      }
      .image-right-info {
        width: 330rpx;
        height: 100%;
        box-sizing: border-box;
        padding-left: 40rpx;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        position: absolute;
        z-index: 200;
        right: 0;
        bottom: 0;
        .right-top_tips {
          margin-top: 126rpx;
          height: 32rpx;
          display: inline-block;
          width: 212rpx;
        }
        .right-doctor_info {
          display: flex;
          justify-content: flex-start;
          flex-direction: column;
          opacity: 0;
          &.active {
            opacity: 1;
          }
          .info-name_tag {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: baseline;
            margin-right: 10rpx;
            line-height: 40rpx;
            .name {
              font-family: DFKingGothicSC16-Regular;
              font-size: 44rpx;
              color: #333333;
              letter-spacing: 0;
              text-align: left;
              font-weight: 400;
              word-wrap: break-word;
              word-break: break-all;
              vertical-align: middle;
            }
            .line {
              width: 1rpx;
              height: 20rpx;
              background-color: #333333;
              margin: 0 10rpx;
              display: inline-block;
              vertical-align: middle;
            }
            .tag {
              font-family: DFKingGothicSC16-Medium;
              font-size: 20rpx;
              color: #333333;
              letter-spacing: 0;
              font-weight: 500;
              vertical-align: middle;
            }
          }
          .info-certificate {
            margin-top: 18rpx;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            .name {
              font-family: DFKingGothicSC16-Regular;
              font-size: 18rpx;
              color: #333333;
              letter-spacing: 0;
              font-weight: 400;
            }
            .number {
              font-family: OutFit-Regular;
              font-size: 18rpx;
              color: #333333;
              letter-spacing: 0;
              font-weight: 200;
            }
          }
          .info-line {
            margin: 32rpx 0 26rpx;
            width: 100%;
            height: 1rpx;
            background-color: #333333;
          }
          .info-tags {
            .tags-box:nth-child(2n) {
              margin-right: 0rpx;
            }
            .tags-box {
              display: inline-flex;
              flex-direction: column;
              justify-content: flex-start;
              margin-right: 54rpx;
              margin-bottom: 2rpx;
              .box-wrap {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                font-family: PingFangSC-Medium;
                font-size: 20rpx;
                color: #ffffff;
                letter-spacing: 0;
                font-weight: 500;
                padding: 0 4rpx;
                background: #333333;
                &.active {
                  background-color: #ff6c0e;
                }
              }
              .box-tips {
                font-family: PingFangSC-Regular;
                font-size: 20rpx;
                color: #333333;
                letter-spacing: 0;
                font-weight: 400;
                margin-top: 2rpx;
              }
              .box-number {
                font-family: Outfit-ExtraLight;
                font-size: 34rpx;
                color: #333333;
                letter-spacing: 0;
                font-weight: 200;
                margin-top: -4rpx;
              }
            }
          }
        }
        .right-bottom_tips {
          height: 152rpx;
          width: 164rpx;
          position: absolute;
          bottom: 30rpx;
          right: 30rpx;
        }
      }
      .image-cover {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 100;
      }
    }
    .doctor-info-extra {
      padding: 60rpx 60rpx 0rpx;
    }
    .doctor-info-share {
      position: fixed;
      bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
      bottom: env(safe-area-inset-bottom);
      left: 50%;
      transform: translateX(-50%);
      margin-bottom: 40rpx;
      padding: 0rpx 45rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100vw;
      box-sizing: border-box;
      & > button {
        flex: 1;
        height: 84rpx;
        line-height: 84rpx;
        background: #333333;
        font-size: 26rpx;
        color: #ffffff;
        text-align: center;
        border-radius: 0;
        border: 0;
        //font-weight: 500;
      }
    }
  }
  &.sketelon {
    .img-cover {
      background-color: #f6f6f6;
      img {
        opacity: 0;
      }
    }
    .section-wrap {
      color: @sketelonBackgroundColor;
      .section-title {
        width: 150rpx;
        overflow: hidden;
        white-space: nowrap;
        background-color: @sketelonBackgroundColor;
      }
    }
    .buy-button {
      display: none;
    }
  }

  .info-footer-description {
    display: flex;
    flex-direction: column;
    position: absolute;
    bottom: 30rpx;
    right: 30rpx;
    // max-width: 192rpx;
    align-items: flex-end;
    img {
      width: 164rpx;
      height: 16rpx;
      margin-bottom: 10rpx;
    }
    &-p {
      width: 100%;
      font-family: PingFangSC-Light;
      font-size: 14rpx;
      color: #333333;
      font-weight: 200;
      text-align: right;
      line-height: 26rpx;
    }
  }
}
</style>
