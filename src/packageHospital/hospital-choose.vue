<template>
  <div class="hospital-map-list-page">
    <Searcher
      :city-id.sync="wantCityId"
      @filter="afterSearch"
      @city-open="afterCityOpen"
      @city-close="afterCityClose"
      @city-change="onCityChange"
    >
      <hospitalList
        :hospital-id="hospitalId"
        max-height="100vh - 104rpx"
        :filter-text="searchText"
        :skeleton="skeleton"
        :show-count="false"
        :hospital-list="hospitalList"
        @after-select="afterSelect"
      />
    </Searcher>
    <map
      :enable-scroll="mapEnabled"
      :enable-zoom="mapEnabled"
      class="map-container"
      id="map_container"
      :longitude="lng"
      :latitude="lat"
      :show-location="true"
      :scale="zoom"
      :min-scale="3"
      :max-scale="20"
      :markers="markers"
      @markertap="handleMarkerTap"
      @callouttap="handleCalloutTap"
    >
      <cover-view slot="callout">
        <block v-for="hos in hospitalList" :key="hos.hospital_id">
          <cover-view
            :marker-id="hos.hospital_id"
            class="callout-box-wraper"
            v-if="hos.hospital_id === hospitalId"
          >
            <cover-view class="callout-box">
              {{
                hos.hospital_name.length > 28
                  ? hos.hospital_name.slice(0, 28) + '...'
                  : hos.hospital_name
              }}
              <cover-image
                class="callout-arrow"
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1725611272079.png"
              ></cover-image>
            </cover-view>
          </cover-view>
        </block>
      </cover-view>
      <view
        class="user-position"
        @click="handleFindMe"
        v-if="scopeUserLocation && hasLocation"
      >
        <image
          class="position-icon"
          src="https://static.soyoung.com/sy-pre/1xqkn5at6qx8z-1725934200651.png"
        ></image>
      </view>
      <view class="location-tip" v-else>
        <image
          class="left"
          src="https://static.soyoung.com/sy-pre/1xqkn5at6qx8z-1725934200651.png"
        ></image>
        <view class="center">开启定位，为您匹配附近门店</view>
        <view class="right" @click="handleGetLocationScope">去开启</view>
      </view>
    </map>
    <hospitalList
      :hospital-id="hospitalId"
      :skeleton="skeleton"
      :hospital-list="hospitalList"
      @after-select="afterSelect"
    />
    <root-portal>
      <PageLoading :visible="loading" />
    </root-portal>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import Searcher from './components/hospital-choose/search.vue';
import hospitalList from './components/hospital-choose/hospital-list.vue';
import { cloneDeep, throttle } from 'lodash-es';
import PageLoading from '@/components/pageLoading.vue';
import {
  getHospitalListInChoosePage,
  getRecommendCityId
} from '@/api/hospital.js';

export default {
  pageTrackConfig() {
    return {
      info: 'sy_clinic_other_change_stores_list_page',
      ext: {
        hospital_id: this.hospitalId
      }
    };
  },
  components: {
    hospitalList,
    PageLoading,
    Searcher
  },
  data() {
    return {
      loading: true,
      zoom: 13,
      markers: [], // 显示在地图上的marker
      scopeUserLocation: true,
      hospitalList: [],
      searchText: '',
      hasLocation: true,
      skeleton: true,
      mapEnabled: true,
      wantCityId: 1,
      hospitalId: -1
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo,
      cityId: (state) => state.global.userInfo.cityId
    }),
    storeHospital() {
      return this.userInfo.hospital || null;
    },
    chooseHospital() {
      return this.hospitalList.find(
        ({ hospital_id }) => Number(hospital_id) === this.hospitalId
      );
    },
    lng() {
      return this.chooseHospital?.hospital_lng || 116.407387; // 默认北京天安门
    },
    lat() {
      return this.chooseHospital?.hospital_lat || 39.904179; // 默认北京天安门
    }
  },
  methods: {
    afterCityOpen() {
      this.mapEnabled = false;
    },
    afterCityClose() {
      this.mapEnabled = true;
    },
    // 新建地图对象
    initMap() {
      if (!this.map) {
        this.map = wx.createMapContext('map_container');
      }
    },
    // 获取机构列表
    async fetchHospitalList() {
      this.loading = true;
      let args;
      if (this.scopeUserLocation) {
        // 是否使用公参数
        args = [this.cityId, this.wantCityId, null];
      } else {
        args = [
          '',
          this.wantCityId,
          {
            lat: '',
            lng: '',
            cityId: '',
            cityName: ''
          }
        ];
      }

      const responseData = await getHospitalListInChoosePage(...args);
      if (!responseData || !Array.isArray(responseData.list)) {
        uni.showModal({
          title: '提示',
          content: '获取机构数据异常',
          confirmText: '重试',
          cancelText: '返回',
          success: (resp) => {
            if (resp.confirm) {
              this.fetchHospitalList();
            } else {
              uni.navigateBack();
            }
          }
        });
        this.loading = false;
        return;
      }
      this.setHospitalList(responseData.list);
    },
    // 搜索
    afterSearch(value) {
      this.searchText = value;
    },
    // 获取地理位置定位
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        // 解决下不然会抛出错误
        return {};
      });
      this.hasLocation = Boolean(res?.lat && res?.lng);
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    },
    // 完成机构选择
    afterSelect(hospital) {
      this.PageLoading = true;
      this.$setUserInfoToStorage({ hospital: cloneDeep(hospital) });
      // uni.setStorageSync('want_city_id', this.wantCityId);
      uni.switchTab({
        url: `/pages/item`
      });
      this.PageLoading = false;
    },
    // 城市发生改变
    onCityChange() {
      this.fetchHospitalList();
    },
    // 授权地理定位
    handleGetLocationScope: throttle(
      function () {
        wx.getSetting({
          success: (res) => {
            const setting = res.authSetting['scope.userLocation'];
            if (setting === undefined) {
              this.getLocation();
            } else {
              wx.openSetting();
              this.toScopeUserLocationFlg = true;
            }
          }
        });
      },
      1500,
      { trailing: false }
    ),
    // 点击气泡
    handleCalloutTap: throttle(
      function (evt) {
        const hospitalId = Number(evt.detail.markerId);
        const hospital = this.hospitalList.find(
          ({ hospital_id }) => Number(hospital_id) === hospitalId
        );
        if (!hospital) return;
        this.afterSelect(hospital);
      },
      1500,
      { trailing: false }
    ),
    // 点击标记物
    handleFindMe: throttle(
      function () {
        const { lat, lng } = this.userInfo;
        if (!this.hasLocation) return;
        this.map.moveToLocation({
          longitude: lng,
          latitude: lat
        });
      },
      1500,
      { trailing: false }
    ),
    // 标记物点击
    handleMarkerTap: throttle(
      function (e) {
        if (e.markerId === -1) return;
        this.hospitalId = e.markerId;
      },
      1500,
      { trailing: false }
    ),
    navBack: throttle(
      function () {
        uni.navigateBack();
      },
      1500,
      { trailing: false }
    ),
    sleep(defer = 200) {
      return new Promise((r) => setTimeout(r, defer));
    },
    async setHospitalList(list) {
      console.log('setHospitalList', list, this.storeHospital);
      if (!list) {
        this.hospitalList = [];
        this.loading = false;
        this.skeleton = false;
        return;
      }
      this.hospitalList = list;

      await this.sleep();
      // 如果没有传递 hospital 参数说明没有选中
      if (this.storeHospital) {
        const item = this.hospitalList.find((item) => {
          if (item.hospital_id === +this.storeHospital.hospital_id) {
            return true;
          }
        });
        if (item) {
          this.hospitalId = item.hospital_id;
        } else {
          this.hospitalId = Number(this.hospitalList[0]?.hospital_id || -1);
        }
      } else {
        this.hospitalId = Number(this.hospitalList[0]?.hospital_id || -1);
      }
      this.markers = this.hospitalList.map((hospital, index) => {
        const { hospital_id, hospital_lng, hospital_lat, new_icon } = hospital;
        return {
          id: hospital_id,
          latitude: hospital_lat,
          longitude: hospital_lng,
          iconPath: new_icon,
          width: 25,
          height: 25,
          index,
          customCallout: {
            anchorY: -5,
            anchorX: 0,
            display: 'ALWAYS'
          },
          data: hospital
        };
      });
      // this.setIncludePoints(list);
      this.loading = false;
      this.skeleton = false;
    }
  },
  onLoad(options) {
    this.initMap();
    this.prefetch = Number(options.prefetch || '');
    this.wantCityId = Number(options.want_city_id);
    // uni.setStorageSync('want_city_id', this.wantCityId);
    if (this.prefetch === 1) {
      this.eventChannel = this.getOpenerEventChannel();
      this.eventChannel.once('hospitalMapListData', async (p) => {
        const { list } = await p;
        this.setHospitalList(list);
        this.prefetch = 0;
      });
    }
  },
  async onShow() {
    const isPrefetch = this.prefetch;
    this.scopeUserLocation = await this.checkScopeOfUserLocation();
    await this.getLocation();
    if (this.scopeUserLocation && this.toScopeUserLocationFlg) {
      const res = await getRecommendCityId();
      this.wantCityId = res?.recommend_city_id || 1;
    }
    if (!isPrefetch) {
      this.fetchHospitalList();
    }
    this.$reportPageShow({
      info: 'sy_chain_store_tuan_hospital_list_page',
      ext: {
        location_ext: this.scopeUserLocation ? 1 : 0,
        city_id: this.wantCityId
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_hospital_list_page',
      ext: {
        location_ext: this.scopeUserLocation ? 1 : 0,
        city_id: this.wantCityId
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_hospital_list_page',
      ext: {
        location_ext: this.scopeUserLocation ? 1 : 0,
        city_id: this.wantCityId
      }
    });
  }
};
</script>
<style lang="less" scoped>
.hospital-map-list-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;

  .map-container {
    position: relative;
    width: 100%;
    flex: 1;
  }

  .user-position {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    border-radius: 48rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    .position-icon {
      display: inline-block;
      width: 48rpx;
      height: 48rpx;
    }
  }
  // 未开启定位提示
  .location-tip {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 80rpx;
    width: 100%;
    padding: 0 20rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #ffffff;
    .left {
      display: inline-block;
      width: 40rpx;
      height: 40rpx;
      margin-top: 2rpx;
      margin-right: 4rpx;
    }
    .center {
      flex: 1;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 112rpx;
      height: 48rpx;
      background: #89dc65;
      font-family: PingFangSC-Medium;
      font-size: 24rpx;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 500;
    }
  }
}
.callout-box-wraper {
  text-align: center;
  .callout-box {
    display: inline-block;
    line-height: 60rpx;
    height: 60rpx;
    padding: 0 20rpx;
    background: #fff;
    border-radius: 60rpx;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    text-align: center;
    color: #030303;
    letter-spacing: 0;
    text-align: justify;
    font-weight: 400;
    .callout-arrow {
      display: inline-block;
      margin-left: 8rpx;
      width: 12rpx;
      height: 18rpx;
      vertical-align: -2rpx;
    }
  }
}
</style>
