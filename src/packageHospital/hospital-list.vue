<template>
  <div class="hospital-list" v-if="hospitalData.list">
    <div class="accredit" v-if="!showUnLocationBtn()">
      <div class="accredit__left">
        <img
          class="accredit__icon"
          src="https://static.soyoung.com/sy-design/2hca5b5fhk6c31725851200334.png"
          alt=""
        />
        <span>授权地理定位，为您推荐附近门店</span>
      </div>
      <span class="accredit__btn" @click="handleGetLocationScope"
        >立即授权</span
      >
    </div>
    <div
      class="list-top"
      v-if="hospitalData.tenant_card_info"
      @click="handleTopCard"
    >
      <cardTb :info="hospitalData.tenant_card_info" />
      <div
        class="more"
        @click="
          handleMoreHospital(
            {
              cityId: hospitalData.tenant_card_info.city_id,
              cityName: hospitalData.tenant_card_info.city_name
            },
            'hospital'
          )
        "
      >
        探索更多{{ hospitalData.tenant_card_info.city_name }}门店<i
          class="arrow"
        ></i>
      </div>
    </div>
    <div class="main">
      <div class="title">探索{{ userInfo.cityId ? '更多' : '全部' }}城市</div>
      <div class="city">
        <div
          class="city__item"
          v-for="item in hospitalData.list"
          :key="item.en"
          @click="
            handleMoreHospital(
              {
                cityId: item.city_id,
                cityName: item.city_name
              },
              'city'
            )
          "
        >
          <img class="city__img" :src="item.img.url" mode="aspectFill" alt="" />
          <div class="city__info">
            <div class="city__name">{{ item.city_name }}</div>
            <div class="city__en">{{ item.city_pinyin }}</div>
            <div class="city__num">共{{ item.tenant_total }}家门店</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import cardTb from './components/hospital-list/cardTb.vue';
import { getTenantCityList } from '@/api/hospital';
export default {
  components: { cardTb },
  data() {
    return {
      hospitalData: {},
      first: true
    };
  },
  onShow() {
    console.log(this.userInfo);
    this.getAllCityHospital();

    this.$reportPageShow({
      info: 'sy_chain_store_tuan_hospital_list_page',
      ext: {}
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_hospital_list_page',
      ext: {}
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_hospital_list_page',
      ext: {}
    });
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  watch: {
    userInfo: {
      handler() {
        this.getAllCityHospital();
      },
      deep: true
    }
  },
  methods: {
    // 显示未授权的提示模块
    showUnLocationBtn() {
      const { lat, lng } = this.userInfo;
      return lat && lng;
    },
    // 获取所有城市的门店列表
    async getAllCityHospital() {
      this.hospitalData = await getTenantCityList({
        city_id: this.userInfo?.cityId || ''
      });
      if (this.first) {
        if (!this.showUnLocationBtn()) {
          this.$reportData({
            info: 'sy_chain_store_tuan_hospital_list:loaction_authorize_exposure',
            ext: {}
          });
        }
        if (
          this.hospitalData.tenant_card_info &&
          this.hospitalData.tenant_card_info?.soyoung_hospital_id
        )
          this.$reportData({
            info: 'sy_chain_store_tuan_hospital_list:top_hospital_exposure',
            ext: {
              hospital_id:
                this.hospitalData.tenant_card_info?.soyoung_hospital_id
            }
          });
      }
      this.first = false;
    },
    // 点击顶部机构
    handleTopCard() {
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_list:top_hospital_click',
        ext: {
          hospital_id: this.hospitalData.tenant_card_info?.soyoung_hospital_id
        }
      });
    },
    // 授权地理定位
    async handleGetLocationScope() {
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_list:loaction_authorize_click',
        ext: {}
      });
      wx.getSetting({
        success: (res) => {
          const setting = res.authSetting['scope.userLocation'];
          if (setting === undefined) {
            this.getLocation();
          } else {
            wx.openSetting();
          }
        }
      });
    },
    // 获取地理位置定位
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        // 解决下不然会抛出错误
        return {};
      });
      console.log(res, 'getLocation--------------------------------');
      this.hasLocation = Boolean(res?.lat && res?.lng);
      if (this.hasLocation) {
        console.log(this.hasLocation, 'hasLocation-hasLocation------');
      }
    },
    // 查看当前城市更多门店
    handleMoreHospital(city, type) {
      let info = '';
      if (type === 'hospital') {
        info = 'sy_chain_store_tuan_hospital_list:more_hospital_click';
      } else {
        info = 'sy_chain_store_tuan_hospital_list:city_card_click';
      }
      this.$reportData({
        info,
        ext: {
          city_id: city.cityId
        }
      });
      uni.navigateTo({
        url: `/packageHospital/hospital-city-list?cityId=${city.cityId}&cityName=${city.cityName}`
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.hospital-list {
  padding: 15px;
  .more {
    padding-top: 15px;
    font-size: 12px;
    color: #8c8c8c;
    text-align: center;
    .arrow {
      margin-left: 10rpx;
      vertical-align: -1rpx;
      content: '';
      display: inline-block;
      height: 18rpx;
      width: 12rpx;
      background: url(https://static.soyoung.com/sy-design/37t23gdvyew361726715721914.png)
        no-repeat center center transparent;
      background-size: contain;
    }
  }
  .list-top {
    position: relative;
    padding-bottom: 26px;
  }
  .main {
    .title {
      padding-bottom: 15px;
      color: #030303;
      font-size: 16px;
      font-weight: 500;
    }
    .city {
      &__item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 20px;
        height: 105px;
        background: #f2f2f2;
      }
      &__img {
        width: 246px;
        height: 100%;
        background: #f2f2f2;
      }
      &__info {
        margin-left: 30rpx;
        width: 90px;
        font-size: 13px;
        color: #646464;
        font-weight: 500;
        box-sizing: border-box;
      }
      &__en {
        font-family: OutFit-Regular;
        padding-top: 5px;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &__num {
        margin-top: 23px;
        font-size: 10px;
        color: #8c8c8c;
      }
    }
  }
  .accredit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    padding: 0 10px;
    height: 60px;
    font-size: 13px;
    color: #030303;
    font-weight: 500;
    background: #f2f2f2;
    &__left {
      display: inline-flex;
      align-items: center;
    }
    &__icon {
      display: inline-block;
      margin-right: 5px;
      width: 16px;
      height: 16px;
    }
    &__btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 30px;
      color: #fff;
      font-size: 13px;
      background: #333333;
    }
  }
}
</style>
