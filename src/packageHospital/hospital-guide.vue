<template>
  <div class="page-container">
    <div
      @click="previewImg(img)"
      v-for="(img, index) in list"
      :key="index"
      class="img-wrap"
    >
      <img :src="img" lazy-load mode="widthFix" />
    </div>
  </div>
</template>
<script>
import { getHospitalInfoApi } from '@/api/hospital';
export default {
  data() {
    return {
      list: [],
      hospital_info: null
    };
  },
  onLoad(query) {
    this.hospital_id = query.hospital_id;
    this.getHospitalData();
  },
  methods: {
    previewImg(url) {
      uni.previewImage({
        current: url,
        urls: this.list
      });
    },
    async getHospitalData() {
      const responseData = await getHospitalInfoApi(this.hospital_id);
      if (responseData) {
        const { hospital_info } = responseData;
        this.hospital_info = hospital_info;
        this.list = hospital_info.guide_images;
      }
    }
  },
  onShareAppMessage() {
    return {
      title: `${this.hospital_info.name_cn}- 路线指引`,
      path: `/packageHospital/hospital-guide?hospital_id=${this.hospital_id}`,
      imageUrl:
        this.hospital_info && this.hospital_info.env_images
          ? this.hospital_info.env_images[0]
          : 'https://static.soyoung.com/sy-pre/17n4nx9xnm8fo-1687677000730.png'
    };
  }
};
</script>
<style lang="less" scoped>
.page-container {
  box-sizing: border-box;
  .img-wrap {
    box-sizing: border-box;
    vertical-align: bottom;
    img {
      width: 100%;
    }
  }
}
</style>
