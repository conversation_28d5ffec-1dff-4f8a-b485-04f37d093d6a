<template>
  <div class="hospital-safe">
    <nav-bar
      @onBack="navBack"
      title=" "
      :hasFixedHome="true"
      :hasBack="true"
    ></nav-bar>
    <div class="hos-safe">
      <!-- <img mode="widthFix" class="title-img" :src="banner_img" /> -->
      <img mode="widthFix" class="title-img" :src="banner_img" />
      <div class="safe-content">
        <div class="title">
          <img
            src="https://static.soyoung.com/sy-design/alok43w4257l1717483487598.png"
          />
          严审资质
        </div>
        <div class="safe-ct-btm">
          <div class="subtitle">机构资质审核</div>
          <div class="sub-desc">{{ hosInfo.hospital_text }}</div>
          <div class="safe-imgs">
            <img
              v-if="hosInfo.business_image"
              :src="hosInfo.business_image"
              @click="previewImg(hosInfo.business_image)"
            />
            <img
              v-if="hosInfo.medical_image_1"
              :src="hosInfo.medical_image_1"
              @click="previewImg(hosInfo.medical_image_1)"
            />
            <img
              v-if="hosInfo.medical_image_2"
              :src="hosInfo.medical_image_2"
              @click="previewImg(hosInfo.medical_image_2)"
            />
            <img
              v-if="hosInfo.business_license"
              :src="hosInfo.business_license"
              @click="previewImg(hosInfo.business_license)"
            />
            <img
              v-if="hosInfo.medical_licenses1"
              :src="hosInfo.medical_licenses1"
              @click="previewImg(hosInfo.medical_licenses1)"
            />
          </div>
          <div class="subtitle">医生资质审核</div>
          <div class="sub-desc">{{ hosInfo.doctor_text }}</div>
          <div class="doc-list">
            <div
              v-for="(item, index) in hosInfo.doctor_info"
              :key="index"
              class="doc-item"
            >
              <div class="doc-left">
                <img :src="item.icon" />
              </div>
              <div class="doc-right">
                <div class="doc-right-title">
                  <div>{{ item.name_cn }}</div>
                  <div
                    v-if="item.career_year || item.position_name"
                    class="doc-right-subtitle"
                  >
                    <span>{{ item.position_name }}</span>
                    <span
                      v-if="item.career_year && item.position_name"
                      class="doc-right-subtitle-mid"
                      >|</span
                    >
                    <span v-if="item.career_year"
                      >从业{{ item.career_year }}年</span
                    >
                  </div>
                </div>
                <div v-if="item.bianhao" class="doc-right-desc">
                  职业编号：{{ item.bianhao || '' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="safe-content">
        <div class="title">
          <img
            src="https://static.soyoung.com/sy-design/alok43w4257l1717483487598.png"
          />
          正品保障
        </div>
        <div class="safe-ct-btm">
          <div class="sub-desc">{{ hosInfo.content_2 }}</div>
        </div>
      </div>
      <div class="safe-content">
        <div class="title">
          <img
            src="https://static.soyoung.com/sy-design/am7sotzvfh8y1717483487626.png"
          />
          闪电退款
        </div>
        <div class="safe-ct-btm">
          <div class="sub-desc">{{ hosInfo.content_3 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar';
import { getHospitalSafeGuard } from '@/api/hospital';
export default {
  components: { NavBar },
  data() {
    return {
      banner_img: '',
      banner_s:
        'https://static.soyoung.com/sy-pre/22ex8ite8hwa5-1717744200633.png',
      hosInfo: {}
    };
  },
  onLoad(options) {
    const date = new Date();
    this.year = date.getFullYear();
    this.hospital_id = options.hospital_id;
    this.initHosInfo();
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_qualification_page',
      ext: {
        hospital_id: this.hospital_id
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_qualification_page',
      ext: {
        hospital_id: this.hospital_id
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_qualification_page',
      ext: {
        hospital_id: this.hospital_id
      }
    });
  },
  computed: {},
  methods: {
    wordYear(item) {
      console.log(item);
      const countYear = +item.career_day.slice(0, 4);
      console.log(countYear);
      if (countYear > 0) {
        return this.year - countYear;
      } else {
        return '';
      }
    },
    async initHosInfo() {
      const obj = {
        hospital_id: this.hospital_id
      };
      const res = await getHospitalSafeGuard(obj);
      console.log(res);
      this.hosInfo = res;
      this.banner_img = this.banner_s;
    },
    navBack() {
      uni.navigateBack();
    },
    previewImg(url) {
      uni.previewImage({
        current: url,
        urls: [url]
      });
    }
  }
};
</script>
<style lang="less" scoped>
.hospital-safe {
  .hos-safe {
    background-color: rgb(240, 240, 240);
    padding-bottom: 90rpx;
    .title-img {
      width: 100%;
    }
    .safe-content {
      background-color: #fff;
      border-radius: 16rpx;
      margin: 0 10rpx 10rpx;
      padding: 40rpx 20rpx;
      .title {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Semibold;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0.66rpx;
        font-weight: 600;
        img {
          width: 34rpx;
          height: 34rpx;
          margin-right: 10rpx;
        }
      }
      .safe-ct-btm {
        padding: 0 10rpx;
        .subtitle {
          font-family: PingFangSC-Medium;
          font-size: 28rpx;
          color: #555555;
          letter-spacing: 0.6rpx;
          font-weight: 500;
          margin-top: 30rpx;
        }
        .sub-desc {
          margin-top: 10rpx;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #777777;
          letter-spacing: 0;
          line-height: 40rpx;
          font-weight: 400;
        }
        .safe-imgs {
          margin-top: 20rpx;
          padding-bottom: 30rpx;
          border-bottom: 2rpx solid #f0f0f0;
          img {
            width: 228rpx;
            height: 154rpx;
            margin-right: 16rpx;
          }
          img:nth-child(3n) {
            margin-right: 0;
          }
        }
        .doc-list {
          width: 100%;
          margin-top: 20rpx;
          overflow-y: hidden;
          overflow-x: auto;
          display: flex;
          flex-wrap: nowrap;
          .doc-item {
            width: 452rpx;
            min-width: 452rpx;
            height: 144rpx;
            margin-right: 20rpx;
            background-color: #f8f8f8;
            border-radius: 8rpx;
            box-sizing: border-box;
            padding: 28rpx 20rpx;
            display: flex;
            .doc-left {
              img {
                width: 90rpx;
                height: 90rpx;
                border-radius: 90rpx;
              }
            }
            .doc-right {
              font-family: PingFangSC-Medium;
              font-size: 28rpx;
              color: #333333;
              letter-spacing: 0;
              font-weight: 500;
              margin-left: 10rpx;
              .doc-right-title {
                display: flex;
                white-space: nowrap;
                .doc-right-subtitle {
                  margin-left: 12rpx;
                  height: 38rpx;
                  background: rgba(255, 238, 216, 0.99);
                  border-radius: 20rpx 54rpx 54rpx 0;
                  font-family: PingFangSC-Regular;
                  font-size: 20rpx;
                  color: #be8e4e;
                  letter-spacing: 0;
                  font-weight: 400;
                  padding: 4rpx 10rpx;
                  box-sizing: border-box;
                  .doc-right-subtitle-mid {
                    margin: 0 8rpx;
                  }
                }
              }
              .doc-right-desc {
                font-family: PingFangSC-Regular;
                font-size: 24rpx;
                color: #999999;
                letter-spacing: 0;
                font-weight: 400;
                margin-top: 16rpx;
                white-space: nowrap;
              }
            }
          }
          .doc-item:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>
