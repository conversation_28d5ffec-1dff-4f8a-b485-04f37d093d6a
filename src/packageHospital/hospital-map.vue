<template>
  <div class="hospital-map-page">
    <nav-bar
      title=" "
      :hasFixedHome="true"
      :hasBack="true"
      @onBack="navBack"
    ></nav-bar>

    <map
      class="map-container"
      id="map_container"
      :longitude="hospitalInfo.lon"
      :latitude="hospitalInfo.lat"
      :scale="zoom"
      :show-location="true"
      :min-scale="3"
      :max-scale="20"
      :markers="markers"
      :enable-zoom="true"
      @callouttap="go2there"
    >
      <cover-view slot="callout">
        <cover-view :marker-id="0">
          <cover-view class="callout-box">
            {{ hospitalInfo.name_cn }}
            <cover-image
              class="callout-arrow"
              src="https://static.soyoung.com/sy-design/6eboz32amcrz1725611272079.png"
            ></cover-image>
          </cover-view>
        </cover-view>
      </cover-view>
      <view class="map-tools">
        <!-- 定位，点击后地图切换到当前位置 -->
        <view
          class="user-position"
          @click="moveToMyLocation"
          v-if="hasLocation"
        >
          <image
            class="position-icon"
            src="https://static.soyoung.com/sy-pre/1f132i5u9tou7-1686787800706.png"
          ></image>
        </view>
        <view class="location-tip" v-if="!hasLocation">
          <image
            class="left"
            src="https://static.soyoung.com/sy-pre/1xqkn5at6qx8z-1725934200651.png"
          ></image>
          <view class="center">开启定位权限，为您匹配附近门店</view>
          <view class="right" @click="handleGetLocationScope">去开启</view>
        </view>
        <view class="hospital-info-wrap">
          <HospitalDetail
            :hospitalInfo="hospitalInfo"
            @open-map="go2there"
          ></HospitalDetail>
        </view>
      </view>
    </map>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar';
import HospitalDetail from '@/packageHospital/components/hospital-map/hospital-detail-card.vue';
import { getHospitalInfoApi } from '@/api/hospital';
function waitFactory() {
  let complete = () => {};
  const defered = new Promise((resolve) => {
    complete = resolve;
  });
  defered.done = complete;
  return defered;
}
export default {
  components: { NavBar, HospitalDetail },
  data() {
    return {
      hospital_id: 0,
      zoom: 11,
      markers: [], // 0 为机构，1为用户定位
      hasLocation: true, // 是否已成功获取定位
      myLocation: null,
      hospitalInfo: null,
      available: { top: 0, bottom: 0 }
    };
  },
  onLoad(options) {
    this.hospital_id = options.hospital_id;
  },
  async onShow() {
    this.initMap();
    await this.getLocation();
    this.getHospitalData();
  },
  beforeCreate() {
    this.availableDefer = waitFactory();
    this.hospitalDefer = waitFactory();
  },
  async mounted() {
    const { bottom } = uni.getMenuButtonBoundingClientRect();
    this.available = {
      top: bottom + 50
    };
    this.availableDefer.done();
  },
  watch: {},
  methods: {
    navBack() {
      uni.navigateBack();
    },
    initMap() {
      if (!this.map) {
        this.map = wx.createMapContext('map_container');
      }
    },
    // 添加机构的marker
    addHospitalMarkers() {
      const { lat, lon, default_icon } = this.hospitalInfo;
      const m = {
        id: 0,
        latitude: lat,
        longitude: lon,
        iconPath:
          default_icon ||
          'https://static.soyoung.com/sy-design/q7esh7pojvfk1725617809721.png',
        width: 25,
        height: 25,
        index: 0,
        customCallout: {
          anchorY: -5,
          anchorX: 0,
          display: 'ALWAYS'
        }
      };
      this.markers.push(m);
    },
    moveToHospital() {
      const { lat, lon } = this.hospitalInfo;
      this.map.moveToLocation({
        longitude: lon,
        latitude: lat
      });
    },
    moveToMyLocation() {
      if (!this.hasLocation) return;
      this.map.moveToLocation({
        longitude: this.myLocation.lng,
        latitude: this.myLocation.lat
      });
    },
    async getHospitalData() {
      const responseData = await getHospitalInfoApi(this.hospital_id);
      if (responseData) {
        this.markers = [];
        this.hospitalInfo = responseData.hospital_info;
        this.addHospitalMarkers();
      }
      await this.$nextTick();
      const { windowHeight } = wx.getSystemInfoSync();
      const { top } = await this.$queryFirstNode('.hospital-info-wrap');
      this.available.bottom = windowHeight - top + 50;
      this.hospitalDefer.done();
    },
    // 授权地理定位
    async handleGetLocationScope() {
      wx.getSetting({
        success: (res) => {
          const setting = res.authSetting['scope.userLocation'];
          if (setting === undefined) {
            this.getLocation();
          } else {
            wx.openSetting();
          }
        }
      });
    },
    // 获取地理位置定位
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        // 解决下不然会抛出错误
        return {};
      });
      this.hasLocation = Boolean(res?.lat && res?.lng);
      if (this.hasLocation) {
        this.myLocation = res;
      }
    },
    go2there() {
      // 新氧清水团-清水团-机构主页-机构地址-点击
      this.$reportData({
        info: 'sy_wxtuan_tuan_hospital_homepage:address_click',
        ext: {
          hospital_id: this.hospitalInfo?.hospital_id
        }
      });
      uni.openLocation({
        latitude: Number(this.hospitalInfo.lat),
        longitude: Number(this.hospitalInfo.lon),
        scale: 18,
        name: this.hospitalInfo.name_cn,
        address: this.hospitalInfo.address,
        fail: (e) => {
          uni.showToast({
            title: JSON.stringify(e),
            icon: 'none'
          });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.hospital-map-page {
  .map-container {
    width: 100%;
    height: 100vh;
    position: relative;

    .map-tools {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: flex-end;
      position: absolute;
      bottom: 0;
      left: 0;
      padding: 0 30rpx;
      width: 100%;
      box-sizing: border-box;
    }

    .user-position {
      background: #ffffff;
      border-radius: 60rpx;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20rpx;
      margin-right: 10rpx;
      .position-icon {
        display: inline-block;
        width: 40rpx;
        height: 40rpx;
      }
    }

    // 未开启定位提示
    .location-tip {
      height: 80rpx;
      width: 100%;
      padding: 0 20rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      background: #ffffff;
      margin-bottom: 10rpx;
      .left {
        display: inline-block;
        width: 36rpx;
        height: 36rpx;
        margin-right: 4rpx;
      }
      .center {
        flex: 1;
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #030303;
        letter-spacing: 0;
        font-weight: 400;
      }
      .right {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 112rpx;
        height: 48rpx;
        background: #89dc65;
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 500;
      }
    }

    .hospital-info-wrap {
      margin-bottom: 100rpx;
      width: 100%;
    }
  }
  .callout-box {
    line-height: 60rpx;
    height: 60rpx;
    padding: 0 20rpx;
    background: #fff;
    border-radius: 60rpx;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    text-align: center;
    color: #030303;
    letter-spacing: 0;
    text-align: justify;
    font-weight: 400;
    .callout-arrow {
      margin-left: 8rpx;
      display: inline-block;
      width: 12rpx;
      height: 18rpx;
      vertical-align: -2rpx;
    }
  }
}
</style>
