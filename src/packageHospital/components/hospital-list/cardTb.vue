// 门店卡片上下结构
<template>
  <div class="card" @click="goHosHome">
    <img
      class="flagship"
      src="https://static.soyoung.com/sy-pre/knze6xudthql-1743991800647.png"
      alt="旗舰店"
      v-if="Number(info.hospital_tag_type) === 1"
    />
    <img
      class="flagship"
      src="https://static.soyoung.com/sy-pre/hjmw3pern5-1743991800647.png"
      alt="总部店"
      v-else-if="Number(info.hospital_tag_type) === 2"
    />
    <img class="card__img" :src="info.img.url" mode="aspectFill" alt="" />
    <div class="card__info">
      <div class="row">
        <div class="name">{{ info.name }}</div>
        <div class="distance" v-if="info.juli">
          {{ info.juli }} <i class="arrow"></i>
        </div>
      </div>
      <div class="address">{{ info.address }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    goHosHome() {
      uni.navigateTo({
        url: `/packageHospital/hospital-home?hospital_id=${this.info.soyoung_hospital_id}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  .flagship {
    position: absolute;
    left: 0;
    top: 0;
    width: 66px;
    height: 20px;
  }
  &__img {
    width: 100%;
    height: 147px;
    display: block;
    background-color: #f2f2f2;
  }
  &__info {
    padding: 15px;
    color: #333;
    font-size: 14px;
    background: #f2f2f2;
    .row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
    }
    .name {
      flex: 1;
      font-size: 28rpx;
      color: #030303;
    }
    .distance {
      min-width: 56px;
      font-size: 24rpx;
      color: #030303;
      font-weight: 400;
    }
    .arrow {
      margin-top: 10rpx;
      margin-left: 10rpx;
      content: '';
      display: inline-block;
      height: 18rpx;
      width: 12rpx;
      background: url(https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png)
        no-repeat center center transparent;
      background-size: contain;
    }
    .address {
      padding-top: 5px;
      font-size: 24rpx;
      color: #646464;
      font-weight: 400;
    }
  }
}
</style>
