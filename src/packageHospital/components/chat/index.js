/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-08-07 13:51:20
 * @Last Modified by: <PERSON><PERSON>peng
 * @Last Modified time: 2020-01-15 16:03:15
 * 文档看这里
 * https://soyoung.feishu.cn/wiki/wikcnH2ZjI0D4umYrzPAgg4lRJh
 */
import encoding from './text-encoding';
const config = {
  production: 'wss://im.soyoung.com',
  development: 'wss://im.sy.soyoung.com',
  port: '443',
  sySocket: (() => {
    switch (process.env.NODE_ENV) {
      case 'production':
        return 'wss://chatcon.soyoung.com/sub';
      case 'development':
        return 'wss://chat.sy.soyoung.com:8443/sub';
      default:
        return 'wss://chatcon.soyoung.com/sub'; // 默认值
    }
  })()
};

class PublicWS {
  constructor(opts) {
    this.initOptions(opts);
    if (!this.socketEvent) {
      console.error('Error: this.socketEvent is not initialized');
    } else {
      this.createConnect();
    }
  }

  createConnect() {
    this.connect();
    this._config.heartbeatInterval = null;
  }

  connect() {
    // 将内部方法提前声明，确保事件回调中可用
    const auth = () => {
      let token = '{';
      token += `"mid":${this._config.receiverUid},`;
      token += `"room_id":"${this._config.room_id}",`;
      token += `"platform":"${this._config.platform}",`;
      token += `"accepts":[${this._config.accepts}],`;
      token += `"uid":${this._config.senderUid},`;
      token += `"authorization":"${this._config.token}"`;
      token += '}';
      this.assemblyPackage('auth', token);
      this._log('send: auth token: ' + token);
    };

    const heartbeat = () => {
      this.assemblyPackage('heartbeat');
      this._log('发送心跳检测');
    };

    const messageReceived = (ver, body) => {
      if (typeof this.socketEvent.onMessage === 'function') {
        this.socketEvent.onMessage({
          ver,
          body
        });
      }
    };

    const socketTask = uni.connectSocket({
      url: this._config.wsURL,
      success: () => {
        this._log('websocket连接成功');
      },
      fail: () => {
        this._log('websocket连接失败');
      }
    });

    socketTask.onOpen(() => {
      auth();
      if (this.socketEvent && typeof this.socketEvent.onOpen === 'function') {
        this.socketEvent.onOpen();
      }
    });

    socketTask.onMessage((evt) => {
      console.log('*** 是否收到了message反馈 ***', evt.data);
      const data = evt.data;
      const dataView = new DataView(data, 0);
      let packetLen = dataView.getInt32(this._config.packetOffset);
      let headerLen = dataView.getInt16(this._config.headerOffset);
      let ver = dataView.getInt16(this._config.verOffset);
      let op = dataView.getInt32(this._config.opOffset);
      const seq = dataView.getInt32(this._config.seqOffset);
      let msgBody = null;

      // 根据操作码处理不同业务逻辑
      switch (op) {
        case 8:
          // auth reply ok 鉴权成功
          this.socketEvent.close = function () {
            socketTask.close({});
          };
          this.socketEvent.send = (obj) => {
            socketTask.send(obj);
          };
          this.socketEvent.reConnect = () => {
            this.createConnect();
          };
          this._log('鉴权成功');
          heartbeat();
          this._config.heartbeatInterval = setInterval(heartbeat, 10 * 1000);
          break;
        case 3:
          // 收到心跳检测
          this._log('收到心跳检测');
          break;
        case 9:
          this.returnReceipt(seq);
          // 批量消息处理
          for (
            let offset = this._config.rawHeaderLen;
            offset < data.byteLength;
            offset += packetLen
          ) {
            packetLen = dataView.getInt32(offset);
            headerLen = dataView.getInt16(offset + this._config.headerOffset);
            ver = dataView.getInt16(offset + this._config.verOffset);
            op = dataView.getInt32(offset + this._config.opOffset);
            msgBody = this._config.textDecoder.decode(
              data.slice(offset + headerLen, offset + packetLen)
            );
            messageReceived(ver, msgBody);
          }
          break;
        default:
          msgBody = this._config.textDecoder.decode(
            data.slice(headerLen, packetLen)
          );
          messageReceived(ver, msgBody);
          break;
      }
    });

    socketTask.onClose((res) => {
      console.log('WebSocket 已关闭！', res);
      if (this._config.heartbeatInterval)
        clearInterval(this._config.heartbeatInterval);
      if (typeof this.socketEvent.onClose === 'function') {
        console.log('不重新发起连接嘛');
        this.socketEvent.onClose(res);
      }
    });

    this.socketTask = socketTask;
  }

  mergeArrayBuffer(ab1, ab2) {
    const u81 = new Uint8Array(ab1);
    const u82 = new Uint8Array(ab2);
    const res = new Uint8Array(ab1.byteLength + ab2.byteLength);
    res.set(u81, 0);
    res.set(u82, ab1.byteLength);
    return res.buffer;
  }

  returnReceipt(seq) {
    let _data = '{';
    _data += `"msg_ids":[${seq}],`;
    _data += `"receive_time":${parseInt(
      (new Date().getTime() / 1000).toString()
    )},`;
    _data += `"from_page":"${this._config.currentPages}"`;
    _data += '}';
    this.assemblyPackage('returnReceipt', _data, seq);
    this._log('发送回执信息' + _data);
  }

  assemblyPackage(type, data, seq) {
    const headerBuf = new ArrayBuffer(this._config.rawHeaderLen);
    const headerView = new DataView(headerBuf, 0);
    let bodyBuf = null;
    if (data) {
      bodyBuf = this._config.textEncoder.encode(data);
    }
    if (type === 'heartbeat') {
      headerView.setInt32(this._config.packetOffset, this._config.rawHeaderLen);
    } else {
      headerView.setInt32(
        this._config.packetOffset,
        this._config.rawHeaderLen + bodyBuf.byteLength
      );
    }
    headerView.setInt16(this._config.headerOffset, this._config.rawHeaderLen);
    headerView.setInt16(this._config.verOffset, 1);
    let _op = null;
    let _seq = null;
    switch (type) {
      case 'returnReceipt':
        _op = 20;
        _seq = seq;
        break;
      case 'auth':
        _op = 7;
        _seq = 1;
        break;
      case 'heartbeat':
        _op = 2;
        _seq = 1;
        break;
      default:
        _seq = 1;
        break;
    }
    headerView.setInt32(this._config.opOffset, _op);
    headerView.setInt32(this._config.seqOffset, _seq);
    if (type === 'heartbeat') {
      this.socketTask.send({
        data: headerBuf
      });
    } else {
      this.socketTask.send({
        data: this.mergeArrayBuffer(headerBuf, bodyBuf)
      });
    }
  }

  initOptions(opts) {
    this._config = {
      room_id: opts.config.roomID + '' || null,
      senderUid: opts.config.senderUid || 0,
      receiverUid: opts.config.receiverUid || 0,
      accepts: opts.config.accepts || 0,
      platform: 'weixin',
      token: opts.config.token || 0,
      // socket 地址
      wsURL: config.sySocket,
      rawHeaderLen: 16,
      packetOffset: 0,
      headerOffset: 4,
      verOffset: 6,
      opOffset: 8,
      seqOffset: 12,
      max: 10,
      delay: 15000,
      debug: opts.config.debug || 0,
      textDecoder: new encoding.TextDecoder(),
      textEncoder: new encoding.TextEncoder(),
      currentPages:
        opts.config.currentPages[opts.config.currentPages.length - 1].route ||
        ''
    };
    this.socketEvent = {
      onClose: opts.socketTask.onClose || {},
      onError: opts.socketTask.onError || {},
      onMessage: opts.socketTask.onMessage || {},
      onOpen: opts.socketTask.onOpen || {},
      close: null,
      send: null,
      reConnect: null
    };
    this.socketTask = null;
  }

  _log(msg, type = 'log') {
    if (this._config.debug) {
      console[type]('PublicWS: ' + msg);
    }
  }
}

export default PublicWS;
