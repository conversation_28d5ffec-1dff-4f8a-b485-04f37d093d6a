<template>
  <div class="hospital-detail">
    <img
      v-if="hospitalInfo.is_flagship"
      class="prime-icon"
      src="https://static.soyoung.com/sy-design/13l8ro1htkk0v1725617809726.png"
    />
    <!-- 基础信息 -->
    <div class="info" v-if="hospitalInfo">
      <div class="title">
        {{ hospitalInfo.name_cn }}
      </div>
      <div class="flex">
        <div class="address">
          {{ hospitalInfo.address }}
        </div>
        <div
          class="distance"
          v-if="hospitalInfo.distance"
          :style="distanceStyle"
        >
          {{ hospitalInfo.distance }}
        </div>
      </div>
      <div class="service">
        <div class="service-item">
          <template v-if="hospitalInfo.service_times > 10"
            >已服务{{ hospitalInfo.service_times }}人次</template
          ><template v-else>新店入驻</template>
        </div>
        <div class="service-item">
          营业时间 {{ hospitalInfo.start_time }}-{{ hospitalInfo.end_time }}
        </div>
      </div>
      <div class="btn-group">
        <div class="btn" v-if="showHospitalTel" @click="handleClickHospitalTel">
          <img
            src="https://static.soyoung.com/sy-design/amhiohkx1oen1725617809728.png"
          />
          打电话
        </div>
        <div
          class="btn btn-full"
          :style="bthWidth"
          @click="() => $emit('open-map')"
        >
          <img
            src="https://static.soyoung.com/sy-design/22elqxuramexq1725617809690.png"
          />去这里
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    hospitalInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      defaultIcon:
        'https://static.soyoung.com/sy-pre/20230712-173032-1689153000708.jpeg'
    };
  },
  mounted() {},
  computed: {
    showHospitalTel() {
      return !!this.hospitalInfo?.mobile;
    },
    bthWidth() {
      return this.showHospitalTel ? 'width: 300rpx' : 'width: 100%';
    },
    distanceStyle() {
      return this.hospitalInfo?.distance
        ? `width: ${this.measureText(this.hospitalInfo.distance)}px`
        : '';
    }
  },
  methods: {
    measureText(text) {
      if (!this.offscreen) {
        this.offscreen = wx.createOffscreenCanvas({
          type: '2d'
        });
      }
      const ctx = this.offscreen.getContext('2d');
      ctx.font = '12px/1.2 PingFangSC-Regular';
      return ctx.measureText(text).width + 5;
    },
    handleClickHospitalTel() {
      const { is_work_time, mobile, mobile_msg } = this.hospitalInfo;
      if (+is_work_time === 1) {
        uni.makePhoneCall({
          phoneNumber: mobile
        });
      } else {
        uni.showModal({
          content: mobile_msg,
          showCancel: false
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.hospital-detail {
  background: #ffffff;
  box-sizing: border-box;
  padding: 40rpx 30rpx;
  position: relative;
  box-shadow: 0 5px 15px 0 rgba(138, 138, 138, 0.1);
  .prime-icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 92rpx;
    height: 28rpx;
    z-index: 1;
  }

  // 基础信息
  .info {
    .title {
      margin-bottom: 16rpx;
      line-height: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
      // display: -webkit-box;
      // text-overflow: ellipsis;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 2;
      overflow: hidden;
      font-family: PingFangSC-Medium;
      color: #030303;
    }
    .flex {
      display: flex;
    }
    .address,
    .distance {
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
    }
    .address {
      flex: 1;
      box-sizing: border-box;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
      text-align: left;
      color: #8c8c8c;
    }
    .distance {
      width: 160rpx;
      color: #030303;
    }
    .service {
      padding-top: 16rpx;
      .service-item {
        margin-right: 10rpx;
        display: inline-flex;
        padding: 0 10rpx;
        height: 34rpx;
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #030303;
        letter-spacing: 0;
        font-weight: 400;
        background: #f6f6f6;
      }
    }
    .btn-group {
      padding-top: 28rpx;
      display: flex;
      justify-content: space-between;
      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 74rpx;
        width: 300rpx;
        border: 1px solid #333333;
        font-family: PingFangSC-Medium;
        font-size: 26rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 500;
        img {
          margin-right: 10rpx;
          display: inline-block;
          height: 48rpx;
          width: 48rpx;
        }
      }
      .btn-full {
        background: #333333;
        color: #fff;
        img {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
  }
}
</style>
