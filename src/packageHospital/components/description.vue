<template>
  <div class="description">
    <div class="title">{{ description.title }}</div>
    <div class="subtitle">{{ description.sub_title }}</div>
    <div
      class="content"
      v-if="description.descriptions && description.descriptions.length > 0"
    >
      <div
        class="text"
        v-for="(item, index) in description.descriptions"
        :key="index"
      >
        <div></div>
        <label>{{ item }}</label>
      </div>
    </div>
    <div class="bottom"></div>
    <div class="line" v-if="showLine"></div>
  </div>
</template>
<script>
export default {
  props: {
    description: {
      type: Object,
      default: () => null
    },
    showLine: {
      type: Boolean,
      default: false
    }
  }
};
</script>
<style lang="less" scoped>
.description {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  .title {
    font-family: PingFangSC-Semibold;
    font-size: 40rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
  .subtitle {
    font-family: OutFit-Regular;
    font-size: 24rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 200;
    margin-top: 10rpx;
    text-transform: uppercase;
  }
  .content {
    margin-top: 50rpx;
    .text {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      div {
        width: 10rpx;
        height: 10rpx;
        background-color: #a9ea6a;
        display: inline-block;
        margin-top: 24rpx;
        border-radius: 100% 100%;
      }
      label {
        font-family: Outfit-Regular;
        font-size: 30rpx;
        color: #030303;
        letter-spacing: 0;
        text-align: justify;
        line-height: 58rpx;
        font-weight: 400;
        white-space: wrap;
        margin-left: 20rpx;
        flex: 1;
        white-space: pre-wrap;
      }
    }
  }
  .bottom {
    width: 100%;
    height: 60rpx;
  }
  .line {
    width: 100%;
    height: 2rpx;
    background-color: #c5bbb6;
  }
}
</style>
