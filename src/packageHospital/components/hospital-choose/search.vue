<template>
  <block>
    <div
      class="searcher-root"
      :class="{
        'list-mode': mode === 'list'
      }"
    >
      <div class="search-bar-wraper">
        <div class="search-bar">
          <div class="city-btn-text" :style="cityBtnStyle" @click="openCityBox">
            {{ chooseCityName }}
          </div>
          <img
            class="city-btn-arrow"
            :class="{
              rotate: cityBoxVisible
            }"
            @click="openCityBox"
            :src="arrowUri"
          />
          <div class="city-input-box">
            <div class="icon"></div>
            <input
              type="text"
              placeholder="搜门店"
              placeholder-class="hospital-choose-input-placeholder"
              confirm-type="search"
              data-mode="list"
              @click="handleChangeMode"
              v-model.trim="searchHosInput"
            />
          </div>
          <div
            class="city-switch-map"
            data-mode="map"
            @click="handleChangeMode"
          >
            <img
              src="https://static.soyoung.com/sy-design/1a310ag6zrv7m1726040490912.png"
            />
            <div class="text">地图</div>
          </div>
        </div>
      </div>
      <div class="search-list-wrapper" v-if="mode === 'list'">
        <slot></slot>
      </div>
    </div>

    <BottomModal
      title="选择城市"
      :visible="cityBoxVisible"
      :useSafearea="true"
      @close="handleCityBoxClose"
    >
      <div class="city-bottom-modal-content">
        <div class="city-filter">
          <div class="icon"></div>
          <input
            type="text"
            placeholder="搜城市"
            placeholder-class="hospital-choose-input-placeholder"
            confirm-type="search"
            v-model.trim="searchCityInput"
          />
        </div>
        <scroll-view scroll-y :scroll-x="false" class="city-list">
          <div class="js-wraper" v-if="cityListWithFilter.length">
            <div
              class="city-item"
              :key="city.id"
              v-for="city in cityListWithFilter"
              :data-id="city.id"
              :class="{
                active: Number(chooseCityId) === Number(city.id)
              }"
              @click="handleCitySelect"
            >
              <div class="name">{{ city.name }}</div>
              <div class="duihao"></div>
            </div>
          </div>
          <div v-else class="city-empty">非常抱歉没找到结果</div>
        </scroll-view>
      </div>
    </BottomModal>
    <div class="prefetch">
      <img
        src="https://static.soyoung.com/sy-design/kgmpik5wigpe1726040490911.png"
      />
    </div>
  </block>
</template>
<script>
import BottomModal from '@/components/bottomModal.vue';
import { mapState } from 'vuex';
export default {
  props: {
    cityId: {
      type: Number,
      default: 1
    }
  },
  components: {
    BottomModal
  },
  data() {
    return {
      cityList: [],
      searchHosInput: '',
      cityBoxVisible: false,
      searchCityInput: '',
      mode: 'map' // map 或者 list
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    chooseCityId() {
      // 优先级： 中门店所属城市 > 定位城市 > 最近可服务城市 > ip城市
      return Number(this.cityId);
    },
    chooseCityName() {
      const choose = this.cityList.find(
        ({ id }) => Number(id) === this.chooseCityId
      );
      return choose?.name || '北京市';
    },
    cityBtnStyle() {
      return `width: ${this.measureText(this.chooseCityName)}px`;
    },
    cityListWithFilter() {
      if (!this.searchCityInput) return this.cityList;
      return (
        this.cityList?.filter(
          ({ name, letter }) =>
            name.includes(this.searchCityInput) ||
            letter
              .toLocaleLowerCase()
              .includes(this.searchCityInput.toLocaleLowerCase())
        ) || []
      );
    },
    arrowUri() {
      return this.mode === 'map'
        ? 'https://static.soyoung.com/sy-pre/hphx1rnhs70l-1726042200645.png'
        : 'https://static.soyoung.com/sy-design/kgmpik5wigpe1726040490911.png';
    }
  },
  created() {
    this.fetchCityList();
    this.$getCityId('gcj02').catch(() => ({}));
  },
  watch: {
    searchHosInput(val) {
      this.$emit('filter', val);
    },
    mode: {
      handler(val) {
        if (val === 'list') {
          uni.setNavigationBarTitle({
            title: '搜索门店'
          });
        } else {
          uni.setNavigationBarTitle({
            title: '选择门店'
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchCityList() {
      const list = await this.getHospitalListWithLetter();
      if (list) this.cityList = list;
    },
    handleChangeMode(event) {
      this.mode = event.currentTarget.dataset.mode;
    },
    handleCityBoxClose() {
      this.cityBoxVisible = false;
      this.searchCityInput = '';
      this.$emit('city-close');
    },
    openCityBox() {
      this.cityBoxVisible = true;
      this.$emit('city-open');
    },
    measureText(text) {
      if (!this.offscreen) {
        this.offscreen = wx.createOffscreenCanvas({
          type: '2d',
          width: 1000,
          height: 100
        });
      }
      const ctx = this.offscreen.getContext('2d');
      ctx.font = '14px/1.2 PingFangSC-Regular';
      return ctx.measureText(text).width + 3;
    },
    handleCitySelect(event) {
      const cityId = Number(event.currentTarget.dataset.id);
      const { id } = this.cityList.find(({ id }) => Number(id) === cityId);
      if (this.cityId !== cityId) {
        this.$emit('update:cityId', id);
        this.$emit('city-change', id);
        this.searchHosInput = '';
      }
      this.handleCityBoxClose();
    },
    async getHospitalListWithLetter() {
      const res = await this.$request({
        url: '/syGroupBuy/chain/project/getGroupBuyChainProjectCityList'
      })
        .then((res) => res.data)
        .catch((error) => {
          return {
            errorCode: -100,
            errorMsg: error || '网络错误,稍后再试',
            responseData: null
          };
        });
      const { errorCode, responseData } = res;
      if (+errorCode === 0 && Array.isArray(responseData?.list)) {
        return responseData.list;
      } else {
        return [];
      }
    }
  }
};
</script>
<style lang="less" scoped>
.searcher-root {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  z-index: 5;
  .search-bar-wraper {
    box-sizing: border-box;
    padding: 20rpx 30rpx;
    background: transparent;
  }
  .search-list-wrapper {
    display: none;
  }
  .search-bar {
    box-sizing: border-box;
    padding-left: 30rpx;
    padding-right: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 64rpx;
    background: #fff;
    .city-btn-text {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #646464;
      letter-spacing: 0;
      font-weight: 400;
      white-space: nowrap;
    }
    .city-btn-arrow {
      margin-left: 10rpx;
      margin-right: 34rpx;
      width: 20rpx;
      height: 14rpx;
      transition: transform 0.3s ease-in-out;
      &.rotate {
        transform: rotate(180deg);
      }
    }
    .city-input-box {
      position: relative;
      box-sizing: border-box;
      padding-left: 30rpx;
      height: 64rpx;
      display: flex;
      flex: 1;
      justify-content: space-between;
      align-items: center;
      .icon {
        margin-right: 10rpx;
        width: 28rpx;
        height: 28rpx;
        background: url(https://static.soyoung.com/sy-design/1zuveb3ngazob1725359158395.png)
          no-repeat center center transparent;
        background-size: contain;
      }
      input {
        // 38 图标 width
        // 10 右内边距
        width: calc(100% - 38rpx - 10rpx);
        font-size: 26rpx;
      }
      &::after {
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -15rpx;
        content: '';
        height: 28rpx;
        border-left: 1px solid #f2f2f2;
        width: 1px;
        z-index: 1;
      }
    }
    .city-switch-map {
      display: none;
      text-align: right;
      height: 64rpx;
      width: 70rpx;
      img {
        height: 40rpx;
        width: 40rpx;
      }
      .text {
        line-height: 0;
        width: 100%;
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        color: #222222;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
    }
  }
  &.list-mode {
    height: 100vh;
    background: #f2f2f2;
    .search-bar-wraper {
      background: #fff;
    }
    .search-bar {
      padding-right: 0;
      .city-btn-text {
        color: #030303;
        font-weight: 500;
        transition: color 0.3s;
      }
      .city-input-box {
        background: #f2f2f2;
        transition: background 0.3s;
        &::after {
          display: none;
        }
      }
      .city-switch-map {
        display: block;
      }
    }
    .search-list-wrapper {
      display: block;
    }
  }
}
.city-bottom-modal-content {
  position: relative;
  display: block;
  // 70 UI 要求占屏幕高
  // 104 modal 的 title bar 高度
  height: calc(60vh - 104rpx);
  box-sizing: border-box;
  padding: 0 30rpx;
  .city-filter {
    box-sizing: border-box;
    padding: 0 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64rpx;
    width: 100%;
    background: #f2f2f2;
    border-radius: 2px;
    .icon {
      margin-right: 10rpx;
      width: 28rpx;
      height: 28rpx;
      background: url(https://static.soyoung.com/sy-design/1zuveb3ngazob1725359158395.png)
        no-repeat center center transparent;
      background-size: contain;
    }
    input {
      // 38 图标 width
      // 10 右内边距
      width: calc(100% - 38rpx - 10rpx);
      font-size: 26rpx;
      font-weight: 400;
    }
  }
  .city-list {
    box-sizing: border-box;
    margin: 20rpx 0;
    // 70 UI 要求占屏幕高
    // 64 input检索框高度
    // 104 modal 标题bar 高度
    // 40 城市列表的 上下外边距 之和
    max-height: calc(70vh - 64rpx - 104rpx - 40rpx);
    overflow-y: auto;
    .city-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 80rpx;
      &.active {
        .name {
          color: #61b43e;
          font-weight: 500;
        }
        .duihao {
          transform: opacity, 0.3s;
          opacity: 1;
        }
      }
      .name {
        font-family: PingFangSC-Regular;
        font-size: 30rpx;
        color: #030303;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
      .duihao {
        opacity: 0;
        height: 32rpx;
        width: 32rpx;
        background: url(https://static.soyoung.com/sy-design/b0ptomak0liw1726828968901.png)
          no-repeat center center transparent;
        background-size: 32rpx 32rpx;
      }
    }
  }
  .city-empty {
    box-sizing: border-box;
    padding-top: 30rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
}
// 缓存图片，避免箭头闪烁
.prefetch {
  position: absolute;
  left: -10000px;
  top: -10000px;
  height: 1px;
  width: 1px;
  overflow: hidden;
}
</style>
<style lang="less">
.hospital-choose-input-placeholder {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #bababa;
  font-weight: 400;
}
</style>
