<template>
  <div
    class="container"
    :class="{
      skeleton
    }"
    :style="containerStyle"
  >
    <block v-if="hasHospital">
      <div class="hospital-cnt" v-if="showCount">
        {{ filterList.length }}家门店
      </div>
      <scroll-view
        :scroll-y="true"
        class="scroll-box"
        :style="scrollBoxStyle"
        :scroll-top="scrollTop"
        :scroll-with-animation="true"
      >
        <div
          class="hos-card-wrap js-item"
          v-for="hos in filterList"
          :data-id="hos.hospital_id"
          :data-city-id="hos.city_id"
          :key="hos.city_id"
          @click.stop="onHosClick"
        >
          <img
            v-if="Number(hos.hospital_tag_type) === 1"
            class="prime-logo"
            src="https://static.soyoung.com/sy-pre/knze6xudthql-1743991800647.png"
          />
          <img
            v-else-if="Number(hos.hospital_tag_type) === 2"
            class="prime-logo"
            src="https://static.soyoung.com/sy-pre/hjmw3pern5-1743991800647.png"
          />
          <div
            class="hos-card"
            :class="{
              active: Number(hos.hospital_id) === hospitalId
            }"
          >
            <div class="left">
              <div class="name">
                {{ hos.hospital_name }}
              </div>
              <div class="row mb">
                <img
                  src="https://static.soyoung.com/sy-design/atbnjwk6sovd1725519136858.png"
                />
                <div class="text">
                  {{ hos.hospital_addr }}
                </div>
              </div>
              <div class="row">
                <img
                  src="https://static.soyoung.com/sy-design/1o60sa15mzrxw1725359158384.png"
                />
                <div class="text">
                  {{ hos.business_start }}-{{ hos.business_end }}
                </div>
              </div>
            </div>
            <div class="right">
              <div class="do">
                {{ hospitalId === hos.hospital_id ? '去下单' : '切换门店' }}
              </div>
              <div class="distance">{{ hos.distance_str }}</div>
              <div class="btns">
                <div
                  class="btn"
                  :data-id="hos.hospital_id"
                  @click.stop="onMakeCall"
                >
                  <img
                    src="https://static.soyoung.com/sy-design/2rhbun9c8cczb1725359158388.png"
                  />
                </div>
                <div
                  class="btn"
                  :data-id="hos.hospital_id"
                  @click.stop="go2there"
                >
                  <img
                    src="https://static.soyoung.com/sy-design/o2eve6mngspv1725359158084.png"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="no-more">没有更多了</div>
      </scroll-view>
    </block>
    <div class="empty-list" v-else>
      <div>非常抱歉没找到结果</div>
    </div>
  </div>
</template>
<script>
import { throttle, cloneDeep, isNumber } from 'lodash-es';
import { mapState } from 'vuex';
import dayjs from '@/common/dayjs.min';

export default {
  components: {},
  props: {
    skeleton: {
      type: Boolean,
      default: true
    },
    hospitalId: {
      type: Number,
      default: -1
    },
    hospitalList: {
      type: Array,
      default: () => []
    },
    showCount: {
      type: Boolean,
      default: true
    },
    filterText: {
      type: String,
      default: ''
    },
    maxHeight: {
      type: String,
      default: '50vh'
    }
  },
  watch: {
    hospitalList() {
      this.scrollTop = 0;
    },
    filterText() {
      this.scrollTop = 0;
    },
    hospitalId(id) {
      if (id < 0) return;
      this.scroll2view(id);
    }
  },
  data() {
    return {
      scrollTop: 0
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    hasHospital() {
      return this.filterList?.length > 0;
    },
    filterList() {
      if (this.skeleton) return new Array(3).fill({});
      if (!this.filterText) return this.hospitalList;
      return this.hospitalList.filter(
        (item) =>
          item.hospital_name.includes(this.filterText) ||
          item.hospital_addr.includes(this.filterText)
      );
    },
    containerStyle() {
      return `max-height: calc(${this.maxHeight});min-height: 200rpx`;
    },
    scrollBoxStyle() {
      return `max-height: calc(${this.maxHeight} - ${
        this.showCount ? 56 : 0
      }rpx)`;
    }
  },
  methods: {
    async scroll2view(hospitalId) {
      const index = this.filterList.findIndex(
        (item) => Number(item.hospital_id) === Number(hospitalId)
      );
      if (index < 0) return;
      const nodes = await this.$queryNodes(`.js-item`);
      const start = nodes[0];
      const target = nodes[index];
      if (isNumber(target?.top) && isNumber(start?.top)) {
        this.scrollTop = target?.top - start?.top;
      }
    },
    onHosClick: throttle(
      function (e) {
        if (this.skeleton) return;
        // 在 hospitalList 的index是固定的，但在 filterList 的 index 是不固定的
        // 因为有过滤，所以用 ID 更靠谱一些，始终以 hospitalList 为基准
        const { id, cityId } = e.currentTarget.dataset;
        const hospital = this.hospitalList.find(
          ({ hospital_id }) => Number(hospital_id) === Number(id)
        );
        this.$reportData({
          info: 'sy_clinic_other_change_stores:card_click',
          ext: {
            hospital_id: id,
            cityId
          }
        });
        this.$emit('after-select', cloneDeep(hospital));
      },
      1000,
      { trailing: false }
    ),
    onMakeCall: throttle(
      function (e) {
        if (this.skeleton) return;
        const { id } = e.currentTarget.dataset;
        const hospital = this.hospitalList.find(
          ({ hospital_id }) => Number(hospital_id) === Number(id)
        );
        const { business_start, business_end, service_tel, business_tel_msg } =
          hospital;
        const todayStr = dayjs().format('YYYY-MM-DD');
        if (
          (business_start === '00:00' && business_end === '00:00') ||
          (dayjs().isBefore(`${todayStr} ${business_end}:00`, 'hour') &&
            dayjs().isAfter(`${todayStr} ${business_start}:00`, 'hour'))
        ) {
          uni.makePhoneCall({
            phoneNumber: service_tel
          });
        } else {
          uni.showModal({
            content: business_tel_msg,
            showCancel: false
          });
        }
        this.$reportData({
          info: 'sy_clinic_other_change_stores:button_click',
          ext: {
            hospital_id: id,
            type: 2 // 电话
          }
        });
      },
      1000,
      { trailing: false }
    ),
    go2there: throttle(
      function (e) {
        if (this.skeleton) return;
        const { id } = e.currentTarget.dataset;
        const hospital = this.hospitalList.find(
          ({ hospital_id }) => Number(hospital_id) === Number(id)
        );
        if (!hospital) return;
        uni.openLocation({
          latitude: Number(hospital.hospital_lat),
          longitude: Number(hospital.hospital_lng),
          scale: 18,
          name: hospital.hospital_name,
          address: hospital.hospital_addr,
          fail: (e) => {
            uni.showToast({
              title: JSON.stringify(e),
              icon: 'none'
            });
          }
        });
        this.$reportData({
          info: 'sy_clinic_other_change_stores:button_click',
          ext: {
            hospital_id: id,
            type: 1 // 导航
          }
        });
      },
      1000,
      { trailing: false }
    )
  }
};
</script>
<style lang="less" scoped>
.container {
  box-sizing: border-box;
  width: 100%;
  max-height: 50vh;
  overflow: hidden;
  background: #f2f2f2;
  .hospital-cnt {
    box-sizing: border-box;
    padding: 20rpx 20rpx 0;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: #646464;
    font-weight: 400;
  }
  .scroll-box {
    box-sizing: border-box;
    padding: 20rpx;
    .hos-card-wrap {
      position: relative;
      overflow: hidden;
      .prime-logo {
        position: absolute;
        top: 0;
        right: 1px;
        width: 92rpx;
        height: 28rpx;
        z-index: 1;
      }
    }
    .hos-card {
      position: relative;
      margin-bottom: 20rpx;
      box-sizing: border-box;
      padding: 20rpx;
      width: 355 * 2rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      &.active::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: calc(100% - 2px);
        height: calc(100% - 2px);
        border: 1px solid #030303;
        z-index: 0;
      }
      .left {
        position: relative;
        width: 248 * 2rpx;
        z-index: 1;
        .name {
          margin-bottom: 20rpx;
          font-family: PingFangSC-Medium;
          font-size: 28rpx;
          color: #030303;
          max-height: 80rpx;
          line-height: 40rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          overflow: hidden;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
        }
        .mb {
          margin-bottom: 10rpx;
        }
        .row {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          img {
            margin-top: 6rpx;
            margin-right: 10rpx;
            width: 24rpx;
            height: 24rpx;
          }
          .text {
            // 图标 + 间距
            width: calc(100% - 34rpx);
            font-family: PingFangSC-Regular;
            font-size: 24rpx;
            color: #8c8c8c;
            letter-spacing: 0;
            font-weight: 400;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            overflow: hidden;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
          }
        }
      }
      .right {
        position: relative;
        width: 65 * 2rpx;
        text-align: center;
        z-index: 1;
        .do {
          margin-bottom: 4rpx;
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #030303;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }
        .distance {
          margin-bottom: 20rpx;
          font-family: PingFangSC-Regular;
          font-size: 22rpx;
          color: #646464;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
        }
        .btns {
          width: 100%;
          display: flex;
          justify-content: space-between;
          .btn {
            width: 50rpx;
            height: 50rpx;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    .no-more {
      padding: 10rpx;
      font-size: 22rpx;
      color: #aaabb3;
      text-align: center;
      padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
  .empty-list {
    box-sizing: border-box;
    padding-top: 30rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
}
@skeletonColor: #f2f2f2;
.skeleton {
  height: 50vh;
  .hospital-cnt {
    color: @skeletonColor;
    &::before {
      content: '';
      display: inline-block;
      width: 120rpx;
      height: 32rpx;
      background-color: #fff;
    }
  }
  .scroll-box {
    .prime-logo {
      opacity: 0;
    }
    .hos-card {
      .left {
        .name,
        .text {
          color: @skeletonColor!important;
          background: @skeletonColor;
        }
        img {
          display: none;
        }
      }
    }
    .right {
      .do {
        color: @skeletonColor!important;
        background: @skeletonColor;
      }
      .btns .btn {
        background: @skeletonColor;
        img {
          opacity: 0;
        }
      }
    }
    .no-more {
      display: none;
    }
  }
}
</style>
