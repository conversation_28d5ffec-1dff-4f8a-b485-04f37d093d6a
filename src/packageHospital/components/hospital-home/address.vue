<template>
  <div
    class="address-container"
    :class="{
      sketelon
    }"
  >
    <div class="address">{{ address }}</div>
    <div
      class="item-flex"
      :class="{
        bottom: hasGuide
      }"
      @click="onAddressClick"
    >
      <div class="icon">
        <img
          src="https://static.soyoung.com/sy-design/alojc1oae16j1725617618874.png"
        />
      </div>
      <div class="text">
        <div @click.stop="onSetLocation">
          {{ distance }}
        </div>
      </div>
      <div class="arrow"></div>
    </div>
    <div class="item-flex" v-if="hasGuide" @click="go2Guide">
      <div class="icon">
        <img
          :style="{ width: '32rpx', height: '32rpx' }"
          src="https://static.soyoung.com/sy-design/22elqxuramexq1725617618885.png"
        />
      </div>
      <div class="text">到店线路指引</div>
      <div class="arrow"></div>
    </div>
    <div class="map-wrap">
      <div class="map-market" @tap="onAddressClick"></div>
      <map
        v-if="!sketelon"
        class="map"
        :scale="12"
        id="map_container"
        :longitude="lon"
        :latitude="lat"
        :show-location="true"
        :enable-zoom="false"
        :enable-scroll="false"
        :markers="markers"
        @callouttap="onAddressClick"
      >
        <cover-view slot="callout">
          <cover-view :marker-id="0">
            <cover-view class="callout-box">
              <cover-view class="callout-text">
                {{ info.name_cn }}
              </cover-view>
              <cover-image
                class="callout-arrow"
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1725611272079.png"
              ></cover-image>
            </cover-view>
          </cover-view>
        </cover-view>
      </map>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
export default {
  props: {
    sketelon: {
      type: Boolean,
      default: true
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      markers: [] // 0 为机构，1为用户定位
    };
  },
  watch: {
    info() {
      this.markers.push({
        id: 0,
        latitude: this.lat,
        longitude: this.lon,
        iconPath:
          'https://static.soyoung.com/sy-design/q7esh7pojvfk1725949193871.png',
        width: 25,
        height: 25,
        index: 0,
        customCallout: {
          anchorY: -10,
          anchorX: 0,
          display: 'ALWAYS'
        }
      });
    }
    // userInfo() {
    // console.log(1321321, this.userInfo);
    // if (this.userInfo.lat && this.userInfo.lng) {
    //   this.setUPosition(this.userInfo.lat, this.userInfo.lng);
    // }
    // }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    lon() {
      return this.info?.lon || '';
    },
    lat() {
      return this.info?.lat || '';
    },
    address() {
      return this.info?.address || '';
    },
    distance() {
      return this.info?.distance
        ? `${this.info.distance}`
        : '授权地理位置，查看距离';
    },
    hasPosition() {
      return this.lat && this.lon;
    },
    hasGuide() {
      return (
        Array.isArray(this.info?.guide_images) &&
        this.info?.guide_images.length > 0
      );
    }
  },
  methods: {
    go2Guide() {
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:store_guide_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
      this.$bridge({
        url: `/packageHospital/hospital-guide?hospital_id=${this.info.hospital_id}`
      });
    },
    onAddressClick() {
      this.openLocation();
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:map_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
    },
    onMapClick() {
      this.openLocation();
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:map_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
    },
    openLocation() {
      uni.openLocation({
        latitude: Number(this.lat),
        longitude: Number(this.lon),
        scale: 18,
        name: this.info.name_cn,
        address: this.address,
        fail: (e) => {
          uni.showToast({
            title: JSON.stringify(e),
            icon: 'none'
          });
        }
      });
    },
    onSetLocation() {
      this.$reportData({
        info: this.info?.distance
          ? 'sy_chain_store_tuan_hospital_homepage:address_click'
          : 'sy_chain_store_tuan_hospital_homepage:authorize_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
      if (this.userInfo.lat) {
        this.openLocation();
        return;
      }
      const that = this;
      wx.getSetting({
        success(res) {
          const setting = res.authSetting['scope.userLocation'];
          if (setting === undefined) {
            that.getLocation();
          } else {
            wx.openSetting();
          }
        }
      });
    },
    setUPosition(latitude, longitude) {
      this.markers.push({
        id: 1,
        latitude,
        longitude,
        iconPath:
          'https://static.soyoung.com/sy-design/1y12g3snyh4zs1715862729752.png',
        width: 44,
        height: 44
      });
    },
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        // 解决下不然会抛出错误
        return {};
      });
      if (res.lat && res.lng) {
        this.$emit('after-location');
      }
    },
    // 设置全部显示在视图内的点
    async setIncludePoints(list) {
      if (!this.map) {
        this.map = wx.createMapContext('map_container');
      }
      this.map.includePoints({
        points: list.map((item) => {
          return {
            longitude: item.longitude,
            latitude: item.latitude
          };
        }),
        padding: [50, 50, 50, 50]
      });
    }
  }
};
</script>
<style lang="less" scoped>
.address {
  font-family: OutFit-Regular;
  font-size: 28rpx;
  color: #030303;
  text-align: justify;
  line-height: 48rpx;
  font-weight: 500;
  margin-top: 30rpx;
}
.service-container {
  overflow: hidden;
}
.item-flex {
  position: relative;
  margin-top: 15px;
  min-height: 48rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .icon {
    margin-right: 15rpx;
    width: 40rpx;
    height: 40rpx;
    img {
      width: 100%;
      height: 100%;
      transition: opacity 0.3s;
    }
  }
  .text {
    flex: 1;
    width: 600rpx;
    min-height: 48rpx;
    font-size: 26rpx;
    color: #030303;
    line-height: 48rpx;
    word-break: break-all;
    font-weight: 400;
  }
  .arrow {
    margin-left: 20rpx;
    display: inline-block;
    content: '';
    width: 12rpx;
    height: 17rpx;
    background: url(https://static.soyoung.com/sy-design/3dhi453czg4cw1725950266296.png)
      no-repeat center center transparent;
    background-size: contain;
  }
  // &.bottom::after {
  //   content: '';
  //   position: absolute;
  //   left: 41rpx;
  //   right: 0;
  //   bottom: 0rpx;
  //   border: 1px solid #f1f1f1;
  //   transform: scaleY(0.5);
  // }
}
.tip {
  line-height: 34rpx;
  font-size: 26rpx;
  color: #030303;
  font-weight: 400;
}

.map-wrap {
  margin-top: 30rpx;
  margin-bottom: 10rpx;
  width: 640rpx;
  height: 380rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: @sketelonBackgroundColor;
  position: relative;
  .map-market {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 1;
  }
  .map {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
  }
}

@sketelonBackgroundColor: #f6f6f6;

.sketelon {
  .item-flex {
    .icon {
      background-color: @sketelonBackgroundColor;
      img {
        opacity: 0;
      }
    }
    .arrow {
      background: none;
      background-color: @sketelonBackgroundColor;
    }
    .text {
      color: @sketelonBackgroundColor;
      background-color: @sketelonBackgroundColor;
      .tip {
        display: none;
      }
    }
  }
  .more-btn {
    margin: 0 auto;
    width: 200rpx;
    color: @sketelonBackgroundColor;
    background-color: @sketelonBackgroundColor;
    &::after {
      opacity: 0;
    }
  }
  .bottom::after {
    display: none;
  }
}
.callout-box {
  padding: 10rpx 20rpx;
  background: #fff;
  border-radius: 60rpx;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .callout-text {
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    text-align: center;
    color: #030303;
    letter-spacing: 0;
    text-align: justify;
    font-weight: 400;
    max-width: 460rpx;
    min-width: 300rpx;
    padding-right: 8rpx;
    word-break: break-all;
    word-wrap: break-word;
    text-align: center;
  }
  .callout-arrow {
    display: inline-block;
    width: 12rpx;
    height: 18rpx;
    vertical-align: -2rpx;
    margin-left: 10px;
    align-self: center;
    border-radius: 60rpx;
  }
}
</style>
