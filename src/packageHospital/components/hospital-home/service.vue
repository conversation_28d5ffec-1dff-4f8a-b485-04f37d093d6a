<template>
  <div
    class="service-container"
    :class="{
      sketelon
    }"
  >
    <div class="service-list">
      <div class="item" v-for="(item, index) in showList" :key="index">
        <div class="icon">
          <img :src="item.icon" />
        </div>
        <div class="text">{{ item.name }}</div>
      </div>
    </div>
    <div
      v-if="moreVisible"
      class="more-btn"
      :class="[showAll ? 'all' : '']"
      @click="moreData"
    >
      {{ moreText }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    sketelon: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array
    },
    hospital_id: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showAll: false
    };
  },
  computed: {
    showList() {
      if (this.sketelon) return new Array(8).fill({ icon: '', name: 'xxx' });
      return this.showAll ? this.list : this.list.slice(0, 8);
    },
    moreVisible() {
      if (this.sketelon) return true;
      return this.list.length > 8;
    },
    moreText() {
      return this.showAll ? '收起更多' : '展开更多';
    }
  },
  methods: {
    moreData() {
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:more_facility_click',
        ext: {
          hospital_id: this.hospital_id
        }
      });
      this.showAll = !this.showAll;
    }
  }
};
</script>
<style lang="less" scoped>
.service-container {
}
.service-list {
  box-sizing: border-box;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.item:not(:nth-child(4n + 1)) {
  margin-left: 3px;
}
.item {
  margin: 40rpx 0rpx 20rpx;
  width: 155rpx;
  // &:nth-child(4n + 1) {
  //   margin-left: 0;
  // }
  // &:nth-child(4n + 4) {
  //   margin-right: 0;
  // }
  .icon {
    display: block;
    margin: 0 auto 14rpx;
    width: 48rpx;
    height: 48rpx;
    img {
      width: 100%;
      height: 100%;
      transition: opacity 0.3s;
    }
  }
  .text {
    font-size: 24rpx;
    color: #222222;
    text-align: center;
    font-weight: 400;
    overflow: hidden;
    word-break: keep-all;
    text-overflow: ellipsis;
  }
}
.more-btn {
  padding-top: 20rpx;
  font-size: 24rpx;
  color: #555555;
  text-align: center;
  font-weight: 400;
  &::after {
    margin-left: 5rpx;
    vertical-align: -3rpx;
    display: inline-block;
    content: '';
    width: 24rpx;
    height: 24rpx;
    // https://static.soyoung.com/sy-design/2zfgcua7r2u7z1725950266298.png
    background: url(https://static.soyoung.com/sy-design/3u5f7j35qkn711725950266399.png)
      no-repeat center center transparent;
    background-size: contain;
    // transition: transform 0.3s;
    transform: rotate(0);
  }
  &.all::after {
    transform: rotate(180deg);
  }
}

@sketelonBackgroundColor: #f6f6f6;

.sketelon {
  .item {
    .icon {
      background-color: @sketelonBackgroundColor;
      img {
        opacity: 0;
      }
    }
    .text {
      color: @sketelonBackgroundColor;
      background-color: @sketelonBackgroundColor;
    }
  }
  .more-btn {
    margin: 0 auto;
    width: 200rpx;
    color: @sketelonBackgroundColor;
    background-color: @sketelonBackgroundColor;
    &::after {
      opacity: 0;
    }
  }
}
</style>
