<template>
  <div class="hospital-detail">
    <div
      :class="{
        'hospital-inner': true,
        sketelon
      }"
    >
      <img
        v-if="!sketelon && Number(info.hospital_tag_type) === 1"
        class="zyicon"
        src="https://static.soyoung.com/sy-pre/20gc1nwk5dtgb-1744009800634.png"
      />
      <img
        v-if="!sketelon && Number(info.hospital_tag_type) === 2"
        class="zyicon"
        src="https://static.soyoung.com/sy-pre/lak4rrg5020f-1744009800634.png"
      />
      <!-- 基础信息 -->
      <div class="info">
        <!-- <div class="left">
          <img class="icon" :src="info.icon || defaultIcon" />
        </div> -->
        <div class="right">
          <div class="line1">
            <span class="hos-name">
              {{ info.name_cn }}
            </span>
          </div>
          <div class="line2">
            <div class="service-num">
              <template v-if="info && info.service_times > 10"
                >已服务{{
                  info.service_times > 10000
                    ? (info.service_times / 10000).toFixed(1) + '万'
                    : info.service_times
                }}人次</template
              ><template v-else>新店入驻</template>
            </div>
          </div>
        </div>
      </div>
      <!-- 认证信息 -->
      <div class="auth" @click="toSafePage" v-if="sketelon || info.guarantee">
        <img class="auth-img" mode="widthFix" :src="info.guarantee" />
      </div>
      <div class="service-info">
        <div class="work-time">
          <div
            class="work-status"
            :class="{
              not: workStatus === '休息中'
            }"
          >
            {{ workStatus }}
          </div>
          <div class="work-time-range">
            {{ formatBusinessHours }}
          </div>
        </div>
        <div class="btns">
          <div class="btn" v-if="showHospitalZx" @click="handleZx">
            <img
              src="https://static.soyoung.com/sy-design/lo8uq5z64edr1725617618878.png"
            />
            <span>咨询/预约</span>
          </div>
          <div
            v-if="showHospitalTel"
            class="btn"
            @click="handleClickHospitalTel"
          >
            <img
              src="https://static.soyoung.com/sy-design/2rhbun9c8cczb1725617618873.png"
            />
            <span>电话</span>
          </div>
        </div>
      </div>
    </div>
    <JoincDialog v-model="zxVisible" :hospital_id="info.hospital_id" />
  </div>
</template>
<script>
// import Contact from './contact.vue';
import JoincDialog from '@/components/joinc.vue';
export default {
  components: {
    // Contact,
    JoincDialog
  },
  props: {
    sketelon: {
      type: Boolean,
      default: true
    },
    info: {
      type: Object,
      default: () => null
    },
    get_location_success: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      zxVisible: false,
      defaultIcon:
        'https://static.soyoung.com/sy-design/giv6qptrd2lr1721978880006.png'
    };
  },
  computed: {
    formatBusinessHours() {
      if (!this.info) return '';
      const { start_time, end_time } = this.info;
      return `${start_time}-${end_time}`;
    },
    workStatus() {
      return this.info?.is_work_time === 1 ? '营业中' : '休息中';
    },
    showHospitalTel() {
      return this.info?.mobile !== '';
    },
    showHospitalZx() {
      return this.info?.consultant_qr_code !== '';
    }
  },
  methods: {
    handleZx() {
      this.zxVisible = true;
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:consult_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
    },
    handleClickHospitalTel() {
      const { is_work_time, mobile, mobile_msg } = this.info;
      if (+is_work_time === 1) {
        uni.makePhoneCall({
          phoneNumber: mobile
        });
      } else {
        uni.showModal({
          content: mobile_msg,
          showCancel: false
        });
      }
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:phone_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
    },
    goTothere() {
      uni.openLocation({
        latitude: Number(this.info.hospital_lat),
        longitude: Number(this.info.hospital_lon),
        scale: 18,
        name: this.info.hospital_name,
        address: this.info.hospital_addr,
        fail: (e) => {
          uni.showToast({
            title: JSON.stringify(e),
            icon: 'none'
          });
        }
      });
    },
    toSetLocation() {
      // 新氧清水团-清水团-机构主页-授权位置-点击
      this.$reportData({
        info: 'sy_wxtuan_tuan_hospital_homepage:authorize_location_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
      this.$emit('setLocation');
    },
    // 前往服务保障页
    toSafePage() {
      // 新氧清水团-清水团-机构主页-权益腰带-点击
      this.$reportData({
        info: 'sy_wxtuan_tuan_hospital_homepage:qualification_belt_click',
        ext: {
          hospital_id: this.info.hospital_id
        }
      });
      this.$toH5(
        `https://m.soyoung.com/hospital/safeguard?hospital_id=${this.info.hospital_id}`
      );
      // this.$bridge({
      //   url: `/packageHospital/hospital-safeguard?hospital_id=${this.info.hospital_id}`
      // });
    }
  }
};
</script>
<style lang="less" scoped>
.hospital-detail {
  position: relative;
  &::after {
    content: '';
    display: block;
    width: 100vw;
    height: 29rpx;
    background-image: linear-gradient(180deg, #fff 0%, #f6f6f6 100%);
  }
  .hospital-inner {
    position: relative;
    background: #ffffff;
    box-sizing: border-box;
    padding: 40rpx 30rpx 0;
  }
  .zyicon {
    position: absolute;
    right: 0;
    top: 0;
    display: inline-block;
    width: 132rpx;
    height: 20 * 2rpx;
    z-index: 1;
  }

  // 基础信息
  .info {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    .left {
      margin-right: 20rpx;
      width: 57 * 2rpx;
      height: 57 * 2rpx;
      border-radius: 57rpx 57rpx 0 57rpx;
      overflow: hidden;
      .icon {
        vertical-align: top;
        display: inline-block;
        border-radius: 57rpx 57rpx 0 57rpx;
        width: 100%;
        height: 100%;
        transition: opacity 0.5s;
      }
    }
    .right {
      flex: 1;
      .line1 {
        margin-bottom: 10rpx;
        .hos-name {
          font-size: 18 * 2rpx;
          color: #222222;
          font-weight: 600;
        }
      }

      .line2 {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .service-num {
          padding: 0 10rpx;
          height: 42rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
          color: #555;
          font-weight: 400;
          background-color: #f6f6f6;
          border-radius: 2px;
        }
      }
    }
  }

  // 认证信息
  .auth {
    // height: 90rpx;
    width: 345 * 2rpx;
    margin: 20rpx auto 30rpx;
    .auth-img {
      display: inline-block;
      width: 100%;
      height: 100%;
      transition: opacity 0.5s;
    }
  }

  // 营业时间
  .service-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .work-time {
      .work-status {
        margin-bottom: 14rpx;
        line-height: 42rpx;
        font-size: 34rpx;
        color: #61b43e;
        font-weight: 400;
        &.not {
          font-size: 17px;
          color: #8c8c8c;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
      .work-time-range {
        line-height: 34rpx;
        font-size: 24rpx;
        color: #222;
      }
    }
    .btns {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .btn {
        margin-left: 56rpx;
        text-align: center;
        font-size: 20rpx;
        color: #777;
        img {
          margin: 0 auto 8rpx;
          display: block;
          width: 60rpx;
          height: 60rpx;
          transition: opacity 0.5s;
        }
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}

@sketelonBackgroundColor: #f6f6f6;
.sketelon {
  .left {
    background-color: @sketelonBackgroundColor;
    .icon {
      opacity: 0;
    }
  }
  .right {
    .hos-name {
      color: @sketelonBackgroundColor!important;
      background-color: @sketelonBackgroundColor;
      &::before {
        content: 'xxxxxxxxxxxxxxxxx';
      }
    }
    .service-num {
      color: @sketelonBackgroundColor!important;
      background-color: @sketelonBackgroundColor;
    }
  }
  .auth {
    background-color: @sketelonBackgroundColor;
    .auth-img {
      opacity: 0;
    }
  }
  .service-info {
    .work-time {
      .work-status,
      .work-time-range {
        color: @sketelonBackgroundColor!important;
        background-color: @sketelonBackgroundColor;
      }
    }
    .btns {
      .btn {
        color: @sketelonBackgroundColor!important;
        background-color: @sketelonBackgroundColor;
        img {
          opacity: 0;
        }
      }
    }
  }
}
</style>
