<template>
  <root-portal>
    <Popup ref="popupCommissioner" @maskClick="handleClose">
      <view class="popup-commissioner">
        <image
          class="popup-commissioner-close"
          src="https://static.soyoung.com/sy-design/1azvpt7asrrzj1717411370224.png"
          alt="关闭"
          @click="handleClose"
        ></image>
        <view class="popup-commissioner-header">
          <text class="title"> 感谢您选择 </text>
          <text class="subtitle">
            {{ hospitalName }}
          </text>
        </view>
        <view class="popup-commissioner-body">
          <image
            v-if="qrcode"
            class="code"
            :show-menu-by-longpress="true"
            :src="qrcode"
          ></image>
          <text
            v-if="hospitalTel"
            class="customer-service"
            @click="handleClickHospitalTel"
          >
            电话联系客服
          </text>
        </view>
      </view>
    </Popup>
  </root-portal>
</template>
<script>
import Popup from '@/components/uni/popup.vue';

export default {
  components: {
    Popup
  },
  props: {
    info: {
      type: Object,
      default: () => null
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    hospitalName() {
      return this.info?.name_cn || '';
    },
    hospitalTel() {
      return this.info?.mobile || '';
    },
    qrcode() {
      return this.info?.consultant_qr_code || '';
    }
  },
  watch: {
    /**
     * 联系专员
     * */
    visible() {
      if (this.info.consultant_qr_code) {
        this.$refs.popupCommissioner.open('bottom');
      } else {
        uni.showToast({
          title: '该门店暂无预约专员联系方式，如有问题，请联系客服',
          icon: 'none'
        });
      }
    }
  },
  methods: {
    handleClickHospitalTel() {
      const { is_work_time, mobile, mobile_msg } = this.info;
      if (+is_work_time === 1) {
        uni.makePhoneCall({
          phoneNumber: mobile
        });
      } else {
        return uni.showModal({
          content: mobile_msg,
          showCancel: false
        });
      }
    },
    handleClose() {
      this.$emit('update:visible', false);
      this.$refs.popupCommissioner.close('bottom');
    }
  }
};
</script>

<style lang="less" scoped>
.popup-commissioner {
  width: 100%;
  min-height: 622 * 2rpx;
  background-image: url('https://static.soyoung.com/sy-design/bg1717411370761.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding-top: 15 * 2rpx;
  padding-bottom: 34 * 2rpx;
  position: relative;
  .popup-commissioner-close {
    position: absolute;
    right: 10 * 2rpx;
    top: 16 * 2rpx;
    z-index: 10;
    width: 20 * 2rpx;
    height: 20 * 2rpx;
  }
  .popup-commissioner-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20 * 2rpx;
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 16 * 2rpx;
      color: #333333;
      font-weight: 600;
      padding-bottom: 10 * 2rpx;
    }
    .subtitle {
      font-family: PingFangSC-Semibold;
      font-size: 23 * 2rpx;
      color: #222222;
      font-weight: 600;
      width: calc(100% - 50 * 2rpx);
      display: block;
      text-align: center;
    }
  }
  .popup-commissioner-body {
    width: 337 * 2rpx;
    height: 489 * 2rpx;
    margin: 0 auto;
    background-image: url('https://static.soyoung.com/sy-design/1h1aj4adjaclb1717411370484.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .code {
      position: absolute;
      width: 184 * 2rpx;
      height: 184 * 2rpx;
      left: 50%;
      transform: translateX(-50%);
      top: 167 * 2rpx;
    }
    .customer-service {
      font-family: PingFangSC-Regular;
      font-size: 13 * 2rpx;
      color: #354052;
      text-align: center;
      font-weight: 400;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 16 * 2rpx;
      text-decoration: underline;
    }
  }
}
</style>
