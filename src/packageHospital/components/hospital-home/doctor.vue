<template>
  <div
    class="doctor-list"
    :class="{
      sketelon
    }"
  >
    <div
      :class="['item', `js-ob_${index}`]"
      :data-index="index"
      v-for="(doctor, index) in doctors"
      :key="index"
      @click="goDoctorDetail(doctor)"
    >
      <div class="avatar">
        <img :src="doctor.photo" />
      </div>
      <div class="content">
        <div class="name">
          <div class="ellipsis">
            <div class="doctor_name">{{ doctor.name }}</div>
            <div class="t">{{ doctor.title_show }}</div>
          </div>
          <div class="tag">
            <div
              v-for="(tag, tindex) in doctor.tags"
              :key="tindex"
              :class="['tag_box', { 'master': tag.tag_style == 1 }]"
            >
              <div class="tag_icon">{{ tag.tag_name }}</div>
              <div class="tag_tips">
                {{ `${tag.service_str} ${tag.service_num}` }}
              </div>
            </div>
          </div>
        </div>
        <div class="number">执业证书编号：{{ doctor.certificate_number }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    sketelon: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array,
      default: () => []
    },
    hospital_id: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  mounted() {},
  watch: {
    doctors: {
      handler(val) {
        if (val.length > 0) {
          this.createReportObserver();
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    createReportObserver() {
      const thresholds = [];
      const step = 1;
      for (let value = 0; value <= 100; value += step) {
        thresholds.push(value / 100);
      }
      const ob = this.createIntersectionObserver({
        thresholds: [0, 1],
        observeAll: true
      });
      ob.relativeToViewport({
        top: 0,
        bottom: 0
      });
      ob.observe('.item', (res) => {
        if (res.intersectionRatio === 1) {
          this.$reportData({
            info: 'sy_chain_store_tuan_hospital_homepage:doctor_exposure',
            ext: {
              hospital_id: this.hospital_id,
              content: this.doctors[res.dataset.index].name
            }
          });
        }
      });
      this.$once('hook:beforeDestroy', () => {
        ob.disconnect();
      });
    },
    goDoctorDetail(doctor) {
      this.$reportData({
        info: 'sy_chain_store_other_hospital_homepage:doctor_click',
        ext: {
          doctor_id: doctor.id,
          title: doctor.name
        }
      });
      this.$bridge({
        url: `/packageHospital/hospital-doctor?doctor_id=${doctor.id}`,
        callbackSuccess: (res) => {
          res.eventChannel.emit('packageHospital_doctor_click', {
            cover: doctor.image
          });
        }
      });
    }
  },
  computed: {
    doctors() {
      if (this.sketelon)
        return new Array(3).fill({
          name: 'x',
          title_show: 'x',
          certificate_number: 'x'
        });
      return this.list;
    }
  }
};
</script>
<style lang="less" scoped>
.doctor-list {
  .item {
    margin-top: 60rpx;
    height: 130rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:first-child {
      margin-top: 40rpx;
    }
    .avatar {
      height: 130rpx;
      width: 130rpx;
      border-radius: 50% 50%;
      overflow: hidden;
      background-color: #f2f2f2;
      img {
        vertical-align: top;
        width: 100%;
        height: 100%;
        transition: opacity 0.3s;
      }
    }
    .content {
      margin-left: 20rpx;
      flex: 1;
      width: 520rpx;
      color: #222222;
      .name {
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        width: 100%;
        .ellipsis {
          height: 40rpx;
          display: flex;
          justify-content: flex-start;
          flex-direction: row;
          width: 100%;
          align-items: center;
          .doctor_name {
            max-width: 200rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            font-family: PingFangSC-Medium;
            font-size: 28rpx;
            color: #030303;
            letter-spacing: 0;
            font-weight: 500;
          }
          .t {
            margin-left: 10rpx;
            font-size: 24rpx;
            font-weight: 400;
            font-family: PingFangSC-Regular;
            color: #345f22;
            letter-spacing: 0;
            text-align: justify;
            font-weight: 400;
          }
        }
        .tag {
          display: inline-flex;
          align-items: center;
          position: relative;
          flex-wrap: wrap;
          .tag_box {
            display: inline-flex;
            margin-top: 10rpx;
            margin-right: 14rpx;
          }
          .tag_icon {
            font-family: PingFangSC-Regular;
            font-size: 10px;
            color: #ffffff;
            letter-spacing: 0;
            font-weight: 500;
            background: #333333;
            padding: 0 4rpx;
          }
          .tag_tips {
            font-size: 20rpx;
            color: #333333;
            font-weight: 400;
            background: #ebedea;
            padding: 0 4rpx;
          }
          .master {
            .tag_icon {
              color: #ffffff;
              background: #ff6c0e;
            }
            .tag_tips {
              color: #ff6c0e;
              background: #ffeee2;
            }
          }
        }
      }

      .number {
        font-size: 24rpx;
        color: #8c8c8c;
        margin-top: 10rpx;
      }
    }
  }
}

@sketelonBackgroundColor: #f6f6f6;

.sketelon {
  .item {
    .avatar {
      background-color: @sketelonBackgroundColor;
      img {
        opacity: 0;
      }
    }
    .content {
      .name {
        color: @sketelonBackgroundColor;
        background-color: @sketelonBackgroundColor;
        .tag {
          display: none;
        }
      }
      .t {
        color: @sketelonBackgroundColor;
        background-color: @sketelonBackgroundColor;
      }
      .number {
        color: @sketelonBackgroundColor;
        background-color: @sketelonBackgroundColor;
      }
    }
  }
}
</style>
