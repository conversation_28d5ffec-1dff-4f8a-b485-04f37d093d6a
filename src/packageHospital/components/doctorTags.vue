<template>
  <root-portal>
    <div class="tags-model" v-if="value">
      <div class="mask" @click="onClose"></div>
      <div class="doctor-tags">
        <div class="border"></div>
        <div class="tags-box_border" @click="onClose">
          <image
            src="https://static.soyoung.com/sy-design/bzsokyai5osd1726228765172.png"
            mode="widthFix"
            class="close"
          ></image>
        </div>
        <scroll-view
          class="tags-box_scrollview"
          :scroll-x="false"
          :scroll-y="true"
          :bounces="false"
          :enhanced="true"
        >
          <div class="tags-box">
            <div class="tags-box_description">
              <Description
                :description="info.tag"
                :showLine="false"
              ></Description>
            </div>
            <img
              v-if="info.bottom_img.u"
              :src="info.bottom_img.u"
              mode="widthFix"
              class="tags-box_image"
            />
          </div>
        </scroll-view>
      </div>
    </div>
  </root-portal>
</template>
<script>
import Description from '@/packageHospital/components/description.vue';
import { getDoctorTagInfoApi } from '@/api/hospital';

export default {
  props: {
    tag_id: {
      type: String,
      default: ''
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Description
  },
  data() {
    return {
      info: {}
    };
  },
  watch: {
    value: {
      handler(val) {
        console.log('val', val);
        if (val) {
          this.getInfo();
        }
      },
      immediate: true
    }
  },
  methods: {
    async getInfo() {
      const res = await getDoctorTagInfoApi(this.tag_id);
      console.log('info', res);

      this.info = res;
      console.log('info', this.info);
    },
    onClose() {
      this.$emit('input', false);
    }
  }
};
</script>
<style lang="less" scoped>
.tags-model {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  animation: opacityami 0.3s ease-in-out;
  overflow: hidden;
  .mask {
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10001;
    overflow: hidden;
  }
}
.doctor-tags {
  width: 100%;
  position: relative;
  z-index: 10002;
  background-color: #fff;
  height: 80vh;
  .border {
    width: 100%;
    height: 20rpx;
    background-color: #a9ea6a;
  }
  .tags-box_border {
    width: 100%;
    height: 40rpx;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10003;
    .close {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      right: 20rpx;
      top: 40rpx;
    }
  }
  .tags-box_scrollview {
    height: calc(80vh - 20rpx);
    width: 100vw;
  }
  .tags-box {
    width: 100vw;
    .tags-box_description {
      padding: 40rpx 60rpx 0;
    }
    .tags-box_image {
      display: block;
      width: 100vw;
    }
  }
}

@keyframes opacityami {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
