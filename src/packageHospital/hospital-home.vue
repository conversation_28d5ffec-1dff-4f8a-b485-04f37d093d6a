<template>
  <page-meta :page-style="pageStyle">
    <PageLoading :visible="loading" />
    <div
      class="hospital-home-page"
      :class="{
        sketelon
      }"
    >
      <nav-bar
        :background="background"
        :title="title"
        :hasFixedHome="true"
        :hasBack="true"
        @onBack="navBack"
      ></nav-bar>
      <div class="img-covers-wrap">
        <swiper
          v-if="hasIndicator"
          :current="currentIndex"
          @change="onSwiperBindchange"
          class="swiper"
          autoplay="true"
        >
          <swiper-item
            v-for="(src, index) in banners"
            :item-id="index"
            :key="index"
            @click="previewImg(src)"
          >
            <div class="img-cover">
              <img mode="aspectFill" :src="src" />
            </div>
          </swiper-item>
        </swiper>
        <div v-else class="swiper">
          <div class="img-cover">
            <img
              mode="aspectFill"
              :src="singleImage"
              @click="previewImg(singleImage)"
            />
          </div>
        </div>
        <div class="swiper-slider" v-if="hasIndicator">
          <div class="slider-cur" :style="swiperSliderStyle"></div>
        </div>
      </div>
      <div class="hospital-detail-wrap">
        <Info
          :sketelon="sketelon"
          :info="hospitalInfo"
          @setLocation="setLocation"
        ></Info>
      </div>
      <div style="padding: 0 25rpx">
        <div class="section-wrap" v-if="serviceList.length">
          <div class="section-title">服务设施</div>
          <Service
            :list="serviceList"
            :sketelon="sketelon"
            :hospital_id="hospital_id"
          />
        </div>
        <div class="section-wrap">
          <div class="section-title">门店地址</div>
          <SAddress
            :info="hospitalInfo"
            :sketelon="sketelon"
            @after-location="getHospitalData"
          />
        </div>
        <div class="section-wrap" v-if="doctorList.length">
          <div class="section-title">医生团队</div>
          <Doctor
            :list="doctorList"
            :hospital_id="hospital_id"
            :sketelon="sketelon"
          />
        </div>
      </div>
      <div style="height: 128rpx; width: 100vw"></div>
      <div class="buy-button">
        <div @click="go2Buy">去门店选购</div>
      </div>
      <WeakNetwork
        @refresh="refresh"
        :path="['/syGroupBuy/chain/hospital/homePage']"
        :visible.sync="isNetwork"
      />
    </div>
  </page-meta>
</template>
<script>
import NavBar from '@/components/NavBar';
import Info from '@/packageHospital/components/hospital-home/info.vue';
import Service from '@/packageHospital/components/hospital-home/service.vue';
import SAddress from '@/packageHospital/components/hospital-home/address.vue';
import Doctor from '@/packageHospital/components/hospital-home/doctor.vue';
import { mapState } from 'vuex';
import PageLoading from '@/components/pageLoading.vue';
import { getHospitalInfoApi } from '@/api/hospital';
import WeakNetwork from '@/components/WeakNetwork.vue';
import { cloneDeep } from 'lodash-es';

const banner_default =
  'https://static.soyoung.com/sy-design/2hca5b5fhk6c31721902447806.jpg';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_tuan_hospital_homepage_page',
      ext: {
        hospital_id: this.hospital_id
      }
    };
  },
  components: {
    NavBar,
    Info,
    Service,
    SAddress,
    Doctor,
    PageLoading,
    WeakNetwork
  },
  data() {
    return {
      loading: true,
      sketelon: true,
      currentIndex: 0,
      banners: [],
      hospital_id: 0,
      activity_id: 0,
      hospitalInfo: null, // 机构信息
      background: 'rgba(255,255,255,0)',
      title: '',
      isNetwork: false
    };
  },
  watch: {
    'userInfo.lat'(lat) {
      if (lat && this.userInfo.lng) {
        this.getHospitalData();
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    swiperSliderStyle() {
      const len = this.banners?.length;
      return len > 0
        ? `width: ${100 / len}%;transform: translateX(${
            100 * this.currentIndex
          }%);`
        : '';
    },
    singleImage() {
      return this.banners[0] || banner_default;
    },
    hasIndicator() {
      return this.banners?.length > 1;
    },
    serviceList() {
      return this.hospitalInfo?.service_list || [];
    },
    doctorList() {
      return this.hospitalInfo?.doctor_list || [];
    },
    pageStyle() {
      return '';
    }
  },
  onLoad(query) {
    this.hospital_id = query.hospital_id;
    this.activity_id = query.activity_id;
    this.getHospitalData();
  },
  onShow() {
    this.getLocation();
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_hospital_homepage_page',
      ext: {
        hospital_id: this.hospital_id
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_hospital_homepage_page',
      ext: {
        hospital_id: this.hospital_id
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_hospital_homepage_page',
      ext: {
        hospital_id: this.hospital_id
      }
    });
  },
  // 分享给好友
  onShareAppMessage() {
    return {
      title: this.hospitalInfo.name_cn || '新氧青春',
      path: `/packageHospital/hospital-home?hospital_id=${this.hospital_id}&activity_id=${this.activity_id}`,
      imageUrl:
        'https://static.soyoung.com/sy-pre/sharehospital-1722327000635.jpeg'
    };
  },
  // 朋友圈分享
  onShareTimeline() {
    return {
      title: this.hospitalInfo.name_cn || '新氧青春',
      path: `/packageHospital/hospital-home?hospital_id=${this.hospital_id}&activity_id=${this.activity_id}`,
      imageUrl:
        'https://static.soyoung.com/sy-pre/sharehospital-1722327000635.jpeg'
    };
  },
  methods: {
    go2Buy() {
      this.$reportData({
        info: 'sy_chain_store_tuan_hospital_homepage:go_buy_click',
        ext: {
          hospital_id: this.hospital_id
        }
      });
      this.$setUserInfoToStorage({ hospital: cloneDeep(this.hospitalInfo) });
      uni.switchTab({
        url: `/pages/item`
      });
    },
    navBack() {
      uni.navigateBack();
    },
    async refresh() {
      await this.getHospitalData();
    },
    async getHospitalData() {
      this.loading = true;
      const responseData = await getHospitalInfoApi(this.hospital_id);
      // await new Promise(resolve => setTimeout(resolve, 3000));
      this.loading = false;
      if (responseData) {
        this.isNetwork = false;
        const { hospital_info } = responseData;
        this.hospitalInfo = hospital_info;
        this.banners = hospital_info.env_images?.length
          ? hospital_info.env_images
          : [
              'https://static.soyoung.com/sy-pre/17n4nx9xnm8fo-1687677000730.png'
            ];
        this.sketelon = false;
      } else {
        uni.showModal({
          title: '提示',
          content: '接口请求失败',
          confirmText: '重试',
          cancelText: '返回',
          success: (resp) => {
            if (resp.confirm) {
              this.getHospitalData();
            } else {
              uni.navigateBack();
            }
          }
        });
      }
    },
    previewImg(url) {
      uni.previewImage({
        current: url,
        urls: this.banners
      });
    },
    async getLocation() {
      const res = await this.$getCityId().catch(() => {
        // 解决下不然会抛出错误
        return {};
      });
      console.log('获取地理位置', res);
    },
    async setLocation() {
      const that = this;
      wx.getSetting({
        success(res) {
          const setting = res.authSetting['scope.userLocation'];
          if (setting === undefined) {
            that.getLocation();
          } else {
            wx.openSetting();
          }
        }
      });
    },
    onSwiperBindchange({ detail }) {
      this.currentIndex = detail.current;
    }
  },
  onPageScroll({ scrollTop }) {
    const show = scrollTop > 100 ? 1 : scrollTop / 100;
    this.background = `rgba(255,255,255,${show})`;
    this.title = show === 1 ? '机构主页' : '';
  }
};
</script>
<style lang="less" scoped>
.hospital-home-page {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  .img-covers-wrap {
    position: relative;
    .img-cover {
      width: 100%;
      height: 162 * 2rpx;
      img {
        width: 100%;
        height: 100%;
        transition: opacity 0.3s;
      }
    }
    .swiper-slider {
      position: absolute;
      right: 20rpx;
      bottom: 28rpx;
      height: 6rpx;
      width: 160rpx;
      border-radius: 2px;
      background: rgba(255, 255, 255, 0.5);
      overflow: hidden;
      z-index: 1;
      .slider-cur {
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        border-radius: 2px;
        background: #000;
        transition: transform 0.3s;
        z-index: 1;
      }
    }
  }
  .hospital-detail-wrap {
    transform: translate3d(0px, 0px, 0px);
    // border-radius: 16rpx 16rpx 0 0;
    margin-top: -16rpx;
    overflow: hidden;
  }
  .section-wrap {
    box-sizing: border-box;
    margin-bottom: 20rpx;
    padding: 40rpx 30rpx;
    background: #ffffff;
    color: #222222;
    .section-title {
      font-size: 30rpx;
      font-weight: 600;
    }
  }
  .buy-button {
    position: fixed;
    bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    bottom: env(safe-area-inset-bottom);
    left: 50%;
    margin-left: -345rpx;
    width: 345 * 2rpx;
    height: 88rpx;
    line-height: 88rpx;
    background: #333333;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    z-index: 1;
    margin-bottom: 40rpx;
  }

  @sketelonBackgroundColor: #f6f6f6;
  &.sketelon {
    .img-cover {
      background-color: #f6f6f6;
      img {
        opacity: 0;
      }
    }
    .section-wrap {
      color: @sketelonBackgroundColor;
      .section-title {
        width: 150rpx;
        overflow: hidden;
        white-space: nowrap;
        background-color: @sketelonBackgroundColor;
      }
    }
    .buy-button {
      display: none;
    }
  }
}
</style>
