<template>
  <div>
    <div
      v-if="menuRect.top"
      class="pageTop"
      :style="{ height: menuRect.bottom + 8 + 'px' }"
    >
      <div
        class="nav"
        :style="{
          top: menuRect.top + 'px',
          width: menuRect.width + 'px',
          height: menuRect.height + 'px'
        }"
      >
        <div class="backBox" @click="handleBack">
          <image
            class="back"
            src="https://static.soyoung.com/sy-design/3cj8rc3ipek931726026826755.png"
          />
        </div>
      </div>
    </div>

    <swiper
      class="swiper"
      :style="{ opacity: dataIsNormal ? 1 : 0 }"
      vertical="true"
      @change="swiperChange"
      @animationfinish="swiperAnimationFinish"
      :current="current"
      :duration="duration"
    >
      <block v-if="dataIsNormal">
        <swiper-item>
          <overview
            :register-before-report="register_info.register_before_report"
            :register-date-year="register_info.register_date_year"
            :register-date-month="register_info.register_date_month"
            :register-date-day="register_info.register_date_day"
            :placement="register_info.placement"
            :register-days="register_info.register_days"
          />
        </swiper-item>
        <swiper-item>
          <cost
            :consume-total-amount="consume_info.consume_total_amount"
            :medical-product-count="consume_info.medical_product_count"
            :cosmetic-product-count="consume_info.cosmetic_product_count"
            :total-placement="consume_info.total_placement"
            :savings-amount="consume_info.savings_amount"
          />
        </swiper-item>
        <swiper-item>
          <add-c
            :join-c-count="private_domain_info.join_c_count"
            :join-group-count="private_domain_info.join_group_count"
            :group-friend-count="private_domain_info.group_friend_count"
            :invite-friend-count="private_domain_info.invite_friend_count"
          />
        </swiper-item>
        <swiper-item>
          <private
            :join-group-count="private_domain_info.join_group_count"
            :new-prod-rounds="private_domain_gain.new_prod_rounds"
            :sci-text-count="private_domain_gain.sci_text_count"
          />
        </swiper-item>
        <swiper-item>
          <chain-part-one
            :consume-total-amount="consume_info.consume_total_amount"
            :medical-project-count="category_preference.medical_project_count"
            :preference-project="category_preference.preference_project"
            :verify-count="category_preference.verify_count"
            :preference-tenant="category_preference.preference_tenant"
            :preference-doctor="category_preference.preference_doctor"
          />
        </swiper-item>
        <swiper-item v-if="consume_info.consume_total_amount > 0">
          <chain-part-two
            :order-project-count="project_consume.order_project_count"
            :order-project-total-amount="
              project_consume.order_project_total_amount
            "
            :order-project-top-list="
              project_consume.order_project_top_list || []
            "
            :savings-amount="project_consume.savings_amount"
          />
        </swiper-item>
        <swiper-item>
          <chain-explosive :data="hot_project" source="chain"></chain-explosive>
        </swiper-item>
        <swiper-item>
          <master-team :data="doctor_master"></master-team>
        </swiper-item>
        <swiper-item>
          <beautiful-items :data="cosmetic_product"></beautiful-items>
        </swiper-item>
        <swiper-item>
          <chain-explosive
            :data="cosmetic_hot_product"
            source="beautiful"
          ></chain-explosive>
        </swiper-item>
        <swiper-item v-if="special_moment_order.consume_amount">
          <specialOne :data="special_moment_order"></specialOne>
        </swiper-item>
        <swiper-item
          v-if="
            special_moment_login.login_latest_date &&
            special_moment_login.login_earliest_date
          "
        >
          <specialTwe :data="special_moment_login"></specialTwe>
        </swiper-item>
        <swiper-item>
          <template v-if="consume_info.consume_total_amount > 0">
            <Result1
              :exp="{
                mx_source: this.pageData.scene.s,
                serial_num: current + 2
              }"
              :data="summary_report"
              @review="review"
            />
          </template>
          <template v-else-if="private_domain_info.join_c_count > 0">
            <Result2
              :exp="{
                mx_source: this.pageData.scene.s,
                serial_num: current + 2
              }"
              :data="summary_report"
              @review="review"
            />
          </template>
          <template v-else>
            <Result3
              :exp="{
                mx_source: this.pageData.scene.s,
                serial_num: current + 2
              }"
              :data="summary_report"
              @review="review"
            />
          </template>
        </swiper-item>
      </block>
    </swiper>
  </div>
</template>

<script>
import Result1 from './components/result1.vue';
import Result2 from './components/result2.vue';
import Result3 from './components/result3.vue';
import Overview from './components/overview.vue';
import Cost from './components/cost.vue';
import AddC from './components/addC.vue';
import Private from './components/private.vue';
import ChainPartOne from './components/chainPartOne.vue';
import ChainPartTwo from './components/chainPartTwo.vue';
import specialOne from './components/specialOne.vue';
import specialTwe from './components/specialTwe.vue';
import ChainExplosive from './components/chain-explosive.vue';
import MasterTeam from './components/master-team.vue';
import BeautifulItems from './components/beautiful-items.vue';

import { getReportData } from '@/api/activity';
import { mapState } from 'vuex';

export default {
  components: {
    BeautifulItems,
    MasterTeam,
    Result1,
    Result2,
    Result3,
    ChainExplosive,
    Overview,
    Cost,
    AddC,
    Private,
    ChainPartOne,
    ChainPartTwo,
    specialOne,
    specialTwe
  },
  computed: {
    ...mapState({
      globalUserInfo: (state) => state.global.userInfo
    }),
    /**
     * @typedef {Object} IScene
     * @property {string||number} u - 分享者uid
     * @property {string||number} s - 私域引流入口来源
     * */

    /**
     * @typedef {Object} IPageData
     * @property {string||number} [test_uid] - 调试参数，后续可去除
     * @property {IScene} [scene] - 用户扫码进入入参
     * */

    /**
     * @returns {IPageData}
     * */
    pageData() {
      const data = { scene: { u: 0, s: 0 } };

      if (this.options?.test_uid) {
        data.test_uid = this.options.test_uid;
      }

      try {
        if (this.options?.scene) {
          data.scene.u = JSON.parse(this.options.scene)?.u || 0;
          data.scene.s = JSON.parse(this.options.scene)?.s || 0;
        }
      } catch (e) {
        data.scene = { u: 0, s: 0 };
      }

      return data;
    }
  },
  watch: {
    current(val, oldVal) {
      const data = {
        ext: {
          mx_source: this.pageData.scene.s,
          serial_num: val + 2
        }
      };

      if (val > oldVal) {
        data.info =
          'sy_chain_store_privatedomain_annual_report:next_step_click';
      } else {
        data.info =
          'sy_chain_store_privatedomain_annual_report:previous_step_click';
      }

      this.$reportData(data);
    }
  },
  data() {
    return {
      options: {},
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      dataIsNormal: false,
      current: 0,
      duration: 500,
      register_info: {}, // 个人概览
      special_moment_order: {}, // 特殊时刻
      special_moment_login: {}, // 是否是特殊时刻登录
      summary_report: {}, // 汇总报告
      consume_info: {},
      private_domain_info: {},
      private_domain_gain: {},
      category_preference: {},
      project_consume: {},
      hot_project: {}, // 连锁爆品
      doctor_master: {}, // 大师团
      cosmetic_product: {}, // 好物
      cosmetic_hot_product: {} // 好物爆品
    };
  },
  methods: {
    handleBack() {
      if (getCurrentPages().length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack({
          delta: -1
        });
      }
    },
    nextPage() {
      if (this.current >= 8) {
        this.review();
      } else {
        this.current++;
      }
    },
    review() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:again_click',
        ext: {
          mx_source: this.pageData.scene.s,
          serial_num: this.current + 2
        }
      });
      this.duration = 0;
      // 延迟500ms执行，否则会出现闪屏
      setTimeout(() => {
        this.current = 0;
        this.duration = 500;
      }, 500);
    },
    swiperChange(e) {
      console.log('swiperChange');
      console.log(e);
      this.current = e.target.current;
    },
    swiperAnimationFinish(e) {
      console.log('swiperAnimationFinish');
      console.log(e);
    },
    setReportData(res) {
      this.register_info = res.responseData.register_info;
      this.special_moment_order = res.responseData.special_moment_order;
      this.special_moment_login = res.responseData.special_moment_login;
      this.summary_report = res.responseData.summary_report;
      this.consume_info = res.responseData.consume_info;
      this.private_domain_info = res.responseData.private_domain_info;
      this.private_domain_gain = res.responseData.private_domain_gain;
      this.category_preference = res.responseData.category_preference;
      this.project_consume = res.responseData.project_consume;
      this.hot_project = res.responseData?.hot_project || {};
      this.doctor_master = res.responseData?.doctor_master || {};
      this.cosmetic_product = res.responseData?.cosmetic_product || {};
      this.cosmetic_hot_product = res.responseData?.cosmetic_hot_product || {};
      this.$nextTick(() => {
        this.dataIsNormal = true;
      });
    }
  },
  async onLoad(options) {
    console.log('index onload options ->', options);
    const eventChannel = this.getOpenerEventChannel();
    this.options = options;
    this.menuRect = uni.getMenuButtonBoundingClientRect();

    // 延迟兜底，防止事件消息传递异常
    const delayTheBottomLine = setTimeout(() => {
      const requestBody = {
        agreement_status: 1
      };

      // 用于调试，上线后接口会去除该字段
      if (options?.test_uid) {
        requestBody.test_uid = options.test_uid;
      }

      getReportData(requestBody).then((res) => {
        if (!res?.responseData) {
          uni.navigateBack({
            delta: 1
          });
          return;
        }
        this.setReportData(res);
      });
    }, 600);

    eventChannel.on('REPORT_DATA', (data) => {
      if (data) {
        this.setReportData(data);
        clearTimeout(delayTheBottomLine);
      }
    });
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_chain_store_privatedomain_annual_report_page',
      ext: {
        mx_source: this.pageData.scene.s,
        serial_num: this.current + 2,
        uid: this.pageData.scene.u,
        type: 3
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_privatedomain_annual_report_page',
      ext: {
        mx_source: this.pageData.scene.s,
        serial_num: this.current + 2,
        uid: this.pageData.scene.u,
        type: 4
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_privatedomain_annual_report_page',
      ext: {
        mx_source: this.pageData.scene.s,
        serial_num: this.current + 2,
        uid: this.pageData.scene.u,
        type: 2
      }
    });
  },
  onShareAppMessage() {
    let path = '/packageAnnualReport/home';
    path += `?scene=${encodeURIComponent(
      'u=' + (this.globalUserInfo.uid || 0) + '&s=1002'
    )}`;

    return {
      path,
      title: '查看「新氧2024·我的年度智美报告」！领新年红包👇',
      imageUrl: 'https://static.soyoung.com/sy-pre/output-1734592200626.jpg'
    };
  }
};
</script>

<style lang="less" scoped>
@px: 2rpx;

.swiper {
  height: 100vh;
  width: 100vw;
  background-color: #fff;
  opacity: 0;
  transition: opacity 400ms;
}

.btn {
  position: absolute;
  bottom: 50px;
  right: 20px;
  background: #00a77d;
  padding: 10px;
}
.btn2 {
  position: absolute;
  bottom: 50px;
  left: 20px;
  background: #00a77d;
  padding: 10px;
}

.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;

  .title {
    position: fixed;
    left: 50%;
    height: 88rpx;
    font-family: PingFangSC-Medium;
    font-size: 36rpx;
    color: #333333;
    display: flex;
    align-items: center;
    transform: translateX(-50%);
  }

  .nav {
    position: fixed;
    display: flex;
    align-items: center;
    .back {
      width: 44 * @px;
      height: 44 * @px;
      display: block;
    }
  }
}
</style>
