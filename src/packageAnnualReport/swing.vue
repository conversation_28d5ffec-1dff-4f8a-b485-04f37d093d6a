<script>
export default {
  name: 'swing',
  props: {
    direction: {
      type: String,
      default: 'column'
    }
  }
};
</script>

<template>
  <view class="swing" :class="'swing-' + direction">
    <slot></slot>
  </view>
</template>

<style scoped lang="less">
.swing-column {
  animation-name: animation-swing-column;
}

.swing-row {
  animation-name: animation-swing-row;
}

.swing {
  animation-duration: 2000ms;
  animation-iteration-count: infinite;
  animation-direction: alternate-reverse;
  animation-timing-function: linear;
}

@keyframes animation-swing-column {
  0% {
    transform: translateY(20rpx);
  }

  100% {
    transform: translateY(-20rpx);
  }
}

@keyframes animation-swing-row {
  0% {
    transform: translateX(10rpx);
  }

  100% {
    transform: translateX(-10rpx);
  }
}
</style>
