<script>
export default {
  name: 'beautiful-items',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    topData() {
      return (
        this.data.total_amount ||
        this.data.order_count ||
        this.data.savings_amount
      );
    },

    bottomData() {
      return (
        this.data.preference_product_category &&
        this.data.first_order_date_month &&
        this.data.first_order_date_day
      );
    }
  }
};
</script>

<template>
  <view class="beautiful-items">
    <image
      class="beautiful-items-header-img"
      src="https://static.soyoung.com/sy-pre/10-gif_01-1735020600626.gif"
    ></image>

    <view
      v-if="topData || bottomData"
      style="display: flex; flex: 1; flex-grow: 0; flex-direction: column"
    >
      <view v-if="topData" class="beautiful-items-body-right">
        <view>
          <text>这一年</text>
        </view>
        <view v-if="data.total_amount">
          <text>你在新氧好物累计消费</text>
          <text class="bold">{{ data.total_amount }}</text>
          <text>元</text>
        </view>
        <view v-if="data.order_count">
          <text>下单</text>
          <text class="bold">{{ data.order_count }}</text>
          <text>个好物</text>
        </view>
        <view v-if="data.savings_amount">
          <text>省下</text>
          <text class="bold">{{ data.savings_amount }}</text>
          <text>元</text>
        </view>
      </view>

      <view v-if="bottomData" class="beautiful-items-body-left">
        <block v-if="data.preference_product_brand">
          <view>
            <text>你最爱买的品牌是</text>
            <text class="bold">{{ data.preference_product_brand }}</text>
          </view>
          <view>
            <text>堪称</text>
            <text class="bold">{{ data.preference_product_category }}</text>
            <text>王者</text>
          </view>
        </block>
        <view>
          <text>你在</text>
          <text class="bold">{{ data.first_order_date_month }}</text>
          <text>月</text>
          <text class="bold">{{ data.first_order_date_day }}</text>
          <text>日</text>
        </view>
        <view>
          <text>开启首单</text>
        </view>
        <view v-if="data.first_order_product_category">
          <text style="padding: 0" class="bold">{{
            data.first_order_product_category
          }}</text>
        </view>
        <view>
          <text>质价双优，你好会选</text>
        </view>
      </view>
    </view>

    <image
      v-else
      class="beautiful-items-body-bottom-up"
      src="https://static.soyoung.com/sy-pre/12313123-1736151000705.png"
    ></image>

    <view class="beautiful-items-footer">
      <image
        src="https://static.soyoung.com/sy-pre/2x1xvs1l4msnn-1735020600626.png"
      ></image>
      <image
        src="https://static.soyoung.com/sy-pre/2kiq3hlbeu0mh-1734934200637.gif"
      ></image>
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.beautiful-items {
  width: 100vw;
  height: 100vh;
  max-height: 100vh;
  min-height: 100vh;
  overflow: hidden;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .beautiful-items-header-img {
    width: 375 * @px;
    height: 301.33 * @px;
    min-height: 301.33 * @px;
  }

  .beautiful-items-body {
    view {
      display: flex;
      align-items: baseline;

      text {
        font-family: HYQiHei-EES;
        font-size: 15 * @px;
        color: #333333;
        letter-spacing: 0;
        line-height: 32 * @px;
        font-weight: 400;
      }

      .bold {
        font-family: HYQiHei-HZS;
        font-size: 22 * @px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 27 * @px;
        font-weight: 400;
        padding: 0 5 * @px;
      }
    }
  }

  .beautiful-items-body-right {
    .beautiful-items-body;
    width: 100%;
    box-sizing: border-box;
    padding-right: 30.5 * @px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding-top: 10 * @px;
  }

  .beautiful-items-body-left {
    .beautiful-items-body;
    padding-top: 28 * @px;
    width: 100%;
    box-sizing: border-box;
    padding-left: 34.5 * @px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .beautiful-items-body-bottom-up {
    width: 342 * @px;
    height: 213 * @px;
    min-height: 213 * @px;
    margin-top: 60 * @px;
    transform: translateX(-30 * @px);
  }

  .beautiful-items-footer {
    width: 100%;
    flex: 1;
    box-sizing: border-box;
    padding-left: 33 * @px;
    padding-right: 25 * @px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 140 * @px;
    max-height: 184 * @px;

    image:nth-child(1) {
      width: 158.42 * @px;
      height: 70 * @px;
    }

    image:nth-child(2) {
      width: 46.33 * @px;
      height: 50.33 * @px;
      transform: translateY(8 * @px);
    }
  }

  @media screen and (max-height: 736px) {
    .beautiful-items-header-img {
      margin-top: -120 * @px;
    }
  }
}
</style>
