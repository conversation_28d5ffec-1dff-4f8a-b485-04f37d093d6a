<template>
  <div class="bg">
    <div class="main">
      <image
        class="main-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/20241220-144831-1734675000672.png"
      />
      <img class="avatar" mode="aspectFill" :src="posterParams.avatar" />
      <div class="text">{{ data.smart_tag }}<br />{{ data.beauty_tag }}</div>
      <div class="text1">{{ data.preference_project }}</div>
      <div class="text2">{{ data.preference_project_order_count }}次</div>
      <div class="text3">{{ data.preference_project_total_amount }}元</div>
      <div class="words">
        <div
          v-for="item in data.other_tag_list"
          class="li"
          :class="item.is_rare ? 'active' : ''"
          :key="item.tag"
        >
          {{ item.tag }}
        </div>
      </div>
      <div>
        <div class="button">
          <button open-type="share" @click="report" class="button-img-left" />
          <img
            @click="go"
            mode="aspectFill"
            class="button-img-right"
            src="https://static.soyoung.com/sy-pre/yalrpvfh9sck-1734592200626.png"
          />
        </div>
        <div class="again">
          <img
            src="https://static.soyoung.com/sy-pre/2kmlfdzorqoqj-1734664200645.png"
            class="shareImage"
            @click="showShare"
            mode="aspectFill"
          />
          <img
            mode="aspectFill"
            class="again-img"
            src="https://static.soyoung.com/sy-pre/3kqs4dcdr1mej-1734592200626.png"
            @click="
              () => {
                $emit('review');
              }
            "
          />
        </div>
      </div>
    </div>
    <Poster :params="posterParams" @afterPosterCreated="afterPosterCreated" />
  </div>
</template>

<script>
import Poster from './poster1.vue';
import { getShareQrcode } from '@/api/activity';

export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    exp: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    Poster
  },
  data() {
    return {
      posterParams: {
        backgroundImage:
          'https://static.soyoung.com/sy-pre/20241224-102935-1735006200632.jpeg',
        qr: '',
        avatar: '',
        text1: this.data.smart_tag,
        text2: this.data.beauty_tag,
        text3: this.data.preference_project,
        text4: this.data.preference_project_order_count + '次',
        text5: this.data.preference_project_total_amount + '元',
        words: this.data.other_tag_list
      },
      imageUrl: ''
    };
  },
  mounted() {
    this.$registerExposure(
      '.button-img-left',
      () => {
        console.log('曝光');
        this.$reportData({
          info: 'sy_chain_store_privatedomain_annual_report:share_exposure',
          ext: {
            ...this.exp
          }
        });
      },
      this
    );
  },
  methods: {
    report() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:share_click',
        ext: {
          ...this.exp
        }
      });
    },
    go() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:coupon_click',
        ext: {
          ...this.exp
        }
      });
      console.log('go', this.data.coupon_jump_url);
      uni.navigateTo({
        url: this.data.coupon_jump_url
          ? '/' + this.data.coupon_jump_url
          : '/pages/h5?url=https://m.soyoung.com/tmwap26578&market_activity_id=6248'
      });
    },
    afterPosterCreated(params) {
      params.then((res) => {
        this.imageUrl = res.imageUrl;
      });
    },
    showShare() {
      if (this.imageUrl) {
        this.$reportData({
          info: 'sy_chain_store_privatedomain_annual_report:save_click',
          ext: {
            ...this.exp
          }
        });
        uni.getSetting({
          success: (res) => {
            var album = res.authSetting['scope.writePhotosAlbum'];
            if (album == false) {
              uni.showToast({
                title: '需要授权才能保存海报哦～',
                icon: 'none'
              });
              uni.openSetting({
                success: (res) => {
                  console.log('调用成功', res);
                },
                fail: (res) => {
                  console.log('调用失败', res);
                },
                complete: (res) => {
                  console.log('调用结束', res);
                }
              });
            } else {
              uni.showShareImageMenu({
                path: this.imageUrl,
                success: (res) => {
                  console.log('调用成功', res);
                },
                fail: (res) => {
                  console.log('调用失败', res);
                },
                complete: (res) => {
                  console.log('调用结束', res);
                }
              });
            }
          }
        });
      } else {
        uni.showToast({
          title: '海报生成中，请稍候...'
        });
      }
    }
  },
  async created() {
    if (uni.getStorageSync('AnnualReportUserInformation')) {
      this.posterParams.avatar = JSON.parse(
        uni.getStorageSync('AnnualReportUserInformation')
      ).avatar;
    }
    let res = await getShareQrcode();
    this.posterParams.qr = res.responseData.img;
  }
};
</script>

<style lang="less" scoped>
.bg {
  background-image: url('https://static.soyoung.com/sy-pre/20241219-150254-1734588600653.jpeg');
  background-size: cover;
  height: 100vh;
  position: relative;
  .main {
    position: absolute;
    right: 0;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    .main-img {
      margin-left: 50rpx;
      margin-right: 50rpx;
      width: 650rpx;
      display: block;
    }
    .button {
      display: flex;
      justify-content: space-between;
      margin-top: 50rpx;
      margin-left: 50rpx;
      margin-right: 50rpx;
      .button-img-left {
        width: 368rpx;
        height: 92rpx;
        margin-right: 34rpx;
        flex-shrink: 0;
        background: url('https://static.soyoung.com/sy-pre/yalsvyr7rye5-1734592200626.png')
          no-repeat;
        background-size: cover;
        outline: none;
        border: none;
        background-color: transparent;
        &::before,
        &::after {
          display: none;
        }
      }
      .button-img-right {
        width: 246rpx;
        height: 92rpx;
        flex-shrink: 0;
      }
    }
    .again {
      margin-top: 40rpx;
      display: flex;
      margin-left: 50rpx;
      margin-right: 50rpx;
      justify-content: space-between;
      .shareImage {
        width: 150rpx;
        height: 28rpx;
        flex-shrink: 0;
      }
      .again-img {
        width: 142rpx;
        height: 27rpx;
        flex-shrink: 0;
      }
    }
    .avatar {
      position: absolute;
      left: 90rpx;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: #d8d8d8;
      top: 120rpx;
    }
    .text {
      position: absolute;
      left: 86rpx;
      top: 280rpx;
      font-family: HYQiHei-HZS;
      font-size: 70rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 40px;
    }
    .text1,
    .text2,
    .text3 {
      font-family: HYQiHei-HZS;
      font-size: 40rpx;
      color: #333333;
      position: absolute;
      left: 90rpx;
      top: 550rpx;
      font-weight: 400;
    }
    .text2 {
      top: 670rpx;
    }
    .text3 {
      top: 780rpx;
    }
    .words {
      position: absolute;
      left: 86rpx;
      right: 86rpx;
      top: 860rpx;
      display: flex;
      justify-content: space-between;
      .li {
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: HYQiHei-HZS;
        font-size: 30rpx;
        color: #333333;
        width: 94 * 2rpx;
        height: 82 * 2rpx;
        background: url('https://static.soyoung.com/sy-pre/129pgn4c406vo-1734678600619.png')
          no-repeat;
        background-size: cover;
        text-align: center;
        padding: 0 30rpx;
        box-sizing: border-box;
        &.active {
          background: url('https://static.soyoung.com/sy-pre/1sj4t9ehyetqr-1734678600619.png')
            no-repeat;
          background-size: cover;
        }
      }
    }
  }
}
</style>
