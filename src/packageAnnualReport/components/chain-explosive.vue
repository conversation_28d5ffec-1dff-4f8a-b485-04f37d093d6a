<script>
export default {
  name: 'chain-explosive',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    source: {
      type: String,
      default: 'chain'
    }
  }
};
</script>

<template>
  <view class="chain-explosive">
    <image
      class="chain-explosive-title"
      src="https://static.soyoung.com/sy-pre/32q76aat6dbg7-1734941400623.png"
    ></image>

    <view class="chain-explosive-product-name">{{
      source === 'beautiful' ? '轻漾医用玻尿酸修复贴' : '超声线雕'
    }}</view>

    <image
      class="chain-explosive-background"
      src="https://static.soyoung.com/sy-pre/8-gif_02-1734941400623.gif"
    ></image>

    <view v-if="source === 'chain'" class="chain-explosive-output">
      <view>
        <text>上线</text>
        <text class="bold">9</text>
        <text>个月</text>
      </view>
      <view>
        <text>累计销量突破</text>
        <text class="bold">2.8w</text>
        <text>单</text>
      </view>
      <block v-if="data.order_count">
        <view>
          <text>其中，你买了</text>
          <text class="bold">{{ data.order_count }}</text>
          <text>单</text>
        </view>
        <view>
          <text>你很有眼光～</text>
        </view>
      </block>
      <view v-else> 听我的 </view>
      <view class="product"> 六维力线提升打法 </view>
      <view>
        <text>真的值得</text>
      </view>
    </view>

    <view v-else-if="source === 'beautiful'">
      <view v-if="data.order_count" class="chain-explosive-output">
        <view>
          <text>上线</text>
          <text class="bold">1</text>
          <text>个月</text>
        </view>
        <view>
          <text>累计销量突破</text>
          <text class="bold">8000</text>
          <text>单</text>
        </view>
        <view>
          <text>其中，你买了</text>
          <text class="bold">{{ data.order_count }}</text>
          <text>单</text>
        </view>
        <view style="padding-top: 32rpx; padding-bottom: 52rpx">
          <text style="font-size: 30rpx" class="bold"
            >质价比的刚需好物被你发现了</text
          >
        </view>
      </view>
      <image
        v-else
        class="chain-explosive-priming"
        src="https://static.soyoung.com/sy-pre/345345-1735024200634.png"
      ></image>
    </view>

    <view class="chain-explosive-footer">
      <image
        src="https://static.soyoung.com/sy-pre/srfumwmd5y6s-1734945000654.png"
      ></image>
      <image
        src="https://static.soyoung.com/sy-pre/2kiq3hlbeu0mh-1734934200637.gif"
      ></image>
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.chain-explosive {
  width: 100vw;
  height: 100vh;
  max-height: 100vh;
  min-height: 100vh;
  background-color: #fff;
  box-sizing: border-box;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  justify-content: flex-end;

  .chain-explosive-title {
    width: 285 * @px;
    height: 80 * @px;
    margin-left: 33 * @px;
  }

  .chain-explosive-product-name {
    padding-top: 24 * @px;
    font-family: HYQiHei-HZS;
    font-size: 32 * @px;
    color: #a9ea6a;
    letter-spacing: 0;
    line-height: 32 * @px;
    font-weight: 400;
    padding-left: 34.5 * @px;
    box-sizing: border-box;
  }

  .chain-explosive-background {
    width: 375 * @px;
    height: 220 * @px;
  }

  .chain-explosive-output {
    box-sizing: border-box;
    padding-left: 34.5 * @px;

    view {
      display: flex;
      align-items: baseline;
      line-height: 27 * @px;
    }

    text {
      font-family: HYQiHei-EES;
      font-size: 15 * @px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      padding: 0 3 * @px;
    }

    .bold {
      font-family: HYQiHei-HZS;
      font-size: 22 * @px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 800;
    }

    .product {
      font-family: HYQiHei-HZS;
      font-size: 22 * @px;
      color: #a9ea6a;
      letter-spacing: 0;
      line-height: 27 * @px;
      font-weight: 800;
      padding-top: 13 * @px;
      padding-bottom: 3 * @px;
    }
  }

  .chain-explosive-priming {
    width: 211 * @px;
    height: 131 * @px;
    margin-left: 34.5 * @px;
  }

  .chain-explosive-footer {
    height: 154 * @px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding-left: 35 * @px;
    padding-right: 25 * @px;
    margin-top: 10 * @px;

    image:nth-child(1) {
      width: 98.33 * @px;
      height: 29.33 * @px;
    }

    image:nth-child(2) {
      width: 46.33 * @px;
      height: 50.33 * @px;
    }

    &:before {
      content: '';
      width: 339 * @px;
      height: 6 * @px;
      background-color: #a9ea6a;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 10;
    }
  }

  @media screen and (max-height: 736px) {
    .chain-explosive-footer {
      height: 100 * @px;
    }
  }

  @media screen and (min-height: 737px) {
    .chain-explosive-footer {
      height: 154 * @px;
    }
  }
}
</style>
