<template>
  <div class="cost">
    <div class="cost-title-box ordered" v-if="consumeTotalAmount > 0">
      <div class="cost-title-text">
        <div class="cost-title-header">2024年</div>
        <div class="cost-title-header">你为自己投资：</div>
        <div class="cost-title-item" :style="{ marginTop: '56rpx' }">
          <span class="text">在新氧消费</span>
          <span class="num">{{ consumeTotalAmount }}</span>
          <span class="text">元</span>
        </div>
        <div class="cost-title-item" v-if="medicalProductCount">
          <span class="text">入手了</span>
          <span class="num">{{ medicalProductCount }}</span>
          <span class="text">个医美项目</span>
        </div>
        <div class="cost-title-item" v-if="cosmeticProductCount">
          <span class="text">pick了</span>
          <span class="num">{{ cosmeticProductCount }}</span>
          <span class="text">个好物</span>
        </div>
        <div class="cost-title-item" v-if="totalPlacement">
          <span class="text">在新氧消费排名</span>
          <span class="num">{{ totalPlacement }}</span>
        </div>
        <div
          class="cost-title-header"
          :style="{ marginTop: '88rpx' }"
          v-if="savingsAmount"
        >
          当然
        </div>
        <div class="cost-title-header" v-if="savingsAmount">
          2024年新氧也帮你省下了：
        </div>
        <div class="cost-title-amount" v-if="savingsAmount">
          <span class="num">{{ savingsAmount }}</span>
          <span class="text">元</span>
        </div>
      </div>
    </div>
    <div class="cost-title-box" v-else>
      <image
        class="cost-title"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/1yqhqk0h3968a-1734934200637.png"
      />
    </div>
    <image
      v-if="consumeTotalAmount > 0"
      class="cost-title-image"
      mode="widthFix"
      alt="下过单标题语"
      src="https://static.soyoung.com/sy-pre/3t5asjrrl5e69-1734934200637.png"
    />
    <image
      v-else
      class="cost-title-image"
      mode="widthFix"
      alt="未下单标题语"
      src="https://static.soyoung.com/sy-pre/26lb7oahrkvm9-1734934200637.png"
    />
    <image
      class="cost-info"
      mode="widthFix"
      alt="灰色字"
      src="https://static.soyoung.com/sy-pre/srfumwmd5y6s-1734945000654.png"
    />
    <image
      class="cost-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2kiq3hlbeu0mh-1734934200637.gif"
    />
    <image
      class="cost-bg"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/3-gif-1734934200637.gif"
    />
  </div>
</template>
<script>
export default {
  name: 'Cost',
  props: {
    consumeTotalAmount: {
      // 消费总金额
      type: Number,
      default: 0
    },
    medicalProductCount: {
      // 医美产品数量
      type: Number,
      default: 0
    },
    cosmeticProductCount: {
      // 好物商品数量
      type: Number,
      default: 0
    },
    totalPlacement: {
      // 总排名
      type: Number,
      default: 0
    },
    savingsAmount: {
      // 累计节省金额
      type: Number,
      default: 0
    }
  }
};
</script>
<style lang="less">
.cost {
  width: 100vw;
  height: 100vh;
  background: white;
  position: relative;
  .cost-title-box {
    width: 384rpx;
    position: absolute;
    z-index: 1;
    bottom: 520rpx;
    left: 68rpx;
    .cost-title {
      width: 100%;
    }
  }
  .cost-title-box.ordered {
    width: 572rpx;
    bottom: 520rpx;
    .cost-title {
      width: 100%;
    }
    .cost-title-header {
      font-family: HYQiHei-HZS;
      font-size: 44rpx;
      line-height: 54rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
    }
    .cost-title-item {
      display: flex;
      align-items: baseline;
      .num {
        font-family: HYQiHei-HZS;
        font-size: 44rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        margin: 0 10rpx;
      }
      .text {
        font-family: HYQiHei-EES;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 54rpx;
        font-weight: 400;
      }
    }
    .cost-title-text {
      width: 100%;
      height: 100%;
      .cost-title-date {
        display: flex;
        align-items: baseline;
        .num {
          font-size: 44rpx;
          color: #333333;
          line-height: 60rpx;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
        }
        .text {
          font-size: 30rpx;
          line-height: 46rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin-inline: 20rpx;
        }
      }
      .cost-title-main {
        font-size: 44rpx;
        line-height: 60rpx;
        margin-top: 4rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
      }
      .cost-title-placement {
        display: flex;
        align-items: baseline;
        flex-wrap: wrap;
        margin-top: 28rpx;
        .num {
          font-size: 80rpx;
          line-height: 108rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin: 0 20rpx;
        }
        .text {
          font-size: 30rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
        }
      }
      .cost-title-days {
        max-width: 552rpx;
        line-height: 54rpx;
        .num {
          font-size: 22px;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin: 0 20rpx;
        }
        .text {
          font-size: 15px;
          color: #333333;
          letter-spacing: 0;
          line-height: 27px;
          font-weight: 400;
        }
      }
    }
    .cost-title-amount {
      font-family: HYQiHei-HZS;
      display: flex;
      align-items: center;
      font-size: 80rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
  .cost-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }
  .cost-info {
    width: 200rpx;
    height: 76rpx;
    position: absolute;
    left: 70rpx;
    bottom: 110rpx;
    z-index: 1;
  }
  .cost-title-image {
    width: 252rpx;
    position: absolute;
    z-index: 1;
    right: 62rpx;
    bottom: 296rpx;
  }
  .cost-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
}
</style>
