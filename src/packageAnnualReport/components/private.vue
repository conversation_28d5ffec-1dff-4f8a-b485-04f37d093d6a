<template>
  <div class="private">
    <div class="private-title-box register" v-if="joinGroupCount > 0">
      <div class="private-title-main">在新氧社群</div>
      <div class="private-title-main">你为美商充电</div>
      <div
        class="private-info"
        :style="{ marginTop: '68rpx' }"
        v-if="newProdRounds"
      >
        <span class="text">围观了</span>
        <span class="num">{{ newProdRounds }}</span>
        <span class="text">场新品上新</span>
      </div>
      <div class="private-info" :style="{ marginTop: '48rpx' }">在新氧社群</div>
      <div class="private-info">
        <span class="text">读了约</span>
        <span class="num">{{ sciTextCount }}</span>
        <span class="text">字的变美科普</span>
      </div>
      <div class="private-info" :style="{ marginTop: '40rpx' }">
        你一定获得许多
      </div>
      <div class="private-info">美丽能量和变美启示</div>
    </div>
    <div class="private-title-box" v-else>
      <image
        class="private-title"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/1yqhqk0h3968a-1735017000634.png"
      />
    </div>
    <image
      class="private-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2y7lzjyfckjfz-1734592200626.gif"
    />
    <image
      class="private-bg"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/5-gif-1735009800646.gif"
    />
  </div>
</template>
<script>
export default {
  name: 'Private',
  props: {
    newProdRounds: {
      type: Number,
      default: 1
    },
    sciTextCount: {
      type: String,
      default: ''
    },
    joinGroupCount: {
      type: Number,
      default: 0
    }
  }
};
</script>
<style lang="less">
.private {
  width: 100vw;
  height: 100vh;
  background: white;
  position: relative;
  .private-title-box {
    width: 516rpx;
    position: absolute;
    z-index: 1;
    bottom: 794rpx;
    left: 68rpx;
    .private-title {
      width: 100%;
    }
  }
  .private-title-box.register {
    .private-title-main {
      font-family: HYQiHei-HZS;
      font-size: 64rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 80rpx;
      font-weight: 400;
    }

    .private-info {
      font-family: HYQiHei-EES;
      font-size: 30rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 54rpx;
      font-weight: 400;
      display: flex;
      align-items: baseline;
      justify-content: flex-start;
      .num {
        font-family: HYQiHei-HZS;
        font-size: 44rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: left;
        line-height: 54rpx;
        font-weight: 400;
        margin-inline: 20rpx;
      }
    }
  }
  .private-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }

  .private-title-image {
    width: 400rpx;
    position: absolute;
    z-index: 1;
    right: 130rpx;
    bottom: 380rpx;
  }
  .private-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
}
</style>
