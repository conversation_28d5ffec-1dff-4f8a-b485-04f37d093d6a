<template>
  <div class="chain-part-two">
    <div class="chain-part-two-title-box register">
      <div class="chain-part-two-title-text">
        <div class="chain-part-two-title-main">你为自己投资</div>
        <div class="chain-part-two-title-main">这是件很酷的事儿</div>
        <div class="chain-part-two-info" :style="{ marginTop: '70rpx' }">
          这一年
        </div>
        <div class="chain-part-two-info" v-if="orderProjectCount">
          <span class="text">下单</span>
          <span class="num">{{ orderProjectCount }}</span>
          <span class="text">个项目</span>
        </div>
        <div class="chain-part-two-info" v-if="orderProjectTotalAmount">
          <span class="text">累计消费金额</span>
          <span class="num">{{ orderProjectTotalAmount }}</span>
          <span class="text">元</span>
        </div>
        <div
          class="chain-part-two-item"
          :style="{ marginTop: '44rpx' }"
          v-if="orderProjectTopList.lengt"
        >
          <span class="text">你最喜欢的项目是：</span>
        </div>
        <div
          class="chain-part-two-item"
          v-for="(item, index) in orderProjectTopList"
          :key="index"
        >
          <span class="green">{{ item.project_name }}</span>
          <span class="text">共消费</span>
          <span class="num">{{ item.amount }}</span>
          <span class="text">元</span>
        </div>
        <div class="chain-part-two-info" v-if="savingsAmount > 0">
          <span class="text">新氧为你省下</span>
          <span class="big-num">{{ savingsAmount }}</span>
          <span class="text">元</span>
        </div>
      </div>
    </div>
    <image
      class="chain-part-two-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2y7lzjyfckjfz-1734592200626.gif"
    />
    <image
      class="chain-part-two-bg-header"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/7-gif_01-1735027800642.gif"
    />
    <image
      class="chain-part-two-image-info"
      mode="widthFix"
      alt="白色字"
      src="https://static.soyoung.com/sy-pre/srfumwmd5y6s-1735283400632.png"
    />
    <image
      class="chain-part-two-bg-footer"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/39535knda1uts-1735027800642.png"
    />
  </div>
</template>
<script>
export default {
  name: 'ChainPartTwo',
  props: {
    orderProjectCount: {
      type: Number,
      default: 0
    },
    orderProjectTotalAmount: {
      type: Number,
      default: 0
    },
    orderProjectTopList: {
      type: Array,
      default: () => []
    },
    savingsAmount: {
      type: Number,
      default: 0
    }
  }
};
</script>
<style lang="less">
.chain-part-two {
  width: 100vw;
  height: 100vh;
  background: white;
  position: relative;
  .chain-part-two-title-box.register {
    width: 616rpx;
    position: absolute;
    z-index: 1;
    bottom: 482rpx;
    width: 640rpx;
    left: 68rpx;
    z-index: 1;
    .chain-part-two-title {
      width: 100%;
    }
    .chain-part-two-title-text {
      width: 100%;
      height: 100%;
      .chain-part-two-title-main {
        font-size: 64rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 80rpx;
        text-align: right;
        font-weight: 400;
        font-family: HYQiHei-HZS;
      }

      .chain-part-two-info {
        font-family: HYQiHei-EES;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 54rpx;
        font-weight: 400;
        display: flex;
        align-items: baseline;
        justify-content: flex-start;
        .green {
        }
        .num {
          font-family: HYQiHei-HZS;
          font-size: 44rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
          margin-inline: 20rpx;
        }
        .big-num {
          font-family: HYQiHei-HZS;
          font-size: 80rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 80rpx;
          font-weight: 400;
          margin-inline: 20rpx;
        }
      }
      .chain-part-two-item {
        display: flex;
        align-items: baseline;
        .green {
          font-family: HYQiHei-HZS;
          font-size: 26rpx;
          color: #a9ea6a;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
        }
        .num {
          font-family: HYQiHei-HZS;
          font-size: 44rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          line-height: 54rpx;
          margin: 0 10rpx;
        }
        .text {
          ont-family: HYQiHei-HZS;
          font-size: 26rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
        }
      }
    }
  }
  .chain-part-two-bg-footer {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }
  .chain-part-two-bg-header {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }
  .chain-part-two-image-info {
    width: 200rpx;
    height: 76rpx;
    position: absolute;
    left: 70rpx;
    bottom: 110rpx;
    z-index: 1;
  }
  .chain-part-two-title-image {
    width: 400rpx;
    position: absolute;
    z-index: 1;
    right: 130rpx;
    bottom: 380rpx;
  }
  .chain-part-two-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
}
</style>
