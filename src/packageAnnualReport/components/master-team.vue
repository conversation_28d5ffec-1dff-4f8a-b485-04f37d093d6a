<script>
export default {
  name: 'master-team',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    first() {
      return (
        this.data?.first_verify_date_month &&
        this.data?.first_verify_date_day &&
        this.data?.first_verify_city &&
        this.data?.first_verify_doctor
      );
    },

    generalRecord() {
      return this.data?.total_amount && this.data?.preference_product;
    }
  }
};
</script>

<template>
  <view class="master-team">
    <view class="master-team-header">
      <view class="master-team-header-title"> 今年 </view>
      <view class="master-team-header-subtitle">
        <view>万支大师全国巡回<text>140</text>场次</view>
        <view>很高兴遇见你~</view>
      </view>
    </view>

    <image
      class="master-team-map"
      src="https://static.soyoung.com/sy-pre/20241224-103915-1735006200632.gif"
    ></image>

    <view v-if="first || generalRecord" class="master-team-story">
      <block v-if="first">
        <view>
          <text class="bold">{{ data.first_verify_date_month }}</text>
          <text>月</text>
          <text class="bold">{{ data.first_verify_date_day }}</text>
          <text>日在</text>
          <text class="bold">{{ data.first_verify_city }}</text>
        </view>
        <view>
          <text>第一次遇见</text>
          <text class="bold">{{ data.first_verify_doctor }}</text>
          <text>大师</text>
        </view>
        <view>
          <text>开启了你跟塔尖名医的故事</text>
        </view>
      </block>

      <block v-if="generalRecord">
        <view style="padding-top: 38rpx">
          <text>今年共花了</text>
          <text class="bold">{{ data.total_amount }}</text>
          <text>元</text>
        </view>
        <view>
          <text>你最钟爱的注射品是</text>
          <text class="bold">{{ data.preference_product }}</text>
        </view>
        <view>
          <text>姐妹真是个行家</text>
        </view>
      </block>
    </view>

    <image
      v-else
      class="master-team-bottom-up"
      src="https://static.soyoung.com/sy-pre/1212222-1735006200632.png"
    ></image>

    <view class="master-team-footer">
      <image
        src="https://static.soyoung.com/sy-pre/2lb01try8wvvi-1735009800646.png"
      ></image>
      <image
        src="https://static.soyoung.com/sy-pre/2kiq3hlbeu0mh-1734934200637.gif"
      ></image>
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.master-team {
  width: 100vw;
  height: 100vh;
  min-height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background-color: #fff;

  .master-team-header {
    box-sizing: border-box;
    padding-left: 31 * @px;
    padding-right: 42.5 * @px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .master-team-header-title {
      font-family: HYQiHei-HZS;
      font-size: 40 * @px;
      color: #333333;
      letter-spacing: 0;
      line-height: 40px;
      font-weight: 800;
    }

    .master-team-header-subtitle {
      view > text {
        font-family: HYQiHei-HZS;
        font-size: 22 * @px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 27 * @px;
        font-weight: 800;
        padding: 0 3 * @px;
      }

      view {
        font-family: HYQiHei-HZS;
        font-size: 15 * @px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20 * @px;
        font-weight: 400;
      }
    }
  }

  .master-team-story {
    margin-top: -103 * @px;
    padding-left: 34.5 * @px;

    view {
      display: flex;
      align-items: baseline;

      text {
        font-family: HYQiHei-EES;
        font-size: 15 * @px;
        color: #333333;
        letter-spacing: 0;
        line-height: 32 * @px;
        font-weight: 400;
      }

      .bold {
        font-family: HYQiHei-HZS;
        font-size: 22 * @px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        line-height: 27 * @px;
        font-weight: 800;
        padding: 0 3 * @px;
      }
    }
  }

  .master-team-map {
    width: 375 * @px;
    height: 372 * @px;
  }

  .master-team-bottom-up {
    width: 240 * @px;
    height: 158 * @px;
    margin-left: 34.5 * @px;
    margin-top: -66 * @px;
  }

  .master-team-footer {
    margin-top: 2 * @px;
    height: 182 * @px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 34 * @px;
    padding-right: 25 * @px;
    justify-content: space-between;

    image:nth-child(1) {
      width: 157.66 * @px;
      height: 70 * @px;
    }

    image:nth-child(2) {
      width: 46.33 * @px;
      height: 50.33 * @px;
      transform: translateY(8 * @px);
    }
  }

  @media screen and (max-height: 736px) {
    .master-team-footer {
      height: 118.18 * @px;
    }

    .master-team-bottom-up {
      margin-top: -110 * @px;
    }

    .master-team-story {
      margin-top: -115 * @px;
    }
  }
}
</style>
