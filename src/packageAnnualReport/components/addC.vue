<template>
  <div class="add-c">
    <div class="add-c-title-box register" v-if="joinGroupCount > 0">
      <div class="add-c-title-text">
        <div class="add-c-title-main">2024</div>
        <div class="add-c-title-main">是智美女性觉醒的一年</div>
        <div class="add-c-username">{{ userName }}</div>
        <div class="add-c-username-text">欢迎你</div>
        <div class="add-c-username-text">加入新氧社群</div>
        <div class="add-c-info" :style="{ marginTop: '66rpx' }">这一年</div>
        <div class="add-c-info">你为美商学习</div>
        <div class="add-c-info">————</div>
        <div class="add-c-info">
          <span class="text">加入了</span>
          <span class="num">{{ joinGroupCount }}</span>
          <span class="text">个新氧社群</span>
        </div>
        <div class="add-c-info" v-if="joinCCount">
          <span class="text">认识了</span>
          <span class="num">{{ joinCCount }}</span>
          <span class="text">个“行家”助理羊</span>
        </div>
        <div class="add-c-info" v-if="groupFriendCount > 0">
          <span class="text">遇到了</span>
          <span class="num">{{ groupFriendCount }}</span>
          <span class="text">个志同道合的新氧姐妹</span>
        </div>
        <div class="add-c-info" v-if="inviteFriendCount > 0">
          <span class="text">邀请了</span>
          <span class="num">{{ inviteFriendCount }}</span>
          <span class="text">位你的朋友们一起智美</span>
        </div>
      </div>
    </div>
    <div class="add-c-title-box" v-else>
      <div class="add-c-username absolute">{{ userName }}</div>
      <image
        class="add-c-title"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/2fwj77f7v26qi-1734934200637.png"
      />
    </div>
    <image
      class="add-c-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2kiq3hlbeu0mh-1734934200637.gif"
    />
    <image
      class="add-c-bg"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/4-gif-1734934200637.gif"
    />
  </div>
</template>
<script>
export default {
  name: 'AddC',
  props: {
    joinGroupCount: {
      type: Number,
      default: 0
    },
    joinCCount: {
      type: Number,
      default: 0
    },
    groupFriendCount: {
      type: Number,
      default: 0
    },
    inviteFriendCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      userName: ''
    };
  },
  created() {
    if (uni.getStorageSync('AnnualReportUserInformation')) {
      this.userName = JSON.parse(
        uni.getStorageSync('AnnualReportUserInformation')
      ).name;
    }
  }
};
</script>
<style lang="less">
.add-c {
  width: 100vw;
  height: 100vh;
  background: white;
  position: relative;
  .add-c-title-box {
    width: 628rpx;
    position: absolute;
    z-index: 1;
    bottom: 328rpx;
    left: 54rpx;
    .add-c-username {
      font-family: HYQiHei-HZS;
      font-size: 44rpx;
      line-height: 60rpx;
      color: #a9ea6a;
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
      width: 628rpx;
      text-overflow: ellipsis;
      word-break: break-all;
      overflow: hidden;
      white-space: nowrap;
    }
    .add-c-username.absolute {
      position: absolute;
      bottom: 60rpx;
      right: 0;
    }
    .add-c-title {
      width: 100%;
    }
  }
  .add-c-title-box.register {
    bottom: 328rpx;
    width: 640rpx;
    .add-c-title {
      width: 100%;
    }
    .add-c-title-text {
      width: 100%;
      height: 100%;
      .add-c-title-main {
        font-family: HYQiHei-HZS;
        font-size: 64rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 80rpx;
        font-weight: 400;
      }
      .add-c-username {
        font-family: HYQiHei-HZS;
        margin-top: 244rpx;
        text-align: right;
        width: 640rpx;
      }
      .add-c-username-text {
        font-family: HYQiHei-HZS;
        font-size: 44rpx;
        line-height: 54rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
      .add-c-info {
        font-family: HYQiHei-EES;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: right;
        line-height: 54rpx;
        font-weight: 400;
        display: flex;
        align-items: baseline;
        justify-content: flex-end;
        .num {
          font-family: HYQiHei-HZS;
          font-size: 44rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: right;
          line-height: 54rpx;
          font-weight: 400;
          margin-inline: 20rpx;
        }
      }
    }
  }
  .add-c-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }

  .add-c-title-image {
    width: 400rpx;
    position: absolute;
    z-index: 1;
    right: 130rpx;
    bottom: 380rpx;
  }
  .add-c-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
}
</style>
