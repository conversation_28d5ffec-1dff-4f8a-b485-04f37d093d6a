<template>
  <div class="bg">
    <img
      class="bottom"
      src="https://static.soyoung.com/sy-pre/12-gif_04-1734923400626.png"
    />
    <image
      class="overview-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2y7lzjyfckjfz-1734592200626.gif"
    />
    <div class="bottom-text">
      打开【新氧青春】<br />
      小程序首页查看报告
    </div>
    <img
      class="main-image"
      src="https://static.soyoung.com/sy-pre/39535knda1uts-1734923400626.png"
    />
    <img
      class="main-image-icon"
      src="https://static.soyoung.com/sy-pre/soyoung_-1734923400626.png"
    />
    <div class="text1">
      <span class="lable">{{ data.special_moment_date_year }}</span
      >年<span class="lable">{{ data.special_moment_date_month }}</span
      >月<span class="lable">{{ data.special_moment_date_day }}</span
      >日
    </div>
    <div class="text2">这一天对你很特殊</div>
    <div class="text3">
      消费<span class="lable">{{ data.consume_amount }}</span
      >元
    </div>
    <div class="text4">下单了</div>
    <div class="text5">{{ data.order_product }}</div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {};
  }
};
</script>

<style lang="less" scoped>
.bg {
  height: 100vh;
  position: relative;
  background: #ffffff;
  .bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 450rpx;
    width: 100%;
  }
  .bottom-text {
    position: absolute;
    left: 70rpx;
    bottom: 110rpx;
    font-family: HYQiHei-EES;
    font-size: 22rpx;
    color: #ffffff;
  }
  .overview-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
  .main-image {
    position: absolute;
    width: 348 * 2rpx;
    height: 355 * 2rpx;
    left: 40rpx;
    top: 50%;
    transform: translateY(-50%);
  }
  .main-image-icon {
    position: absolute;
    width: 88 * 2rpx;
    height: 13 * 2rpx;
    right: 33 * 2rpx;
    top: 45%;
    transform: translateY(-50%);
  }
  .text1,
  .text2 {
    position: absolute;
    left: 66rpx;
    top: 260rpx;
    font-family: HYQiHei-HZS;
    font-size: 30rpx;
    color: #333333;
    .lable {
      font-family: HYQiHei-HZS;
      font-size: 64rpx;
      color: #333333;
      padding: 0 4rpx;
    }
  }
  .text2 {
    font-size: 44rpx;
    top: 360rpx;
  }
  .text3,
  .text4,
  .text5 {
    position: absolute;
    left: 70rpx;
    top: 930rpx;
    font-family: HYQiHei-EES;
    font-size: 30rpx;
    color: #333333;
    .lable {
      font-family: HYQiHei-HZS;
      font-size: 44rpx;
      color: #333333;
      padding: 0 20rpx;
    }
  }
  .text4 {
    top: 990rpx;
  }
  .text5 {
    top: 1040rpx;
    width: 610rpx;
    font-family: HYQiHei-HZS;
    font-size: 30rpx;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* 限制显示的行数 */
    -webkit-box-orient: vertical;
  }
}
</style>
