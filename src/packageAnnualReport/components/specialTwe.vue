<template>
  <div class="bg">
    <div class="bottom"></div>
    <image
      class="overview-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2y7lzjyfckjfz-1734592200626.gif"
    />
    <div class="bottom-text">
      打开【新氧青春】<br />
      小程序首页查看报告
    </div>
    <div class="main">
      <div class="year">{{ data.login_earliest_date_year }}</div>
      <div class="times">
        <span class="lable">{{ data.login_earliest_date_month }}</span
        >月<span class="lable">{{ data.login_earliest_date_day }}</span
        >日<span class="lable"
          >{{ data.login_earliest_date_hour }} :
          {{ data.login_earliest_date_minute }}</span
        >
      </div>
      <div class="text">这是你点开新氧最早的一天</div>
    </div>
    <div class="main main_left">
      <div class="year">{{ data.login_latest_date_year }}</div>
      <div class="times">
        <span class="lable">{{ data.login_latest_date_month }}</span
        >月<span class="lable">{{ data.login_latest_date_day }}</span
        >日<span class="lable"
          >{{ data.login_latest_date_hour }} :
          {{ data.login_latest_date_minute }}</span
        >
      </div>
      <div class="text">这是你点开新氧最晚的一天</div>
    </div>
    <div class="text1">好故事早发生</div>
    <div class="text2">好产品值得等</div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {};
  }
};
</script>

<style lang="less" scoped>
.bg {
  height: 100vh;
  position: relative;
  background: url('https://static.soyoung.com/sy-pre/13-gif-1734934200637.gif')
    no-repeat center;
  background-size: 750rpx 1624rpx;
  .bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 280rpx;
    width: 100%;
    background-color: #a9ea6a;
  }
  .bottom-text {
    position: absolute;
    left: 70rpx;
    bottom: 110rpx;
    font-family: HYQiHei-EES;
    font-size: 22rpx;
    color: #ffffff;
  }
  .overview-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
  .main {
    position: absolute;
    left: 50%;
    top: 50%;
    padding: 20rpx;
    transform: translateY(-100%);
    margin-top: -40rpx;
    .year {
      font-family: HYQiHei-HZS;
      font-size: 64rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 40px;
    }
    .times {
      font-family: HYQiHei-EES;
      font-size: 30rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 27px;
      font-weight: 400;
      .lable {
        font-family: HYQiHei-HZS;
        font-size: 15px;
        color: #333333;
        letter-spacing: 0;
        line-height: 30px;
        padding: 0 5rpx;
      }
    }
    .text {
      font-family: HYQiHei-EES;
      font-size: 15px;
      color: #333333;
      letter-spacing: 0;
      line-height: 20px;
      font-weight: 400;
      margin-top: 20rpx;
      width: 210rpx;
    }
  }
  .main_left {
    left: 70rpx;
    transform: translateY(0);
  }
  .text1,
  .text2 {
    font-family: HYQiHei-HZS;
    font-size: 44rpx;
    color: #a9ea6a;
    letter-spacing: 0;
    line-height: 27px;
    font-weight: 400;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateY(-420rpx);
  }
  .text2 {
    position: absolute;
    left: 60rpx;
    top: 50%;
    transform: translateY(260rpx);
  }
}
</style>
