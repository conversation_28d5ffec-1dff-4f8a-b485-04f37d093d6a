<template>
  <div class="chain-part-one">
    <div v-if="consumeTotalAmount > 0">
      <div class="chain-part-one-title-box register top">
        <div class="chain-part-one-title-text">
          <div class="chain-part-one-title-main">这一年</div>
          <div class="chain-part-one-title-sub" :style="{ marginTop: '42rpx' }">
            你遇见新氧青春诊所
          </div>
          <div class="chain-part-one-title-sub">听过豌豆的故事</div>
        </div>
      </div>
      <div class="chain-part-one-title-box register bottom">
        <div class="chain-part-one-title-text">
          <div
            class="chain-part-one-info"
            :style="{ marginTop: '322rpx' }"
            v-if="medicalProjectCount"
          >
            <span class="text">新氧共上线了</span>
            <span class="num">{{ medicalProjectCount }}</span>
            <span class="text">个明星级青春轻医美项目</span>
          </div>
          <div class="chain-part-one-info" v-if="preferenceProject">
            你感兴趣的项目有：
          </div>
          <div class="chain-part-one-info green">{{ preferenceProject }}</div>
          <div
            class="chain-part-one-info"
            :style="{ marginTop: '44rpx' }"
            v-if="verifyCount"
          >
            医美快乐老家【新氧青春诊所】
          </div>
          <div class="chain-part-one-info" v-if="verifyCount">
            <span class="text">你一共去过</span>
            <span class="num">{{ verifyCount }}</span>
            <span class="text">次</span>
          </div>
          <div
            class="chain-part-one-info"
            :style="{ width: '690rpx' }"
            v-if="preferenceTenant"
          >
            <span class="text">最常去的门店是</span>
            <span class="bold">{{ preferenceTenant }}</span>
          </div>
          <div
            class="chain-part-one-info"
            :style="{ marginTop: '40rpx' }"
            v-if="preferenceDoctor"
          >
            <span class="text">你最熟悉的医生是</span>
            <span class="bold">{{ preferenceDoctor }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="chain-part-one-title-box" v-else>
      <image
        class="chain-part-one-title"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/1yqhqk0h3968a-1735020600626.png"
      />
    </div>
    <image
      class="chain-part-one-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2y7lzjyfckjfz-1734592200626.gif"
    />
    <image
      class="chain-part-one-image-info"
      mode="widthFix"
      alt="白色字"
      src="https://static.soyoung.com/sy-pre/srfumwmd5y6s-1735283400632.png"
    />
    <image
      class="chain-part-one-bg"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/6-gif-1735020600626.gif"
    />
  </div>
</template>
<script>
export default {
  name: 'ChainPartOne',
  props: {
    consumeTotalAmount: {
      // 消费总金额
      type: Number,
      default: 0
    },
    medicalProjectCount: {
      type: Number,
      default: 1
    },
    preferenceProject: {
      type: String,
      default: '相控微针、元气超光子、FOTONA 4D PRO'
    },
    verifyCount: {
      type: Number,
      default: 0
    },
    preferenceTenant: {
      type: String,
      default: ''
    },
    preferenceDoctor: {
      type: String,
      default: ''
    }
  }
};
</script>
<style lang="less">
.chain-part-one {
  width: 100vw;
  height: 100vh;
  background: white;
  position: relative;
  .chain-part-one-title-box {
    width: 480rpx;
    position: absolute;
    z-index: 1;
    bottom: 410rpx;
    left: 56rpx;
    .chain-part-one-title {
      width: 100%;
    }
  }
  .chain-part-one-title-box.register {
    bottom: 312rpx;
    width: 640rpx;
    .chain-part-one-title {
      width: 100%;
    }
    .chain-part-one-title-text {
      width: 100%;
      height: 100%;
      .chain-part-one-title-main {
        font-family: HYQiHei-HZS;
        font-size: 80rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 80rpx;
        font-weight: 400;
      }
      .chain-part-one-title-sub {
        font-family: HYQiHei-HZS;
        font-size: 44rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 54rpx;
        font-weight: 400;
      }

      .chain-part-one-info {
        font-family: HYQiHei-EES;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0;
        line-height: 54rpx;
        font-weight: 400;
        display: flex;
        align-items: baseline;
        justify-content: flex-start;
        flex-wrap: wrap;
        .num {
          font-family: HYQiHei-HZS;
          font-size: 44rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
          margin-inline: 15rpx;
        }
        .bold {
          font-family: HYQiHei-HZS;
          font-size: 30rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
          margin-inline: 10rpx;
        }
      }
      .chain-part-one-info.green {
        color: #a9ea6a;
        display: -webkit-box;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
      }
    }
  }
  .chain-part-one-title-box.register.top {
    bottom: 1100rpx;
  }
  .chain-part-one-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }

  .chain-part-one-image-info {
    width: 200rpx;
    height: 76rpx;
    position: absolute;
    left: 70rpx;
    bottom: 110rpx;
    z-index: 1;
  }
  .chain-part-one-title-image {
    width: 400rpx;
    position: absolute;
    z-index: 1;
    right: 130rpx;
    bottom: 380rpx;
  }
  .chain-part-one-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
}
</style>
