<template>
  <div class="bg">
    <div class="main">
      <image
        class="main-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/20241220-113203-1734664200645.png"
      />
      <img class="avatar" mode="aspectFill" :src="posterParams.avatar" />
      <div class="text">{{ data.smart_tag }}<br />变美人脉拥有者</div>
      <div>
        <div class="button">
          <button open-type="share" @click="report" class="button-img-left" />
          <img
            @click="go"
            mode="aspectFill"
            class="button-img-right"
            src="https://static.soyoung.com/sy-pre/yalrpvfh9sck-1734592200626.png"
          />
        </div>
        <div class="again">
          <img
            src="https://static.soyoung.com/sy-pre/2kmlfdzorqoqj-1734664200645.png"
            class="shareImage"
            @click="showShare"
            mode="aspectFill"
          />
          <img
            mode="aspectFill"
            @click="
              () => {
                $emit('review');
              }
            "
            class="again-img"
            src="https://static.soyoung.com/sy-pre/3kqs4dcdr1mej-1734592200626.png"
          />
        </div>
      </div>
    </div>
    <Poster :params="posterParams" @afterPosterCreated="afterPosterCreated" />
  </div>
</template>

<script>
import Poster from './poster2.vue';
import { getShareQrcode } from '@/api/activity';

export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    exp: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    Poster
  },
  data() {
    return {
      posterParams: {
        backgroundImage:
          'https://static.soyoung.com/sy-pre/20241224-102943-1735006200632.jpeg',
        qr: '',
        avatar: '',
        text1: this.data.smart_tag || '高质量的',
        text2: '变美人脉拥有者'
      },
      imageUrl: ''
    };
  },
  mounted() {
    this.$registerExposure(
      '.button-img-left',
      () => {
        this.$reportData({
          info: 'sy_chain_store_privatedomain_annual_report:share_exposure',
          ext: {
            ...this.exp
          }
        });
      },
      this
    );
  },
  methods: {
    report() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:share_click',
        ext: {
          ...this.exp
        }
      });
    },
    go() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:coupon_click',
        ext: {
          ...this.exp
        }
      });
      console.log('go', this.data.coupon_jump_url);
      uni.navigateTo({
        url: this.data.coupon_jump_url
          ? '/' + this.data.coupon_jump_url
          : '/pages/h5?url=https://m.soyoung.com/tmwap26578&market_activity_id=6248'
      });
    },
    afterPosterCreated(params) {
      params.then((res) => {
        this.imageUrl = res.imageUrl;
      });
    },
    showShare() {
      if (this.imageUrl) {
        this.$reportData({
          info: 'sy_chain_store_privatedomain_annual_report:save_click',
          ext: {
            ...this.exp
          }
        });
        uni.showShareImageMenu({
          path: this.imageUrl
        });
      } else {
        uni.showToast({
          title: '海报生成中，请稍候...'
        });
      }
    }
  },
  async created() {
    if (uni.getStorageSync('AnnualReportUserInformation')) {
      this.posterParams.avatar = JSON.parse(
        uni.getStorageSync('AnnualReportUserInformation')
      ).avatar;
    }
    let res = await getShareQrcode();
    this.posterParams.qr = res.responseData.img;
  }
};
</script>

<style lang="less" scoped>
.bg {
  background-image: url('https://static.soyoung.com/sy-pre/20241219-150254-1734588600653.jpeg');
  background-size: cover;
  height: 100vh;
  position: relative;
  .main {
    position: absolute;
    right: 0;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    .main-img {
      margin-left: 50rpx;
      margin-right: 50rpx;
      width: 650rpx;
      display: block;
    }
    .button {
      display: flex;
      justify-content: space-between;
      margin-top: 50rpx;
      margin-left: 50rpx;
      margin-right: 50rpx;
      .button-img-left {
        width: 368rpx;
        height: 92rpx;
        margin-right: 34rpx;
        flex-shrink: 0;
        background: url('https://static.soyoung.com/sy-pre/yalsvyr7rye5-1734592200626.png')
          no-repeat;
        background-size: cover;
        outline: none;
        border: none;
        background-color: transparent;
        &::before,
        &::after {
          display: none;
        }
      }
      .button-img-right {
        width: 246rpx;
        height: 92rpx;
        flex-shrink: 0;
      }
    }
    .again {
      margin-top: 40rpx;
      display: flex;
      margin-left: 50rpx;
      margin-right: 50rpx;
      justify-content: space-between;
      .shareImage {
        width: 148rpx;
        height: 27rpx;
        flex-shrink: 0;
      }
      .again-img {
        width: 142rpx;
        height: 27rpx;
        flex-shrink: 0;
      }
    }
    .avatar {
      position: absolute;
      left: 90rpx;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: #d8d8d8;
      top: 120rpx;
    }
    .text {
      position: absolute;
      left: 86rpx;
      top: 280rpx;
      font-family: HYQiHei-HZS;
      font-size: 70rpx;
      color: #333333;
      letter-spacing: 0;
      line-height: 40px;
    }
  }
}
</style>
