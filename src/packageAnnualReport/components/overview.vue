<template>
  <div class="overview">
    <div class="overview-title-box register" v-if="registerBeforeReport === 1">
      <div class="overview-title-text">
        <div class="overview-title-date">
          <span class="num">{{ registerDateYear }}</span>
          <span class="text">年</span>
          <span class="num">{{ registerDateMonth }}</span>
          <span class="text">月</span>
          <span class="num"> {{ registerDateDay }}</span>
          <span class="text">日</span>
        </div>
        <div class="overview-title-main">你遇见新氧</div>
        <div class="overview-title-placement" v-if="placement">
          <span class="text">成为第</span>
          <span class="num">{{ placement }}</span>
          <span class="text">位新氧姐妹</span>
        </div>
        <div
          class="overview-title-days"
          :style="{ marginTop: '60rpx' }"
          v-if="registerDays"
        >
          <span class="text">相识的</span>
          <span class="num">{{ registerDays }}</span>
          <span class="text">天里</span>
        </div>
        <div class="overview-title-days" v-if="registerDays">
          <span class="text">我们有了新的青春故事</span>
        </div>
      </div>
    </div>
    <div class="overview-title-box" v-else>
      <image
        class="overview-title"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/2fwj61bw4k0ox-1734592200626.png"
      />
    </div>

    <image
      v-if="registerBeforeReport === 1"
      class="overview-title-image"
      mode="widthFix"
      alt="已注册标题语"
      src="https://static.soyoung.com/sy-pre/26lb7oahrkvm9-1734592200626.png"
    />
    <image
      v-else
      class="overview-title-image"
      mode="widthFix"
      alt="未注册标题语"
      src="https://static.soyoung.com/sy-pre/1kbtj0hmmhmjk-1734592200626.png"
    />
    <image
      class="overview-info"
      mode="widthFix"
      alt="白色字"
      src="https://static.soyoung.com/sy-pre/srfumwmd5y6s-1735283400632.png"
    />
    <image
      class="overview-arrow"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2y7lzjyfckjfz-1734592200626.gif"
    />
    <image
      class="overview-bg"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/2-gif-1734592200626.gif"
    />
  </div>
</template>
<script>
export default {
  name: 'Overview',
  props: {
    registerBeforeReport: {
      type: Number,
      default: 1
    },
    registerDate: {
      type: String,
      default: ''
    },
    placement: {
      type: Number,
      default: 0
    },
    registerDays: {
      type: Number,
      default: 0
    },
    registerDateYear: {
      type: String,
      default: '2024'
    },
    registerDateMonth: {
      type: String,
      default: '12'
    },
    registerDateDay: {
      type: String,
      default: '23'
    }
  }
};
</script>
<style lang="less">
.overview {
  width: 100vw;
  height: 100vh;
  background: white;
  position: relative;
  .overview-title-box {
    width: 572rpx;
    position: absolute;
    z-index: 1;
    bottom: 866rpx;
    left: 68rpx;
    .overview-title {
      width: 100%;
    }
  }
  .overview-title-box.register {
    .overview-title {
      width: 100%;
    }
    .overview-title-text {
      width: 100%;
      height: 100%;
      .overview-title-date {
        display: flex;
        align-items: baseline;
        .num {
          font-family: HYQiHei-HZS;
          font-size: 44rpx;
          color: #333333;
          line-height: 60rpx;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
        }
        .text {
          font-family: HYQiHei-EES;
          font-size: 30rpx;
          line-height: 46rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin-inline: 20rpx;
        }
      }
      .overview-title-main {
        font-family: HYQiHei-HZS;
        font-size: 44rpx;
        line-height: 60rpx;
        margin-top: 4rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
      }
      .overview-title-placement {
        display: flex;
        align-items: baseline;
        flex-wrap: wrap;
        margin-top: 28rpx;
        .num {
          font-family: HYQiHei-HZS;
          font-size: 80rpx;
          line-height: 108rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin: 0 20rpx;
        }
        .text {
          font-family: HYQiHei-EES;
          font-size: 30rpx;
          color: #333333;
          letter-spacing: 0;
          line-height: 54rpx;
          font-weight: 400;
        }
      }
      .overview-title-days {
        max-width: 552rpx;
        line-height: 54rpx;
        .num {
          font-family: HYQiHei-HZS;
          font-size: 22px;
          color: #333333;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin: 0 20rpx;
        }
        .text {
          font-family: HYQiHei-EES;
          font-size: 15px;
          color: #333333;
          letter-spacing: 0;
          line-height: 27px;
          font-weight: 400;
        }
      }
    }
  }
  .overview-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 0;
  }
  .overview-info {
    width: 200rpx;
    height: 76rpx;
    position: absolute;
    left: 70rpx;
    bottom: 110rpx;
    z-index: 1;
  }
  .overview-title-image {
    width: 400rpx;
    position: absolute;
    z-index: 1;
    right: 130rpx;
    bottom: 380rpx;
  }
  .overview-arrow {
    width: 80rpx;
    height: 92rpx;
    position: absolute;
    bottom: 114rpx;
    right: 50rpx;
    z-index: 1;
  }
}
</style>
