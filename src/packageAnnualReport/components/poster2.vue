<template>
  <div class="canvas-wrap">
    <canvas
      type="2d"
      id="myCanvas"
      :style="{
        width: width * scale + 'px',
        height: height * scale + 'px'
      }"
    ></canvas>
  </div>
</template>
<script>
let ctx;
let canvas;
const dpr = 2;
const width = 750 * dpr;
const height = 1624 * dpr;
const fix = (n) => n * dpr;
const scale = 1 / dpr;
// 全局缓存加载的图片资源，不load两次
const catched = new Map();
export default {
  props: {
    params: {
      type: Object,
      default: () => ({
        backgroundImage: '',
        avatar: '',
        text1: '',
        qr: ''
      })
    }
  },
  data() {
    return {
      width,
      height,
      scale,
      queue: [],
      resourceUrls: []
    };
  },
  created() {
    const { backgroundImage, avatar, text1, qr, text2 } = this.params;
    if (backgroundImage && avatar && text1 && qr && text2) {
      this.createPoster(this.params);
    }
  },
  watch: {
    params(params) {
      const { backgroundImage, avatar, text1, qr, text2 } = params;
      if (backgroundImage && avatar && text1 && qr && text2) {
        this.createPoster(params);
      }
    }
  },
  methods: {
    createPoster({ backgroundImage, avatar, text1, text2, qr }) {
      const pro = new Promise((resolve) => {
        this.resetCanvas();
        this.drawBgImage(backgroundImage);
        this.loadCoverAvatar(avatar);
        this.loadCoverQr(qr);
        this.drawText(text1);
        this.drawText2(text2);
        this.draw(resolve);
      });
      this.$emit('afterPosterCreated', pro);
      return pro;
    },
    async draw(resolve) {
      this.queue.push(function scaleCanvas() {
        ctx.scale(this.scale, this.scale);
        uni.canvasToTempFilePath({
          canvas,
          x: 0,
          y: 0,
          width,
          height,
          fileType: 'png',
          quality: 1,
          destWidth: width,
          destHeight: height,
          success: (res) => {
            console.log('图片生成：', res.tempFilePath);
            resolve({
              imageUrl: res.tempFilePath
            });
          }
        });
      });
      // 所有的方法依赖ctx初始化
      await this.getCtxPromise();
      this.resourceUrls.forEach((url) => this.loadResource(url));
      console.log(
        '绘制操作队列：',
        this.queue.map((func) => func.name)
      );
      this.queue.reduce((prev, cur) => {
        return prev.then((...args) => {
          ctx.save();
          let isPromise = cur.apply(this, args);
          if (!isPromise) {
            isPromise = Promise.resolve(isPromise);
          }
          return isPromise.then((result) => {
            ctx.restore();
            return result;
          });
        });
      }, Promise.resolve());
    },
    resetCanvas() {
      this.queue = [];
      this.queue.unshift(function resetCanvas() {
        ctx.clearRect(0, 0, width, height);
      });
    },
    getCtxPromise() {
      return this.queryNode('#myCanvas').then((res) => {
        if (!res) {
          return Promise.reject('没有查询到canvas');
        }
        const [{ node }] = res;
        canvas = node;
        canvas.width = width;
        canvas.height = height;
        ctx = canvas.getContext('2d');
        ctx.strokeStyle = '#000';
        ctx.strokeRect(0, 0, width, height);
      });
    },
    drawText(text) {
      this.queue.push(function drawText() {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillStyle = '#333333';
        ctx.font = `bold ${fix(70)}px HYQiHei-HZS`;
        ctx.fillText(text, fix(43 * 2), fix(248 * 2));
      });
    },
    drawText2(text) {
      this.queue.push(function drawText() {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillStyle = '#333333';
        ctx.font = `bold ${fix(70)}px HYQiHei-HZS`;
        ctx.fillText(text, fix(43 * 2), fix(290 * 2));
      });
    },
    drawBgImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawBgImage() {
        const image = await this.loadResource(url);

        const radius = fix(24);

        ctx.beginPath();
        // 绘制圆角矩形
        ctx.moveTo(radius, 0);
        ctx.arcTo(width, 0, width, height, radius);
        ctx.arcTo(width, 0 + height, 0, height, radius);
        ctx.arcTo(0, height, 0, 0, radius);
        ctx.arcTo(0, 0, width, 0, radius);
        ctx.closePath();

        // 裁剪路径
        ctx.clip();

        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          0,
          0,
          width,
          height
        );
      });
    },
    loadCoverQr(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function loadCoverQr() {
        const image = await this.loadResource(url);
        const dx = fix(148 * 2);
        const dy = fix(670 * 2);
        const dWidth = fix(160);
        const dHeight = fix(160);
        const radius = Math.min(dWidth, dHeight) / 2;

        ctx.beginPath();
        // 绘制圆角矩形
        ctx.moveTo(dx + radius, dy);
        ctx.arcTo(dx + dWidth, dy, dx + dWidth, dy + dHeight, radius);
        ctx.arcTo(dx + dWidth, dy + dHeight, dx, dy + dHeight, radius);
        ctx.arcTo(dx, dy + dHeight, dx, dy, radius);
        ctx.arcTo(dx, dy, dx + dWidth, dy, radius);
        ctx.closePath();

        // 裁剪路径
        ctx.clip();

        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    loadCoverAvatar(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function loadCoverAvatar() {
        const image = await this.loadResource(url);
        const dx = fix(88);
        const dy = fix(340);
        const dWidth = fix(80);
        const dHeight = fix(80);
        const radius = Math.min(dWidth, dHeight) / 2;

        // 开始路径
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = fix(4);
        ctx.beginPath();
        // 绘制圆角矩形
        ctx.moveTo(dx + radius, dy);
        ctx.arcTo(dx + dWidth, dy, dx + dWidth, dy + dHeight, radius);
        ctx.arcTo(dx + dWidth, dy + dHeight, dx, dy + dHeight, radius);
        ctx.arcTo(dx, dy + dHeight, dx, dy, radius);
        ctx.arcTo(dx, dy, dx + dWidth, dy, radius);
        ctx.closePath();
        ctx.stroke();

        // 裁剪路径
        ctx.clip();
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    loadResource(url) {
      const fetch = (url, tryTimes = 5) =>
        new Promise((resolve, reject) => {
          const image = canvas.createImage();
          image.onload = () => resolve(image);
          image.onerror = (error) => {
            console.log(`load image error :try:${tryTimes} !!!`);
            if (tryTimes-- > 0) {
              fetch(
                url.replace(/\?t=\d+/g, '') + '?t=' + new Date().getTime(),
                tryTimes
              );
            } else {
              reject(error);
            }
          };
          image.src = url;
        });
      // return promise<image> or undefined
      if (catched.has(url)) {
        const target = catched.get(url);
        target.catch(() => catched.delete(url));
      } else {
        catched.set(url, fetch(url));
      }
      return catched.get(url);
    },
    queryNode(selector, times = 10) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(selector)
          .fields({ node: true })
          .exec((res) => {
            if (res?.length > 0) {
              resolve(res);
            } else {
              if (times--) {
                setTimeout(() => {
                  resolve(this.queryNode(selector, times));
                }, 50);
              } else {
                uni.$log('[poster]没有查询到节点！', this.params.path, 'error');
                resolve([]);
              }
            }
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.canvas-wrap {
  position: absolute;
  transform: translate3d(-2000px, -2000px, 1px); // 单独来个图层
}
</style>
