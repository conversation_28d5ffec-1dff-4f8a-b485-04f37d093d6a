<template>
  <div class="bg">
    <div class="main">
      <image
        class="main-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/20241219-150748-1734588600653.png"
      />
      <img class="avatar" mode="aspectFill" :src="avatar" />
      <img
        class="qr"
        mode="aspectFill"
        :show-menu-by-longpress="true"
        :src="
          data.add_c_img ||
          'https://static.soyoung.com/sy-pre/output-1734592200626.png'
        "
      />
      <div>
        <div class="button">
          <button open-type="share" @click="report" class="button-img-left" />
          <img
            @click="go"
            mode="aspectFill"
            class="button-img-right"
            src="https://static.soyoung.com/sy-pre/yalrpvfh9sck-1734592200626.png"
          />
        </div>
        <div class="again">
          <img
            mode="aspectFill"
            @click="
              () => {
                $emit('review');
              }
            "
            class="again-img"
            src="https://static.soyoung.com/sy-pre/3kqs4dcdr1mej-1734592200626.png"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    exp: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      avatar: ''
    };
  },
  mounted() {
    this.$registerExposure(
      '.button-img-left',
      () => {
        this.$reportData({
          info: 'sy_chain_store_privatedomain_annual_report:share_exposure',
          ext: {
            ...this.exp
          }
        });
      },
      this
    );
  },
  methods: {
    report() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:share_click',
        ext: {
          ...this.exp
        }
      });
    },
    go() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:coupon_click',
        ext: {
          ...this.exp
        }
      });
      console.log('go', this.data.coupon_jump_url);
      uni.navigateTo({
        url: this.data.coupon_jump_url
          ? '/' + this.data.coupon_jump_url
          : '/pages/h5?url=https://m.soyoung.com/tmwap26578&market_activity_id=6248'
      });
    }
  },
  created() {
    if (uni.getStorageSync('AnnualReportUserInformation')) {
      this.avatar = JSON.parse(
        uni.getStorageSync('AnnualReportUserInformation')
      ).avatar;
    }
  }
};
</script>

<style lang="less" scoped>
.bg {
  background-image: url('https://static.soyoung.com/sy-pre/20241219-150254-1734588600653.jpeg');
  background-size: cover;
  height: 100vh;
  position: relative;
  .main {
    position: absolute;
    right: 0;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    .main-img {
      margin-left: 50rpx;
      margin-right: 50rpx;
      width: 650rpx;
      display: block;
    }
    .button {
      display: flex;
      justify-content: space-between;
      margin-top: 50rpx;
      margin-left: 50rpx;
      margin-right: 50rpx;
      .button-img-left {
        width: 368rpx;
        height: 92rpx;
        margin-right: 34rpx;
        flex-shrink: 0;
        background: url('https://static.soyoung.com/sy-pre/yalsvyr7rye5-1734592200626.png')
          no-repeat;
        background-size: cover;
        outline: none;
        border: none;
        background-color: transparent;
        &::before,
        &::after {
          display: none;
        }
      }
      .button-img-right {
        width: 246rpx;
        height: 92rpx;
        flex-shrink: 0;
      }
    }
    .again {
      margin-top: 40rpx;
      display: flex;
      justify-content: center;
      .again-img {
        width: 142rpx;
        height: 27rpx;
        flex-shrink: 0;
      }
    }
    .avatar {
      position: absolute;
      left: 90rpx;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: #d8d8d8;
      top: 120rpx;
    }
    .qr {
      width: 254rpx;
      height: 254rpx;
      position: absolute;
      background: #d8d8d8;
      top: 542rpx;
      left: 249rpx;
    }
  }
}
</style>
