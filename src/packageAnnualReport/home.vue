<script>
import qs from 'qs';
import { mapGetters, mapState } from 'vuex';
import { getUserInfoApi } from '@/api/my';
import { getReportData } from '@/api/activity';
import Swing from '@/packageAnnualReport/swing.vue';

export default {
  name: 'home',
  components: { Swing },
  data() {
    return {
      userInfo: {
        avatar: '',
        name: ''
      },
      agreementConfirm: false,
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      scene: {},
      options: {}
    };
  },
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState({
      globalUserInfo: (state) => state.global.userInfo
    }),

    /**
     * @typedef {Object} IScene
     * @property {string||number} u - 分享者uid
     * @property {string||number} s - 私域引流入口来源
     * */

    /**
     * @typedef {Object} IPageData
     * @property {string||number} [test_uid] - 调试参数，后续可去除
     * @property {IScene} [scene] - 用户扫码进入入参
     * */

    /**
     * @returns {IPageData}
     * */
    pageData() {
      const data = {
        scene: { u: 0, s: 0 }
      };

      if (this.options?.test_uid) {
        data.test_uid = this.options.test_uid;
      }

      if (this.options?.scene) {
        try {
          const scene = qs.parse(decodeURIComponent(this.options.scene));
          data.scene.u = scene.u || 0;
          data.scene.s = scene.s || 0;
        } catch (error) {
          data.scene = { u: 0, s: 0 };
        }
      }

      return data;
    }
  },
  methods: {
    /**
     * 未登陆自动跳转登陆页
     * @returns {Promise<Boolean>}
     * */
    async login() {
      return await new Promise((resolve) => {
        (async () => {
          if (!this.isLogin) {
            const isAuth = await this.$login().catch(() => {
              return null;
            });
            if (!isAuth) {
              resolve(false);
              return;
            }
            resolve(true);
            return;
          }
          resolve(true);
        })();
      });
    },

    /**
     * 查看用户报告
     * */
    async checkDetails() {
      this.$reportData({
        info: 'sy_chain_store_privatedomain_annual_report:start_click',
        ext: {
          mx_source: this.pageData.scene.s,
          btn_type: !this.agreementConfirm ? 2 : 1
        }
      });

      if (!(await this.login())) return;

      if (!this.agreementConfirm) {
        uni.showToast({
          title: '请授权协议',
          icon: 'none'
        });
        return;
      }

      this.cacheUserInfo(this.userInfo);

      let url = `/packageAnnualReport/index?scene=${JSON.stringify(
        this.pageData.scene
      )}`;

      if (this.pageData.test_uid) {
        url += `&test_uid=${this.pageData.test_uid}`;
      }

      const requestBody = {
        agreement_status: this.agreementConfirm ? 1 : 0
      };
      if (this.pageData.test_uid) {
        requestBody.test_uid = this.pageData.test_uid;
      }

      const reportData = await getReportData(requestBody);
      console.log('【getReportData data】', reportData);

      if (!reportData?.responseData) {
        uni.showToast({
          title: '服务异常',
          icon: 'none'
        });
        return;
      }

      uni.navigateTo({
        url,
        success: (res) => {
          res.eventChannel.emit('REPORT_DATA', reportData);
        }
      });
    },

    /**
     * 返回用户头像名称
     * 1. 优先获取用户查看年报缓存后的头像
     * 2. 获取全局缓存中用户主页头像
     * 3. 调用用户主页接口查询站内头像
     * 4. 重新跳转登陆
     * @returns {Promise<{ avatar: String, name: String }>}
     * */
    async getUserInfo() {
      return new Promise((resolve) => {
        (async () => {
          try {
            let userAnnualReportStorageData = uni.getStorageSync(
              'AnnualReportUserInformation'
            );
            if (userAnnualReportStorageData) {
              userAnnualReportStorageData = JSON.parse(
                userAnnualReportStorageData
              );
              if (
                userAnnualReportStorageData?.name &&
                userAnnualReportStorageData?.avatar
              ) {
                resolve(userAnnualReportStorageData);
                return;
              }
            }

            const userStorageData = uni.getStorageSync('user_info');
            if (userStorageData?.user_name && userStorageData?.avatar) {
              resolve({
                name: userStorageData?.user_name || '',
                avatar: userStorageData?.avatar || ''
              });
              return;
            }

            const responseData = await getUserInfoApi({});
            if (responseData) {
              resolve({
                name: responseData?.user_name || '',
                avatar: responseData?.avatar || ''
              });
              return;
            }

            throw new Error('跳转登陆');
          } catch (error) {
            console.error(error);
            // uni.navigateTo({
            //   url: '/packageAccount/login?type=3',
            //   complete: () => {
            //     resolve({ avatar: '', name: '' });
            //   }
            // });
          }
        })();
      });
    },

    /**
     * 缓存用户头像名称
     * @param {Object} param
     * @param {string} [param.name] - 用户名称
     * @param {string} [param.avatar] - 用户头像
     * */
    cacheUserInfo(param = {}) {
      let annualReportUserInformation = uni.getStorageSync(
        'AnnualReportUserInformation'
      );
      console.log('【缓存中的用户头像名称数据】', annualReportUserInformation);
      try {
        annualReportUserInformation = JSON.parse(annualReportUserInformation);
      } catch (error) {
        console.error('【缓存用户头像名称异常】', error);
        annualReportUserInformation = {};
      }

      Object.keys(param).forEach((item) => {
        annualReportUserInformation[item] = param[item];
      });

      uni.setStorageSync(
        'AnnualReportUserInformation',
        JSON.stringify(annualReportUserInformation)
      );
    },

    /**
     * 查看协议内容
     * */
    viewAgreement() {
      uni.navigateTo({
        url:
          '/pages/h5?url=' +
          encodeURIComponent('https://m.soyoung.com/tmwap26563')
      });
    },

    /**
     * 获取用户微信头像 -> 上传 -> 缓存
     * 从基础库2.24.4版本起，若用户上传的图片未通过安全监测，不触发chooseavatar事件。
     * */
    async setAvatar(param) {
      const res = await this.$uploadImg({
        url: '/xinyang/posts/addPic',
        filePath: param.detail.avatarUrl,
        name: 'imgFile'
      });

      let avatar =
        'https://static.soyoung.com/sy-pre/20241220-174622-1734685800631.png';

      if (res.errorCode !== 0 || !res.responseData?.u) {
        uni.showToast({
          title: '获取失败',
          icon: 'none'
        });
      } else {
        avatar = res.responseData.u;
      }

      this.cacheUserInfo({ avatar });
      this.userInfo.avatar = avatar;
    },

    /**
     * 获取用户微信昵称 -> 缓存
     * */
    setName() {
      this.cacheUserInfo({ name: this.userInfo.name });
    },

    handleBack() {
      if (getCurrentPages().length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack({
          delta: -1
        });
      }
    }
  },

  onLoad(options) {
    console.log('home onload options', options);
    this.options = options;
    this.menuRect = uni.getMenuButtonBoundingClientRect();
  },

  async onShow() {
    this.$reportPageShow({
      info: 'sy_chain_store_privatedomain_annual_report_page',
      ext: {
        mx_source: this.pageData.scene.s,
        serial_num: 1,
        uid: this.pageData.scene.u,
        type: 3
      }
    });

    if (!this.repeatedly) {
      this.repeatedly = true;
      await this.login();
    }

    const { avatar, name } = await this.getUserInfo();
    this.userInfo.avatar = avatar;
    this.userInfo.name = name;
  },

  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_privatedomain_annual_report_page',
      ext: {
        mx_source: this.pageData.scene.s,
        serial_num: 1,
        uid: this.pageData.scene.u,
        type: 4
      }
    });
  },

  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_privatedomain_annual_report_page',
      ext: {
        mx_source: this.pageData.scene.s,
        serial_num: 1,
        uid: this.pageData.scene.u,
        type: 2
      }
    });
  },

  onShareAppMessage() {
    let path = '/packageAnnualReport/home';
    path += `?scene=${encodeURIComponent(
      'u=' + (this.globalUserInfo.uid || 0) + '&s=1002'
    )}`;

    return {
      path,
      title: '查看「新氧2024·我的年度智美报告」！领新年红包👇',
      imageUrl: 'https://static.soyoung.com/sy-pre/output-1734592200626.jpg'
    };
  }
};
</script>

<template>
  <view class="annual-report-home">
    <div
      v-if="menuRect.top"
      class="pageTop"
      :style="{ height: menuRect.bottom + 8 + 'px' }"
    >
      <div
        class="nav"
        :style="{
          top: menuRect.top + 'px',
          width: menuRect.width + 'px',
          height: menuRect.height + 'px'
        }"
      >
        <div class="backBox" @click="handleBack">
          <image
            class="back"
            src="https://static.soyoung.com/sy-design/3cj8rc3ipek931726026826755.png"
          />
        </div>
      </div>
    </div>

    <view class="annual-report-home-background">
      <image
        class="annual-report-home-logo"
        src="https://static.soyoung.com/sy-pre/soyoung-1735031400632.png"
      ></image>

      <Swing>
        <image
          class="annual-report-home-background-character"
          src="https://static.soyoung.com/sy-pre/2su9h4r8d53a-1734599400633.png"
        ></image>
      </Swing>

      <image
        class="annual-report-home-img"
        src="https://static.soyoung.com/sy-pre/1212112122222-1735031400632.png"
      ></image>
    </view>

    <view
      style="
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 340rpx;
        max-height: 600rpx;
      "
    >
      <view class="annual-report-home-user-info">
        <button
          class="annual-report-home-user-avatar"
          open-type="chooseAvatar"
          @chooseavatar="setAvatar"
        >
          <image
            class="avatar"
            mode="aspectFill"
            :src="
              userInfo.avatar ||
              'https://static.soyoung.com/sy-pre/20241220-174622-1734685800631.png'
            "
          ></image>
          <image
            class="edit"
            src="https://static.soyoung.com/sy-pre/20241220-143955-1734675000672.png"
          ></image>
        </button>
        <view
          class="annual-report-home-user-name"
          :style="{ borderBottom: '2rpx solid #333333' }"
        >
          <input
            v-model="userInfo.name"
            type="nickname"
            placeholder="请输入昵称"
            @blur="setName"
          />
          <view>{{ userInfo.name }}</view>
        </view>
      </view>

      <Swing direction="row">
        <view class="annual-report-home-text" @click="checkDetails">
          <text>打开我的专属报告</text>
          <image
            src="https://static.soyoung.com/sy-pre/12121212121212-1734592200626.png"
          ></image>
        </view>
      </Swing>

      <view
        class="annual-report-home-agreement"
        @click="agreementConfirm = !agreementConfirm"
      >
        <image
          :src="
            agreementConfirm
              ? 'https://static.soyoung.com/sy-pre/20241219-164241-1734595800635.png'
              : 'https://static.soyoung.com/sy-pre/4434343434-1734592200626.png'
          "
        ></image>
        <text>
          授权新氧查询并使用个人数据生成报告<text
            style="padding: 0 5rpx"
            @click.stop="viewAgreement"
            >《授权协议》</text
          >
        </text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.annual-report-home {
  min-height: 100vh;
  max-height: 100vh;
  width: 100vw;
  overflow: hidden;
  padding-bottom: 55 * @px;
  box-sizing: border-box;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .annual-report-home-background {
    width: 354 * @px;
    //height: 359 * @px;
    margin: 113 * @px auto 0;
    position: relative;
    //background-image: url('https://static.soyoung.com/sy-pre/32q76aat6dbg7-1734599400633.png');
    //background-size: 312 * @px 456 * @px;
    //background-repeat: no-repeat;
    //background-position: 16 * @px 7 * @px;

    .annual-report-home-logo {
      width: 101 * @px;
      height: 36.33 * @px;
      position: absolute;
      left: 20 * @px;
      top: 7 * @px;
    }

    .annual-report-home-img {
      width: 312 * @px;
      height: 155 * @px;
      margin-top: -60 * @px;
      margin-left: 16 * @px;
    }

    .annual-report-home-background-character {
      width: 352.37 * @px;
      height: 359 * @px;
    }
  }

  .annual-report-home-user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15 * @px;

    .annual-report-home-user-avatar {
      width: 60 * @px;
      height: 60 * @px;
      position: relative;
      background-color: transparent;
      margin: 0;
      padding: 0;
      border: none;

      &:after {
        display: none;
      }

      .avatar {
        border-radius: 50%;
        width: 100%;
        height: 100%;
      }

      .edit {
        width: 15px;
        height: 15px;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }

    .annual-report-home-user-name {
      margin-top: 4 * @px;
      width: max-content;
      position: relative;

      view {
        opacity: 0;
        height: 20 * @px;
        min-width: 70 * @px;
      }

      input {
        font-family: HYQiHei-EES;
        font-size: 10 * @px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        width: 100%;
        z-index: 10;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
      }
    }
  }

  .annual-report-home-text {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5 * @px;
    margin-bottom: 17 * @px;

    text {
      font-family: HYQiHei-EES;
      font-size: 21 * @px;
      line-height: 21 * @px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }

    image {
      width: 20 * @px;
      height: 20 * @px;
      margin-left: 6 * @px;
    }
  }

  .annual-report-home-agreement {
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      margin-right: 3 * @px;
      width: 12 * @px;
      height: 12 * @px;
    }

    text {
      font-family: HYQiHei-DZJ;
      font-size: 10 * @px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
    }
  }

  .pageTop {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 9999;

    .title {
      position: fixed;
      left: 50%;
      height: 88rpx;
      font-family: PingFangSC-Medium;
      font-size: 36rpx;
      color: #333333;
      display: flex;
      align-items: center;
      transform: translateX(-50%);
    }

    .nav {
      position: fixed;
      display: flex;
      align-items: center;
      .back {
        width: 44 * @px;
        height: 44 * @px;
        display: block;
      }
    }
  }
}

@media screen and (max-height: 736px) {
  .annual-report-home {
    padding-bottom: 15 * @px !important;
  }

  .annual-report-home-img {
    margin-top: -150 * @px !important;
  }

  .annual-report-home-background-character {
    transform: scale(0.83) translateX(40rpx) translateY(-80rpx);
  }
}
</style>
