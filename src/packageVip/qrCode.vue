<template>
  <div class="qrcode-page">
    <image
      src="https://static.soyoung.com/sy-design/2hca4x4bkq4bk1747035832959.jpg"
      mode="widthFix"
      class="background-image"
    ></image>
    <image
      class="qrcode-back"
      alt=""
      src="https://static.soyoung.com/sy-design/iphone 13 pro1747035825434.png"
      mode="widthFix"
      @click="handleBack"
    />

    <div class="qrcode-container">
      <div class="qrcode-header">
        <image
          :src="
            levelImage ||
            'https://static.soyoung.com/sy-design/3kih5yufewpn41747035826614.jpg'
          "
          mode="widthFix"
          class="level-image"
        ></image>
        <span class="qrcode-name">{{ curUserName || '' }}</span>
      </div>
      <div class="qrcode-content">
        <div class="qrcode-content-text-info">{{ headerText }}</div>
        <div class="qrcode-wrapper">
          <tki-qrcode
            ref="qrcode"
            :val="qrValue"
            :size="386"
            background="#ffffff"
            foreground="#000000"
            pdground="#000000"
            :showLoading="false"
          ></tki-qrcode>
        </div>
        <div class="qrcode-content-text-bottom">{{ footerText }}</div>
      </div>
    </div>
    <image
      src="https://static.soyoung.com/sy-design/2hca5b5fhk6c31747035825877.png"
      mode="widthFix"
      class="logo-image"
    ></image>
  </div>
</template>

<script>
import tkiQrcode from 'tki-qrcode';
import { getQrCodeInfo } from '@/api/vip';

export default {
  name: 'QrCodePage',
  components: {
    tkiQrcode
  },
  data() {
    return {
      curLevel: 1,
      curUserName: '',
      qrValue: '',
      remainingTime: 0,
      timer: null,
      // 二维码样式配置
      background: '', // 背景色
      foreground: '', // 前景色
      pdground: '#000000', // 定位角颜色
      headerText: '',
      footerText: '',
      levelImage: ''
    };
  },
  onLoad() {
    this.getQrCodeData();
  },
  methods: {
    // 获取二维码数据
    async getQrCodeData() {
      const res = await getQrCodeInfo();
      if (res) {
        this.qrValue = res.code;
        this.levelImage = res.level_background_image;
        this.remainingTime = res.expired_seconds;
        this.curLevel = res.level;
        this.curUserName = res.user_name;
        this.headerText = res.header_text;
        this.footerText = res.footer_text;
        this.startTimer();
        this.$nextTick(() => {
          if (this.$refs.qrcode) {
            this.$refs.qrcode._makeCode();
          }
        });
      }
    },
    // 开始倒计时
    startTimer() {
      this.clearTimer();
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--;
        } else {
          this.clearTimer();
          // 倒计时结束后自动重新获取二维码
          this.getQrCodeData();
        }
      }, 1000);
    },
    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 返回上一页
    handleBack() {
      uni.navigateBack({
        delta: 1
      });
    }
  },
  onUnload() {
    this.clearTimer();
  },
  onHide() {
    this.clearTimer();
  }
};
</script>

<style lang="less" scoped>
.qrcode-page {
  min-height: 100vh;
  background-color: #e0e2e8;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;
  position: relative;
  .qrcode-back {
    width: 40rpx;
    height: 38rpx;
    position: absolute;
    top: 110rpx;
    left: 40rpx;
  }
  .background-image {
    width: 100%;
    height: 414rpx;
  }
  .qrcode-container {
    width: 634rpx;
    height: 814rpx;
    background: #ffffff;
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    .qrcode-header {
      width: 100%;
      height: 146rpx;
      position: relative;
      .level-image {
        width: 100%;
        height: 100%;
      }
      .qrcode-name {
        position: absolute;
        max-width: 400rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        word-break: break-all;
        bottom: 26rpx;
        right: 32rpx;
        font-family: DfKing-Regular;
        font-family: DFKingGothicSC16-Regular;
        font-size: 30rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
    }
  }
  .logo-image {
    position: absolute;
    bottom: 40rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 406rpx;
    height: 106rpx;
  }
}

.qrcode-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.qrcode-back {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.qrcode-content {
  padding: 60rpx 40rpx 50rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-content-text-info {
  font-family: DfKing-Regular;
  font-size: 24rpx;
  color: #8c8c8c;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  margin-bottom: 30rpx;
}
.qrcode-content-text-bottom {
  width: 400rpx;
  font-family: DfKing-Regular;
  font-size: 24rpx;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 34rpx;
  font-weight: 400;
}

.qrcode-wrapper {
  position: relative;
  margin-bottom: 34rpx;

  &.expired {
    opacity: 0.6;
  }
}

.expired-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.expired-text {
  font-size: 16px;
  color: #ff4d4f;
  margin-bottom: 15px;
}

.refresh-btn {
  padding: 6px 16px;
  background-color: #1890ff;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: #40a9ff;
  }
}

.qrcode-timer {
  font-size: 14px;
  color: #666;
}
</style>
