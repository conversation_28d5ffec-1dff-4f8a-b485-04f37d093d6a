<template>
  <page-meta :page-style="pageScrollStyle">
    <div class="vip" :class="{ 'page-skeleton-diagram': !hasData }">
      <div
        v-if="menuRect.top"
        class="vip-header-top"
        :style="{
          height: menuRect.bottom + 8 + 'px',
          background
        }"
      >
        <div
          class="vip-navbar"
          :style="{
            top: menuRect.top + 'px',
            width: menuRect.width + 'px',
            height: menuRect.height + 'px'
          }"
          @click="handleBack"
        >
          <div class="vip-back-wrapper">
            <image
              class="vip-back"
              mode="widthFix"
              src="https://static.soyoung.com/sy-design/3cj8rc3ipek931726026826755.png"
            />
          </div>
        </div>
        <div
          v-if="false"
          class="vip-share-wrapper"
          :style="{
            top: menuRect.top - 7 + 'px',
            right: menuRect.width + 30 + 'px'
          }"
          @click="handleShare"
        >
          <img
            class="vip-share"
            src="https://static.soyoung.com/sy-pre/1wu38mq9u5lep-1716797400623.png"
            alt=""
          />
        </div>
      </div>
      <scroll-view
        @scroll="handleScroll"
        :refresher-enabled="true"
        :refresher-triggered="pullDown"
        :refresher-threshold="100"
        refresher-background="transparent"
        @refresherpulling="handlePullDown"
        @refresherrefresh="handlePullDownRefresh"
        @refresherrestore="handlePullDownRestore"
        @refresherabort="handlePullDownAbort"
        :scroll-y="!popupVisible && !relegationVisible && !maturityVisible"
        style="height: 100vh"
      >
        <div
          v-if="hasData"
          class="vip-header"
          :style="{
            paddingTop: menuRect.bottom + 8 + 'px'
          }"
        >
          <uni-transition
            ref="curAni"
            :show="curShow"
            :styles="{
              width: '100%',
              height: '1314rpx',
              position: 'absolute',
              top: 0,
              background: 'url(' + curBackground + ') no-repeat top center',
              backgroundSize: 'cover'
            }"
          >
            <div class="vip-header-filter"></div>
          </uni-transition>
          <uni-transition
            ref="nextAni"
            :show="nextShow"
            :styles="{
              width: '100%',
              height: '1314rpx',
              position: 'absolute',
              top: 0,
              background: 'url(' + nextBackground + ') no-repeat top center',
              backgroundSize: 'cover'
            }"
          >
            <div class="vip-header-filter"></div>
          </uni-transition>
          <div class="vip-level-bar">
            <div
              class="vip-level-item"
              v-for="(item, index) in levelList"
              :key="index"
            >
              <div class="vip-level-header">
                <image
                  v-if="curDetail.level_value === item.level_value"
                  class="vip-level-dot"
                  mode="widthFix"
                  src="https://static.soyoung.com/sy-design/img_v3_02h8_9b9fcb62-d312-4830-9c8a-b32b5b3a0beg1734071102279.png"
                />
                <div v-else class="vip-level-dot-inactive" />
                <div
                  v-if="index < levelList.length - 1"
                  class="vip-level-devide"
                ></div>
              </div>
              <div
                class="vip-level-name"
                :style="{
                  color:
                    curDetail.level_value === item.level_value
                      ? '#333333'
                      : 'rgba(0, 0, 0, 0.2)'
                }"
              >
                {{ item.level_value_name }}
              </div>
            </div>
          </div>
          <swiper
            :current="curShowLevel"
            :indicator-dots="false"
            :autoplay="false"
            class="vip-main"
            @change="handleLevelChange"
            @animationfinish="handleChangeFinish"
          >
            <swiper-item v-for="item in levelList" :key="item.level_value">
              <div class="vip-main-item">
                <div
                  class="vip-main-info"
                  :style="{
                    background:
                      'url(' + item.level_img + ') no-repeat center center',
                    backgroundSize: 'cover'
                  }"
                >
                  <div
                    class="vip-main-tips"
                    :style="{
                      background: !item.is_lock
                        ? 'rgba(3, 3, 3, 1)'
                        : 'rgba(3, 3, 3, 0.3)'
                    }"
                  >
                    {{ item.level_status_name }}
                  </div>
                  <div class="vip-main-value">
                    {{ item.level_value_name }}
                  </div>
                  <div class="vip-main-name">
                    {{ item.level_name }}
                  </div>
                </div>
                <div
                  class="vip-item-tips"
                  :style="{
                    color: `${item.page_color || '#ffffff'}`
                  }"
                  v-if="item.relegation_experience_desc"
                >
                  {{ item.relegation_experience_desc }}
                </div>
                <div class="vip-progress">
                  <div
                    class="vip-progress-bar"
                    v-if="
                      (item.level_value === curLevel &&
                        curLevel !== highestLevel.level_value) ||
                      (curLevel === highestLevel.level_value &&
                        item.relegation_experience > curExp)
                    "
                  >
                    <div
                      class="active"
                      :style="{
                        width: `${(curExp / item.highest_experience) * 100}%`
                      }"
                    ></div>
                    <div
                      class="vip-progress-point"
                      v-if="item.relegation_experience"
                      :style="{
                        left: `calc(${
                          (item.relegation_experience /
                            item.highest_experience) *
                          100
                        }% - 12rpx)`
                      }"
                    >
                      <div
                        v-if="item.relegation_experience > curExp"
                        class="vip-progress-tooltips"
                        :class="
                          item.relegation_experience / item.highest_experience <
                          0.75
                            ? 'left'
                            : 'right'
                        "
                        @click="handleOpenRelegation(item)"
                      >
                        {{ '保级点' + item.relegation_experience }}
                      </div>
                    </div>
                  </div>
                  <div class="vip-progress-left">
                    <div
                      class="vip-progress-number"
                      v-if="!item.is_lock"
                      @click="handleClickToPeaValue"
                    >
                      <div class="vip-number-cur">
                        {{ curExp }}
                      </div>
                      <div
                        class="vip-number-total"
                        v-if="
                          item.level_value === curLevel &&
                          curExp < highestLevel.highest_experience
                        "
                      >
                        /
                      </div>
                      <div
                        class="vip-number-total"
                        v-if="
                          item.level_value === curLevel &&
                          curExp < highestLevel.highest_experience
                        "
                      >
                        {{ item.highest_experience }}
                      </div>
                      <image
                        class="vip-arrow"
                        mode="widthFix"
                        src="https://static.soyoung.com/sy-design/6miozm5ozd4r1731661015603.png"
                      />
                    </div>
                    <div
                      :class="['vip-progress-info', { lock: item.is_lock }]"
                      v-else
                      @click="handleJump(peaValueUrl)"
                    >
                      {{ item.level_unlock_desc }}
                    </div>
                  </div>
                  <div class="vip-main-desc-wrapper">
                    <div
                      class="vip-main-desc"
                      v-if="item.level_tips"
                      @click="handleOpenMaturity(item)"
                    >
                      <image
                        v-if="item.level_value === curLevel || item.is_lock"
                        class="vip-main-desc-icon"
                        mode="widthFix"
                        src="https://static.soyoung.com/sy-design/238380hlp6pof1731661015709.png"
                      />
                      <div
                        v-for="(i, idx) in item.level_tips"
                        class="vip-main-desc-text"
                        :key="idx"
                        :class="i.type === 2 ? 'bold' : ''"
                      >
                        {{ i.title }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </swiper-item>
          </swiper>
          <div class="vip-equity" v-show="levelEquity.rights_list.length">
            <div class="vip-equity-title">
              <div class="left">
                <image
                  class="vip-equity-icon"
                  mode="widthFix"
                  src="https://static.soyoung.com/sy-design/alola77ukb961731661015591.png"
                />
                <div class="vip-equity-text">
                  {{ levelEquity.rights_title }}
                </div>
              </div>
              <div
                class="right"
                @click="handleExpand"
                v-if="levelEquity.rights_list.length > 6"
              >
                <div class="vip-equity-more">
                  {{
                    levelEquityExpand
                      ? '收起'
                      : '查看全部' + levelEquity.rights_list.length + '项'
                  }}
                </div>
                <image
                  class="vip-equity-more-arrow"
                  :style="{
                    transform: levelEquityExpand
                      ? 'rotate(180deg)'
                      : 'rotate(0deg)'
                  }"
                  src="https://static.soyoung.com/sy-design/6mioll1s5b481732087434753.png"
                />
              </div>
              <div v-else></div>
            </div>
            <div
              class="vip-equity-body"
              :class="
                !levelEquityExpand && levelEquity.rights_list.length > 6
                  ? 'short'
                  : ''
              "
            >
              <div
                v-for="(item, index) in levelEquity.rights_list"
                :key="item.rights_id"
                class="vip-equity-item"
                :data-index="index"
                :data-title="item.rights_name"
                :class="item.is_lock ? 'is-lock' : ''"
                @click="handleGoToEquity(item, index)"
              >
                <image
                  :src="item.rights_imgs"
                  mode="aspectFill"
                  class="vip-equity-item-background"
                ></image>
                <div class="vip-equity-item-content">
                  <div v-if="!item.is_lock" class="vip-equity-item-lock-tip">
                    已解锁
                  </div>
                  <div class="vip-equity-item-title">
                    {{ item.rights_name }}
                  </div>
                  <div class="vip-equity-item-subtitle">
                    {{ item.rights_tips }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="vip-banner"
          v-if="levelHomeBanner && levelHomeBanner.banner_img"
          @click="handleJumpToActivity(levelHomeBanner.banner_url)"
        >
          <image
            class="vip-banner-img"
            mode="widthFix"
            :src="levelHomeBanner.banner_img"
          />
        </div>
        <div class="vip-task" v-if="hasData">
          <div class="vip-task-title">
            <div class="vip-task-main">
              <image
                class="vip-task-icon"
                mode="widthFix"
                src="https://static.soyoung.com/sy-design/alola77ukb961731661015591.png"
              />
              <div class="vip-task-text">
                {{ levelTask.task_title }}
              </div>
            </div>
            <div class="vip-task-more" @click="handleGoTaskDesc">
              <div class="vip-task-more-text">
                {{ levelTask.level_desc }}
              </div>
              <image
                class="vip-task-more-arrow"
                mode="widthFix"
                src="https://static.soyoung.com/sy-pre/6zxqc68bj077-1731985800663.png"
              />
            </div>
          </div>
          <div class="vip-task-list">
            <div
              class="vip-task-item"
              v-for="(item, index) in levelTask.task_list"
              :key="index"
              :data-index="index"
              :data-title="item.task_title"
              :data-count="item.task_count"
              :data-completed="item.task_completed"
            >
              <div class="vip-task-item-main">
                <div class="vip-task-item-title-wrapper">
                  <div class="vip-task-item-title">{{ item.task_title }}</div>
                  <div class="vip-task-item-title" v-if="item.task_count">
                    ({{ item.task_completed + '/' + item.task_count }})
                  </div>
                  <div class="vip-task-item-exp" v-if="item.experience">
                    {{ item.experience }}
                  </div>
                </div>
                <div class="vip-task-item-subtitle-wrapper">
                  <div class="vip-task-item-subtitle">
                    {{ item.task_subtitle }}
                  </div>
                  <tooltips
                    class="vip-task-item-icon-wrapper"
                    v-if="item.task_desc"
                    :content="item.task_desc"
                  />
                </div>
              </div>
              <div
                class="vip-task-item-btn"
                :class="!item.can_jump ? 'disabled' : ''"
                @click="hendleClickToTask(item, index)"
              >
                {{ item.jump_title }}
              </div>
            </div>
          </div>
        </div>
        <div style="height: 112rpx"></div>
        <bottom-modal
          :height="280"
          title="保级规则"
          :visible="relegationVisible"
          @visible="relegationVisible = false"
        >
          <div class="relegation-bottom-text">
            {{ curDetail.relegation_experience_desc }}
          </div>
        </bottom-modal>
        <bottom-modal
          :height="280"
          title="有效期"
          :visible="maturityVisible"
          @visible="maturityVisible = false"
        >
          <div class="relegation-bottom-text">
            {{ curDetail.level_maturity_desc }}
          </div>
        </bottom-modal>
        <level-up-popup :info="popupInfo" :visible.sync="popupVisible" />
        <weak-network
          @refresh="getData"
          :path="['/syGroupBuy/chain/level/levelHome']"
          :visible.sync="weakNetwork"
        />
      </scroll-view>
    </div>
  </page-meta>
</template>
<script>
import { getVipHome } from '@/api/vip';
import BottomModal from './components/BottomModal.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import LevelUpPopup from './components/LevelUpPopup.vue';
import Tooltips from './components/Tooltips.vue';
import UniTransition from '@/components/uni/transition/index.vue';
import SubscribeMixins from '@/mixins/subscribe';

export default {
  name: 'packageVip',
  mixins: [SubscribeMixins],
  components: {
    BottomModal,
    WeakNetwork,
    LevelUpPopup,
    Tooltips,
    UniTransition
  },
  data() {
    return {
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      hasData: false,
      background: '',
      curLevel: 1, // 当前用户等级
      highestLevel: {}, //当前配置最高等级
      curDetail: {}, // 当前等级详情
      curShowLevel: 0, // 当前用户查看等级
      curExp: 0, // 当前用户经验值
      levelList: [],
      levelEquity: {}, // 当前等级权益
      levelTask: {}, // 等级任务
      levelHomeBanner: {}, // 等级首页banner
      relegationVisible: false,
      maturityVisible: false,
      peaValueUrl: '',
      weakNetwork: false,
      levelEquityExpand: false, // 等级权益展开
      popupVisible: false,
      popupInfo: {},
      pageScrollStyle: '',
      curShow: true,
      nextShow: false,
      curBackground: '',
      nextBackground: '',
      up: 0,
      down: -1,
      pullDown: false,
      scale: 1
    };
  },
  watch: {
    relegationVisible(val) {
      if (val) {
        this.disablePageScroll();
      } else {
        this.enablePageScroll();
      }
    },
    maturityVisible(val) {
      if (val) {
        this.disablePageScroll();
      } else {
        this.enablePageScroll();
      }
    },
    popupVisible(val) {
      if (val) {
        this.disablePageScroll();
      } else {
        this.enablePageScroll();
      }
    }
  },
  onLoad() {
    this.menuRect = uni.getMenuButtonBoundingClientRect();
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_member_info_page',
      ext: {
        content: this.curDetail.level_value_name
      }
    });
  },

  onShow() {
    this.$reportPageShow({
      info: 'sy_chain_store_tuan_member_info_page',
      ext: {}
    });
    this.getData();
  },
  async onPullDownRefresh() {
    console.log('onPullDownRefresh');
    if (this.weakNetwork) return;
    await this.getData();
    wx.stopPullDownRefresh();
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_member_info_page',
      ext: {
        content: this.curDetail.level_value_name
      }
    });
  },

  methods: {
    handleScroll(e) {
      console.log(e.target.scrollTop);
      if (e.target.scrollTop < 100) {
        this.background = `rgba(255,255,255,${e.target.scrollTop / 100})`;
      } else if (this.background !== '#ffffff') {
        this.background = `#ffffff`;
      }
    },
    handlePullDown(e) {
      if (e.detail.dy < 0) return;
      if (e.detail.dy < 100) {
        this.scale = 1 + e.detail.dy / 300;
      }

      this.pullDown = true;
    },
    handlePullDownRefresh() {
      if (this.weakNetwork) return;
      if (this.pullDown) {
        this.getData();
      }
    },
    handlePullDownRestore() {
      this.scale = 1;
    },
    handlePullDownAbort() {
      this.scale = 1;
    },
    disablePageScroll() {
      this.pageScrollStyle = 'overflow:hidden;height:100vh;';
    },
    enablePageScroll() {
      this.pageScrollStyle = '';
    },
    handleBack() {
      try {
        if (getCurrentPages().length === 1) {
          uni.reLaunch({
            url: '/pages/index'
          });
        } else {
          uni.navigateBack();
        }
      } catch (error) {
        console.error(error);
      }
    },
    handleShare() {},
    handleLevelChange(e) {
      this.curDetail = this.levelList[e.detail.current];
      if (this.curShow) {
        this.nextBackground =
          this.levelList[e.detail.current].level_img_background;
      } else {
        this.curBackground =
          this.levelList[e.detail.current].level_img_background;
      }
      this.curShow = !this.curShow;
      this.nextShow = !this.nextShow;
    },
    handleChangeFinish() {
      if (!this.curShow) {
        this.curBackground = this.nextBackground;
      } else {
        this.nextBackground = this.curBackground;
      }
    },
    handleClickToPeaValue() {
      this.$reportData({
        info: 'sy_chain_store_other_member_info:experience_click',
        ext: {
          content: this.curExp
        }
      });
      this.handleJump(this.peaValueUrl);
    },
    handleJumpToActivity(url) {
      this.$reportData({
        info: 'sy_chain_store_other_member_info:middle_banner_click',
        ext: {
          content: this.curExp
        }
      });
      uni.navigateTo({
        url: url
      });
    },
    handleJump(link) {
      if (link.indexOf('.com') > -1 || link.indexOf('http') > -1) {
        this.$toH5(link);
      } else {
        this.$bridge({
          url: link
        });
      }
    },
    handleExpand() {
      this.$reportData({
        info: 'sy_chain_store_other_member_info:more_click',
        ext: {
          status: this.levelEquityExpand ? 0 : 1
        }
      });
      this.levelEquityExpand = !this.levelEquityExpand;
    },
    async handleGoToEquity(item, index) {
      this.$reportData({
        info: 'sy_chain_store_other_member_info:benefit_click',
        ext: {
          serial_num: index + 1,
          title: item.rights_name
        }
      });
      if (item.rights_id === 29) {
        // TODO @风尘
        await this.createGroupBySub(
          ['2EIe7K95g8vMiuvNk_cbxsl8AgSm4J_Xe1m02b2MHnU'],
          []
        );
      }
      this.handleJump(item.jump_url);
    },
    handleGoTaskDesc() {
      this.$reportData({
        info: 'sy_chain_store_other_member_info:level_des_click',
        ext: {}
      });
      this.handleJump(this.levelTask.level_desc_url);
    },
    hendleClickToTask(item, index) {
      if (!item.can_jump) {
        if (item.task_tips) {
          uni.showToast({
            title: item.task_tips,
            icon: 'none'
          });
        }
        return;
      }
      if (item.task_completed >= item.task_count && item.task_count > 0) {
        if (item.task_tips) {
          uni.showToast({
            title: item.task_tips,
            icon: 'none'
          });
        }
        return;
      }
      this.$reportData({
        info: 'sy_chain_store_other_member_info:task_click',
        ext: {
          serial_num: index + 1,
          title:
            item.task_title +
            (item.task_count
              ? '(' + item.task_completed + '/' + item.task_count + ')'
              : '')
        }
      });
      this.handleJump(item.jump_url);
    },
    initExposure() {
      this.$nextTick(() => {
        // 豆腐块埋点
        this.$registerExposure('.vip-equity-item', (res) => {
          this.$reportData({
            info: 'sy_chain_store_other_member_info:benefit_exposure',
            ext: {
              serial_num: res.dataset.index + 1,
              title: res.dataset.title
            }
          });
        });
        this.$registerExposure('.vip-task-item', (res) => {
          this.$reportData({
            info: 'sy_chain_store_other_member_info:task_exposure',
            ext: {
              serial_num: res.dataset.index + 1,
              title:
                res.dataset.title +
                (res.dataset.count
                  ? '(' + res.dataset.completed + '/' + res.dataset.count + ')'
                  : '')
            }
          });
        });
        this.$registerExposure('.vip-main-value', () => {
          this.$reportData({
            info: 'sy_chain_store_other_member_info:level_exposure',
            ext: {
              content: this.curDetail.level_value_name
            }
          });
        });
      });
    },
    handleOpenRelegation(item) {
      if (!item.relegation_experience_desc) {
        return;
      }
      this.relegationVisible = true;
    },
    handleOpenMaturity(item) {
      if (item.level_value !== this.curLevel && !item.is_lock) {
        return;
      }
      this.maturityVisible = true;
    },
    async getData() {
      const res = await getVipHome();
      if (res) {
        this.levelList = res.level_home_head.levelinfo_list || [];
        this.curLevel = res.level_home_head.customer_level_value ?? 1;
        this.peaValueUrl = res.level_home_head.experience_detail_url || '';
        this.curDetail = this.levelList.find(
          (item) => item.level_value === this.curLevel
        );
        this.curBackground = this.curDetail.level_img_background;
        this.nextBackground = this.curDetail.level_img_background;
        this.highestLevel = this.levelList.length
          ? this.levelList[this.curLevel]
          : {};
        this.curExp = res.level_home_head.customer_experience || 0;
        this.levelEquity = res.level_rights || {};
        this.levelTask = res.level_task_data || {};
        this.popupInfo = res.coustomer_level_popup || {};
        this.levelHomeBanner = res.level_home_banner || {};
        this.hasData = true;
        this.weakNetwork = false;
        this.pullDown = false;
        // 解决 swiper index变化后，swiper没更新的bug，很重要，不能删除
        if (this.curShowLevel) {
          this.curShowLevel = 0;
          this.$nextTick(() => {
            this.curShowLevel =
              this.levelList.findIndex(
                (item) => item.level_value === this.curLevel
              ) || 0;
          });
        } else {
          this.curShowLevel =
            this.levelList.findIndex(
              (item) => item.level_value === this.curLevel
            ) || 0;
        }
        setTimeout(() => {
          this.initExposure();
        }, 1000);
      }
    }
  }
};
</script>
<style lang="less">
.page-skeleton-diagram {
  background-image: url('https://static.soyoung.com/sy-pre/20241121-145528-1732169400634.png');
  background-size: 100%;
  background-position: top;
  background-repeat: no-repeat;
  max-height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
}
.vip {
  box-sizing: border-box;
  background-color: #f6f6f6;
  height: 100vh;
  overflow-y: auto;
  .vip-header-top {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 9999;
    .vip-share-wrapper {
      display: flex;
      position: fixed;
      align-items: center;
      height: 88rpx;
      right: -100%;
      .vip-share {
        width: 60rpx;
        height: 60rpx;
      }
    }
    .vip-navbar {
      position: fixed;
      display: flex;
      align-items: center;
      .vip-back-wrapper {
        display: block;
        .vip-back {
          width: 88rpx;
          height: 88rpx;
        }
      }
    }
  }
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none !important;
    background: transparent !important;
    color: transparent !important;
  }
  .vip-header {
    width: 100%;
    background: #f6f6f6;
    box-sizing: border-box;
    padding-bottom: 30rpx;
    position: relative;
    .vip-header-filter {
      height: 80rpx;
      background-image: linear-gradient(
        180deg,
        rgba(248, 248, 248, 0) 0%,
        #f8f8f8 100%
      );
      position: absolute;
      bottom: -2rpx;
      width: 100%;
    }
    .vip-header-background {
      width: 100%;
      height: 1314rpx;
      position: absolute;
      top: 0;
    }

    .vip-level-bar {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .vip-level-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .vip-level-header {
          display: flex;
          align-items: center;
          .vip-level-dot {
            width: 20rpx;
            height: 20rpx;
            position: relative;
            z-index: 1;
          }
          .vip-level-dot-inactive {
            width: 20rpx;
            height: 20rpx;
            opacity: 0.1;
            background: #030303;
            border-radius: 6px;
            position: relative;
            z-index: 1;
          }
          .vip-level-devide {
            width: 42rpx;
            height: 2rpx;
            opacity: 0.1;
            background: #1e1e1e;
          }
        }
        .vip-level-name {
          font-family: Outfit-Regular;
          font-size: 20rpx;
          color: #000000;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin-top: 16rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
          width: 42rpx;
          margin-left: -10rpx;
        }
      }
    }
    .vip-main {
      margin-top: 30rpx;
      height: 440rpx;
      .vip-main-item {
        margin: 0 30rpx;
        height: 440rpx;
        position: relative;
        .vip-item-tips {
          font-family: PingFangSC-Regular;
          font-size: 11px;
          color: #ffffff;
          text-align: right;
          font-weight: 400;
          position: absolute;
          right: 30rpx;
          bottom: 104rpx;
          text-align: right;
        }
        .vip-main-info {
          padding: 54rpx 30rpx;
          box-sizing: border-box;
          position: relative;
          .vip-main-tips {
            position: absolute;
            top: 0;
            left: 0;
            background: rgba(3, 3, 3, 1);
            font-family: PingFangSC-Medium;
            font-size: 20rpx;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            font-weight: 500;
            padding: 4rpx 10rpx;
            box-sizing: border-box;
          }
          .vip-main-value {
            opacity: 0;
            font-family: OutFit-Regular;
            font-size: 120rpx;
            line-height: 152rpx;
            color: #030303;
            letter-spacing: 0;
            font-weight: 200;
            margin-bottom: -10rpx;
          }
          .vip-main-name {
            opacity: 0;
            font-family: PingFangSC-Medium;
            font-size: 40rpx;
            color: #030303;
            letter-spacing: 0;
            font-weight: 500;
            margin-bottom: 54rpx;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
          }
        }
      }
    }
    .vip-progress {
      height: 80rpx;
      line-height: 80rpx;
      background-color: #333333;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      .vip-progress-left {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding-left: 30rpx;
      }
      .vip-progress-bar {
        position: absolute;
        height: 6rpx;
        top: 0;
        left: 0;
        right: 0;
        background-color: #5c5c5c;
        .active {
          height: 100%;
          background-color: #8adc65;
        }
        .vip-progress-point {
          position: absolute;
          top: -4rpx;
          width: 12rpx;
          height: 12rpx;
          background: #89dc65;
          border: 1px solid #ffffff;
          box-sizing: border-box;
          .vip-progress-tooltips {
            height: 40rpx;
            line-height: 40rpx;
            background-color: rgba(85, 85, 85, 0.7);
            position: absolute;
            bottom: 28rpx;
            font-family: PingFangSC-Regular;
            font-size: 22rpx;
            color: #ffffff;
            text-align: center;
            font-weight: 400;
            padding: 0 10rpx;
            white-space: nowrap;
          }
          .vip-progress-tooltips.left {
            left: 0;
            &::after {
              content: '';
              position: absolute;
              width: 0;
              height: 0;
              bottom: -8rpx;
              left: 0;
              border: 4rpx solid transparent;
              border-left-color: rgba(85, 85, 85, 0.7);
              border-top-color: rgba(85, 85, 85, 0.7);
            }
          }
          .vip-progress-tooltips.right {
            right: 0;
            &::after {
              content: '';
              position: absolute;
              width: 0;
              height: 0;
              bottom: -8rpx;
              right: 0;
              border: 4rpx solid transparent;
              border-right-color: rgba(85, 85, 85, 0.7);
              border-top-color: rgba(85, 85, 85, 0.7);
            }
          }
        }
      }
      .vip-progress-number {
        display: flex;
        align-items: center;
        .vip-number-cur {
          font-family: OutFit-Regular;
          font-size: 28rpx;
          color: #ffffff;
          font-weight: 200;
        }
        .vip-number-total {
          font-family: OutFit-Regular;
          font-size: 28rpx;
          color: #999999;
          font-weight: 200;
        }
        .vip-arrow {
          width: 12rpx;
          height: 18rpx;
          margin-left: 6rpx;
        }
      }
      .vip-progress-info {
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #ffffff;
        font-weight: 400;
        margin-left: 10rpx;
        &.lock {
          margin-left: 0;
        }
      }
      .vip-main-desc-wrapper {
        height: 34rpx;
        line-height: 34rpx;
      }
      .vip-main-desc {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .vip-main-desc-icon {
          width: 28rpx;
          height: 28rpx;
          margin-right: 4rpx;
        }
        .vip-main-desc-text {
          font-family: DfKing-Regular;
          font-size: 24rpx;
          color: #7d7d7d;
          font-weight: 400;
          margin-right: 10rpx;
          &:last-child {
            margin-right: 30rpx;
          }
        }
        .vip-main-desc-text.bold {
          font-family: DfKing-Regular;
          font-size: 24rpx;
          color: #c8c8c8;
          font-weight: 400;
        }
      }
    }
  }
  .vip-equity {
    margin: 40rpx 30rpx 0;
    padding: 30rpx;
    background-color: #ffffff;
    box-sizing: border-box;
    position: relative;
    .vip-equity-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30rpx;
      .left {
        display: flex;
        align-items: center;
        .vip-equity-icon {
          width: 36rpx;
          height: 36rpx;
          margin-right: 10rpx;
        }
        .vip-equity-text {
          font-family: PingFangSC-Medium;
          font-size: 32rpx;
          color: #030303;
          letter-spacing: 0;
          font-weight: 500;
        }
      }
      .right {
        display: flex;
        align-items: center;
        .vip-equity-more {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #030303;
          letter-spacing: 0;
          text-align: right;
          font-weight: 400;
        }
        .vip-equity-more-arrow {
          width: 18rpx;
          height: 14rpx;
          margin-left: 12rpx;
        }
      }
    }
    .vip-equity-body {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 20rpx;
      flex-wrap: wrap;
      justify-content: space-between;
      overflow: hidden;
      .vip-equity-item {
        width: 196rpx;
        height: 142rpx;
        background: #f6f6f6;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
        word-break: break-word;
        .vip-equity-item-background {
          width: 100%;
          height: 100%;
        }
        .vip-equity-item-content {
          padding: 30rpx 20rpx;
          box-sizing: border-box;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }
        .vip-equity-item-title {
          font-family: DfKing-Medium;
          font-size: 28rpx;
          color: #030303;
          letter-spacing: 0;
          font-weight: 500;
          margin-bottom: 16rpx;
          margin-top: 6rpx;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .vip-equity-item-subtitle {
          font-family: DfKing-Regular;
          font-size: 22rpx;
          color: #030303;
          font-weight: 400;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .vip-equity-item-lock-tip {
          position: absolute;
          background: #030303;
          font-family: PingFangSC-Medium;
          font-size: 16rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          top: 0;
          right: 0;
          line-height: 26rpx;
          padding: 0 6rpx;
          &.wait-lock {
            background: #89dc65;
            color: #030303;
          }
        }
      }
      .vip-equity-item.is-lock {
        .vip-equity-item-title {
          color: #030303;
        }
        .vip-equity-item-subtitle {
          color: #030303;
        }
      }
    }
    .vip-equity-body.short {
      height: 304rpx;
    }
  }
  .vip-banner {
    padding: 0 30rpx;
    position: relative;
    .vip-banner-img {
      margin: 0 auto;
      display: block;
      width: 100%;
    }
  }
  .vip-task {
    margin: 30rpx 30rpx 0;
    background-color: #ffffff;
    position: relative;
    padding: 30rpx;
    .vip-task-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .vip-task-main {
        display: flex;
        align-items: center;
        .vip-task-icon {
          width: 36rpx;
          height: 36rpx;
          margin-right: 10rpx;
        }
        .vip-task-text {
          font-family: PingFangSC-Medium;
          font-size: 32rpx;
          color: #030303;
          letter-spacing: 0;
          font-weight: 500;
        }
      }
      .vip-task-more {
        display: flex;
        align-items: center;
        .vip-task-more-text {
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #030303;
          letter-spacing: 0;
          font-weight: 400;
        }
        .vip-task-more-arrow {
          width: 12rpx;
          height: 19rpx;
          margin-left: 10rpx;
        }
      }
    }
    .vip-task-list {
      box-sizing: border-box;
      .vip-task-item {
        padding: 40rpx 0;
        box-sizing: border-box;
        border-bottom: 2rpx solid #f2f2f2;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &:last-child {
          border-bottom: none;
        }
        .vip-task-item-main {
          flex: 1;
          .vip-task-item-title-wrapper {
            display: flex;
            align-items: center;
            margin-bottom: 10rpx;
            .vip-task-item-title {
              font-family: PingFangSC-Medium;
              font-size: 28rpx;
              color: #030303;
              letter-spacing: 0;
              font-weight: 500;
            }
            .vip-task-item-icon {
              width: 32rpx;
              height: 32rpx;
              margin-left: 10rpx;
            }
            .vip-task-item-exp {
              font-family: Outfit-Regular;
              font-size: 14px;
              color: #61b43e;
              letter-spacing: 0;
              font-weight: 400;
              margin-left: 6rpx;
            }
          }
          .vip-task-item-subtitle-wrapper {
            display: flex;
            align-items: center;
          }
          .vip-task-item-subtitle {
            font-family: PingFangSC-Regular;
            font-size: 22rpx;
            color: #8c8c8c;
            font-weight: 400;
          }
          .vip-task-item-icon-wrapper {
            width: 28rpx;
            height: 28rpx;
            margin-left: 6rpx;
          }
        }
        .vip-task-item-btn {
          flex-shrink: 0;
          text-align: center;
          font-family: PingFangSC-Medium;
          font-size: 24rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          line-height: 50rpx;
          height: 50rpx;
          padding: 0 24rpx;
          background-color: #333333;
        }
        .vip-task-item-btn.disabled {
          background-color: #bababa;
        }
      }
    }
  }
}
.relegation-bottom-text {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #333333;
  letter-spacing: 0;
  line-height: 40rpx;
  font-weight: 400;
  margin-bottom: 50rpx;
}
.relegation-bottom-link {
  font-family: PingFangSC-Medium;
  text-decoration: underline;
  font-weight: 500;
}
</style>
