<template>
  <div class="tooltip-container" @click.stop>
    <div class="tooltip-trigger">
      <image
        class="vip-task-tooltips-icon"
        mode="widthFix"
        src="https://static.soyoung.com/sy-design/238380hlp6pof1731661015709.png"
        @click="toggleTooltip"
      />
    </div>
    <div v-if="visible" class="tooltip-content">
      <div>{{ content }}</div>
      <div class="tooltip-mask" @click="handleCloseTooltips"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Tooltips',
  props: {
    content: {
      type: String,
      default: ''
    },
    trigger: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: this.trigger || false
    };
  },
  watch: {
    trigger(newVal) {
      this.visible = newVal;
    }
  },
  methods: {
    toggleTooltip() {
      this.visible = !this.visible;
    },
    handleCloseTooltips() {
      this.$emit('close');
      this.visible = false;
    }
  }
};
</script>

<style scoped lang="less">
.tooltip-container {
  width: 28rpx;
  height: 28rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.tooltip-trigger {
  width: 28rpx;
  height: 28rpx;
  display: flex;
  align-items: center;
}
.tooltip-content {
  width: 220rpx;
  background-color: rgba(85, 85, 85, 0.7);
  position: absolute;
  bottom: 43rpx;
  padding: 6rpx 20rpx;
  box-sizing: border-box;
  left: calc(-110rpx + 8rpx);
  div {
    line-height: 32rpx;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #ffffff;
    font-weight: 400;
    text-overflow: ellipsis;
  }
  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    top: 100%;
    left: calc(50% - 6rpx);
    border: 12rpx solid transparent;
    border-top-color: rgba(85, 85, 85, 0.7);
  }
}
.tooltip-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
