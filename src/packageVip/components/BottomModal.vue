<template>
  <div
    class="modal-mask"
    @click="close"
    :style="{ bottom: '-1px' }"
    @animationend="animationend"
    :class="{
      'fade-in': transform
    }"
    v-if="visible"
  >
    <div class="mask"></div>
    <div
      class="pannel"
      :class="{
        safe: false
      }"
      @click.stop
    >
      <div class="title">{{ title }}</div>
      <div class="close" @click="close"></div>
      <div
        class="relegation-bottom-modal-content"
        :style="{
          minHeight: `${height}rpx`
        }"
      >
        <slot></slot>
        <div class="relegation-bottom-btn" @click="close">知道了</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    height: {
      type: Number,
      default: 375
    }
  },
  data() {
    return {
      transform: false
    };
  },
  created() {},
  watch: {
    visible(visible) {
      if (visible) {
        setTimeout(() => {
          this.transform = true;
        }, 50);
      } else {
        this.transform = false;
      }
    }
  },
  methods: {
    async animationend() {
      setTimeout(() => {
        this.$emit('open');
      }, 100);
    },
    close() {
      this.$emit('visible', false);
    }
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(900px);
  &.fade-in {
    .mask {
      opacity: 1;
    }
    .pannel {
      transform: translateY(0);
    }
  }
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 2px);
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.7);
  transition: opacity 0.2s;
}
.pannel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.2s;
  background-color: #ffffff;
  &.safe {
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
}
.title {
  .flex-align-center;
  font-family: PingFangSC-Medium;
  justify-content: center;
  height: 52 * 2rpx;
  font-size: 32rpx;
  color: #030303;
  font-weight: 500;
}
.close {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 28rpx;
  background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1726026826488.png)
    no-repeat center / 100%;
  z-index: 9;
}

.relegation-bottom-modal-content {
  position: relative;
  display: block;
  // 70 UI 要求占屏幕高
  // 104 modal 的 title bar 高度
  box-sizing: border-box;
  padding: 30rpx 50rpx;

  .relegation-bottom-btn {
    background: #333333;
    height: 84rpx;
    line-height: 84rpx;
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 500;
  }
}
</style>
<style lang="less">
.hospital-choose-input-placeholder {
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #bababa;
  font-weight: 400;
}
</style>
