// 版本
import { version } from '../package.json';
const sys = '8'; // 平台
const appId = __PRIME_APPID__; // appid,新氧系统的appid,非小程序appid
// API基础路径 (默认正式环境)
let baseApi = 'https://wxapi.soyoung.com';
let payBaseApi = 'https://pay.soyoung.com';
let webviewHost = 'https://m.soyoung.com/';
let passportApi = 'https://passport.soyoung.com';
let primeApi = 'https://prime.soyoung.com';
let statApi = 'https://st3.soyoung.com/wxa';
let sySocket = 'wss://chatcon.soyoung.com/sub';
const NODE_ENV = process.env.NODE_ENV;
// 判断开发环境，变更基础API
switch (__API_ENV__) {
  case 'dev': {
    const apiDevNum = Number(__API_ENV_DEV_NUM__) || '';
    baseApi = `https://devwxapi${apiDevNum}.sy.soyoung.com`;
    payBaseApi = `https://pay11.sy.soyoung.com`;
    webviewHost = `https://m.sy.soyoung.com/`;
    passportApi = `https://passport.sy.soyoung.com`;
    primeApi = `https://prime.sy.soyoung.com`;
    sySocket = 'wss://chat.sy.soyoung.com:8443/sub';
    statApi = 'https://testst3.soyoung.com/wxa';
    break;
  }
  case 'test': {
    const apiDevNum = Number(__API_ENV_DEV_NUM__) || '';
    baseApi = `https://wxapi${apiDevNum}.sy.soyoung.com`;
    payBaseApi = `https://pay11.sy.soyoung.com`;
    webviewHost = `https://m.sy.soyoung.com/`;
    passportApi = `https://passport.sy.soyoung.com`;
    primeApi = `https://prime.sy.soyoung.com`;
    sySocket = 'wss://chat.sy.soyoung.com:8443/sub';
    statApi = 'https://testst3.soyoung.com/wxa';
    break;
  }
  case 'pre':
    baseApi = 'https://wxapi8.soyoung.com';
    payBaseApi = 'https://pay8.soyoung.com';
    webviewHost = 'https://m8.soyoung.com/';
    passportApi = 'https://passport8.soyoung.com';
    primeApi = 'https://prime8.soyoung.com';
    sySocket = 'wss://chatcon.soyoung.com/sub';
    statApi = 'https://testst3.soyoung.com/wxa';
    break;
  case 'prod':
    break;
  default:
    break;
}

console.log(
  '❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️'
);
console.log('【APP_ID】', appId);
console.log('【NODE_ENV】', NODE_ENV);
console.log('【api_env】', __API_ENV__);
console.log('【version】', version);
console.log('【baseApi】', baseApi);
console.log('【webview】', webviewHost);
console.log('【payApi】', payBaseApi);
console.log('【passportApi】', passportApi);
console.log('【primeApi】', primeApi);
console.log('【statApi】', statApi);
console.log('【sySocket】', sySocket);
console.log(
  '❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️'
);

// const sySocket = {
//   // production: 'wss://chat.soyoung.com:8445/sub',
//   development: 'wss://chat.sy.soyoung.com:8443/sub',
//   production: 'wss://chatcon.soyoung.com/sub',
//   preRelease: 'wss://chatcon.soyoung.com/sub'
//   // development: 'wss://chatcon.soyoung.com/sub',
// };

export default {
  env: NODE_ENV,
  webviewHost,
  baseApi,
  sySocket,
  payBaseApi,
  passportApi,
  primeApi,
  statApi,
  version,
  appId,
  sys
};
