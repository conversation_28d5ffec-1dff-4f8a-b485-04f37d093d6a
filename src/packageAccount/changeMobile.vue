<template>
  <div class="change-mobile">
    <view class="p-form">
      <view class="row">
        <picker
          @change="bindPickerChange"
          range-key="name"
          :value="codeIndex"
          :range="codeList"
        >
          <view class="picker">
            {{ showText }}
            <i class="picker__icon"></i>
          </view>
        </picker>
        <input
          class="phone-input"
          v-model="mobile"
          bindinput="getMobile"
          maxlength="11"
          type="number"
          placeholder="请输入手机号码"
          placeholder-class="placeholder-custom"
          @focus="showClear = true"
          @blur="showClear = false"
        />
        <div
          class="clear-input adjust"
          v-if="mobile.length && showClear"
          @click="mobile = ''"
        >
          <img
            src="https://static.soyoung.com/sy-pre/12pl6hfybugkm-1617189000631.png"
            alt=""
          />
        </div>
      </view>
      <view class="row">
        <input
          class="code-input"
          bindinput="getCode"
          v-model="msgCode"
          type="number"
          placeholder="请输入手机验证码"
          placeholder-class="placeholder-custom"
        />
        <view class="code" @tap="sendMsg" v-show="!showCountDown">
          <text>{{ sendText }}</text>
        </view>
        <view class="count-down" v-show="showCountDown">
          <text>{{ second }}秒</text>
        </view>
      </view>
      <view class="gt-box" v-if="gtData.isShow">
        <sm-captcha
          :options="options"
          @captchaReady="captchaReady"
          @success="captchaSuccess"
          @failure="captchaFailure"
          @error="captchaError"
          @close="captchaClose"
        >
        </sm-captcha>
      </view>
      <form @submit="doNewSubmit" :report-submit="true">
        <button class="btn__bg" form-type="submit" hover-class="defaultTap">
          确定
        </button>
      </form>
    </view>
  </div>
</template>

<script>
import { mapState } from 'vuex';
const API_SEND_MSG = '/Wxpassport/ChangeMobileCode';
const API_BIND_NEW = '/Wxpassport/ChangeMobile';
const API_GT = '/Wxpassport/geetestregister';
import config from '@/config';

export default {
  pageTrackConfig: 'sy_cards_mck_change_mobile_phone_page',
  data() {
    return {
      codeIndex: 0, // 国际码默认选择
      idIndex: '0086', // 国际码默认ID
      codeList: [], // 国际码列表
      sendText: '获取验证码', // 发送按钮文案
      second: 60,
      timer: null,
      mobile: '', // 手机号
      msgCode: '', // 验证码
      timeLock: false,
      showCountDown: false,
      gtData: {
        loadCaptcha: false,
        toReset: false,
        gt: '',
        challenge: '',
        success: 0,
        isShow: false,
        styleConfig: {
          color: '#ff6161',
          btnWidth: '325px'
        }
      },
      gtResult: null,
      plugin: null,
      // 数美参数配置
      options: {
        organization: 'vy4mR7dCx1I4xsC1fLWR', // 公司标识必填这里询问锦鲤
        product: 'popup', // popup展示方式，默认不会弹出，需要调用verify
        mode: 'select'
      }
    };
  },
  onLoad() {
    // eslint-disable-next-line no-undef
    this.plugin = requirePlugin('smCaptcha');
  },
  methods: {
    /**
     * 发送短信验证码
     */
    async sendMsg() {
      // let that = this
      if (this.timeLock) {
        return false;
      }
      if (this.mobile === '') {
        uni.showModal({
          content: '请填写手机号',
          showCancel: false
        });
        return false;
      }
      this.gtData.isShow = true;
      this.gtData.toReset = true;
      this.verify();
    },
    /**
     * 倒计时
     */
    runDownCount() {
      this.timeLock = true;
      this.second = 60;
      clearInterval(this.timer);
      this.timer = null;
      this.timer = setInterval(() => {
        if (this.second <= 1) {
          this.timeLock = false;
          clearInterval(this.timer);
          this.timer = null;
          this.second = 60;
          this.showCountDown = false;
        } else {
          this.second--;
          this.timeLock = true;
        }
      }, 1000);
    },
    /**
     * 请求发送验证码接口
     */
    async getMsg() {
      const data = {
        mobile: this.mobile,
        countryCode: this.idIndex
      };
      // 切换账号时使用passort域名和对应的接口
      const res = await this.$request({
        host: config.passportApi,
        url: API_SEND_MSG,
        method: 'POST',
        data: Object.assign(data, this.gtResult, {
          sm_verify_rid: this.gtResult.rid
        })
      }).catch((err) => {
        console.log(err);
        return false;
      });
      return res;
    },
    /**
     * 获取国际码
     */
    async getCountryCode() {
      const { data } = await this.$request({
        url: '/Wxpassport/GetCountryCodeList',
        data: {}
      });
      this.codeList = Object.values(data.responseData);
    },
    /**
     * 国际码切换
     */
    bindPickerChange(e) {
      console.log(e);
      this.codeIndex = e.detail.value;
      this.idIndex = this.codeList[this.codeIndex].id;
    },
    /**
     * 校验数据
     */
    checkVaild() {
      let title = '';
      let res = true;

      if (this.msgCode === '') {
        res = false;
        title = '请填写验证码';
      }
      if (this.mobile === '') {
        res = false;
        title = '请填写手机号';
      }
      if (!res) {
        uni.showModal({
          content: title,
          showCancel: false
        });
      }
      return res;
    },
    /**
     * 提交表单
     */
    async doNewSubmit() {
      if (!this.checkVaild()) {
        return false;
      }
      const res = await this.$request({
        host: config.passportApi,
        url: API_BIND_NEW,
        data: {
          mobile: this.mobile,
          countryCode: this.idIndex,
          code: this.msgCode,
          union_id: this.userInfo.unionId
        }
      }).catch((err) => {
        this.showError();
        console.log(err);
      });

      if (res?.data?.errorCode === 0) {
        uni.navigateBack();
      } else {
        this.showError(res.data.errorMsg);
      }
      this.$reportData({
        info: 'sy_cards_mck_change_mobile_phone:define_click'
      });
    },
    /**
     * 提示错误弹框
     */
    showError(msg = '服务器错误') {
      uni.showModal({
        content: msg,
        showCancel: false
      });
    },
    /**
     * 初始化极验验证码
     */
    async initGeeTest() {
      const res = await this.$request({
        url: API_GT,
        data: {}
      });
      const { gt, challenge, success } = res.data && res.data.responseData;
      this.gtData.gt = gt;
      this.gtData.challenge = challenge;
      this.gtData.success = success;
      this.gtData.loadCaptcha = true;
    },
    /**
     * 极验，验证正确的回调
     */
    async captchaSuccess(res) {
      console.log(res.detail, 'captcha-success');
      this.gtResult = res.detail;
      this.gtData.toReset = false;
      const resp = await this.getMsg();
      if (resp && resp.data.errorCode === 0) {
        this.showCountDown = true;
        this.runDownCount();
        this.gtData.isShow = false;
      } else {
        uni.showModal({
          content: resp.data.errorMsg,
          showCancel: false,
          success: (resp) => {
            if (resp.confirm) {
              this.gtData.toReset = true;
            }
          }
        });
      }
    },

    verify() {
      this.plugin.verify();
    },
    resetCaptcha() {
      // 某些场景需要初始化验证码
      this.plugin.reset();
    },
    captchaReady() {
      console.log('captcha-ready');
    },
    captchaClose() {
      console.log('captcha-Close');
    },
    captchaError(res) {
      console.log(res.detail, 'captcha-Error');
      this.resetCaptcha();
    },
    captchaFailure(res) {
      // 验证失败时触发
      console.log('我是验证失败的回调', res.detail);
    }
  },
  mounted() {
    this.initGeeTest();
    this.getCountryCode();
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    showText() {
      if (this.codeList.length === 0) {
        return '';
      }
      return this.codeList[this.codeIndex].name.match(/(\+\d*)/)[0];
    }
  }
};
</script>

<style lang="less" scoped>
@import url('./style/common.less');
</style>
