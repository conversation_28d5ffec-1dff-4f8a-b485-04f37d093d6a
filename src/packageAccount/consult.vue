<!-- 新氧优享咨询二维码 s-->
<template>
  <div>
    <div
      class="img-inner-wrap"
      v-if="joinc_check_flag === 1 && has_join_c === 1"
      @click="jump"
    >
      <img
        class="img-inner"
        :src="base_url"
        :show-menu-by-longpress="true"
        mode="widthFix"
      />
    </div>
    <div class="join" v-else>
      <img
        class="join__background_img"
        :src="poster_url"
        :show-menu-by-longpress="!code_url"
        mode="widthFix"
      />
      <img
        v-if="code_url"
        class="join__img"
        :src="code_url"
        :show-menu-by-longpress="true"
      />
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { apiGetAcodeUrl, apiUserUnionIdRelation } from '@/api/account';
export default {
  name: 'join',
  data() {
    return {
      codeImageFromUrl: '', // 传过来的code
      params: {},
      // map: {
      //   ********:
      //     'https://static.soyoung.com/sy-pre/********-145804-*************.jpeg',
      //   145: 'https://static.soyoung.com/sy-pre/********-160635-*************.jpeg',
      //   93: 'https://static.soyoung.com/sy-pre/********-184626-*************.jpeg',
      //   90: 'https://static.soyoung.com/sy-pre/14vdfqsyg5i6t-*************.png',
      //   47: 'https://static.soyoung.com/sy-pre/14rzexp5flh1h-*************.png',
      //   9: 'https://static.soyoung.com/sy-pre/********-162854-*************.jpeg',
      //   8: 'https://static.soyoung.com/sy-pre/2jvhf1xv06vjo-*************.png',
      //   7: 'https://static.soyoung.com/sy-pre/76xqcxp0i7cu-*************.png'
      // },
      code_url: '',
      has_join_c: 0,
      poster_url: '',
      base_url: '',
      base_jump_url: '',
      joinc_check_flag: 0
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin'])
  },
  methods: {
    async getConfigData() {
      try {
        const responseData = await apiGetAcodeUrl({
          ...this.params,
          market_activity_id: this.userInfo.marketActivityId
        });
        if (!responseData) return;
        const {
          code_url,
          has_join_c,
          poster_url,
          base_url,
          base_jump_url,
          joinc_check_flag
        } = responseData;
        this.code_url = code_url;
        this.has_join_c = has_join_c;
        this.poster_url = poster_url;
        this.base_url = base_url;
        this.base_jump_url = base_jump_url;
        this.joinc_check_flag = joinc_check_flag;
      } catch (err) {
        uni.$log(err, 'err');
      }
    },
    jump() {
      if (!this.base_jump_url) return;
      if (this.has_join_c !== 1) return;
      if (/^http(s)?/g.test(this.base_jump_url)) {
        this.$toH5(this.base_jump_url);
      } else {
        const url = this.base_jump_url.startsWith('/')
          ? this.base_jump_url
          : `/${this.base_jump_url}`;
        const path = [
          '/pages/index',
          '/pages/item',
          '/pages/coupon-center',
          '/pages/my'
        ];
        if (path.includes(url)) {
          uni.switchTab({
            url
          });
        } else {
          uni.navigateTo({
            url
          });
        }
      }
    }
  },
  async onLoad(opt) {
    const {
      img,
      type,
      city_id,
      spuid = '',
      skuid = '',
      scene = '',
      is_login = '', // 通过上游参数判断是否需要登录拦截，即加C落地页路由增加is_login参数，不带参数默认为不校验，赋值=1时则需要校验登录
      qzychannel = '',
      app_uid = '' // 加C落地页处增加uid参数，uid有值时调用用户组接口上报数据
      // https://soyoung.feishu.cn/wiki/Kwy3wapTAicdlQkYQvGcscJtnyg
    } = opt;
    if (Number(is_login) === 1) {
      if (!this.isLogin) {
        await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
      }
    } else {
      await this.$uniLogin();
    }
    uni.$log('consult', opt, 'info');
    this.codeImageFromUrl = decodeURIComponent(img);
    this.opt = opt;
    this.params = {
      type,
      city_id,
      channel: qzychannel, // 对外投放的加C页来源渠道
      spu_id: spuid, // 商品spu_id
      pid: skuid, // 商品 sku_id
      scene // 场景，售前1，售后2
    };
    this.getConfigData();
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_wx_customer_service_page',
      ext: {
        channel_id: qzychannel,
        scene_id: scene,
        business_ext: JSON.stringify(opt)
      }
    });
    if (app_uid) {
      apiUserUnionIdRelation({
        app_uid
      });
    }
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_wx_customer_service_page',
      ext: {
        channel_id: this.qzychannel,
        scene_id: this.scene,
        business_ext: JSON.stringify(this.opt)
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_wx_customer_service_page',
      ext: {
        channel_id: this.qzychannel,
        scene_id: this.scene,
        business_ext: JSON.stringify(this.opt)
      }
    });
  }
};
</script>

<style lang="less" scoped>
.img-inner-wrap {
  .img-inner {
    width: 100%;
    height: auto;
  }
}
.join {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  // background: url('https://static.soyoung.com/sy-pre/1j1q0qn0i87r2-1662106200711.jpg')
  //   no-repeat center top;
  // background-size: 100%;
  &__background_img {
    width: 100vw;
  }
  &__img {
    position: absolute;
    top: 856rpx;
    left: 50%;
    z-index: 10;
    transform: translateX(-50%);
    padding: 10rpx;
    width: 308rpx;
    height: 304rpx;
    background: #fff;
    border: 0.5px solid #a6a6a6;
  }
}
</style>
