<template>
  <div class="sign-middle">
    <img
      class="load-icon"
      src="https://static.soyoung.com/sy-design/32q76aat6dbg71736759178375.png"
      alt=""
    />
    <div class="load-str">加载中</div>
    <div class="load-desc">
      如未成功跳转，
      <div class="load-btn" @tap="goAuthFace">点击此处</div>
      手动跳转
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bizToken: '',
      goFaceDone: false,
      redirectUrl: ''
    };
  },
  onLoad(options) {
    console.log('---middle onLoad', options);
    uni.showToast({
      title: 'middle onLoad',
      icon: 'none'
    });
    this.bizToken = options.bizToken;
    this.redirectUrl = decodeURIComponent(options.redirectUrl);
    this.goAuthFace();
  },
  onShow() {
    const options = uni.getEnterOptionsSync();
    if (!this.goFaceDone) {
      return;
    }
    if (
      options.scene === 1038 &&
      options.referrerInfo.extraData &&
      options.referrerInfo.extraData.faceResult
    ) {
      const pages = getCurrentPages();
      const pre = pages[pages.length - 2];
      // 重新加载实名页面
      if (
        pre?.$vm &&
        pre?.$vm.reloadPage &&
        typeof pre?.$vm.reloadPage === 'function'
      ) {
        pre?.$vm.reloadPage(
          this.redirectUrl + `&timeStamp=${new Date().getTime()}`
        );
        uni.navigateBack({
          delta: 1
        });
      }
    }
  },
  methods: {
    goAuthFace() {
      this.goFaceDone = false;
      uni.navigateToMiniProgram({
        // envVersion: 'develop',
        appId: 'wx1cf2708c2de46337', // 公证签小程序APPID
        path: `/pages/face/index?bizToken=${this.bizToken}`, // 刷脸页面地址
        success: (res) => {
          console.log('success', res);
          this.goFaceDone = true;
        },
        fail: (err) => {
          console.log('???eError:', err);
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.sign-middle {
  background: #f2f2f2;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  .load-icon {
    margin-top: 194rpx;
    width: 70rpx;
    height: 70rpx;
  }
  .load-str {
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: #000000;
    text-align: center;
    font-weight: 500;
    margin-top: 20rpx;
    margin-bottom: 12rpx;
  }
  .load-desc {
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #030303;
    text-align: center;
    display: flex;
    .load-btn {
      font-family: PingFangSC-Medium;
      color: #000000;
      font-weight: 500;
      text-decoration: underline;
    }
  }
}
</style>
