<template>
  <view class="login-page">
    <nav-bar
      :title="' '"
      :hasFixedHome="!isShowReturnBtn"
      :hasBack="isShowReturnBtn"
      :background="'transparent'"
      @onBack="navBack"
    ></nav-bar>
    <img
      class="login__img"
      :class="{ 'showPhoneForm': showPhoneForm }"
      src="https://static.soyoung.com/sy-design/32q76aat6dbg71730863776553.png"
      alt=""
    />
    <div class="select-box">
      <p class="tips">
        <span class="tips__icon"></span>
        未满18周岁用户请开启未成年人模式
      </p>
      <div class="aggrees">
        <div class="select-icon" @click="onClickAgreement">
          <div class="agreement-tip" v-if="showAgreementTip">
            <span>请先阅读并同意协议</span>
            <span class="triangle"></span>
          </div>
          <span class="empty-circle">
            <span v-if="selectedAgreement" class="s-icon"></span>
          </span>
        </div>
        <div class="select-text">
          <span class="text" @click="onClickAgreement"
            >我已阅读并同意以下协议</span
          >
          <span class="link" @click="toUserH5('introduct/useAgreement')"
            >《新氧用户使用协议》</span
          >
          <span class="link" @click="toUserH5('introduct/privacy')"
            >《隐私政策》</span
          >
          <span
            class="link"
            @click="toUserH5('https://m.soyoung.com/policy/PreviewView?id=743')"
            >《新氧会员活动的条款和条件》</span
          >
        </div>
      </div>
      <div class="select-mode" v-show="!showPhoneForm">
        <template v-if="selectedAgreement">
          <button v-if="isSwitch" class="login__btn" @click="switchLogin()">
            手机号快捷登录
          </button>
          <button
            v-else
            open-type="getPhoneNumber"
            @getphonenumber="getPhone"
            class="login__btn"
          >
            手机号快捷登录
          </button>
        </template>
        <template v-else>
          <!-- 未选择已阅读协议 -->
          <button class="login__btn" @click="showAgreementTip = true">
            手机号快捷登录
          </button>
        </template>

        <button @tap="showForm" class="login__btn phone">短信验证码登录</button>
      </div>

      <!-- 手机号验证码登录表单 -->
      <view class="p-form" v-show="showPhoneForm">
        <view class="row">
          <picker
            @change="bindPickerChange"
            range-key="name"
            :value="codeIndex"
            :range="codeList"
          >
            <view class="picker">
              {{ showText }}
              <i class="picker__icon"></i>
            </view>
          </picker>
          <input
            class="phone-input"
            v-model="mobile"
            @input="inputMobile"
            maxlength="11"
            type="number"
            placeholder="请输入手机号码"
            placeholder-class="placeholder-custom"
            @focus="showClear = true"
            @blur="showClear = false"
          />
          <div
            class="clear-input adjust"
            v-if="mobile.length && showClear"
            @click="mobile = ''"
          >
            <img
              class="clear-input__icon"
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1725449464351.png"
              alt=""
            />
          </div>
        </view>
        <view class="row">
          <input
            class="code-input"
            bindinput="getCode"
            v-model="msgCode"
            type="number"
            placeholder="请输入验证码"
            placeholder-class="placeholder-custom"
          />
          <view
            class="code"
            @tap="sendMsg"
            v-show="!showCountDown"
            :class="{ waitClick: mobile != '' }"
          >
            <text>{{ sendText }}</text>
          </view>
          <view class="count-down" v-show="showCountDown">
            <text>{{ second }}秒</text>
          </view>
        </view>
        <view class="gt-box" v-show="gtData.isShow">
          <sm-captcha
            :options="options"
            @captchaReady="captchaReady"
            @success="captchaSuccess"
            @failure="captchaFailure"
            @error="captchaError"
            @close="captchaClose"
          >
          </sm-captcha>
        </view>
        <form @submit="doNewSubmit" :report-submit="true">
          <button
            class="btn__bg"
            form-type="submit"
            hover-class="defaultTap"
            :class="!msgCode || !mobile ? 'disabled' : ''"
          >
            确定
          </button>
        </form>
      </view>

      <div class="note" :class="{ isios: isIOS() }">
        未注册手机号将创建新氧账号
      </div>
    </div>

    <!-- 跨境隐私协议弹窗 -->
    <customPopup
      :title="crossBorderPrivacyInfo.title"
      class="cross-border-privacy-modal"
      v-model="crossBorderPrivacyVisible"
      width="300px"
    >
      <template #body>
        <div class="cross-border-privacy-body">
          <div class="cross-border-privacy-centent">
            <p
              class="desc-p"
              v-for="(info, index) in crossBorderPrivacyInfo.desc"
              :key="index"
            >
              <template v-if="!info.link">{{ info.text }}</template>
              <span
                @click="onClickDesc(index)"
                :style="
                  'color: ' +
                  (crossBorderPrivacyInfo.desc[index + 1].color
                    ? crossBorderPrivacyInfo.desc[index + 1].color
                    : '#000') +
                  ';'
                "
                v-if="
                  info.text.indexOf('参见') != -1 ||
                  info.text.indexOf('详见') != -1
                "
                >{{ crossBorderPrivacyInfo.desc[index + 1].text }}</span
              >
            </p>
          </div>
          <div class="cross-border-privacy-btns">
            <img
              class="btn-bg"
              src="https://static.soyoung.com/sy-pre/3b5ihx7zyczv2-1691392200871.png"
            />
            <div class="btn confrim" @click="onCrossBorderPrivacyConfrim">
              已阅读并同意
            </div>
          </div>
        </div>
      </template>
      <template #footer></template>
    </customPopup>
    <view class="bottomline"></view>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getCrossBorderPrivacy, setCrossBorderPrivacy } from '@/api/user';
import customPopup from '@/components/popup';

const API_BIND_NEW = '/Wxpassport/mobilebind';
const API_BIND_OLD = '/Wxpassport/usermobilebind';
const API_BIND_NEW_WX = '/Wxpassport/mobilebindbyencrypteddata';
const API_BIND_OLD_WX = '/Wxpassport/usermobilebindbyencrypteddata';
const API_CHANGE_WX = '/Wxpassport/wechatrebind';
const API_SEND_MSG = '/Wxpassport/getmobilebindcode';
const API_REPLACE_WECHAT = '/Wxpassport/replaceandbindnewthird'; // 半账号已经绑定微信换绑微信

// 切换账号时发送验证码和登录
const API_SWITCH_MSG = '/Wxpassport/QuickLoginCode';
const API_SWITCH_LOGIN = '/Wxpassport/QuickLogin';

import config from '@/config';
import NavBar from '@/components/NavBar';

import { getLoginBasicParameters } from '@/plugins/login';

import SyEncrypt from '@soyoung/SyEncrypt';
const syEncrypt = new SyEncrypt({
  key: 'x31e7B56uaTjob78'
});

export default {
  pageTrackConfig() {
    if (this.isSwitch) {
      return {
        info: 'lr_switching_accounts_page'
      };
    }
  },
  components: {
    NavBar,
    customPopup
  },
  data() {
    return {
      // 是否是切换账号
      isSwitch: false,
      isNew: true,
      showPhoneForm: false,
      codeIndex: 0, // 国际码默认选择
      idIndex: '0086', // 国际码默认ID
      codeList: [], // 国际码列表
      sendText: '获取验证码', // 发送按钮文案
      second: 60,
      timer: null,
      mobile: '', // 手机号
      msgCode: '', // 验证码
      timeLock: false,
      bindURL: '',
      showCountDown: false,
      gtData: {
        loadCaptcha: false,
        toReset: false,
        gt: '',
        challenge: '',
        success: 0,
        isShow: false,
        styleConfig: {
          color: '#ff6161',
          btnWidth: '325px'
        }
      },
      gtResult: null,
      showClear: false,
      isMy: false,
      isShowReturnBtn: false,
      plugin: null,
      // 数美参数配置
      options: {
        organization: 'vy4mR7dCx1I4xsC1fLWR', // 公司标识必填这里询问锦鲤
        product: 'popup', // popup展示方式，默认不会弹出，需要调用verify
        mode: 'select',
        width: '90%'
      },
      selectedAgreement: false, // 选择协议
      showAgreementTip: false, // 显示未选协议的提示
      mobileFrom: '', // 手机号来源,用于协助后端获取真实手机号： 1 用户手动输入的 2从后端拿的加密的
      crossBorderPrivacyInfo: {}, // 跨境隐私弹窗数据
      crossBorderPrivacyVisible: false,
      saveDataCache: {}, // 登录成功后，将要存入缓存的数据，临时存储在这里，用作登录的拦截
      eventChannel: null
    };
  },
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    showText() {
      if (this.codeList.length === 0) {
        return '';
      }
      return this.codeList[this.codeIndex].name.match(/(\+\d*)/)[0];
    }
  },
  methods: {
    async getPhone(wx) {
      this.bindURL = this.isNew ? API_BIND_NEW_WX : API_BIND_OLD_WX;
      console.log(wx.detail);
      if (wx.detail.errMsg === 'getPhoneNumber:ok') {
        const res = await this.bindMobileByWx(wx.detail);
        console.log('wx', res);
        if (!res) return;
        // issue/glimis 2020年09月12日09:48:38 ： 萌新判断 --> 绑定的手机号,为刚绑定的,可提供一次性活动
        uni.setStorageSync('is_new', res.is_new);
        // Wxpassport/usermobilebindbyencrypteddata 和 Wxpassport/mobilebindbyencrypteddata 返回的 login_mobile 作为 mobile 取请求 Wxpassport/LoginByMobile
        this.mobile = res.login_mobile;
        // 标识下手机号从后端接口读取的
        this.mobileFrom = 2;
        this.doLogin(res);
      }
    },
    showForm() {
      // 提示阅读协议
      if (!this.selectedAgreement) {
        this.showAgreementTip = true;
        return;
      }
      this.showPhoneForm = true;
      this.bindURL = this.isNew ? API_BIND_NEW : API_BIND_OLD;
      this.getCountryCode();
      if (this.isSwitch) {
        this.$reportData({
          info: 'lr_switching:mobile_login_click'
        });
      }
    },
    showCodes() {},
    /**
     * 发送短信验证码
     */
    async sendMsg() {
      // let that = this
      if (this.timeLock) {
        return false;
      }
      if (this.mobile === '') {
        uni.showModal({
          content: '请填写手机号',
          showCancel: false
        });
        return false;
      }
      this.gtData.isShow = true;
      this.gtData.toReset = true;
      this.verify();
    },
    /**
     * 倒计时
     */
    runDownCount() {
      this.timeLock = true;
      this.second = 60;
      clearInterval(this.timer);
      this.timer = null;
      this.timer = setInterval(() => {
        if (this.second <= 1) {
          this.timeLock = false;
          clearInterval(this.timer);
          this.timer = null;
          this.second = 60;
          this.showCountDown = false;
        } else {
          this.second--;
          this.timeLock = true;
        }
      }, 1000);
    },
    /**
     * 请求发送验证码接口
     */
    async getMsg() {
      const data = {
        mobileFrom: this.mobileFrom,
        mobile: syEncrypt.encrypt(this.mobile),
        countryCode: this.idIndex
      };
      // 切换账号时使用passort域名和对应的接口
      const res = await this.$request({
        host: this.isSwitch ? config.passportApi : config.baseApi,
        url: this.isSwitch ? API_SWITCH_MSG : API_SEND_MSG,
        method: 'POST',
        data: Object.assign(data, this.gtResult, {
          sm_verify_rid: this.gtResult.rid
        })
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试',
            responseData: null
          };
        });
      return res;
    },
    /**
     * 获取国际码
     */
    async getCountryCode() {
      const res = await this.$request({
        url: '/Wxpassport/GetCountryCodeList',
        data: {}
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试',
            responseData: null
          };
        });
      this.codeList = Object.values(res.responseData || {});
    },
    /**
     * 国际码切换
     */
    bindPickerChange(e) {
      console.log(e);
      this.codeIndex = e.detail.value;
      this.idIndex = this.codeList[this.codeIndex].id;
    },
    /**
     * 提交表单
     */
    async doNewSubmit() {
      // 标识手机号来源于用户输入
      this.mobileFrom = 1;
      // 提示阅读协议
      if (!this.selectedAgreement) {
        this.showAgreementTip = true;
        return;
      }
      if (!this.checkVaild()) {
        return false;
      }
      /**
       * 添加切换账号逻辑，
       * 当isSwitch为true时，直接使用新的登录接口
       * server张志强
       */
      if (this.isSwitch) {
        console.log('切换账号登录');
        const res = await this.switchMobileLogin();
        console.log(res, 'switch res');
        this.saveData(res);
        return false;
      }

      const res = await this.bindMobile();
      // issue/glimis 2020年09月12日09:48:38 ： 萌新判断 --> 绑定的手机号,为刚绑定的,可提供一次性活动

      res && uni.setStorageSync('is_new', res.is_new);

      this.doLogin(res);
    },
    // 手动输入手机时号时，绑定手机号或者老用户登录,点击提交第一个接口
    async bindMobile() {
      const res = await this.$request({
        url: this.bindURL,
        data: {
          mobileFrom: this.mobileFrom,
          mobile: syEncrypt.encrypt(this.mobile),
          countryCode: this.idIndex,
          code: this.msgCode,
          union_id: this.userInfo.unionId,
          nick_name: this.userInfo.nickName,
          avatar: this.userInfo.avatarUrl
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试'
          };
        });

      if (res.errorCode === 0) {
        return res.responseData;
      } else {
        this.showError('绑定手机号失败:' + res.errorMsg);
      }
    },
    /**
     * 从微信直接获取手机号时，绑定手机号或者老用户登录，
     * 此方法与 bindMobile 返回数据一样
     * @param {Object} detail 用户授权微信获取手机号后回调返回的加密数据
     */
    async bindMobileByWx(detail) {
      const res = await this.$request({
        url: this.bindURL,
        data: {
          sessionKey: this.userInfo.sessionKey,
          encryptedData: detail.encryptedData,
          iv: detail.iv,
          union_id: this.userInfo.unionId,
          nick_name: this.userInfo.nickName,
          avatar: this.userInfo.avatarUrl
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试'
          };
        });
      if (res.errorCode === 0) {
        return res.responseData;
      } else {
        this.showError('BIND_MOBILE:' + res.errorMsg);
        return null;
      }
    },
    /**
     * 根据bindMobile 方法返回的 action，做出各种处理
     */
    async doLogin(res) {
      switch (res?.action) {
        // 以下为type=1时的接口返回的action,即新用户调用的接口mobilebind
        case 'mobile_wechat_exist': {
          const flag = await this.ifChangeWechat();
          if (flag) {
            this.changeWechat();
          }
          break;
        }
        case 'bind_login_success':
          this.saveData(res);
          break;

        // 以下为type=3时的接口返回的action,即老用户调用的接口usermobilebind
        case 'mobile_in_use': {
          // const old_flag = await this.ifmobileLogin();
          // if (old_flag) {
          const login_res = await this.mobileLogin(res.key);
          this.saveData(login_res);
          // }
          break;
        }
        // node12
        case 'bind_success':
          this.saveData(res);
          break;

        case 'premature_user_account': {
          const temp_flag = await this.ifTempChangeWechat();
          if (temp_flag) {
            this.tempChangeWechat(res.key);
          }
          break;
        }
        // 此ation两个接口都有返回，做相同处理,[node18]
        case 'login_success':
          this.saveData(res);
          break;
      }
    },
    /**
     * 弹框--此手机已绑定其他微信号，是否变更为当前微信号
     */
    ifChangeWechat() {
      return new Promise((resolve) => {
        uni.showModal({
          title: '提示',
          content: '此手机已绑定其他微信号，是否变更为当前微信号',
          success: (res) => {
            if (res.confirm) {
              resolve(true);
            } else if (res.cancel) {
              this.mobile = '';
              this.msgCode = '';
            }
          }
        });
      });
    },

    /**
     * 请求--换绑微信号,节点9
     */
    async changeWechat() {
      const res = await this.$request({
        url: API_CHANGE_WX,
        data: {
          mobileFrom: this.mobileFrom,
          mobile: syEncrypt.encrypt(this.mobile),
          union_id: this.userInfo.unionId
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试'
          };
        });
      if (res.responseData?.action === 'login_success') {
        this.saveData(res.responseData);
        return true;
      }
      return false;
    },

    /**
     * 弹框--该手机号已被注册，是否使用该手机号继续登录
     */
    ifmobileLogin() {
      return new Promise((resolve) => {
        uni.showModal({
          content: '该手机号已被注册，是否使用该手机号继续登录',
          cancelText: '更换号码',
          confirmText: '是',
          success: (res) => {
            if (res.confirm) {
              resolve(true);
            } else if (res.cancel) {
              this.mobile = '';
              this.msgCode = '';
            }
          }
        });
      });
    },

    /**
     * 请求--用手机号登录，node13
     */
    async mobileLogin(key) {
      const res = await this.$request({
        url: '/Wxpassport/LoginByMobile',
        data: {
          key: key,
          union: this.userInfo.unionId,
          mobileFrom: this.mobileFrom,
          mobile: syEncrypt.encrypt(this.mobile)
        }
      }).catch((err) => {
        if (err.statusCode === 500) {
          this.showError('LOGINBYMOBILE');
          return false;
        }
      });
      return res.data.responseData;
    },

    /**
     * 弹框--半账号，手机已经绑定其他微信，是否有此微信替换,node17
     */
    ifTempChangeWechat() {
      return new Promise((resolve) => {
        uni.showModal({
          title: '提示',
          content: '此手机已经绑定其他微信，是否用此微信替换',
          cancelText: '不替换',
          confirmText: '替换',
          success: (res) => {
            if (res.confirm) {
              resolve(true);
            } else if (res.cancel) {
              this.mobile = '';
              this.msgCode = '';
            }
          }
        });
      });
    },

    /**
     * 请求--半账号，使用手机对应的uid登录，同时将原来绑定的微信替换成现在的微信，半账号作废
     */
    async tempChangeWechat(key) {
      const res = await this.$request({
        url: API_REPLACE_WECHAT,
        data: {
          union_id: this.userInfo.unionId,
          mobileFrom: this.mobileFrom,
          mobile: syEncrypt.encrypt(this.mobile),
          key: key
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试'
          };
        });
      if (res.responseData?.action === 'login_success') {
        this.saveData(res.data.responseData);
      } else {
        this.showError(res.errorMsg);
      }
    },
    /**
     * 校验数据
     */
    checkVaild() {
      // let title = '';
      let res = true;

      if (this.msgCode === '') {
        res = false;
        // title = '请填写验证码';
      }
      if (this.mobile === '') {
        res = false;
        // title = '请填写手机号';
      }
      // if (res) {
      //   uni.showModal({
      //     content: title,
      //     showCancel: false
      //   });
      // }

      return res;
    },
    /**
     * 提示登录成功，并保存uid和xy_token
     */
    async saveData(res) {
      console.log('saveData', res, res.uid, res.xy_token);
      if (res.uid && res.xy_token) {
        this.saveDataCache = {
          isTempFlag: false,
          uid: res.uid,
          xyToken: res.xy_token
        };
        try {
          // 跨境合规协议拦截
          this.getCrossBorderPrivacyFn();
        } catch (e) {
          console.log(e);
          this.loginSuccessLastStep();
        }
      } else {
        this.showError('登录失败，服务器数据有误');
        console.log('--------------没有返回uid或者xy_token----------');
      }
    },
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    toUserH5(url) {
      console.log(url);
      this.$bridge({
        url: '/pages/h5?url=' + encodeURIComponent(url)
      });
    },
    async switchLogin() {
      this.$reportData({ info: 'lr_switching:wechat_login_click' });
      this.$setUserInfoToStorage({
        uid: '',
        xyToken: ''
      });
      await this.$login(true);
      this.goBack();
    },
    /**
     * 切换账号使用手机直接登录
     */
    async switchMobileLogin() {
      const res = await this.$request({
        host: config.passportApi,
        url: API_SWITCH_LOGIN,
        data: {
          mobileFrom: this.mobileFrom,
          mobile: syEncrypt.encrypt(this.mobile),
          countryCode: this.idIndex,
          code: this.msgCode
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试'
          };
        });
      if (res.errorCode === 0) {
        return res.responseData;
      } else {
        this.showError(res.errorMsg);
      }
    },
    /**
     * 验证正确的回调
     */
    async captchaSuccess(res) {
      console.log(res.detail, 'captcha-success');
      this.gtResult = res.detail;
      this.gtData.toReset = false;
      const data = await this.getMsg();
      if (data?.errorCode === 0) {
        this.showCountDown = true;
        this.runDownCount();
        this.gtData.isShow = false;
      } else {
        uni.showModal({
          content: data.errorMsg,
          showCancel: false,
          success: (resp) => {
            if (resp.confirm) {
              this.gtData.toReset = true;
            }
          }
        });
      }
    },
    verify() {
      this.plugin.verify();
    },
    resetCaptcha() {
      // 某些场景需要初始化验证码
      this.plugin.reset();
    },
    captchaReady() {
      console.log('captcha-ready');
    },
    captchaClose() {
      console.log('captcha-Close');
    },
    captchaError(res) {
      console.log(res.detail, 'captcha-Error');
      this.resetCaptcha();
    },
    captchaFailure(res) {
      // 验证失败时触发
      console.log('我是验证失败的回调', res.detail);
    },
    /**
     * 提示错误弹框
     */
    showError(msg = '服务器错误') {
      uni.showModal({
        content: msg,
        showCancel: false
      });
    },
    navBack() {
      uni.navigateBack();
    },
    // 点击协议
    onClickAgreement() {
      this.selectedAgreement = !this.selectedAgreement;
      if (this.selectedAgreement) {
        this.showAgreementTip = false;
      }
    },
    inputMobile() {
      this.mobileFrom = 1;
    },
    // 是否需要弹合规弹窗-跨境隐私
    async getCrossBorderPrivacyFn() {
      const res = await getCrossBorderPrivacy({
        uid: this.saveDataCache.uid,
        xy_token: this.saveDataCache.xyToken
      });
      console.log(res);
      if (res && +res.has_accept === 0) {
        this.crossBorderPrivacyInfo = res;
        this.crossBorderPrivacyVisible = true;
        this.$reportData({
          info: res?.a_generalmd?.en?.en_exp,
          ext: {}
        });
      } else {
        this.loginSuccessLastStep();
      }
    },
    // 跨境隐私协议同意按钮点击
    onCrossBorderPrivacyConfrim() {
      this.$reportData({
        info: this.crossBorderPrivacyInfo?.a_generalmd?.en?.en_click,
        ext: {}
      });
      this.crossBorderPrivacyVisible = false;
      try {
        setCrossBorderPrivacy({
          uid: this.saveDataCache.uid,
          xy_token: this.saveDataCache.xyToken
        });
      } catch (e) {
        console.log(e);
      }
      this.loginSuccessLastStep();
    },
    // 登录成功后的最后一步，登录uid、xytoken 存入缓存，设置标记，回退上一页
    loginSuccessLastStep() {
      this.$setUserInfoToStorage(this.saveDataCache);
      // 通知登录成功了
      this.hasSaveData = true;
      // 取消点击退出登录标记
      uni.setStorageSync('clicked_my_page_signout_btn', 0);
      uni.showToast({
        title: '登录成功',
        icon: 'none',
        success: () => {
          this.goBack();
          uni.$emit('after_login_success_from_login_page');
        }
      });
    },
    onClickDesc(index) {
      console.log(index);
      const info = this.crossBorderPrivacyInfo.desc[index + 1];
      if (info.link) {
        this.$toH5(info.link);
      }
    }
  },
  async onLoad(options) {
    // eslint-disable-next-line no-undef
    this.plugin = requirePlugin('smCaptcha');
    if (options.type === '3') {
      this.isNew = false;
    }
    if (options.isSwitch) {
      this.isSwitch = true;
    }
    // 判断是否从我的页进入
    const pages = getCurrentPages(); // 页面对象
    const prevpage = pages[pages.length - 2]; // 上一个页面对象
    this.isShowReturnBtn = pages.length > 1;
    this.isMy =
      prevpage && prevpage.route && prevpage.route.includes('pages/my');
    // 登录事件的监控
    this.eventChannel = this.getOpenerEventChannel();
    this.eventChannel.once('afterReachLogin', () => {
      this.isTriggerLoginJs = true;
    });
  },
  onShow() {
    // 重新获取登录基础参数，session_key、union_id、openid，解决session_key过期导致的手机号解密失败问题
    getLoginBasicParameters();
  },
  onUnload() {
    if (this.isTriggerLoginJs) {
      if (this.hasSaveData) {
        this.eventChannel.emit('afterLoginSuccess');
      } else {
        this.eventChannel.emit('afterLoginFail', '页面退回');
      }
    }
    this.eventChannel = null;
  }
};
</script>

<style lang="less">
page {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
</style>
<style lang="less">
@import url('./style/common.less');
.login-page {
  height: 100vh;
  text-align: center;
  background: #f0f5f8
    url('https://static.soyoung.com/sy-design/2lhy1p7kocpyz1730970828965.png')
    no-repeat top center / 100%;
  background-size: 100%;
  .bottomline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 16rpx;
    background: #a9ea6a;
  }
  // .nav-bar {
  //   .back {
  //     position: absolute;
  //     left: 0;
  //     top: 0;
  //     height: 32px;
  //     .icon-left-arrow {
  //       width: 80rpx;
  //       height: 80rpx;
  //       background: url('https://static.soyoung.com/sy-pre/6hchdphe1xji-1649830200685.png') -22rpx -10rpx
  //         no-repeat;
  //       background-size: 100%;
  //     }
  //   }
  // }
  .login__img {
    padding: 290rpx 0 100rpx;
    width: 212rpx;
    height: 152rpx;
    &.showPhoneForm {
      padding-bottom: 100rpx;
    }
  }
  .select-box {
    text-align: center;
    font-size: 24rpx;
    .tips {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      // width: 650rpx;
      padding: 0 90rpx;
      box-sizing: border-box;
      height: 34rpx;
      line-height: 34rpx;
      font-family: PingFangSC-Regular;
      color: #333;
      margin: 0 auto 22rpx;
      font-size: 24rpx;
      &__icon {
        display: inline-block;
        margin-right: 20rpx;
        width: 24rpx;
        height: 24rpx;
        background: url('https://static.soyoung.com/sy-design/icon1726038721108.png')
          no-repeat;
        background-size: 100%;
      }
    }
    .aggrees {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #333333;
      font-weight: 400;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      // width: 650rpx;
      padding: 0 90rpx;
      box-sizing: border-box;
      line-height: 40rpx;
      margin: 0 auto 40rpx;
      .select-icon {
        width: 24rpx;
        height: 24rpx;
        cursor: pointer;
        position: relative;
        margin-right: 20rpx;
        flex: 0;
        .agreement-tip {
          width: 118 * 2rpx;
          height: 28 * 2rpx;
          text-align: center;
          line-height: 28 * 2rpx;
          background: rgba(0, 0, 0, 0.7);
          // border-radius: 8rpx;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #ffffff;
          text-align: center;
          font-weight: 400;
          position: absolute;
          top: -65rpx;
          left: -1rpx;
          z-index: 10;
          .triangle {
            position: absolute;
            left: 6rpx;
            bottom: -10rpx;
            width: 0;
            height: 0;
            line-height: 0;
            font-size: 0;
            border-top: 10rpx solid rgba(0, 0, 0, 0.7);
            border-right: 8rpx solid rgba(0, 0, 0, 0);
            border-bottom: 0rpx solid rgba(0, 0, 0, 0);
            border-left: 8rpx solid rgba(0, 0, 0, 0);
          }
        }
        .empty-circle {
          display: inline-block;
          width: 24rpx;
          height: 24rpx;
          border: 2rpx solid #333333;
          position: relative;
          margin-right: 10rpx;
          margin-left: 0rpx;
          box-sizing: border-box;
          .s-icon {
            display: inline-block;
            width: 16rpx;
            height: 16rpx;
            background-color: #333333;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            margin: auto;
          }
        }
      }
      .select-text {
        text-align: left;
        display: flex;
        flex-wrap: wrap;
      }
      .text {
        white-space: nowrap;
      }
      .link {
        font-family: PingFangSC-Medium;
        color: @text-color;
        white-space: nowrap;
        text-decoration: underline;
      }
    }
    .login__btn {
      margin-bottom: 40rpx;
      width: calc(100vw - 180rpx);
      height: 88rpx;
      line-height: 88rpx;
      color: #fff;
      font-size: 26rpx;
      font-family: PingFangSC-Regular;
      // background: #5448EE;
      background: @border-color;
      border-radius: 0;
      box-sizing: border-box;
      &::after {
        display: none;
      }
      &.phone {
        border: 1px solid @border-color;
        color: @text-color;
        background: rgba(#eff3f6, 0.7);
      }
    }
    .note {
      position: fixed;
      bottom: 40rpx;
      left: 0;
      width: 100%;
      text-align: center;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      &.isios {
        padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
        padding-bottom: env(safe-area-inset-bottom);
      }
      .link {
        color: #222;
      }
    }
  }
  .waitClick {
    color: #222;
  }
  .btn__bg {
    margin-top: 60rpx;
    border-radius: 0;
    font-size: 30rpx;
    font-family: PingFangSC-Regular;
    font-size: 13px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
}

// 跨境协议
.cross-border-privacy-body {
  position: relative;
}
.cross-border-privacy-centent {
  height: 600rpx;
  padding-bottom: 150rpx;
  overflow-y: scroll;
  .desc-p {
    text-align: left;
    text-indent: 56rpx;
    display: inline-block;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #333333;
    letter-spacing: 0;
    line-height: 40rpx;
    font-weight: 400;
  }
}
.cross-border-privacy-btns {
  text-align: center;
  margin-bottom: -30rpx;
  height: 150rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  bottom: 0;
  .btn-bg {
    display: inline-block;
    width: 100%;
    height: 150rpx;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .btn {
    box-sizing: border-box;
    display: inline-block;
    padding: 0;
    width: 128 * 2rpx;
    height: 60rpx;
    line-height: 30 * 2rpx;
    background: @border-color;
    border-radius: 42rpx;
    color: #fff;
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    z-index: 1;
  }
}

.p-form {
  padding: 0 70rpx;
  background: transparent;
}

.row {
  border-radius: 0;
  background: #fff;
  .picker {
    font-family: Outfit-Regular;
    font-size: 28rpx;
    color: #030303;
    letter-spacing: 0;
    font-weight: 400;
  }
  .code {
    color: #61b43e;
  }
  .phone-input {
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    letter-spacing: 0;
    font-weight: 400;
  }
  .code-input {
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    letter-spacing: 0;
    font-weight: 400;
  }
  .clear-input__icon {
    width: 40rpx;
    height: 40rpx;
  }
}
.btn__bg {
  width: calc(100vw - 140rpx);
  border: none;
  &:after {
    border: none;
  }
}
</style>
