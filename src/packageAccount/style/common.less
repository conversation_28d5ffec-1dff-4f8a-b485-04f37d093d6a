.p-form {
  background: #fff;
  padding: 0 50rpx;
}
.row {
  display: flex;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  justify-content: space-between;
  align-items: center;
  // border: 2rpx solid rgba(34,34,34,1);
  border-radius: 22 * 2rpx;
  background-color: #f6f9f9;
  .picker {
    display: flex;
    align-items: center;
    width: 98rpx;
    font-size: 30rpx;
    color: #222;
    height: 88rpx;
    &__icon {
      margin-left: 10rpx;
      width: 12rpx;
      height: 8rpx;
      background: url('https://static.soyoung.com/sy-design/1sntk1g3r416o1728616139092.png')
        no-repeat;
      background-size: 100%;
    }
  }
  .phone-input {
    width: 540rpx;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 30rpx;
    text-align: left;
  }
  .code {
    width: 160rpx;
    height: 48rpx;
    line-height: 48rpx;
    font-size: 26rpx;
    color: #aaabb3;
    border-radius: 26rpx;
    text-align: center;
  }
  .count-down {
    width: 160rpx;
    height: 48rpx;
    line-height: 48rpx;
    font-size: 24rpx;
    color: #222;
    text-align: right;
  }
  .code-input {
    width: 400rpx;
    height: 88rpx;
    line-height: 88rpx;
    font-size: 30rpx;
    text-align: left;
  }
  .clear-input {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    &.adjust {
      position: relative;
      left: 10px;
    }
    image {
      width: 26rpx;
      height: 26rpx;
    }
  }
}
.btn__bg {
  margin-top: 80rpx;
  width: 650rpx;
  height: 88rpx;
  line-height: 88rpx;
  color: #fff;
  font-size: 30rpx;
  font-family: PingFangSC-Medium;
  background: @border-color;
  border-radius: 0;
  border-radius: 22 * 2rpx;
}
.btn__bg.disabled {
  background: #aaabb3;
}
.gt-box {
  margin-bottom: 30rpx;
}
/deep/ .placeholder-custom {
  color: #aaabb3;
}
