<template>
  <div class="join">
    <img
      class="join__background_img"
      src="https://img2.soyoung.com/origin/********/8/c0e6abfac6dbf201f690074830d5aa10.jpg"
      mode="widthFix"
    />
    <div class="btn" @click="handleAuth" v-if="hasNoLocationAuth">
      授权地理位置
    </div>
    <img
      v-else
      class="join__img"
      :src="url"
      :show-menu-by-longpress="true"
      mode="widthFix"
    />
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getChannelCodeUrl } from '@/api/account';
export default {
  name: 'joinc',
  data() {
    return {
      hasNoLocationAuth: false,
      url: ''
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin'])
  },
  methods: {
    handleAuth() {
      wx.showToast({
        title: '将跳转设置，请打开定位权限',
        icon: 'none'
      });
      wx.openSetting();
    },
    // 获取地理位置定位
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        return null;
      });
      this.hasNoLocationAuth = !(res?.lat && res?.lng);
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    },
    async getConfigData() {
      const scopeUserLocation = await this.checkScopeOfUserLocation();
      if (scopeUserLocation === false) {
        this.hasNoLocationAuth = true;
      } else {
        this.hasNoLocationAuth = false;
        await this.getLocation();
      }
      const responseData = await getChannelCodeUrl({
        city_id: this.userInfo.cityId
      });
      if (!responseData) return;
      this.url = responseData;
    }
  },
  onShow() {
    this.getConfigData();
  },
  onHide() {},
  onUnload() {}
};
</script>

<style lang="less" scoped>
.join {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  &__background_img {
    width: 100vw;
  }
  &__img {
    position: absolute;
    top: 856rpx;
    left: 50%;
    z-index: 10;
    transform: translateX(-50%);
    padding: 10rpx;
    width: 308rpx;
    height: 304rpx;
    background: #fff;
    border: 0.5px solid #a6a6a6;
  }
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70rpx;
    width: 200rpx;
    font-size: 26rpx;
    color: #fff;
    background-color: #030303;
    position: absolute;
    top: 1000rpx;
    left: 50%;
    z-index: 10;
    transform: translateX(-50%);
  }
}
</style>
