<!-- 新氧优享咨询二维码 -->
<template>
  <div class="consult-reduce-ctn">
    <nav-bar :hasFixedHome="true"></nav-bar>
    <div class="consult-reduce" v-if="!loading">
      <div
        class="reduce-title"
        :style="{ 'background-image': `url(${headImage})` }"
      >
        <div @click="getRules" class="reduce-title-rule">活动规则</div>
      </div>
      <div class="reduce-content" :style="backgroundImg">
        <div class="reduce-package">
          <div class="reduce-package-title">
            <div class="reduce-package-tl"></div>
            <div class="reduce-package-tr" v-if="!noPackage">
              <div>距离结束仅剩</div>
              <div class="tr-time">
                <div>{{ hour }}</div>
                <span>:</span>
                <div>{{ minute }}</div>
                <span>:</span>
                <div>{{ second }}</div>
              </div>
            </div>
          </div>
          <div class="reduce-package-cont">
            <template v-if="noPackage">
              <!-- 没有优惠券的样式 -->
              <div class="reduce-package-cont-p no-package">
                <img
                  class="red-package-img"
                  src="https://static.soyoung.com/sy-pre/am7qcncef55s-1684393800719.png"
                />
                <div class="tips">暂无可领优惠券，敬请期待～</div>
                <div class="btn" @click="goToHome">前往首页</div>
              </div>
            </template>
            <div class="reduce-package-cont-p" v-else>
              <!-- 有优惠券的样式 -->
              <scroll-view
                scroll-x="true"
                scroll-y="false"
                @scroll="viewScroll"
                class="package2-ctn"
              >
                <!-- 第一行 -->
                <div class="first-cont">
                  <div class="package-1" v-if="packageListT.length == 1">
                    <div
                      v-for="(item, index) in packageListT"
                      :key="index"
                      class="pkg-1-item"
                    >
                      <div class="pkg-1-item-l item-tixian-ctn">
                        <span v-if="item.ticket_type == 2" class="span-m-txt"
                          >最高可领</span
                        >
                        <span v-if="item.is_discount != 1"
                          ><span class="span-m">￥</span
                          >{{
                            item.ticket_type == 1 || item.ticket_type == 3
                              ? item.price_deposit_cutdown || 0
                              : item.back_money || 0
                          }}
                        </span>
                        <span v-if="item.is_discount == 1"
                          >{{ item.discount_rate }}
                          <span class="zhe-txt">折</span></span
                        >
                        <span v-if="item.ticket_type == 2" class="item-tixian"
                          ><img
                            src="https://static.soyoung.com/sy-pre/y6y3y3jabrwx-1672819800673.png"
                          />可微信提现</span
                        >
                      </div>
                      <div class="pkg-1-item-r">
                        <div>
                          <span v-if="item.ticket_type == 2"
                            >活动时间内消费立返</span
                          >
                        </div>
                        <div>
                          <span v-if="item.money_min > 0"
                            >满{{ item.money_min }}元可使用</span
                          >
                          <span v-else>无门槛</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="package-2"
                    v-if="packageListT.length == 2 || packageListT.length == 3"
                  >
                    <div
                      v-for="(item, index) in packageListT"
                      :key="index"
                      class="pkg-2-item"
                      :class="{
                        'pkg-3-item': packageListT.length == 3,
                        'pkg-3-item-z':
                          packageListT.length == 3 && packageListB.length > 3
                      }"
                    >
                      <div class="pkg-2-item-top">
                        <span v-if="item.ticket_type == 2" class="span-m-txt"
                          >最高可领</span
                        >
                        <span v-if="item.is_discount != 1"
                          ><span class="span-m">￥</span
                          >{{
                            item.ticket_type == 1 || item.ticket_type == 3
                              ? item.price_deposit_cutdown || 0
                              : item.back_money || 0
                          }}
                        </span>
                        <span v-if="item.is_discount == 1"
                          >{{ item.discount_rate
                          }}<span class="zhe-txt">折</span></span
                        >
                      </div>
                      <div class="pkg-2-item-btm item-tixian-ctn">
                        <span v-if="item.ticket_type != 2">
                          <span v-if="item.money_min > 0"
                            >满{{ item.money_min }}元可使用</span
                          >
                          <span v-else>无门槛</span>
                        </span>
                        <span class="item-tixian item-tixian-back" v-else
                          ><img
                            src="https://static.soyoung.com/sy-pre/y6y3y3jabrwx-1672819800673.png"
                          />可微信提现</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="package-3" v-if="packageListT.length > 3">
                    <div
                      v-for="(item, index) in packageListT"
                      :key="index"
                      class="pkg-3m-item"
                    >
                      <div class="pkg-2-item-top">
                        <span v-if="item.ticket_type == 2" class="span-m-txt"
                          >最高可领</span
                        >
                        <span v-if="item.is_discount != 1"
                          ><span class="span-m">￥</span
                          >{{
                            item.ticket_type == 1 || item.ticket_type == 3
                              ? item.price_deposit_cutdown || 0
                              : item.back_money || 0
                          }}
                        </span>
                        <span v-if="item.is_discount == 1"
                          >{{ item.discount_rate
                          }}<span class="zhe-txt">折</span></span
                        >
                      </div>
                      <div class="pkg-2-item-btm item-tixian-ctn">
                        <span v-if="item.ticket_type != 2">
                          <span v-if="item.money_min > 0"
                            >满{{ item.money_min }}元可使用</span
                          >
                          <span v-else>无门槛</span>
                        </span>
                        <span class="item-tixian item-tixian-back" v-else
                          ><img
                            src="https://static.soyoung.com/sy-pre/y6y3y3jabrwx-1672819800673.png"
                          />可微信提现</span
                        >
                      </div>
                    </div>
                    <div class="package-3-r"></div>
                  </div>
                </div>
                <!-- 第二行 -->
                <div class="second-cont">
                  <div class="package-1" v-if="packageListB.length == 1">
                    <div class="pkg-1-item"></div>
                  </div>
                  <div
                    class="package-2"
                    v-if="packageListB.length == 2 || packageListB.length == 3"
                  >
                    <div
                      v-for="(item, index) in packageListB"
                      :key="index"
                      class="pkg-2-item"
                      :class="{ 'pkg-3-item': packageListB.length == 3 }"
                    >
                      <div class="pkg-2-item-top">
                        <span v-if="item.ticket_type == 2" class="span-m-txt"
                          >最高可领</span
                        >
                        <span v-if="item.is_discount != 1"
                          ><span class="span-m">￥</span
                          >{{
                            item.ticket_type == 1 || item.ticket_type == 3
                              ? item.price_deposit_cutdown || 0
                              : item.back_money || 0
                          }}
                        </span>
                        <span v-if="item.is_discount == 1"
                          >{{ item.discount_rate
                          }}<span class="zhe-txt">折</span></span
                        >
                      </div>
                      <div class="pkg-2-item-btm item-tixian-ctn">
                        <span v-if="item.ticket_type != 2">
                          <span v-if="item.money_min > 0"
                            >满{{ item.money_min }}元可使用</span
                          >
                          <span v-else>无门槛</span>
                        </span>
                        <span class="item-tixian item-tixian-back" v-else
                          ><img
                            src="https://static.soyoung.com/sy-pre/y6y3y3jabrwx-1672819800673.png"
                          />可微信提现</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="package-3" v-if="packageListB.length > 3">
                    <div
                      v-for="(item, index) in packageListB"
                      :key="index"
                      class="pkg-3m-item"
                    >
                      <div class="pkg-2-item-top">
                        <span v-if="item.ticket_type == 2" class="span-m-txt"
                          >最高可领</span
                        >
                        <span v-if="item.is_discount != 1"
                          ><span class="span-m">￥</span
                          >{{
                            item.ticket_type == 1 || item.ticket_type == 3
                              ? item.price_deposit_cutdown || 0
                              : item.back_money || 0
                          }}
                        </span>
                        <span v-if="item.is_discount == 1"
                          >{{ item.discount_rate
                          }}<span class="zhe-txt">折</span></span
                        >
                      </div>
                      <div class="pkg-2-item-btm item-tixian-ctn">
                        <span v-if="item.ticket_type != 2">
                          <span v-if="item.money_min > 0"
                            >满{{ item.money_min }}元可使用</span
                          >
                          <span v-else>无门槛</span>
                        </span>
                        <span class="item-tixian item-tixian-back" v-else
                          ><img
                            src="https://static.soyoung.com/sy-pre/y6y3y3jabrwx-1672819800673.png"
                          />可微信提现</span
                        >
                      </div>
                    </div>
                    <div class="package-3-r"></div>
                  </div>
                </div>
              </scroll-view>
              <div
                v-if="packageListT.length > 3 || packageListB.length > 3"
                class="reduce-scroll"
              >
                <div
                  :style="{
                    transform: `translateX(${
                      (sWidth - sWidthlit) * percent + 'px'
                    })`
                  }"
                  class="reduce-itm"
                ></div>
              </div>
              <!-- 未加c展示二维码的情况 -->
              <div
                v-if="(!isJoinC || (isJoinC && hasEnd)) && maImage"
                class="package2-btm"
              >
                <span v-if="!hasEnd && !isJoinC"
                  >识别下方二维码，成功添加顾问/社区后即可领取</span
                >
                <span v-if="hasEnd"
                  >本场<span class="package2-btm-red">活动已结束</span
                  >，添加客服获取最新福利</span
                >
                <div class="jian-block"></div>
              </div>
              <!-- 已加c或未配图显示领取状态 -->
              <div
                v-if="isJoinC || !maImage"
                :class="{ 'got-btn': unreceived_ticket_num == 0 || hasEnd }"
                class="get-btn"
                @click="getPackage"
              >
                {{
                  hasEnd
                    ? '活动已结束'
                    : unreceived_ticket_num == 0
                    ? '已领取'
                    : '全部领取'
                }}
              </div>
            </div>
            <div class="reduce-package-cont-img-c" v-if="!noPackage">
              <img
                v-if="!isJoinC && maImage"
                class="reduce-package-cont-img"
                src="https://static.soyoung.com/sy-pre/339khpxug38xg-1672297800695.png"
              />
            </div>
          </div>
        </div>
        <img
          v-if="noPackage"
          class="soyoung-logo"
          mode="widthFix"
          src="https://static.soyoung.com/sy-pre/20230518-161843-1684397400722.png"
        />
        <template v-else>
          <!-- 未加c或活动结束 -->
          <div v-if="(!isJoinC || hasEnd) && maImage" class="reduce-jiac">
            <!-- src="https://static.soyoung.com/sy-pre/2aj1nm6mqbboh-1672726200752.png" -->
            <img
              mode="widthFix"
              src="https://static.soyoung.com/sy-pre/29dvus6sdwz2d-1678774200720.png"
            />
            <img class="reduce-jiac-ma" show-menu-by-longpress :src="maImage" />
            <image
              class="hand-btn"
              src="https://static.soyoung.com/sy-pre/iej5gqj32r3x-1662430200668.png"
            ></image>
            <img
              class="reduce-jiac-btn"
              src="https://static.soyoung.com/sy-pre/1uytlxpca301w-1672719000682.png"
            />
          </div>
          <div :class="{ 'reduce-btm-ctn-c': isJoinC }" class="reduce-btm-ctn">
            <div
              v-if="proList.length"
              class="reduce-p-title"
              :style="[isJoinC ? 'padding-top:0rpx' : '']"
            >
              <img
                src="https://static.soyoung.com/sy-pre/3hlmqaji9mmju-1672297800695.png"
              />
              <span>适用商品</span>
              <img
                src="https://static.soyoung.com/sy-pre/3hlsewpk3y08x-1672297800695.png"
              />
            </div>
            <div v-if="proList.length" class="reduce-p">
              <div
                v-for="(item, index) in proList"
                @click="toProduct(item, index)"
                :key="index"
                :data-pid="item.pid"
                :data-materialid="item.material_id"
                :data-index="index + 1"
                class="reduce-p-item"
              >
                <div class="item-left">
                  <img :src="item.cover_img" />
                </div>
                <div class="item-right">
                  <div class="item-right-title">
                    {{ item.title }}
                  </div>
                  <div class="item-right-cont">
                    <img
                      v-if="item.floor_price > 0"
                      src="https://static.soyoung.com/sy-pre/3skcgkrkjl6wt-1672301400661.png"
                    />
                    <div
                      class="item-right-cont-o"
                      :class="{ 'item-right-cont-o-b': item.floor_price <= 0 }"
                    >
                      <span>￥</span>
                      {{ item.price_online }}
                    </div>
                    <div class="item-right-cont-n">
                      <div
                        v-if="item.floor_price > 0"
                        class="item-right-cont-n-c"
                      >
                        <span class="item-right-cont-m">￥</span
                        >{{ item.floor_price }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div v-if="rulesTxt" @click="rulesTxt = false" class="reduce-dialog-rule">
      <div @click.stop="() => {}" class="reduce-dialog-ctn">
        <div class="dialog-ctn-t">
          活动规则
          <div @click="rulesTxt = false" class="close-btn"></div>
        </div>
        <div class="dialog-ctn-txt">
          <div v-html="ruleT"></div>
        </div>
      </div>
    </div>
    <PageLoading :visible="mixin_login_doing" />
  </div>
</template>

<script>
import {
  joinCIndex,
  checkUserJoinC,
  joinCRule,
  joinCReceive
} from '@/api/consult-reduce.js';
import NavBar from '@/components/NavBar';
import SubscribeMixins from '@/mixins/subscribe';
import PageLoading from '@/components/pageLoading';

import keepSessionMixins from '@/components/confirm-order/keepSession';

export default {
  name: 'joinC',
  components: {
    PageLoading,
    NavBar
  },
  mixins: [SubscribeMixins, keepSessionMixins],
  data() {
    return {
      ruleT: '',
      rulesTxt: false,
      activity_id: '',
      unreceived_ticket_num: 1,
      hasEnd: false,
      isJoinC: false,
      maImage: '',
      headImage: '',
      backgroundImg: '',
      cWidth: 0,
      sWidth: 0,
      sWidthlit: 0,
      percent: 0,
      endTime: '',
      hour: '00',
      minute: '00',
      second: '00',
      proList: [],
      packageListT: [],
      packageListB: [],
      loading: true
    };
  },
  computed: {
    // 无优惠券
    noPackage() {
      let flag = false;
      if (this.packageListT.length === 0 && this.packageListB.length === 0) {
        flag = true;
      }
      return flag;
    }
  },
  methods: {
    getElInfo() {
      const info = uni.createSelectorQuery().select('.package2-ctn');
      info &&
        info
          .boundingClientRect((data) => {
            // data - 各种参数
            data && (this.cWidth = data.width);
          })
          .exec();
      const info1 = uni.createSelectorQuery().select('.reduce-scroll');
      info1 &&
        info1
          .boundingClientRect((data) => {
            // data - 各种参数
            data && (this.sWidth = data.width);
          })
          .exec();
      const info2 = uni.createSelectorQuery().select('.reduce-itm');
      info2 &&
        info2
          .boundingClientRect((data) => {
            // data - 各种参数
            data && (this.sWidthlit = data.width);
          })
          .exec();
    },
    viewScroll(e) {
      const w = e.target.scrollWidth;
      const l = e.target.scrollLeft;
      this.percent = l / (w - this.cWidth);
    },
    toProduct(item, index) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_youhuiquan_c:pro_list_click',
        ext: {
          product_id: item.pid || item.material_id,
          serial_num: index + 1
        }
      });
      const url = `/pages/product?material_id=${item.material_id}&material_type=${item.material_type}&sku_id=${item.pid}`;
      this.$bridge({
        url
      });
    },
    setFeedExpose() {
      this.feedExpose?.disconnect?.();
      this.feedExpose = this.$registerExposure(
        '.reduce-p-item',
        (info) => {
          const data = info?.dataset || {};
          try {
            this.$reportData({
              info: 'sy_wxtuan_tuan_youhuiquan_c:pro_list_exposure',
              ext: {
                product_id: data.pid || data.materialid,
                serial_num: data.index
              }
            });
          } catch (err) {
            console.log(err);
          }
        },
        this
      );
    },
    async getInfo() {
      const responseData = await joinCIndex({
        activity_id: this.activity_id
      });
      if (responseData) {
        const {
          end_time = '',
          product_list,
          join_c_img = '',
          cover_img = '',
          background_img = '',
          unreceived_ticket_num = 0,
          ticket_list
        } = responseData;
        this.endTime = end_time;
        this.proList = product_list || [];
        this.maImage = join_c_img;
        this.headImage = cover_img;
        this.backgroundImg = background_img
          ? `background-image:url(${background_img})`
          : '';
        this.unreceived_ticket_num = unreceived_ticket_num;
        this.countTime();
        this.tInterval = setInterval(this.countTime, 1000);
        this.setPackage(ticket_list || []);
        this.$nextTick(() => {
          this.getElInfo();
          this.setFeedExpose();
        });
        this.loading = false;
      }
    },
    async jiacType() {
      const responseData = await checkUserJoinC({});
      if (responseData) {
        this.isJoinC = responseData.is_join_c;
      }
    },
    async getRules() {
      const responseData = await joinCRule({});
      if (responseData) {
        this.ruleT = responseData;
        this.rulesTxt = true;
      }
    },
    async getPackage() {
      if (this.unreceived_ticket_num === 0 || this.hasEnd) {
        return;
      }
      this.$reportData({
        info: 'sy_wxtuan_tuan_bt:lingqu_click',
        ext: {
          activity_id: this.activity_id
        }
      });
      const { errorCode, errorMsg } = await joinCReceive({
        activity_id: this.activity_id
      });
      let msg = '领取成功';
      if (errorCode === 200 || errorCode === 0) {
        this.unreceived_ticket_num = 0;
        this.tempMessage();
      } else if (errorMsg) {
        msg = errorMsg;
      }
      uni.showToast({
        title: msg,
        icon: 'none'
      });
    },
    // 触发模板消息
    tempMessage() {
      this.createSubscribe({
        '-T_JKqBVyqol6W0O_fKSsUreGchqleENO0qIXXqrmDg': [25]
      }).then((subscribe) => {
        subscribe?.();
      });
    },
    setPackage(pkgList) {
      const len = pkgList.length;
      if (len <= 3) {
        this.packageListT = pkgList;
      } else if (len === 4 || len === 6) {
        this.packageListT = pkgList.slice(0, len / 2);
        this.packageListB = pkgList.slice(len / 2);
      } else {
        // 奇数
        if (len % 2 === 1) {
          this.packageListT = pkgList.slice(0, (len - 1) / 2);
          this.packageListB = pkgList.slice((len - 1) / 2);
        } else {
          this.packageListT = pkgList.slice(0, len / 2);
          this.packageListB = pkgList.slice(len / 2);
        }
      }
    },
    countTime() {
      const date = new Date();
      const now = date.getTime();
      const endDate = new Date(this.endTime.replace(/-/g, '/')); // 设置截止时间
      const end = endDate.getTime();
      const leftTime = end - now; // 时间差
      let h, m, s, ms;
      if (leftTime >= 0) {
        // d = Math.floor(leftTime / 1000 / 60 / 60 / 24);
        h = Math.floor((leftTime / 1000 / 60 / 60) % 24);
        m = Math.floor((leftTime / 1000 / 60) % 60);
        s = Math.floor((leftTime / 1000) % 60);
        ms = Math.floor(leftTime % 1000);
        if (ms < 100) {
          ms = '0' + ms;
        }
        if (s < 10) {
          s = '0' + s;
        }
        if (m < 10) {
          m = '0' + m;
        }
        if (h < 10) {
          h = '0' + h;
        }
      } else {
        console.log('已截止');
        clearInterval(this.tInterval);
        this.hasEnd = true;
      }
      // 将倒计时赋值到div中
      // document.getElementById("_d").innerHTML = d + "天";
      this.hour = h || '00';
      this.minute = m || '00';
      this.second = s || '00';
      // document.getElementById("_ms").innerHTML = ms + "毫秒";
      // 递归每秒调用countTime方法，显示动态时间效果
      // setTimeout(this.countTime, 50);
    },
    // 跳转首页
    goToHome() {
      uni.switchTab({
        url: '/pages/index'
      });
    }
  },
  async onShow() {
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_youhuiquan_c_page',
      ext: {
        activity_id: this.activity_id
      }
    });
    if (!(await this.keepSession())) return;
    this.jiacType();
    this.getInfo();
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_youhuiquan_c_page',
      ext: {
        activity_id: this.activity_id
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_youhuiquan_c_page',
      ext: {
        activity_id: this.activity_id
      }
    });
  },
  onShareAppMessage() {
    const res = {
      title: '【限时补贴】您有一个专属神券礼包待领取，现金券可提现，速戳福利>>',
      path: `/packageAccount/consultReduce?activity_id=${
        this.activity_id || ''
      }`
    };
    return res;
  },
  onLoad(options) {
    const { activity_id } = options;
    this.activity_id = activity_id;
  }
};
</script>

<style lang="less" scoped>
.close-btn {
  position: absolute;
  right: 20rpx;
  top: 30%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background: url('https://static.soyoung.com/sy-pre/56ro94fk2w7q-*************.png')
    center/contain no-repeat;
}
.zhe-txt {
  font-size: 30rpx;
}
.consult-reduce-ctn {
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}
.reduce-dialog-rule {
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  z-index: 999;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  .reduce-dialog-ctn {
    width: 650rpx;
    min-height: 300rpx;
    background: #fff;
    border-radius: 20rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 9;
    .dialog-ctn-t {
      font-family: PingFangSC-Medium;
      font-size: 34rpx;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
      margin-top: 30rpx;
      margin-bottom: 20rpx;
      position: relative;
    }
    .dialog-ctn-txt {
      width: 100%;
      text-align: left;
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      line-height: 48rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      padding: 0 40rpx 50rpx;
      box-sizing: border-box;
      max-height: 750rpx;
      overflow-y: auto;
    }
  }
  .reduce-dialog-hongbao {
    width: 514rpx;
    height: 334rpx;
    position: absolute;
    left: 50%;
    top: 474rpx;
    transform: translate(-50%);
    z-index: 8;
  }
}
.consult-reduce {
  @keyframes longPress {
    from {
      transform: scale(1);
    }
    25% {
      transform: scale(0.9);
    }
    50% {
      transform: scale(0.9);
    }
    75% {
      transform: scale(0.9);
    }
    to {
      transform: scale(1);
    }
  }
  .hand-btn {
    position: absolute;
    z-index: 99;
    width: 210rpx;
    height: 146rpx;
    right: -20rpx;
    bottom: 18rpx;
    animation-name: longPress;
    animation-duration: 2s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
  }
  .reduce-title {
    width: 100%;
    height: 560rpx;
    // background-image: url(https://static.soyoung.com/sy-pre/pscbjpv3n3tc-1672121400682.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    .reduce-title-rule {
      position: absolute;
      right: 0;
      top: 198rpx;
      background: rgba(255, 255, 255, 0.37);
      border-radius: 22rpx 0 0 22rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 400;
      width: 136rpx;
      height: 45rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .item-tixian-ctn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .item-tixian {
      font-family: PingFangSC-Medium;
      font-size: 22rpx;
      color: #00a77d;
      text-align: center;
      font-weight: 500;
      display: flex;
      align-items: center;
      margin-top: -6rpx;
      padding: 3rpx 13rpx;
      img {
        width: 24rpx;
        height: 20rpx;
        margin-right: 4rpx;
      }
    }
    .item-tixian-back {
      display: block;
      background: #ffffff;
      border-radius: 18rpx;
      margin-bottom: -6rpx;
    }
  }
  .reduce-content {
    width: 100%;
    height: auto;
    min-height: 573 * 2rpx;
    // background: url(https://static.soyoung.com/sy-pre/20221227-151008-1672125000674.png);
    background: url(https://static.soyoung.com/sy-pre/329mmel400qds-1678774200720.png);
    background-size: 100% auto;
    background-repeat: repeat-y;
    position: relative;
    box-sizing: border-box;
    .reduce-jiac {
      margin-top: -202rpx;
      position: relative;
      img {
        width: 100%;
      }
      .reduce-jiac-btn {
        width: 580rpx;
        height: 120rpx;
        position: absolute;
        bottom: 72rpx;
        left: 50%;
        transform: translate(-50%);
      }
      .reduce-jiac-ma {
        width: 220rpx;
        height: 220rpx;
        position: absolute;
        left: 50%;
        transform: translate(-50%);
        top: 164rpx;
      }
    }
    .reduce-p {
      width: 710rpx;
      margin: 0 auto;
      border-radius: 20rpx;
      background: #ffffff;
      padding: 10rpx 20rpx;
      box-sizing: border-box;
      box-shadow: inset 0 0 28rpx 0 rgba(255, 152, 144, 0.25);
      .reduce-p-item {
        display: flex;
        // justify-content: space-between;
        border-bottom: 2rpx solid #f0f0f0;
        padding-top: 30rpx;
        padding-bottom: 24rpx;
        .item-left {
          img {
            width: 228rpx;
            height: 184rpx;
            border-radius: 8rpx;
          }
        }
        .item-right {
          font-family: PingFangSC-Medium;
          font-size: 30rpx;
          color: #222222;
          line-height: 48rpx;
          font-weight: 500;
          box-sizing: border-box;
          padding-left: 20rpx;
          .item-right-title {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            overflow: hidden;
            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
            min-height: 2em;
          }
          .item-right-cont {
            position: relative;
            img {
              position: absolute;
              width: 280rpx;
              height: 40rpx;
              top: -15rpx;
              left: 4rpx;
            }
            .item-right-cont-o {
              font-family: PingFangSC-Regular;
              font-size: 22rpx;
              color: #999999;
              text-align: center;
              line-height: 32rpx;
              font-weight: 400;
              text-align: left;
              margin-top: 16rpx;
            }
            .item-right-cont-o-b {
              font-family: PingFangSC-Semibold;
              font-size: 32rpx;
              color: #fb444f;
              font-weight: 600;
              span {
                margin-right: -4rpx;
              }
            }
            .item-right-cont-n {
              font-family: PingFangSC-Semibold;
              font-size: 32rpx;
              height: 50rpx;
              color: #fb444f;
              font-weight: 600;
              text-align: left;
              // padding-left: 80rpx;
              box-sizing: border-box;
              position: relative;
              .item-right-cont-n-c {
                position: absolute;
                left: 140rpx;
                transform: translate(-50%);
                .item-right-cont-m {
                  font-family: PingFangSC-Semibold;
                  font-size: 22rpx;
                  color: #fb444f;
                  font-weight: 600;
                }
              }
            }
          }
        }
      }
      .reduce-p-item:last-child {
        border-bottom: 0;
      }
    }
    .reduce-btm-ctn {
      padding-bottom: 70rpx;
      // background-image: linear-gradient(0deg, #ff7022 0%, #ff2436 100%);
      // background-image: linear-gradient(0deg, #8424ff 0%, #a368f3 99%);
      // background-repeat: no-repeat;
    }
    .reduce-btm-ctn-c {
      background-size: 100% 350vh;
      background-repeat: no-repeat;
      background-position: 0 540rpx;
      margin-top: -150rpx;
    }
    .reduce-p-title {
      font-family: PingFangSC-Semibold;
      font-size: 40rpx;
      color: #ffffff;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;
      margin-top: -83rpx;
      padding-top: 50rpx;
      img {
        width: 85rpx;
        height: 20rpx;
      }
      span {
        margin: 0 13rpx;
      }
    }
    .reduce-package {
      width: 710rpx;
      margin: 0 auto;
      // margin-top: -10rpx;
      transform: translateY(-143rpx);
      .reduce-package-title {
        width: 100%;
        height: 98rpx;
        background-image: url(https://static.soyoung.com/sy-pre/2eqk8th16m3n7-1678774200720.png);
        // background-image: url(https://static.soyoung.com/sy-pre/b86nw05w6d8g-1672125000674.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        border-radius: 20rpx 20rpx 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 60rpx;
        box-sizing: border-box;
        .reduce-package-tl {
          font-family: PingFangSC-Semibold;
          font-size: 40rpx;
          color: #ffffff;
          text-align: center;
          text-shadow: 0 4rpx 30rpx rgba(185, 0, 0, 0.5);
          font-weight: 600;
          margin-top: 10rpx;
        }
        .reduce-package-tr {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #ffffff;
          text-align: center;
          font-weight: 400;
          display: flex;
          margin-top: 10rpx;
          .tr-time {
            display: flex;
            margin-left: 20rpx;
            padding-right: 28rpx;
            div {
              width: 40rpx;
              height: 36rpx;
              background: #fff;
              border-radius: 6rpx;
              font-family: PingFangSC-Semibold;
              font-size: 26rpx;
              color: #ff0002;
              font-weight: 600;
            }
            span {
              font-family: PingFangSC-Regular;
              font-size: 20rpx;
              color: #ffffff;
              font-weight: 400;
              margin: 0 5rpx;
            }
          }
        }
      }
      .reduce-package-cont {
        position: relative;
        .reduce-package-cont-img-c {
          width: 100%;
          position: absolute;
          bottom: -12rpx;
          height: 20rpx;
          width: 100%;
          overflow: hidden;
          .reduce-package-cont-img {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50rpx;
          }
        }
        .reduce-package-cont-p {
          width: 100%;
          background: #fff;
          border-radius: 0 0 20rpx 20rpx;
          // padding-bottom: 40rpx;
          overflow: hidden;
          .get-btn {
            margin: 0 auto 50rpx;
            // background-image: linear-gradient(96deg, #ff4c4b 0%, #fe301c 100%);
            // box-shadow: 0 10rpx 12rpx 0 rgba(255, 68, 68, 0.4);
            background-image: linear-gradient(96deg, #ff9289 0%, #fd2a57 100%);
            box-shadow: 0 5px 6px 0 rgba(255, 67, 104, 0.4),
              inset 0 1px 0 0 #ff8b6c;
            border-radius: 44rpx;
            width: 646rpx;
            height: 88rpx;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 30rpx;
            font-family: PingFangSC-Semibold;
            font-size: 32rpx;
            color: #ffffff;
            text-align: center;
            font-weight: 600;
          }
          .got-btn {
            opacity: 0.2;
            background-image: linear-gradient(96deg, #ff4c4b 0%, #fe301c 100%);
          }
          .package2-ctn {
            width: 100%;
            overflow-x: auto;
            .first-cont,
            .second-cont {
              .package-1 {
                padding-top: 24rpx;
                display: flex;
                justify-content: center;
                .pkg-1-item {
                  width: 650rpx;
                  height: 144rpx;
                  // background-image: url(https://static.soyoung.com/sy-pre/2uf967t2v4hzg-1672283400676.png);
                  background-image: url(https://static.soyoung.com/sy-pre/2uf967t2v4hzg-1678774200720.png);
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  box-sizing: border-box;
                  .pkg-1-item-l {
                    width: 234rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: PingFangSC-Semibold;
                    font-size: 48rpx;
                    color: #ff0002;
                    line-height: 44rpx;
                    font-weight: 600;
                    .span-m {
                      font-family: PingFangSC-Semibold;
                      font-size: 30rpx;
                      color: #ff0002;
                      line-height: 44rpx;
                      font-weight: 600;
                      margin-right: -6rpx;
                    }
                    .span-m-txt {
                      font-family: PingFangSC-Regular;
                      font-size: 20rpx;
                      color: #bf8a72;
                      font-weight: 400;
                      margin-top: 0rpx;
                    }
                  }
                  .pkg-1-item-r {
                    width: 416rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: PingFangSC-Semibold;
                    font-size: 28rpx;
                    color: #ffffff;
                    text-align: center;
                    font-weight: 600;
                    flex-direction: column;
                  }
                }
              }
              .package-2 {
                padding: 40rpx 24rpx 0;
                display: flex;
                justify-content: space-between;
                .pkg-2-item {
                  width: 316rpx;
                  height: 184rpx;
                  // background-image: url(https://static.soyoung.com/sy-pre/1uj5fppqei9ia-1672294200701.png);
                  background-image: url(https://static.soyoung.com/sy-pre/1uj5fppqei9ia-1678774200720.png);
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  position: relative;
                  .pkg-2-item-top {
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%);
                    white-space: nowrap;
                    top: 36rpx;
                    font-family: PingFangSC-Semibold;
                    font-size: 48rpx;
                    color: #ff0002;
                    line-height: 44rpx;
                    font-weight: 600;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .span-m {
                      font-family: PingFangSC-Semibold;
                      font-size: 30rpx;
                      color: #ff0002;
                      line-height: 44rpx;
                      font-weight: 600;
                      margin-right: -6rpx;
                    }
                    .span-m-txt {
                      font-family: PingFangSC-Regular;
                      font-size: 20rpx;
                      color: #bf8a72;
                      font-weight: 400;
                      margin-top: -30rpx;
                    }
                  }
                  .pkg-2-item-btm {
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%);
                    white-space: nowrap;
                    bottom: 22rpx;
                    font-family: PingFangSC-Regular;
                    font-size: 20rpx;
                    color: #ffffff;
                    text-align: center;
                    font-weight: 400;
                  }
                }
                .pkg-3-item {
                  width: 208rpx;
                  height: 184rpx;
                  // background-image: url(https://static.soyoung.com/sy-pre/5h5zu1thkwqp-1672294200701.png);
                  background-image: url(https://static.soyoung.com/sy-pre/5h5zu1thkwqp-1678774200720.png);
                }
                .pkg-3-item-z {
                  margin-right: 12rpx;
                }
              }
              .package-3 {
                padding: 40rpx 24rpx 0 24rpx;
                display: flex;
                .pkg-3m-item {
                  min-width: 208rpx;
                  height: 184rpx;
                  margin-right: 12rpx;
                  background-size: 100% 100%;
                  background-repeat: no-repeat;
                  // background-image: url(https://static.soyoung.com/sy-pre/5h5zu1thkwqp-1672294200701.png);
                  background-image: url(https://static.soyoung.com/sy-pre/5h5zu1thkwqp-1678774200720.png);
                  position: relative;
                  .pkg-2-item-top {
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%);
                    white-space: nowrap;
                    top: 36rpx;
                    font-family: PingFangSC-Semibold;
                    font-size: 48rpx;
                    color: #ff0002;
                    line-height: 44rpx;
                    font-weight: 600;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .span-m {
                      font-family: PingFangSC-Semibold;
                      font-size: 30rpx;
                      color: #ff0002;
                      line-height: 44rpx;
                      font-weight: 600;
                      margin-right: -6rpx;
                    }
                    .span-m-txt {
                      font-family: PingFangSC-Regular;
                      font-size: 20rpx;
                      color: #bf8a72;
                      font-weight: 400;
                      margin-top: -30rpx;
                    }
                  }
                  .pkg-2-item-btm {
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%);
                    white-space: nowrap;
                    bottom: 22rpx;
                    font-family: PingFangSC-Regular;
                    font-size: 20rpx;
                    color: #ffffff;
                    text-align: center;
                    font-weight: 400;
                  }
                }
                .package-3-r {
                  width: 24rpx;
                  min-width: 12rpx;
                }
              }
            }
          }
          .reduce-scroll {
            width: 84rpx;
            height: 12rpx;
            background: #e5e5e5;
            border-radius: 6rpx;
            margin: 22rpx auto 0;
            position: relative;
            .reduce-itm {
              position: absolute;
              top: 0;
              left: 0;
              width: 34rpx;
              height: 12rpx;
              background: #fe3b21;
              border-radius: 6rpx;
            }
          }
          .package2-btm {
            width: 640rpx;
            height: 52rpx;
            background: rgba(255, 81, 0, 0.12);
            font-family: PingFangSC-Regular;
            font-size: 28rpx;
            color: #ad6b4c;
            text-align: center;
            line-height: 40rpx;
            font-weight: 400;
            border-radius: 28rpx;
            margin: 30rpx auto;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            .package2-btm-red {
              color: #fe3b21;
            }
            .jian-block {
              position: absolute;
              transform: translate(-50%, 100%);
              bottom: 0rpx;
              left: 50%;
              width: 0;
              height: 0;
              border-top: 12rpx solid rgba(255, 81, 0, 0.12);
              border-right: 12rpx solid transparent;
              border-bottom: 12rpx solid transparent;
              border-left: 12rpx solid transparent;
            }
          }
        }

        // 无优惠券时样式
        .no-package {
          text-align: center;
          .red-package-img {
            width: 222 * 2rpx;
            height: 141 * 2rpx;
            display: inline-block;
            margin: 65 * 2rpx 0 auto;
          }
          .tips {
            font-family: PingFangSC-Regular;
            font-size: 14 * 2rpx;
            color: #777777;
            text-align: center;
            font-weight: 400;
            margin-bottom: 50 * 2rpx;
          }
          .btn {
            background-image: linear-gradient(97deg, #ff9289 0%, #fd2a57 100%);
            box-shadow: 0 10rpx 12rpx 0 rgba(255, 67, 104, 0.4),
              inset 0 2rpx 0 0 #ff8b6c;
            border-radius: 44rpx;
            width: 296 * 2rpx;
            height: 44 * 2rpx;
            line-height: 44 * 2rpx;
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            color: #ffffff;
            text-align: center;
            font-weight: 600;
            margin: 0 auto 89 * 2rpx;
          }
        }
      }
    }
  }
}
.soyoung-logo {
  display: inline-block;
  width: 100%;
  margin-top: -93rpx;
}
</style>
