<template>
  <view>
    <web-view :src="url" @message="handleGetMessage"></web-view>
  </view>
</template>

<script>
export default {
  pageTrackConfig() {
    return {
      info: 'sy_wxtuan_other_webview_page',
      ext: {
        url: this.originUrl
      },
      is_h5: 1
    };
  },
  data() {
    return {
      url: '',
      originUrl: ''
    };
  },
  onShow() {},
  async onLoad(options) {
    console.log(options);
    uni.hideShareMenu({});
    let url = decodeURIComponent(options.url);
    this.url = url;
  },
  computed: {},
  methods: {
    reloadPage(redirectUrl) {
      console.log('---webview reloadPage', redirectUrl);
      this.url = redirectUrl;
    },

    handleGetMessage(e) {
      try {
        console.log('handleGetMessage>>>>>>>>>>', e);
        if (e.detail.data[0].result == 'success') {
          uni.showToast({
            title: '校验成功',
            icon: 'none'
          });
          const pages = getCurrentPages();
          console.log('校验成功>?>>>>>>>>>>', pages);
          // wx.navigateTo({
          //   url: '/packageAccount/middle'
          // });
        }
      } catch (error) {
        console.log('handleGetMessage: Error: >>>>>>>', error.message);
      }
    }
  }
};
</script>

<style></style>
