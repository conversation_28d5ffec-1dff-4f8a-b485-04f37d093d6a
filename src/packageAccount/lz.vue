<template>
  <div class="form-container">
    <div class="header"></div>
    <div class="form" @submit.prevent="handleSubmit">
      <div class="form-group">
        <label>姓名</label>
        <input
          type="text"
          placeholder-class="__lz-placeholder-custom__"
          v-model="name"
          placeholder="请填写您的姓名"
          maxlength="50"
        />
      </div>
      <div class="form-group">
        <label>手机号</label>
        <button
          v-if="autoGet === -1"
          class="auto-get-btn"
          open-type="getPhoneNumber"
          @getphonenumber="getPhoneNumber"
        >
          请填写您的手机号
        </button>
        <input
          v-else-if="autoGet === 1"
          disabled
          type="text"
          :value="masked_mobile"
          style="color: #030303"
        />
        <input
          v-else
          type="number"
          :focus="focus"
          maxlength="11"
          @blur="handleMobileBlur"
          placeholder-class="__lz-placeholder-custom__"
          ref="mobileRef"
          v-model="mobile"
          placeholder="请填写您的手机号"
        />
      </div>
      <div class="form-group">
        <label>性别</label>
        <Selector
          title="选择性别"
          :options="genderOptions"
          :value="genderIndex"
          @confirm="handleConfirmGender"
        >
          <div class="selector-text">
            <div v-if="genderLabel">{{ genderLabel }}</div>
            <div v-else class="__lz-placeholder-custom__">请选择您的性别</div>
          </div>
        </Selector>
      </div>
      <div class="form-group">
        <label>意向门店</label>
        <Selector
          title="选择门店"
          :options="tenantOptions"
          :value="tenantIndex"
          @confirm="handleConfirmTenant"
        >
          <div
            v-if="hasNoLocationAuth"
            class="selector-text"
            @click.stop="handleAuth"
          >
            <div class="__lz-placeholder-custom__">请选择您的意向门店</div>
          </div>
          <div v-else class="selector-text">
            <div v-if="tenantLabel">{{ tenantLabel }}</div>
            <div v-else class="__lz-placeholder-custom__">
              请选择您的意向门店
            </div>
          </div>
        </Selector>
      </div>
      <div class="form-protocol">
        <div
          class="checkbox"
          :class="{ active: checked }"
          @click="() => (checked = !checked)"
        ></div>
        <div class="text">
          您确认并同意，授权尺颜医美（北京）咨询有限公司收集您的姓名、手机号、性别、意向门店等信息，用于完成用户身份建档、向您推荐适合的门店、服务项目、提供商品展示、交易、售后及用户服务、优化产品功能、提升用户体验、经您另行同意的其他用途；法律要求的其他用途。
          <div class="underline" @click="handleProtocol">
            《个人信息收集与使用授权协议》
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="button" @click="handleSubmit">提交</div>
    </div>
  </div>
</template>
<script>
import Selector from './components/selector.vue';
import {
  apiGetUserPhone,
  apiSaveLiuziCustomer,
  apiGetUserNearestTenant
} from '@/api/account.js';

export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_other_market_leads_page',
      ext: {
        mx_source: this.mx_source
      }
    };
  },
  components: {
    Selector
  },
  data() {
    return {
      genderOptions: [
        { label: '男', value: 1 },
        { label: '女', value: 2 },
        { label: '未知', value: 0 }
      ],
      tenantOptions: [],
      genderIndex: 1,
      tenantIndex: 0,
      focus: false,
      name: '',
      mobile: '',
      autoGet: -1,
      checked: false,
      hasNoLocationAuth: false,
      encrypt_mobile: '',
      masked_mobile: ''
    };
  },
  computed: {
    genderLabel() {
      return this.genderOptions?.[this.genderIndex]?.label || '';
    },
    tenantLabel() {
      return this.tenantOptions?.[this.tenantIndex]?.label || '';
    },
    gender() {
      return this.genderOptions?.[this.genderIndex]?.value;
    },
    tenant() {
      return this.tenantOptions?.[this.tenantIndex]?.value || '';
    }
  },
  onShow() {
    this.getNearestTenant();
  },
  methods: {
    handleAuth() {
      wx.showToast({
        title: '将跳转设置，请打开定位权限',
        icon: 'none'
      });
      wx.openSetting();
    },
    // 获取地理位置定位
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        return null;
      });
      this.hasNoLocationAuth = !(res?.lat && res?.lng);
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    },
    async getNearestTenant() {
      const scopeUserLocation = await this.checkScopeOfUserLocation();
      if (scopeUserLocation === false) {
        this.hasNoLocationAuth = true;
      } else {
        this.hasNoLocationAuth = false;
        await this.getLocation();
      }
      const userInfo = uni.getStorageSync('user_info');
      const { lat: user_lat, lng: user_lng } = userInfo;
      console.log('地理位置授权情况', user_lat, user_lng);
      const responseData = await apiGetUserNearestTenant({
        user_lat,
        user_lng
      });
      if (!responseData || !Array.isArray(responseData?.list)) return;
      this.tenantOptions = responseData.list.map((item) => ({
        label: item.tenant_name,
        value: item.tentant_id
      }));
    },
    async handleSubmit() {
      if (!this.name) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return;
      }
      if (this.name.length > 10) {
        uni.showToast({
          title: '姓名不能大于10个字符',
          icon: 'none'
        });
        return;
      }

      let mobile;
      if (this.autoGet === 1) {
        mobile = this.encrypt_mobile;
      } else if (this.autoGet === 0) {
        mobile = this.mobile;
      }
      if (!mobile) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return;
      }
      if (this.autoGet === 0 && !/^[1]{1}[0-9]{10}$/g.test(this.mobile)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        });
        return;
      }
      if (this.gender === undefined) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        });
        return;
      }
      if (!this.tenant) {
        uni.showToast({
          title: '请选择意向门店',
          icon: 'none'
        });
        return;
      }
      if (!this.checked) {
        uni.showToast({
          title: '请同意授权协议',
          icon: 'none'
        });
        return;
      }
      uni.showLoading({
        title: '提交中...',
        mask: true
      });
      const { errorCode, errorMsg } = await apiSaveLiuziCustomer({
        real_name: this.name,
        mobile: mobile,
        mobile_type: this.autoGet === 1 ? 2 : 1,
        gender: this.gender, //性别 1：男 2：女 0未知
        tenant_id: this.tenant
      });
      uni.hideLoading();
      if (errorCode === 0) {
        wx.showToast({
          title: '提交成功',
          icon: 'none'
        });
      } else {
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
      this.$reportData({
        info: 'sy_chain_store_other_market_leads:submit_click',
        ext: {
          mx_source: this.mx_source,
          btn_type: errorCode === 0 ? 1 : 2
        }
      });
    },
    handleConfirmGender(index) {
      this.genderIndex = index;
    },
    handleConfirmTenant(index) {
      this.tenantIndex = index;
    },
    async getPhoneNumber(e) {
      switch (e.detail.errMsg) {
        case 'getPhoneNumber:ok': {
          const resp = await apiGetUserPhone({
            code: e.detail.code
          });
          this.encrypt_mobile = resp.encrypt_mobile;
          this.masked_mobile = resp.masked_mobile;
          this.autoGet = 1;
          break;
        }
        case 'getPhoneNumber:fail user deny':
          this.autoGet = 0;
          await this.$nextTick();
          this.focus = true;
          break;
      }
    },
    handleMobileBlur() {
      this.focus = false;
    },
    handleProtocol() {
      this.$toH5('https://m.soyoung.com/tmwap27021');
    }
  },
  onLoad(options) {
    this.mx_source = options.mx_source ? Number(options.mx_source) : '';
  }
};
</script>
<style lang="less" scoped>
.form-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .header {
    width: 375 * 2rpx;
    height: 287 * 2rpx;
    background: url(https://static.soyoung.com/sy-pre/banner-1744963800653.jpg)
      no-repeat center top transparent;
    background-size: 375 * 2rpx 287 * 2rpx;
  }
  .form {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 50rpx 40rpx;
    padding-bottom: env(safe-area-inset-bottom);
    .form-group {
      width: 100%;
      height: 58 * 2rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #f2f2f2;
      label {
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #030303;
        letter-spacing: 0;
        font-weight: 400;
        &::before {
          padding-right: 10rpx;
          content: '*';
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #61b43e;
          font-weight: 500;
        }
      }
      input,
      select {
        // flex: 1;
        width: 500rpx;
        text-align: right;
        font-family: PingFangSC-Medium;
        font-size: 26rpx;
        color: #030303;
        letter-spacing: 0;
        font-weight: 500;
      }
    }
    .selector-text {
      width: 500rpx;
      position: relative;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      text-align: right;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 500;
      // background-color: red;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &::after {
        margin-left: 20rpx;
        content: '';
        height: 18rpx;
        width: 12rpx;
        background: url(https://static.soyoung.com/sy-design/ayjcvdzd56671744709336074.png)
          no-repeat center center transparent;
        background-size: contain;
      }
    }
    .auto-get-btn {
      margin: 0;
      display: inline-block;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #bababa;
      padding: 0;
      letter-spacing: 0;
      font-weight: 400;
      background-color: transparent;
      &::after {
        content: '';
        background-color: transparent;
        border: none;
      }
    }
    .form-protocol {
      padding-top: 50rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #030303;
      letter-spacing: 0;
      text-align: justify;
      font-weight: 400;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      .checkbox {
        display: inline-block;
        height: 50rpx;
        width: 50rpx;
        margin-right: -8rpx;
        transform: translate(-8rpx, -8rpx);
        background: url(https://static.soyoung.com/sy-design/g3col5twx35s1744709336075.png)
          no-repeat center center transparent;
        background-size: 24rpx 24rpx;
        &.active {
          background: url(https://static.soyoung.com/sy-design/m6nemdnykrbc1744709336069.png)
            no-repeat center center transparent;
          background-size: 24rpx 24rpx;
        }
      }
      .text {
        flex: 1;
        text-align-last: left;
        text-align: left;
      }
      .underline {
        position: relative;
        display: inline;
        text-decoration: underline;
        text-decoration-color: #030303;
        text-decoration-thickness: 1rpx;
        // &::after {
        //   content: '';
        //   position: absolute;
        //   left: 12rpx;
        //   right: 12rpx;
        //   bottom: 1rpx;
        //   height: 1rpx;
        //   background-color: #030303;
        // }
      }
    }
  }
  .footer {
    width: 100%;
    position: relative;
    padding-bottom: env(safe-area-inset-bottom);
    overflow: hidden;
    .button {
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 325 * 2rpx;
      height: 42 * 2rpx;
      background-color: #333;
      color: #fff;
    }
  }
}
</style>
<style>
.__lz-placeholder-custom__ {
  font-family: PingFangSC-Regular;
  font-size: 13px;
  color: #bababa;
  letter-spacing: 0;
  font-weight: 400;
}
</style>
