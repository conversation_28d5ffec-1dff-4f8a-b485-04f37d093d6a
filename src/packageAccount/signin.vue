<template>
  <page-meta>
    <NavBar :hasFixedHome="true" />
    <div
      class="account-pkg-signin-main"
      :style="{
        backgroundImage: `url(${tenant_obj.tenant_img})`
      }"
    >
      <scroll-view
        class="scroll"
        :scroll-y="true"
        :show-scrollbar="false"
        :enhanced="true"
      >
        <StepUserInsert
          v-show="step === 1"
          :user_obj="user_obj"
          :idcard_categories_obj="idcard_categories_obj"
          @handleTriggerPickerVisible="handleTriggerPickerVisible"
          @change="userChangeHandler"
          :showRegionData.sync="showRegionData"
          :tenant_obj="tenant_obj"
          :step="step"
        />
        <StepWantSelect
          v-show="step === 2"
          :user_obj="user_obj"
          :has_select_source="has_select_source"
          :select_source_id.sync="select_source_id"
          :select_index.sync="select_index"
          :select_source="selectSource"
          :parts_list.sync="parts_list"
          :source_list="source_list"
          :want_obj="want_obj"
          :tenant_obj="tenant_obj"
          :oldnewInfo="oldnewInfo"
          :visit_obj="visit_obj"
          :step="step"
          @handleOldBringNew="handleOldBringNew"
        />
        <StepSuccess
          v-if="step === 3"
          :realname="form.realname || user_obj.realname"
          :mobile="form.mobile || user_obj.mobile"
          :visit_time="visit_time"
          :select_source_name="select_source_name"
          :select_parts="select_parts"
          :reserve_obj="reserve_obj"
          :has_reserve="has_reserve"
          :want_obj="want_obj"
          :visit_obj="visit_obj"
          :user_obj="user_obj"
        />
      </scroll-view>
      <footer class="footer">
        <div class="privacy" v-if="step === 1">
          <div
            @click="onPrivacyClick"
            class="checkbox"
            :class="{
              checked: privacyChecked
            }"
          ></div>
          <div class="text">
            {{ agreementInfo.desc }}
            <span
              v-for="(item, ind) in agreementInfo.child_list"
              :key="ind"
              class="em"
              @click="onPrivacyRead(item)"
              >{{ item.title }}</span
            >
          </div>
        </div>
        <button
          type="button"
          @click="nextStep"
          class="next"
          :disabled="!isRequiredComplete || !privacyChecked"
          v-if="step === 1"
        >
          {{ ext_obj.first_bottom_button_txt || '' }}
        </button>
        <block v-if="step === 2">
          <button type="button" @click="onBack" class="back">
            {{ ext_obj.second_bottom_button_txt1 || '' }}
          </button>
          <button
            type="button"
            @click="nextStep"
            :disabled="!isVisitComplete"
            class="next"
          >
            {{ ext_obj.second_bottom_button_txt2 }}
          </button>
        </block>
        <button
          class="go2home"
          type="button"
          @click="go2home"
          v-if="step === 3"
        >
          {{ ext_obj.success_bottom_button_txt }}
        </button>
      </footer>
    </div>
    <PageLoading :visible="loading" />
    <StepJiaC :channel="channel" :visible="joinCVisible" />

    <view
      @click="handleClickPickerVisibleToHide"
      :class="[pickerVisible ? 'full-mask-show' : 'full-mask-hide']"
    >
    </view>
    <view v-if="pickerVisible" class="picker-view-wrapper">
      <view class="picker-view-btns picker-view-btns-top">
        <view class="picker-view-title">选择城市</view>
        <image
          class="picker-view-close"
          @click="handleClickPickerVisibleToHide"
          src="https://static.soyoung.com/sy-design/bzsokyai5osd1726715721872.png"
          alt="关闭按钮"
          mode="widthFix"
        ></image>
      </view>
      <picker-view
        class="picker-view-show"
        indicator-style="height: 82rpx;"
        @change="handlePickerChange"
        :value="regionData"
      >
        <picker-view-column>
          <view
            v-for="item in renderRegionObj.province"
            :key="item.value"
            class="picker-view-text"
          >
            {{ item.label }}
          </view>
        </picker-view-column>
        <picker-view-column>
          <view
            v-for="item in renderRegionObj.city"
            :key="item.value"
            class="picker-view-text"
          >
            {{ item.label }}
          </view>
        </picker-view-column>
        <picker-view-column>
          <view
            v-for="item in renderRegionObj.district"
            :key="item.value"
            class="picker-view-text"
          >
            {{ item.label }}
          </view>
        </picker-view-column>
      </picker-view>
      <view class="picker-view-btns picker-view-btns-bottom">
        <view @click="handleClickPickerConfirm" class="picker-view-btns-submit"
          >确认</view
        >
      </view>
    </view>

    <Popup type="bottom" ref="popup">
      <div class="invite-box">
        <div class="invite-title">确认邀请人信息</div>
        <div class="invite-content">
          <div class="invite-wrapper">
            <input
              class="phone-input"
              v-model="oldBringNewInfo.mobile"
              maxlength="11"
              type="number"
              placeholder="输入手机号"
            />
            <view
              class="code"
              @tap="handlerSearch"
              :class="{ codenable: oldBringNewInfo.mobile != '' }"
            >
              <text>查询</text>
            </view>
          </div>
          <div v-if="oldBringNewInfo.is_customer" class="invite-info">
            <div class="invite-info-left">
              {{ oldBringNewInfo.customer_info.name }}
              <template v-if="oldBringNewInfo.customer_info.gender">
                |
                {{ oldBringNewInfo.customer_info.gender }}
              </template>
            </div>
            <div class="invite-info-right">
              {{ oldBringNewInfo.customer_info.mobile }}
            </div>
          </div>
          <div
            v-else-if="oldBringNewInfo.is_customer === 0"
            class="invite-empty"
          >
            {{ oldBringNewInfo.msg }}
          </div>
        </div>
        <div class="invite-bottom">
          <div class="button cancel" @click="onBottomModalClose">取 消</div>
          <div
            class="button confirm"
            :class="{ buttonable: oldBringNewInfo.is_customer }"
            @click="onBottomModalSubmit"
          >
            确 认
          </div>
        </div>
      </div>
    </Popup>
  </page-meta>
</template>
<script>
import { mapGetters } from 'vuex';
import PageLoading from '@/components/pageLoading.vue';
import { apiGetSignInInfo, apiSaveSignInInfo, apiGetCityTree } from '@/api/crm';
import StepUserInsert from './components/signin/signin-user.vue';
import StepWantSelect from './components/signin/signin-want.vue';
import StepSuccess from './components/signin/signin-success.vue';
import StepJiaC from './components/signin/signin-jiac.vue';
import NavBar from '@/components/NavBar.vue';
import Popup from '@/components/uni/popup.vue';
import SyEncrypt from '@soyoung/SyEncrypt';
const syEncrypt = new SyEncrypt({
  key: 'X2Du8HEfRVTRDqaB'
});

export default {
  pageTrackConfig: 'sy_wxtuan_tuan_store_sign_in_page',
  components: {
    PageLoading,
    StepSuccess,
    StepUserInsert,
    StepWantSelect,
    StepJiaC,
    NavBar,
    Popup
  },
  data() {
    return {
      // 门店ID
      tenant_id: '',
      visit_status: 0,
      visit_time: '',
      loading: false,
      step: 0,
      // 第一步使用的
      idcard_categories_obj: [],
      // 用户信息，处理之前的
      user_obj: {},
      ext_obj: {},
      want_obj: {},
      // 用户信息，处理之后的
      form: {},
      // 第二部使用的
      has_select_source: false,
      select_parts: [],
      select_source_id: '',
      select_index: null,
      select_source_name: '',
      source_list: [],
      parts_list: [],
      // 第三部使用的
      has_reserve: 0,
      reserve_obj: [],
      // 是否加C
      has_join_c: 0,
      joinCVisible: false,
      channel: '',
      privacyChecked: true,
      pickerVisible: false,
      regionData: [0, 0, 0],
      showRegionData: [],
      regionList: [],
      renderRegionObj: {
        province: [],
        city: [],
        district: []
      },
      tenant_obj: {
        tenant_img: ''
      },
      visit_obj: {},
      marketActivityId: '',
      agreementInfo: {
        desc: '',
        child_list: []
      },
      oldBringNewInfo: {
        mobile: ''
      }, // 老带新 信息
      oldnewInfo: null
    };
  },
  watch: {
    // regionData: {
    //   handler(newVal, oldVal) {
    //     console.log('watchRegionData', newVal, oldVal);
    //     if(newVal && oldVal && newVal[0] !== oldVal[0]){
    //       this.handlePickerChange(newVal[0])
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // },
  },
  computed: {
    // 表单必填信息是否完成
    isRequiredComplete() {
      // return true;
      console.log('isRequiredComplete', this.form, this.showRegionData);
      return !!(
        (
          this.form.realname &&
          this.form.gender &&
          this.form.mobile &&
          this.isCardValidate &&
          this.form.card_type
        )
        // this.form.birthday &&
        // this.showRegionData &&
        // this.showRegionData.length > 0
      );
    },
    isCardValidate() {
      if (this.form.card_type === 'identification') {
        return this.form.id_card?.length === 18;
      }
      return this.form.id_card;
    },
    // 签到信息是否完整
    isVisitComplete() {
      if (
        Array.isArray(this.parts_list) &&
        this.parts_list.length > 0 &&
        !this.parts_list.some((item) => item.selected)
      ) {
        return false;
      }
      console.log(
        this.selectSource?.old_bring_new,
        this.oldnewInfo?.customer_id
      );
      if (
        this.selectSource?.old_bring_new == 1 &&
        this.oldnewInfo?.customer_id
      ) {
        return true;
      }
      console.log(
        Array.isArray(this.source_list),
        this.source_list.length > 0,
        this.select_source_id
      );
      if (
        Array.isArray(this.source_list) &&
        this.source_list.length > 0 &&
        !this.select_source_id
      ) {
        return false;
      }

      return true;
      // return (
      //   this.select_source_id && this.parts_list.some((item) => item.selected)
      // );
    },
    selectSource() {
      if (this.select_index !== null) {
        return this.source_list[this.select_index];
      } else {
        return {};
      }
    },
    ...mapGetters(['isLogin'])
  },
  async onLoad(options) {
    const { id, qzychannel = '', market_activity_id } = options;
    this.tenant_id = id;
    this.channel = qzychannel;
    this.marketActivityId = market_activity_id;
  },
  async onShow() {
    await this.fetchData();
    // 本地缓存不要了
    // uni.$emit('__signin_fetch_main_compleated__');
    if (this.channel && !this.has_join_c) {
      this.joinCVisible = true;
    } else if (this.visit_status > 1) {
      this.step = 3;
    } else if (this.checkUserInfoCompleate()) {
      this.step = 2;
    } else {
      this.step = 1;
    }
    this.fetchCityTree();

    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_store_sign_in_page',
      ext: {
        status: this.step
      }
    });
  },
  methods: {
    onBottomModalClose() {
      this.oldBringNewInfo = {
        mobile: ''
      };
      this.$refs.popup.close();
    },
    onBottomModalSubmit() {
      if (this.oldBringNewInfo.is_customer !== 1) {
        return;
      }
      this.oldnewInfo = {
        ...this.oldBringNewInfo.customer_info
      };
      this.select_index = this.oldBringNewInfo.index;
      this.onBottomModalClose();
    },
    async handlerSearch() {
      if (this.oldBringNewInfo.mobile == '') {
        return;
      }
      const res = await this.$request({
        url: '/syGroupBuy/chain/visit/getCustomerByMobile',
        data: {
          mobile: syEncrypt.encrypt(this.oldBringNewInfo.mobile)
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -1000,
            errorMsg: err || '网络错误,稍后再试',
            responseData: null
          };
        });
      if (res.errorCode !== 0) {
        this.oldBringNewInfo = {
          ...this.oldBringNewInfo,
          is_customer: 0,
          msg: '网络错误,稍后再试',
          customer_info: null
        };
        return;
      }
      const data = res.responseData;
      this.oldBringNewInfo = {
        ...this.oldBringNewInfo,
        is_customer: data.is_customer,
        msg: data.msg,
        customer_info: {
          ...data.customer_info,
          mobile: this.oldBringNewInfo.mobile
        }
      };
    },
    async handleOldBringNew({ index }) {
      this.oldBringNewInfo = {
        mobile: '',
        index
      };
      this.$refs.popup.open();
    },
    handlePickerChange(eventhandle) {
      const [newProvince, newCity, newDistrict] = eventhandle.detail.value;
      const [oldProvince, oldCity] = this.regionData;

      if (newProvince !== oldProvince) {
        this.renderRegionObj.city = this.regionList[newProvince].children;
        this.renderRegionObj.district = this.renderRegionObj.city[0].children;
        this.regionData = [newProvince, 0, 0];
      } else if (newCity !== oldCity) {
        this.renderRegionObj.district =
          this.renderRegionObj.city[newCity].children;
        this.regionData = [newProvince, newCity, 0];
      } else {
        this.regionData = [newProvince, newCity, newDistrict];
      }

      console.log('final', this.regionData);
    },
    fetchCityTree() {
      apiGetCityTree()
        .then((res) => {
          if (res && Array.isArray(res.list) && res.list.length > 0) {
            this.regionList = res.list;
            this.renderRegion();
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    renderRegion(province = '北京市', city, district) {
      let provinceList = this.regionList.map((item) => {
        return {
          label: item.label,
          value: item.value
        };
      });

      let findProvince = this.regionList?.findIndex(
        (item) => item.label === province
      );
      let findCity = this.regionList[findProvince]?.children.findIndex(
        (item) => item.label === city
      );
      let findDistrict = this.regionList[findProvince].children[
        findCity
      ]?.children.findIndex((item) => item.label === district);

      let cityList =
        findCity > -1
          ? this.regionList[findProvince].children
          : this.regionList.find((item) => item.label === province)?.children ||
            [];

      let districtList = findDistrict
        ? cityList[findCity].children
        : cityList[0]?.children || [];

      this.renderRegionObj = {
        province: provinceList,
        city: cityList,
        district: districtList
      };

      console.log('find', findProvince, findCity, findDistrict);
      if (findProvince > -1 && findCity > -1 && findDistrict > -1) {
        this.regionData = [findProvince, findCity, findDistrict];
      } else {
        this.regionData = [0, 0, 0];
      }

      console.log('provinceList', provinceList);
    },
    handleClickPickerConfirm() {
      this.showRegionData = this.regionDataToShow();
      this.handleClickPickerVisibleToHide();
    },
    handleClickPickerVisibleToHide() {
      this.pickerVisible = false;
      this.regionData = [0, 0, 0];
      this.renderRegion();
    },
    handleTriggerPickerVisible() {
      this.pickerVisible = true;

      if (this.showRegionData && this.showRegionData.length > 0) {
        const [province, city, district] = this.showRegionData.split(',');
        this.renderRegion(province, true);
        console.log('showRegionData', province, city, district);
        this.renderRegion(province, city, district);
      }
    },
    onPrivacyRead(item) {
      this.$toH5(item.jump_url);
    },
    onPrivacyClick() {
      this.privacyChecked = !this.privacyChecked;
    },
    userChangeHandler(data) {
      console.log('form format for', data);
      this.form = data;
    },
    // 判断用户信息是否完整
    checkUserInfoCompleate() {
      // return [
      //   'realname',
      //   'gender',
      //   'mobile',
      //   'card_type',
      //   'id_card',
      //   'birthday',
      //   'province',
      //   'city',
      //   'district',
      //   'contact_address'
      // ].every((key) => {
      //   return String(this.user_obj[key]).length > 0;
      // });
      // 改为接口下发
      return this.user_obj?.show_first_form < 1;
    },
    regionDataToShow() {
      const arrString = this.regionData
        .map((item, index) => {
          switch (index) {
            case 0:
              return this.renderRegionObj.province[item].label;
            case 1:
              return this.renderRegionObj.city[item].label;
            case 2:
              return this.renderRegionObj.district?.[item]?.label || '';
            default:
              return '';
          }
        })
        .join(',');

      return arrString;
    },
    async submitVisitInfo() {
      // 提交数据格式化
      let params = { ...this.form, market_activity_id: this.marketActivityId };
      // params.region = params.region.join(',');

      params.region = this.showRegionData;

      // 如果是编辑
      if (this.user_obj.mobile) {
        params.mobile = '';
      }
      // 门店id
      params.tenant_id = this.tenant_id;
      // 下单类型
      // 渠道的在isVisitComplete中判断
      // if (!this.select_source_id) {
      //   uni.showToast({
      //     title: '请选择下单类型',
      //     icon: 'none'
      //   });
      //   return false;
      // }
      // 治疗意向
      if (
        this.has_reserve !== 1 &&
        this.parts_list.every((item) => !item.selected)
      ) {
        uni.showToast({
          title: '请选择治疗意向',
          icon: 'none'
        });
        return false;
      }
      this.loading = true;
      const parts_str = JSON.stringify(
        this.parts_list
          .filter((item) => item.selected)
          .map((item) => ({
            name: item.name,
            img: item.img
          }))
      );
      console.log(
        'save data is',
        this.tenant_id,
        this.select_source_id,
        params,
        parts_str
      );
      params.tenant_id = this.tenant_id;
      params.parts_str = parts_str;
      if (this.selectSource?.old_bring_new == 1) {
        if (!this.oldnewInfo?.customer_id) {
          // uni.showToast({
          //   title: '请选择治疗意向',
          //   icon: 'none'
          // });

          return false;
        }
        params.recommend_customer = this.oldnewInfo?.customer_id;
      } else {
        params.select_source_id = this.select_source_id;
      }

      const res = await apiSaveSignInInfo(params);
      this.loading = false;
      return res;
    },
    async nextStep() {
      this.$reportData({
        info: 'sy_chain_store_other_my:confirm_button_click',
        ext: {
          status: this.step
        }
      });
      if (this.step >= 3) return;
      switch (this.step) {
        case 1: {
          if (this.has_reserve) {
            if (!(await this.submitVisitInfo())) return;
            await this.fetchData();
          }
          // 第二页啥也没返回就直接提交
          if (
            (!Array.isArray(this.source_list) || this.source_list.length < 1) &&
            (!Array.isArray(this.parts_list) || this.parts_list.length < 1)
          ) {
            if (!(await this.submitVisitInfo())) return;
            await this.fetchData();
          }
          break;
        }
        case 2:
          if (!(await this.submitVisitInfo())) return;
          await this.fetchData();
          break;
        default:
          break;
      }
      if (this.visit_status === 2) {
        this.step = 3;
        return;
      }
      await this.scrollTop();
      this.step++;
    },
    async fetchData() {
      this.loading = true;
      this.user_obj = {};
      if (!this.isLogin) {
        const auth = await this.$login().catch(() => false);
        if (!auth) {
          this.loading = false;
          return;
        }
      }
      const res = await apiGetSignInInfo({
        tenant_id: this.tenant_id,
        channel_id: this.channel,
        market_activity_id: this.marketActivityId
      });
      if (!res) {
        this.loading = false;
        return;
      }
      // this.visit_status = +res.visit_status;
      this.agreementInfo = res.agreement;
      this.visit_status = +res?.visit_obj?.visit_status;
      this.ext_obj = res.ext_obj || {
        first_bottom_button_txt: 'first_bottom_button_txt',
        second_bottom_button_txt1: 'second_bottom_button_txt1',
        second_bottom_button_txt2: 'second_bottom_button_txt2',
        success_bottom_button_txt: 'success_bottom_button_txt',
        success_bottom_button_jump: null
      };

      this.tenant_obj = res.tenant_obj || {
        tenant_img: ''
      };

      const {
        parts_list,
        source_list,
        select_parts,
        select_source_id,
        select_source_name
      } = res.want_obj;
      this.source_list = source_list;
      this.select_parts = select_parts;
      this.select_source_id = select_source_id;
      this.has_select_source = false;
      //  !!select_source_id;
      this.select_source_name = select_source_name;
      this.parts_list = parts_list.map(({ name, img }) => ({
        name,
        img,
        selected: !!select_parts?.find((s) => s.name === name)
      }));
      this.has_reserve = res.has_reserve;
      this.has_join_c = res.has_join_c || 0;
      this.reserve_obj = res.reserve_obj || [];
      this.idcard_categories_obj = res.idcard_categories_obj || [];
      this.user_obj = res.user_obj;

      if (
        this.user_obj.province &&
        this.user_obj.city &&
        this.user_obj.district
      ) {
        this.showRegionData = [
          this.user_obj.province,
          this.user_obj.city,
          this.user_obj.district
        ].join(',');
      }

      // this.visit_time = res.user_obj.visit_time;
      this.visit_time = res?.visit_obj?.visit_time;
      this.visit_obj = res.visit_obj;
      this.want_obj = res.want_obj || {
        visit_time_title: 'visit_time_title',
        select_parts_title: 'select_parts_title',
        source_title: 'source_title',
        part_title: 'part_title'
      };

      this.loading = false;
      console.log('fetch complated!!!');
    },
    scrollTop() {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select('.scroll')
          .fields({ node: true })
          .exec((res) => {
            if (!res?.[0]) return;
            const { node } = res[0];
            node.scrollTo({
              top: 0
            });
            resolve();
          });
      });
    },
    onBack() {
      this.step--;
    },
    go2home() {
      let url = this.ext_obj.success_bottom_button_jump || '/pages/index';
      try {
        this.$reportData({
          info: 'sy_chain_store_other_my:bottom_button_click',
          ext: {
            content: this.ext_obj.success_bottom_button_txt,
            url: url
          }
        });
      } catch (e) {
        console.log(e);
      } finally {
        if (url === 'pages/index') {
          uni.switchTab({
            url: `/${url}`
          });
        } else {
          uni.switchTab({
            url: url
          });
        }
      }
    }
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_store_sign_in_page',
      ext: {
        status: this.step
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_store_sign_in_page',
      ext: {
        status: this.step
      }
    });
  }
};
</script>
<style lang="less" scoped>
@footerBtnHeight: 88rpx;
.panel() {
  padding: 40rpx 30rpx;
  margin: 0 30rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  font-weight: 400;
}
.account-pkg-signin-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  box-sizing: border-box;
  height: 100vh;
  // background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
  //   no-repeat top center #f8f8f8;
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 750rpx 320rpx;
  background-color: #f8f8f8;
  overflow: hidden;
  .scroll {
    width: 100%;
    flex: 1;
    overflow-x: hidden;
  }
  .footer {
    box-sizing: border-box;
    padding-left: 30rpx;
    padding-right: 30rpx;
    padding-top: 20rpx;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: ~'max(env(safe-area-inset-bottom), 10px)';
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    .privacy {
      margin-bottom: 30rpx;
      display: flex;
      width: 100%;
      justify-content: space-between;
      align-items: center;
      .checkbox {
        flex-grow: 0;
        flex-shrink: 0;
        margin-right: 16rpx;
        height: 26rpx;
        width: 26rpx;
        background: url(https://static.soyoung.com/sy-design/8k1ijrc526id1726715721617.png)
          no-repeat center center transparent;
        background-size: 24rpx 24rpx;
        &.checked {
          background: url(https://static.soyoung.com/sy-design/1i3fb7pl05wlg1726715721649.png)
            no-repeat center center transparent;
          background-size: 24rpx 24rpx;
        }
      }
      .text {
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #030303;
        letter-spacing: 0;
        line-height: 40rpx;
        font-weight: 400;
        .em {
          font-weight: 600;
        }
      }
    }
    .next {
      flex: 1;
    }
    button {
      display: flex;
      justify-content: center;
      align-items: center;
      height: @footerBtnHeight;
      background-color: #333;
      border-radius: 0;
      font-size: 26rpx;
      color: #ffffff;
      font-weight: 500;
      font-family: PingFangSC-Medium;
      &::after {
        display: none;
      }
      &[disabled] {
        // background-color: #d2cdea;
        background: #bababa;
      }
    }
    .back {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: 310rpx;
      width: 310rpx;
      margin-right: 30rpx;
      background-color: transparent;
      color: @text-color;
      border: 1.5px solid @border-color;
    }
    .go2home {
      position: relative;
      width: 325 * 2rpx;

      // &::before {
      //   position: absolute;
      //   top: 50%;
      //   left: 20rpx;
      //   margin-top: -30rpx;
      //   margin-right: 20rpx;
      //   content: '';
      //   height: 60rpx;
      //   width: 60rpx;
      //   background: url(https://static.soyoung.com/sy-pre/go2home-1703571000694.png)
      //     no-repeat center center transparent;
      //   background-size: contain;
      // }
    }
  }
}

.full-mask-show {
  display: inline-block;
  width: 100vh;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.5);
}
.full-mask-hide {
  display: none;
}

.picker-view-wrapper {
  display: block;
  width: 100%;
  height: 720rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
  box-sizing: border-box;
  padding: 0 30rpx;
}
.picker-view-title {
  font-family: PingFangSC-Medium;
  font-size: 32rpx;
  color: #030303;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  flex: 1;
  text-indent: 40rpx;
}
.picker-view-close {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
}

.picker-view-btns {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  height: 104rpx;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  box-sizing: border-box;
}
.picker-view-btns-bottom {
  padding-top: 40rpx;
  height: 144rpx;
}

.picker-view-btns-submit {
  background: #333333;
  font-family: PingFangSC-Medium;
  font-size: 26rpx;
  color: #ffffff;
  text-align: center;
  font-weight: 500;
  width: 650rpx;
  height: 84rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.picker-view-show {
  width: 100%;
  height: 460rpx;

  //flex-direction: column;
  //justify-content: flex-start;
  //align-items: flex-start;

  .picker-view-text {
    width: 100%;
    height: 82rpx;
    line-height: 82rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333;
  }
}
.invite-box {
  background: #fff;
  padding: 50rpx;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: ~'max(env(safe-area-inset-bottom), 10px)';
  box-sizing: border-box;
  height: 544rpx;
  display: flex;
  flex-direction: column;
  .invite-title {
    font-family: PingFangSC-Medium;
    font-size: 32rpx;
    color: #030303;
    letter-spacing: 1rpx;
    font-weight: 500;
    line-height: 44rpx;
  }
  .invite-content {
    flex: 1;
  }
  .invite-wrapper {
    margin-top: 30rpx;
    background: #f2f2f2;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    height: 84rpx;
    .picker {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
      display: inline-flex;
      height: 100%;
      align-items: center;
      &__icon {
        margin-left: 10rpx;
        width: 16rpx;
        height: 16rpx;
        background: url('https://static.soyoung.com/sy-pre/component_arrow_black_small@<EMAIL>')
          no-repeat;
        background-size: 100%;
      }
    }
    .phone-input {
      flex: 1;
      margin-left: 40rpx;
      height: 100%;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
      line-height: 84rpx;
    }
    .code {
      height: 56rpx;
      width: 108rpx;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 400;
      background: #bababa;
      &.codenable {
        background: #030303;
      }
    }
  }
  .invite-info {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    height: 84rpx;
    background: #f8f8f8;
    justify-content: space-between;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: #8c8c8c;
    letter-spacing: 0;
    font-weight: 400;
  }
  .invite-empty {
    margin-top: 50rpx;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: #8c8c8c;
    letter-spacing: 0;
    font-weight: 400;
    display: flex;
    justify-content: center;
  }
  .invite-bottom {
    display: flex;
    .button {
      width: 310rpx;
      height: 84rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      text-align: center;
      font-weight: 500;
    }
    .cancel {
      background: #fff;
      color: #030303;
      border: 2rpx solid #030303;
    }
    .confirm {
      color: #fff;
      background: #bababa;
      margin-left: 30rpx;
      &.buttonable {
        background: #030303;
      }
    }
  }
}
</style>
<style lang="less">
.account-pkg-signin-main {
  .animate__animated {
    animation-duration: 0.5s;
    animation-duration: 1;
    animation-fill-mode: both;
  }
  .animate__fadeInUp {
    animation-name: fadeInUp;
  }
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 10%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
</style>
