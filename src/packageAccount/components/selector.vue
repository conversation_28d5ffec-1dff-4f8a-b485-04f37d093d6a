<template>
  <div>
    <div @click="open">
      <slot></slot>
    </div>
    <Popup ref="popupRef">
      <div class="picker-box">
        <div class="close" @click="close"></div>
        <div class="header">
          {{ title }}
        </div>
        <picker-view
          :value="[current]"
          class="picker-row"
          indicator-class="__common_picker-indicator__"
          :immediate-change="true"
          @change="change"
        >
          <picker-view-column class="picker-col">
            <div
              v-for="(g, index) in options"
              :key="index"
              :class="{
                label: true,
                active: current === index
              }"
            >
              {{ g.label }}
            </div>
          </picker-view-column>
        </picker-view>
        <div class="picker-confirm" @click="onConfirm">确定</div>
      </div>
    </Popup>
  </div>
</template>

<script>
import Popup from '@/components/uni/popup';
export default {
  components: { Popup },
  props: {
    title: String,
    value: {
      type: Number,
      default: null
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      current: 0
    };
  },
  watch: {
    value: {
      handler(val) {
        this.current = val;
      },
      immediate: true
    }
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  methods: {
    open() {
      this.$refs.popupRef.open('bottom');
    },
    change({ detail }) {
      [this.current] = detail.value;
    },
    onConfirm() {
      this.$emit('confirm', this.current);
      this.current = 0;
      this.close();
    },
    close() {
      this.$refs.popupRef.close('bottom');
    }
  }
};
</script>
<style lang="less" scoped>
.picker-box {
  position: relative;
  box-sizing: border-box;
  padding: 0 50rpx;
  background: #ffffff;
  padding-bottom: env(safe-area-inset-bottom);

  .close {
    position: absolute;
    right: 0;
    top: 0;
    height: 104rpx;
    width: 104rpx;
    background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1744709336227.png)
      no-repeat center center transparent;
    background-size: 40rpx 40rpx;
    z-index: 1;
  }
  .header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 104rpx;
    font-family: PingFangSC-Medium;
    font-size: 32rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
  .picker-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 186 * 2rpx;
    box-sizing: border-box;
    .picker-col {
      height: 100%;
      width: calc(100vw - 104rpx);
      .label {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        font-size: 34rpx;
        color: #aaabb3;
        transition: color 0.3s;
      }
      .active {
        font-weight: 500;
        color: #333;
      }
    }
  }
  .picker-confirm {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20rpx 0;
    width: 325 * 2rpx;
    height: 84rpx;
    font-size: 26rpx;
    color: #ffffff;
    background-color: #333;
    font-weight: 500;
  }
}
</style>

<style lang="less">
.__common_picker-indicator__ {
  height: 84rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border: 0.1px solid #f3f3f3;
  &::before,
  &::after {
    display: none;
  }
}
</style>
