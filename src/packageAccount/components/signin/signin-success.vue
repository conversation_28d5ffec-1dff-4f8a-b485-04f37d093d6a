<template>
  <section v-if="pageShow" class="result animate__animated animate__fadeInUp">
    <div class="status">
      <div class="result-icon"></div>
      <img
        src="https://static.soyoung.com/sy-pre/20240925-115219-1727241000644.gif"
        alt=""
      />
      <div class="h1">{{ visit_obj.main_title }}</div>
      <div class="title">{{ visit_obj.sub_title }}</div>
    </div>
    <div class="result-banner">
      <img
        class="banner-img"
        mode="widthFix"
        @click="handleGo"
        src="https://static.soyoung.com/sy-pre/20240925-141611-1727244600652.jpeg"
        alt=""
      />
    </div>
    <ul class="info">
      <li class="row">
        <div class="em">{{ realname }}</div>
        <div class="em fontOutFit fontSize14">{{ mobile }}</div>
      </li>
      <li class="row">
        <div class="text">{{ visit_obj.visit_time_title }}</div>
        <div class="em fontOutFit fontSize14">
          {{ visit_obj.visit_time || '无' }}
        </div>
      </li>
      <!--      <li class="row">-->
      <!--        <div class="text">用户来源</div>-->
      <!--        <div class="em">{{ select_source_name || '无' }}</div>-->
      <!--      </li>-->
      <li class="row">
        <div class="text fontSize14">{{ want_obj.select_parts_title }}</div>
        <div class="em fontSize14">
          {{ want_obj.select_parts_desc }}
        </div>
      </li>
    </ul>
    <block v-if="has_reserve">
      <div class="title">今日预约</div>
      <ul class="appt">
        <li v-for="(item, index) in reserve_obj" :key="index">
          <div class="wrap">
            <img :src="item.cover" class="avatar" />
            <div class="text">
              <div style="font-weight: 500">{{ item.title }}</div>
              <div>预约时间：{{ item.reserve_start_time }}</div>
            </div>
          </div>
          <div class="warn" v-if="item.describe">{{ item.describe }}</div>
        </li>
      </ul>
    </block>
  </section>
</template>
<script>
export default {
  props: {
    realname: {
      type: String,
      default: ''
    },
    mobile: {
      type: String,
      default: ''
    },
    visit_time: {
      type: String,
      default: ''
    },
    select_source_name: {
      type: String,
      default: ''
    },
    select_parts: {
      type: Array,
      default: () => []
    },
    reserve_obj: {
      type: Array,
      default: () => []
    },
    has_reserve: {
      type: Number,
      default: 0
    },
    want_obj: {
      type: Object,
      default: () => {}
    },
    visit_obj: {
      type: Object,
      default: () => {}
    },
    user_obj: {
      type: Object,
      default: () => {}
    }
  },
  // computed: {
  // 治疗意向
  // treatWantText() {
  //   return this.has_reserve === 1
  //     ? '共' + this.select_parts.length + '个项目'
  //     : this.select_parts.map((s) => s.name).join();
  // }
  // },
  data() {
    return {
      pageShow: false
    };
  },
  mounted() {
    this.preloadImage();

    this.$reportPageShow({
      info: 'sy_chain_store_other_sign_in_success_page',
      ext: {
        id: this.visit_obj?.visit_id || 0
      }
    });
  },
  methods: {
    preloadImage() {
      uni.getImageInfo({
        src: 'https://static.soyoung.com/sy-pre/20240925-115219-1727241000644.gif',
        success: () => {
          this.pageShow = true;
        }
      });
    },
    handleGo() {
      this.$bridge({
        url:
          '/pages/h5?url=' +
          encodeURIComponent(`https://m.soyoung.com/tmwap25795#/`) // 均跳转App下载引导
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import url(./mixin.less);
.result {
  position: relative;
  padding-top: 290rpx;
  // padding-bottom: 60rpx;
  z-index: 1;
  .status {
    .panel;
    // .panel();
    // padding-top: 157 * 2rpx;
    // background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
    //   no-repeat top center #fff;
    // background-size: 345 * 2rpx 142 * 2rpx;
    img {
      display: block;
      margin: 0 auto;
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 30rpx;
      margin-top: 20rpx;
    }
    .h1 {
      margin-bottom: 20rpx;
      font-size: 40rpx;
      color: #222222;
      text-align: center;
      font-family: PingFangSC-Medium;
      letter-spacing: 1.25rpx;
      font-weight: 500;
    }
    .title {
      color: #030303;
    }
  }
  .result-banner {
    margin: 30rpx;
    .banner-img {
      width: 100%;
      height: 312rpx;
    }
  }
  .info {
    .panel();
    .row {
      box-sizing: border-box;
      padding: 30rpx 0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      min-height: 100rpx;
      border-bottom: 0.5px solid #f0f0f0;
      &:last-child {
        border-bottom: none;
      }
      .text {
        font-size: 28rpx;
        color: #777777;
      }
      .em {
        font-size: 32rpx;
        color: #2b2b2b;
      }
      .fontSize14 {
        font-size: 28rpx;
      }
    }
  }
  .appt {
    li {
      .panel();
      padding: 30rpx;
      .wrap {
        display: flex;
        justify-content: space-between;
        .avatar {
          flex-shrink: 0;
          width: 182rpx;
          height: 136rpx;
          border-radius: 12rpx;
        }
        .text {
          flex: 1;
          box-sizing: border-box;
          padding-left: 20rpx;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: wrap;
          & > div {
            width: 100%;
            font-size: 26rpx;
            padding: 10rpx 0;
            line-height: 40rpx;
          }
        }
      }
      .warn {
        box-sizing: border-box;
        padding: 16rpx 20rpx;
        margin-top: 20rpx;
        background: #f0edff;
        border-radius: 6px;
        font-size: 26rpx;
        color: #8c84ba;
        font-weight: 400;
      }
    }
  }
}
</style>
