@footerBtnHeight: 88rpx;
.panel() {
  padding: 40rpx 30rpx;
  margin: 0 30rpx 30rpx;
  background: #fff;
  // border-radius: 24rpx;
  font-weight: 400;
}

.title {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #aaabb3;
  &::before,
  &::after {
    content: '';
    margin: 0 15rpx;
    height: 14rpx;
    width: 64rpx;
    background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
      no-repeat center center transparent;
    background-size: contain;
    transform-origin: center;
  }
  &::after {
    transform: rotate(180deg);
  }
}
.form {
  position: relative;
  padding-top: 290rpx;
  // padding-bottom: 60rpx;
  z-index: 1;
  .h2 {
    margin-bottom: 10rpx;
    font-family: PingFangSC-Medium;
    font-size: 32rpx;
    color: #030303;
    letter-spacing: 1rpx;
    font-weight: 500;
  }
  .header {
    .panel;
    overflow: hidden;
    .location {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #030303;
      font-weight: 500;
      font-family: PingFangSC-Medium;
      &::before {
        content: '';
        margin-right: 10rpx;
        height: 40rpx;
        width: 40rpx;
        background: url(https://static.soyoung.com/sy-design/2hca4x4bkq4bk1726222138417.png)
          no-repeat center center transparent;
        background-size: contain;
        transform-origin: center;
      }
    }
    .list {
      padding: 20rpx 0 20rpx;
      .row {
        display: flex;
        & > li {
          //margin-bottom: 30rpx;
          &:first-child {
            margin-right: 30rpx;
          }
        }
        // .margin-bottom-30rpx {
        //   margin-bottom: 30rpx;
        // }
      }

      .order-type {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 300rpx;
        height: 84rpx;
        box-sizing: border-box;
        background: #f2f2f2;
        // border-radius: 44rpx;
        font-size: 26rpx;
        border: 4rpx solid #f2f2f2;
        color: #030303;
        &.selected {
          background-color: #fff;
          border: 4rpx solid #333;
          position: relative;
          &::before {
            content: '';
            position: absolute;
            bottom: -4rpx;
            right: -4rpx;
            width: 35rpx;
            height: 28rpx;
            background: url(https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1726222138655.png)
              0 0 no-repeat;
            background-size: 100%;
          }
        }
      }
    }
    .sub-list {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 20rpx;
      background: #f8f8f8;
      //border-radius: 16rpx;
      overflow: hidden;
      width: 100%;
      box-sizing: border-box;

      .sub-order-type {
        width: 180rpx;
        height: 64rpx;
        //margin-bottom: 20rpx;
        //margin-right: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;
        font-size: 26rpx;
        color: #222;
        border: 2rpx solid #fff;
        &:nth-child(3n) {
          margin-right: 0;
        }
        &.selected {
          border: 4rpx solid #333;
          position: relative;
          &::before {
            content: '';
            position: absolute;
            bottom: -4rpx;
            right: -4rpx;
            width: 35rpx;
            height: 28rpx;
            background: url(https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1726222138655.png)
              0 0 no-repeat;
            background-size: 100%;
          }
        }
      }
    }
  }
  .body {
    .panel;
    padding-top: 50rpx;
    padding-bottom: 10rpx;
    overflow: hidden;
    .row {
      box-sizing: border-box;
      padding: 30rpx 0;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      min-height: 100rpx;
      border-bottom: 0.5px solid #f0f0f0;
      &:last-child {
        border-bottom: none;
      }
      .label {
        flex-shrink: 0;
        width: 136rpx;
        font-size: 28rpx;
        color: #777777;
      }
      .input {
        flex: 1;
        color: #303030;
        width: 100%;
        height: 40rpx;
        min-height: 40rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        text-align: right;
        &textarea {
          padding-top: 4rpx;
        }
        &[disabled] {
          color: #777777;
        }
      }
      ////
      .selector {
        flex: 1;
        width: 100%;
        &[disabled] .selector-text {
          color: #777777;
        }
        .selector-text {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 40rpx;
          line-height: 40rpx;
          font-size: 28rpx;
          color: #303030;

          &.arrow-right::after {
            display: inline-block;
            content: '';
            height: 20rpx;
            width: 22rpx;
            background: url('https://static.soyoung.com/sy-pre/20250109-173449-1736413800630.png')
              no-repeat center center transparent;
            background-size: contain;
            transform: translateY(-2rpx);
          }
        }
        .flex-end {
          justify-content: flex-end;
        }
      }
    }
    .item-type {
      padding-top: 30rpx;
      padding-bottom: 20rpx;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;
      li {
        margin-bottom: 30rpx;
        width: 310rpx;
        text-align: center;
        .text {
          text-align: center;
          font-size: 26rpx;
        }
        img {
          box-sizing: border-box;
          margin-bottom: 10rpx;
          height: 184rpx;
          width: 184rpx;
          //border-radius: 12rpx;
          border: 4rpx solid #ffffff;
          background-color: #f8f8f8;
        }
        &:nth-child(2n-1) {
          margin-right: 6rpx;
        }
        &.selected {
          img {
            border: 4rpx solid @border-color;
            background-color: #fff;
            position: relative;
            &::before {
              content: '';
              position: absolute;
              bottom: -4rpx;
              right: -4rpx;
              width: 35rpx;
              height: 28rpx;
              background: url(https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1726222138655.png)
                0 0 no-repeat;
              background-size: 100%;
            }
          }
          .text {
            color: @text-color;
          }
        }
      }
    }
  }
}
.fontOutFit {
  font-family: Outfit-Regular;
}
