<template>
  <picker
    class="selector"
    @change="pickerChange"
    mode="selector"
    :value="index"
    :range="options"
    :range-key="optionLabel"
  >
    <div class="selector-text arrow-right">
      <span>{{ selectorText }}</span>
    </div>
  </picker>
</template>
<script>
export default {
  name: 'signin-selector',
  props: {
    value: {
      type: [String, Number]
    },
    options: {
      type: Array,
      default: () => []
    },
    optionLabel: {
      type: String,
      default: 'label'
    },
    optionValue: {
      type: String,
      default: 'value'
    }
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  data() {
    return {
      index: -1
    };
  },
  watch: {
    value: {
      handler(val) {
        this.index = this.options.findIndex(
          (opt) => opt[this.optionValue] === val
        );
      },
      immediate: true
    }
  },
  computed: {
    selectorText() {
      return (
        this.options.find((opt) => opt[this.optionValue] === this.value)?.[
          this.optionLabel
        ] || ''
      );
    }
  },
  methods: {
    pickerChange(e) {
      const index = e.detail.value;
      this.$emit('input', this.options[index]?.[this.optionValue]);
      setTimeout(() => {
        console.log(this.value);
      });
    }
  }
};
</script>
<style lang="less" scoped>
.selector {
  display: block;
  width: 100%;
  &[disabled] .selector-text {
    color: #777777;
  }
  .selector-text {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    color: #2b2b2b;

    &.arrow-right::after {
      display: inline-block;
      content: '';
      height: 20rpx;
      width: 22rpx;
      background: url('https://static.soyoung.com/sy-pre/20250109-173449-1736413800630.png')
        no-repeat center center transparent;
      background-size: contain;
      transform: translateY(-2rpx);
    }
  }
}
</style>
