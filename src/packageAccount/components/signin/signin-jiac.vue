<!-- 新氧优享咨询二维码 -->
<template>
  <div class="container" v-if="visible">
    <div
      class="img-inner-wrap"
      v-if="joinc_check_flag === 1 && has_join_c === 1"
      @click="jump"
    >
      <img
        class="img-inner"
        :src="base_url"
        :show-menu-by-longpress="true"
        mode="widthFix"
      />
    </div>
    <div class="join" v-else>
      <img
        class="join__background_img"
        :src="poster_url"
        :show-menu-by-longpress="!code_url"
        mode="widthFix"
      />
      <img
        v-if="code_url"
        class="join__img"
        :src="code_url"
        :show-menu-by-longpress="true"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { apiGetAcodeUrl } from '@/api/account';
export default {
  name: 'join',
  props: {
    channel: {
      type: String,
      default: ''
    },
    visible: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  watch: {
    channel: {
      handler() {
        this.getConfigData();
      },
      immediateL: true
    }
  },
  data() {
    return {
      code_url: '',
      has_join_c: 0,
      poster_url: '',
      base_url: '',
      base_jump_url: '',
      joinc_check_flag: 0
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  methods: {
    async getConfigData() {
      await this.$uniLogin();
      try {
        const responseData = await apiGetAcodeUrl({
          channel: this.channel,
          market_activity_id: this.userInfo.marketActivityId
        });
        if (!responseData) return;
        const {
          code_url,
          has_join_c,
          poster_url,
          base_url,
          base_jump_url,
          joinc_check_flag
        } = responseData;
        this.code_url = code_url;
        this.has_join_c = has_join_c;
        this.poster_url = poster_url;
        this.base_url = base_url;
        this.base_jump_url = base_jump_url;
        this.joinc_check_flag = joinc_check_flag;
      } catch (err) {
        uni.$log('sigin', err, 'err');
      }
    },
    jump() {
      if (!this.base_jump_url) return;
      if (this.has_join_c !== 1) return;
      if (/^http(s)?/g.test(this.base_jump_url)) {
        this.$toH5(this.base_jump_url);
      } else {
        const url = this.base_jump_url.startsWith('/')
          ? this.base_jump_url
          : `/${this.base_jump_url}`;
        const path = [
          '/pages/index',
          '/pages/item',
          '/pages/coupon-center',
          '/pages/my'
        ];
        if (path.includes(url)) {
          uni.switchTab({
            url
          });
        } else {
          uni.navigateTo({
            url
          });
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: #fff;
  z-index: 10;
}
.img-inner-wrap {
  .img-inner {
    width: 100%;
    height: auto;
  }
}
.join {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
  &__background_img {
    width: 100vw;
  }
  &__img {
    position: absolute;
    top: 856rpx;
    left: 50%;
    z-index: 10;
    transform: translateX(-50%);
    padding: 10rpx;
    width: 308rpx;
    height: 304rpx;
    background: #fff;
    border: 0.5px solid #a6a6a6;
  }
}
</style>
