<template>
  <section class="form animate__animated animate__fadeInUp">
    <div class="header">
      <div class="title">{{ tenant_obj.tenant_title }}</div>
      <div class="location">{{ tenant_name || '加载中...' }}</div>
    </div>
    <div class="body">
      <div class="h2">{{ user_obj.user_title }}</div>
      <ul>
        <li class="row">
          <label class="label">{{ user_obj.realname_title }}</label>
          <input class="input" type="text" v-model.trim="form.realname" />
        </li>
        <li class="row">
          <label class="label">{{ user_obj.gender_title }}</label>
          <Selector style="flex: 1" v-model="form.gender" :options="sexList" />
        </li>
        <li class="row">
          <label class="label">{{ user_obj.mobile_title }}</label>
          <input
            :disabled="disabledMobile"
            class="input fontOutFit"
            type="number"
            v-model.trim="form.mobile"
            maxlength="13"
          />
        </li>
        <li class="row">
          <label class="label">{{ user_obj.card_type_title }}</label>
          <Selector
            style="flex: 1"
            v-model="form.card_type"
            :options="idcard_categories_obj"
            optionLabel="name"
            optionValue="code"
            @input="onCardTypeChange"
          />
        </li>
        <li class="row">
          <label class="label">{{ user_obj.id_card_title }}</label>
          <input
            class="input fontOutFit"
            type="text"
            :value="form.id_card"
            :maxlength="isIdCardType ? 18 : 30"
            @input="onIdcardInput"
          />
        </li>
        <li class="row">
          <label class="label">{{ user_obj.birthday_title }}</label>
          <picker
            @change="onBirthDayChange"
            :value="form.birthday"
            class="selector"
            mode="date"
          >
            <div class="selector-text arrow-right flex-end">
              <span>{{ form.birthday }}</span>
            </div>
          </picker>
        </li>
        <li class="row">
          <label class="label">{{ user_obj.region_title }}</label>
          <view
            class="touch-block touch-block-txt flex-end"
            @click="handleTriggerPickerVisibleToshow"
            >{{ showRegionData }}</view
          >
        </li>
        <li class="row">
          <label class="label">{{ user_obj.contact_address_title }}</label>
          <textarea
            class="input"
            v-model="form.contact_address"
            auto-height="true"
            disable-default-padding="true"
          />
        </li>
      </ul>
    </div>
  </section>
</template>

<script>
import Selector from './signin-selector.vue';
import dayjs from '@/common/dayjs.min';
import dayjs_plugin_advancedFormat from '@/common/dayjs/customParseFormat/index';
dayjs.extend(dayjs_plugin_advancedFormat);
const sexList = [
  {
    value: 2,
    label: '女'
  },
  {
    value: 1,
    label: '男'
  }
];
export default {
  components: { Selector },
  props: {
    user_obj: {
      type: Object,
      default: () => {}
    },
    idcard_categories_obj: {
      type: Array,
      default: () => []
    },
    pickerVisible: {
      type: Boolean,
      default: false
    },
    showRegionData: {
      type: String,
      default: ''
    },
    tenant_obj: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    showRegionData: {
      handler(val) {
        this.form.region = val;
      },
      deep: true
    },
    user_obj: {
      handler(val) {
        // 机构名称
        // this.tenant_name = val.tenant_name;
        this.setEditData(val);
      },
      deep: true,
      immediate: true
    },
    tenant_obj: {
      handler(val) {
        // 机构名称
        this.tenant_name = val.tenant_name;
        // this.setEditData(val);
      },
      deep: true,
      immediate: true
    },
    idcard_categories_obj: {
      handler(val) {
        if (this.form.card_type === '') {
          this.setDefaultCardType(val);
        }
      },
      immediate: true
    },
    form: {
      handler() {
        this.$emit('change', {
          ...this.form
          // region: [...this.form.region]
        });
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      tenant_name: '',
      disabledMobile: true,
      form: {
        realname: '',
        gender: 2,
        mobile: '',
        id_card: '',
        card_type: '',
        birthday: '',
        // region: [],
        contact_address: ''
      },
      sexList
    };
  },
  computed: {
    // selectorRegionText() {
    //   return this.form.region.join(' ');
    // },
    isIdCardType() {
      return this.form.card_type === 'identification';
    }
  },
  methods: {
    handleTriggerPickerVisibleToshow() {
      this.$emit('handleTriggerPickerVisible');
    },
    onCardTypeChange() {
      this.form.id_card = '';
    },
    onBirthDayChange(e) {
      this.form.birthday = e.detail.value;
    },
    onRegionChange(e) {
      this.form.region = e.detail.value;
    },
    onIdcardInput(e) {
      this.form.id_card = e.detail.value;
      if (!this.isIdCardType) return;
      if (this.form.id_card.length !== 18) return;
      this.parseIdCard();
    },
    parseIdCard() {
      const idCard = this.form.id_card;
      const matches = /^\d{6}(\d{4})(\d{2})(\d{2})\d{2}(\d{1})[\dxX]{1}$/g.exec(
        idCard
      );
      if (!matches) return;
      const [_, year, month, day, gender] = matches;
      const dateStr = `${year}-${month}-${day}`;
      console.log(
        '身份证解析验证',
        dayjs(dateStr, 'YYYY-MM-DD', true).isValid()
      );
      if (dayjs(dateStr, 'YYYY-MM-DD', true).isValid()) {
        this.form.birthday = dateStr;
      } else {
        this.form.birthday = '';
      }
      this.form.gender = gender % 2 === 0 ? 2 : 1;
      console.log(_);
    },
    // setEditDataFromStorage() {
    //   const data = wx.getStorageSync('__crm_sign_in_form__');
    //   // wx.removeStorageSync('__crm_sign_in_form__');
    //   if (!data) return;
    //   const {
    //     save_timestamp,
    //     realname,
    //     gender,
    //     mobile,
    //     card_type,
    //     id_card,
    //     birthday,
    //     region,
    //     contact_address
    //   } = data;
    //   // 检验数据的有效性
    //   if (!save_timestamp) {
    //     return;
    //   }
    //   // 本地缓存有效期
    //   if (Date.now() - save_timestamp > 3600 * 1000) {
    //     return;
    //   }
    //   uni.$log('signin_storage', data);
    //   // 姓名
    //   this.form.realname = realname;
    //   // 性别
    //   this.form.gender = gender;
    //   // 电话号码
    //   this.form.mobile = mobile;
    //   // 证件类型
    //   this.form.card_type = card_type;
    //   // 身份证号
    //   this.form.id_card = id_card;
    //   // 生日
    //   this.form.birthday = birthday;
    //   // 地理位置
    //   this.form.region = region;
    //   // 联系方式
    //   this.form.contact_address = contact_address;
    //   console.log('info data set from storage !!!');
    // },
    setDefaultCardType(list) {
      if (list.length === 0) return;
      // 默认选中身份证，没有身份证，就选中列表中的第一个
      const idCard = list.find(({ name }) => name === '身份证');
      if (idCard) {
        this.form.card_type = idCard.code || '';
      } else {
        this.form.card_type = list[0]?.code || '';
      }
    },
    setEditData({
      // 姓名
      realname,
      // 性别
      gender,
      // 手机号
      mobile,
      // 证件类型
      card_type,
      // 证件号
      id_card,
      // 生日
      birthday,
      // 所属地区
      province,
      city,
      district,
      // 联系地址
      contact_address
    }) {
      // 姓名
      this.form.realname = realname;
      // 性别
      this.form.gender = gender;
      // 电话号码格式化
      if (mobile) {
        this.form.mobile = mobile;
        this.disabledMobile = true;
      }
      // 证件类型
      if (card_type) this.form.card_type = card_type;
      // 身份证号格式化
      if (id_card) {
        this.form.id_card = id_card;
      }
      this.form.birthday = birthday;
      // 所在区域
      if (province && city && district) {
        this.form.region = [province, city, district];
      }
      // 家庭住址
      this.form.contact_address = contact_address;
      console.log('info data set from webserver !!!', this.form);
    }
  },
  beforeCreate() {
    // uni.$on('__signin_fetch_main_compleated__', () => {
    //   this.setEditDataFromStorage();
    // });
  },
  async onPageShow() {},
  onPageHide() {
    // wx.setStorageSync('__crm_sign_in_form__', {
    //   ...this.form,
    //   save_timestamp: Date.now()
    // select_source_id: this.select_source_id,
    // parts_list: this.parts_list,
    // });
  }
};
</script>
<style lang="less" scoped>
@import url(./mixin.less);

.touch-block {
  flex: 1;
  height: 40rpx;
  text-align: right;
}
.touch-block-txt {
  line-height: 40rpx;
  font-size: 28rpx;
  color: #2b2b2b;
}
</style>
