<template>
  <section class="form animate__animated animate__fadeInUp">
    <div class="header">
      <div class="title">{{ tenant_obj.tenant_title }}</div>
      <div class="location">{{ tenant_obj.tenant_name || '加载中...' }}</div>
    </div>
    <div
      class="header"
      style="padding-bottom: 20rpx"
      v-if="
        (!want_obj.select_source_id || want_obj.select_source_id < 1) &&
        rows &&
        rows.length > 0
      "
    >
      <!-- <div class="title">选择您本次的服务意向</div> -->
      <div class="h2" style="padding-top: 10rpx">
        {{ want_obj.source_title }}
      </div>
      <ul class="list" attr-data="渠道">
        <block v-for="(row, rowIndex) in rows" :key="rowIndex">
          <div class="row" style="margin-bottom: 30rpx">
            <li v-for="(item, index) in row" :key="rowIndex * 2 + index">
              <div
                :class="{
                  'order-type': true,
                  selected: String(select_source.id) === String(item.id),
                  'margin-bottom-30rpx':
                    String(select_source.id) === String(item.id)
                }"
                :data-item="item"
                :data-index="rowIndex * 2 + index"
                @click="onToggleVisit"
              >
                {{ item.name }}
              </div>
            </li>
          </div>
          <template
            v-if="
              select_index !== null &&
              Math.floor(select_index / 2) === rowIndex &&
              subSourceList.length
            "
          >
            <template v-if="select_source.old_bring_new == 1">
              <div v-if="oldnewInfo" class="sub-old-new">
                <div class="oldnew-text">
                  <div>
                    {{ oldnewInfo.name }}
                    <template v-if="oldnewInfo.gender">
                      |{{ oldnewInfo.gender }}
                    </template>
                  </div>

                  <div class="oldnew-mobile">{{ oldnewInfo.mobile }}</div>
                </div>

                <div class="change-btn" @click="handleChangeOldNew">更换</div>
              </div>
            </template>

            <view v-else class="sub-list" style="margin-bottom: 30rpx">
              <li
                v-for="item in subSourceList"
                class="sub-order-type"
                :key="item.id"
                :data-id="item.id"
                :class="{
                  selected: String(select_source_id) === String(item.id)
                }"
                @click="onToggleSubVisit"
              >
                {{ item.name }}
              </li>
            </view>
          </template>
        </block>
      </ul>
    </div>
    <div class="body" v-if="!visit_obj.visit_status > 0" attr-data="意向">
      <div class="h2">{{ want_obj.part_title }}</div>
      <ul class="item-type">
        <li
          :class="{
            selected: item.selected
          }"
          v-for="item in parts_list"
          :data-img="item.img"
          @click="onToggleParts"
          :key="item.id"
        >
          <img :src="item.img" lazy-load />
          <div class="text">{{ item.name }}</div>
        </li>
      </ul>
    </div>
  </section>
</template>
<script>
export default {
  props: {
    user_obj: {
      type: Object,
      default: () => {}
    },
    has_select_source: {
      type: Boolean,
      default: false
    },
    select_source_id: {
      type: String,
      default: ''
    },
    select_index: {
      type: Number,
      default: null
    },
    oldnewInfo: Object,
    select_source_name: {
      type: String,
      default: ''
    },
    parts_list: {
      type: Array,
      default: () => []
    },
    source_list: {
      type: Array,
      default: () => []
    },
    select_source: {
      type: Object,
      default: () => {}
    },
    want_obj: {
      type: Object,
      default: () => {}
    },
    tenant_obj: {
      type: Object,
      default: () => {}
    },
    visit_obj: {
      type: Object,
      default: () => {}
    },
    step: {
      type: Number,
      default: 0
    }
  },
  watch: {
    step: {
      handler(newVal) {
        if (newVal && newVal === 2) {
          this.$reportPageShow({
            info: 'sy_chain_store_other_second_sign_in_page'
          });
        }
      }
    },
    select_source_id: {
      handler() {
        this.setDefaultTopId();
      },
      immediate: true
    }
  },
  computed: {
    rows() {
      const result = [];
      for (let i = 0; i < this.source_list.length; i += 2) {
        result.push(this.source_list.slice(i, i + 2));
      }
      console.log('result', result);
      return result;
    },
    subSourceList() {
      const target = this.source_list.find(
        (item) => String(this.select_source.id) === String(item.id)
      );
      return target?.child_item || [];
    }
  },
  methods: {
    async setDefaultTopId() {
      await this.$nextTick();
      for (let i = 0, length = this.source_list.length; i < length; i++) {
        const cur = this.source_list[i];
        if (
          cur.child_item?.find(
            (item) => String(item.id) === String(this.select_source_id)
          )
        ) {
          this.$emit('update:select_index', i);

          break;
        }
      }
    },
    onToggleVisit(e) {
      const item = e.currentTarget.dataset.item;
      const index = e.currentTarget.dataset.index;
      console.log('onToggleVisit', this.select_source.id === item.id);
      if (this.select_source.id === item.id) {
        return;
      }
      if (item.old_bring_new == 1 && !this.oldnewInfo) {
        this.$emit('handleOldBringNew', {
          index
        });
      } else {
        this.$emit('update:select_index', index);
        this.$emit('update:select_source_id', '');
      }
    },
    handleChangeOldNew() {
      this.$emit('handleOldBringNew', {
        index: this.select_index,
        item: this.oldnewInfo
      });
    },
    onToggleSubVisit(e) {
      const id = e.currentTarget.dataset.id;
      if (this.select_source_id === id) {
        this.$emit('update:select_source_id', '');
      } else {
        this.$emit('update:select_source_id', id);
      }

      try {
        const _title =
          this.rows[Math.floor(this.select_index / 2)][this.select_index % 2]
            .name;

        const _content = this.subSourceList.find(
          (item) => String(item.id) === String(id)
        ).name;

        this.$reportData({
          info: 'sy_chain_store_other_my:client_resource_click',
          ext: {
            title: _title,
            content: _content
          }
        });
      } catch (e) {
        console.log(e);
      }
    },
    onToggleParts(e) {
      const img = e.currentTarget.dataset.img;
      const parts_list = [];
      this.parts_list.forEach((item) => {
        if (item.img === img) {
          parts_list.push({
            ...item,
            selected: !item.selected
          });
        } else {
          parts_list.push({ ...item });
        }
      });
      this.$emit('update:parts_list', parts_list);

      this.$reportData({
        info: 'sy_chain_store_other_my:preferred_body_parts_click',
        ext: {
          content: parts_list
            .filter((item) => item.selected)
            .map((item) => item.name)
            .join(',')
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
@import url(./mixin.less);
.sub-old-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f2f2f2;
  padding: 0 24rpx;
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #8c8c8c;
  letter-spacing: 0;
  height: 96rpx;
  font-weight: 400;
  margin-bottom: 30rpx;
  .oldnew-text {
    display: flex;
  }
  .oldnew-mobile {
    margin-left: 20rpx;
  }
  .change-btn {
    width: 108rpx;
    background: #030303;
    height: 48rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
