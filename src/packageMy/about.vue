<template>
  <div class="about">
    <div class="about__logo">
      <img
        class="about__logo--icon"
        src="https://static.soyoung.com/sy-design/32q76aat6dbg71726113001444.png"
        alt=""
        @click="handleClickLogo"
      />
      <p class="about__logo--version">v{{ wxa_ver }}</p>
    </div>
    <ul class="about__list">
      <!-- 需要根据red_dot_cnt判断是否显示红点，新增一个类名动态去判断，原有的类名不丢失 -->
      <li
        class="about__list--li"
        v-for="(item, ind) in itemList"
        :key="item.name"
        :style="{ fontWeight: Number(item.bold_yn) === 1 ? 'bold' : 'normal' }"
        @click="toPage(item, ind)"
      >
        <span
          :class="[
            'about__list--text',
            item.red_dot_cnt > 0 ? 'about__list--text-red-dot' : ''
          ]"
          >{{ item.name }}</span
        >
        <i class="about__list--icon"></i>
      </li>
      <li class="about__list--li" @click="onClearCache">
        <span class="about__list--text">清除缓存</span>
      </li>
      <li class="about__list--li" @click="onFixLogin">
        <span class="about__list--text">重置登陆</span>
      </li>
      <li class="about__list--li" @click="showQrCodeUtils">
        <span class="about__list--text">开发者信息</span>
        <span class="about__list--company">北京新氧科技有限公司</span>
      </li>
      <block v-if="scanVisible">
        <li class="about__list--li">
          <span class="about__list--text-mini">基础库版本</span>
          <span class="about__list--company">{{ version }}</span>
        </li>
        <li class="about__list--li" @click="scanShiningCode">
          <span class="about__list--text-mini">扫码</span>
          <span class="about__list--company">点击</span>
        </li>
      </block>
    </ul>
  </div>
</template>

<script>
import config from '@/config';
import smartReport from '@/common/smartReport';
import { getAboutUsApi, ViewPolicy } from '@/api/my';
import { uniLogin, syLogin } from '@/plugins/login';
export default {
  pageTrackConfig: 'sy_wxtuan_tuan_about_us_page',
  name: 'about',
  data() {
    return {
      clickLogoCount: 0,
      wxa_ver: config.version,
      isBack: true,
      itemList: [],
      clickDevCount: 0,
      scanVisible: false,
      version: wx.getAppBaseInfo().SDKVersion,
      productPage: 0
    };
  },
  created() {
    // this.getPageData();
    this.scanVisible = uni.getStorageSync('__ABOUT_DEV_VISIBLE__') || 0;
  },
  computed: {
    appName() {
      return __MINI_PRIME_CHAIN_STORE__ ? '优享连锁' : '优享';
    }
  },
  methods: {
    // 获取页面数据
    async getPageData() {
      const responseData = await getAboutUsApi();
      if (responseData) {
        this.itemList = responseData;
      }
    },
    handleClickLogo() {
      this.clickLogoCount += 1;
      if (this.clickLogoCount > 5) {
        const uid = uni.getStorageSync('uid') || 0;
        wx.scanCode({
          success(res) {
            const { result } = res;
            if (result) {
              uni.request({
                header: {
                  'content-type': 'application/x-www-form-urlencoded;'
                },
                url: result,
                data: {
                  uid: uid
                },
                method: 'POST'
              });
              console.log('埋点测试平台返回地址:', result);
              console.log('uid', uid);
              smartReport.enableReportAnalyze();
            }
          }
        });
      }
    },
    scanShiningCode() {
      wx.scanCode({
        success(res) {
          const { result } = res;
          if (!result) return;
          let prevFix = result.startsWith('/');
          wx.restartMiniProgram({
            path: `${prevFix ? '' : '/'}${result}`
          });
        }
      });
    },
    showQrCodeUtils() {
      this.clickDevCount += 1;
      if (this.clickDevCount >= 3) {
        this.scanVisible = true;
        uni.setStorageSync('__ABOUT_DEV_VISIBLE__', 1);
      }
      this.productPage += 1;
      if (this.productPage >= 5) {
        uni.showModal({
          showCancel: true,
          editable: true,
          success: (res) => {
            if (res.confirm) {
              uni.navigateTo({
                url: `/pages/product?id=${res.content}`
              });
            }
          }
        });
        this.productPage = 0;
      }
    },
    onFixLogin() {
      wx.showLoading({
        title: '修复中',
        mask: true
      });
      uni.removeStorageSync('user_info');
      uniLogin.clear();
      syLogin(true).finally(() => {
        wx.hideLoading();
      });
    },
    onClearCache() {
      // spu详情页 加C拦截
      uni.removeStorageSync('__SKU_POPUP_GROUP_JOINC_FLAG__');
      // spu详情页 红包弹窗
      uni.removeStorageSync('__SKU_POPUP_COUPON_FLAG__');
      // app 埋点队列
      uni.removeStorageSync('__APP_REPORT_QUEUE__');
      // 签到缓存数据
      uni.removeStorageSync('__crm_sign_in_form__');
      uni.showToast({
        title: '已清除缓存',
        icon: 'none'
      });
    },
    async toPage(data, index) {
      // 埋点
      this.$reportData({
        info: 'sy_wxtuan_tuan_sy:qualification_click',
        ext: {
          serial_num: index + 1 || '',
          content: data.name || '',
          url: data.url || ''
        }
      });
      if (data.key && data.url) {
        const responseData = await ViewPolicy({ key: data.key });
        console.log('responseData', responseData);
      }

      this.isBack = false;

      const url = '/pages/h5?url=' + encodeURIComponent(data.url);
      this.$bridge({
        url: url
      });
    }
  },
  onUnload() {
    if (this.isBack) return;
    // 埋点
    this.$reportData({
      info: 'sy_wxtuan_tuan_my_back:btn_click',
      ext: {}
    });
  },
  onShow() {
    this.getPageData();
  }
};
</script>

<style lang="less" scoped>
.about {
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  &__logo {
    color: #999;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    text-align: center;
    &--icon {
      padding: 80rpx 0 10rpx;
      width: 280rpx;
      height: 122rpx;
    }
  }
  &__list {
    margin-top: 80rpx;
    padding: 0 30rpx;
    &--li {
      display: flex;
      height: 124rpx;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px #f2f2f2 solid;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
      position: relative;
      &:last-child {
        border-bottom: none;
      }
    }
    &--text-red-dot {
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 10%;
        right: -12rpx;
        width: 10rpx;
        height: 10rpx;
        background-color: #ff6600;
        border-radius: 50%;
      }
    }
    &--icon {
      display: inline-block;
      width: 15rpx;
      height: 21rpx;
      background: url(https://static.soyoung.com/sy-pre/right-1657505400720.png)
        no-repeat;
      background-size: 100%;
    }
    &--text-mini {
      font-size: 0.8em;
      color: #555;
    }
    &--company {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #646464;
    }
  }
}
</style>
