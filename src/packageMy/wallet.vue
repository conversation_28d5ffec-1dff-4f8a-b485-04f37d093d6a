<template>
  <div class="wallet" :class="{ isios: isIOS() }">
    <!-- top  start  -->
    <div class="wallet__top">
      <p class="wallet__top--title">
        <span>总金额（元）</span>
        <span class="right" @click="handleAuth">{{
          allow_authentication != 1 ? '提现请先完成实名认证' : '实名认证'
        }}</span>
      </p>
      <div class="wallet__top--price">
        <span class="wallet__top--num">{{ balance }}</span>
        <span
          class="wallet__top--btn"
          :class="
            allow_authentication == 1 && Number(balance) > 0 ? 'active' : ''
          "
          @click="openWithdraw"
          >提现</span
        >
      </div>
      <p class="use-tip" @click="showUseTip">
        <img
          src="https://static.soyoung.com/sy-design/238380hlp6pof1725621591651.png"
          alt=""
        />
        使用须知
      </p>
    </div>
    <!-- top   end -->
    <div class="wallet__list">
      <div class="wallet__list--title">账单记录</div>
      <ul v-if="detailrecords.length">
        <li class="wallet__list--li" v-for="item in detailrecords" :key="item">
          <div class="wallet__li--type">
            <span>{{ item.type }}</span
            ><span class="wallet__li--num"
              >{{ item.in_or_out == 1 ? '+' : '-'
              }}{{ item.in_or_out_amount }}</span
            >
          </div>
          <div class="wallet__li--count">
            <span>{{ item.ctime }}</span
            ><span>余额：{{ item.amount }}</span>
          </div>
        </li>
      </ul>
      <div v-else class="wallet__list--empty">
        <img
          src="https://static.soyoung.com/sy-design/3up7pnqhzxgn81727169008482.png"
          alt=""
        />
        <div>暂无账单记录</div>
      </div>
    </div>
    <customPopup title="提示" v-model="popupStatus" width="300px">
      <template #body>
        <rich-text :nodes="popupText"></rich-text>
      </template>
    </customPopup>

    <Popup ref="popupRef" @change="handlerPopupChange">
      <div class="withdraw-box">
        <div class="withdraw-close" @click="closeWithdraw"></div>
        <div class="withdraw-header">提现到微信</div>
        <div class="withdraw-form">
          <div class="prefix">￥</div>
          <input
            :value="withdrawValue"
            class="input"
            type="digit"
            :focus="withdrawFocus"
            placeholder="请输入金额"
            :cursor-spacing="cursorSpacing"
            cursor-color="#89DC65"
            placeholder-style="color: #BABABA;"
            @input="handlerInput"
          />
          <div class="withdraw-all" @click="withdrawAll">全部提现</div>
        </div>
        <div class="withdraw-total">当前余额{{ balance }}</div>
        <div class="withdraw-footer">
          <div class="btn-cancel" @click="closeWithdraw">取消</div>
          <div
            class="btn-ok"
            :class="{ active: withdrawValue > 0 }"
            @click="handlerWithdraw"
          >
            确认
          </div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script>
import Popup from '@/components/uni/popup';
import customPopup from '@/components/popup';
import { getAccountListApi, applyWithdrawalApi } from '@/api/my';
import SubscribeMixins from '@/mixins/subscribe';

export default {
  name: 'wallet',
  components: {
    customPopup,
    Popup
  },
  mixins: [SubscribeMixins],
  data() {
    return {
      balance: '',
      list: [],
      detailrecords: [],
      avatar: '',
      loadmore: {
        more: 1,
        emptyText: '没有发现任何数据',
        total: 1
      },
      page: 1,
      allow_authentication: 0, // 等于1的时候提现按钮可点 其他未实名置灰
      is_first: 0,
      not_allow_authentication: {},
      popupStatus: false,
      popupText: '',
      notice: '',
      withdrawFocus: false,
      withdrawValue: '',
      cursorSpacing: 100,
      min: 0.3,
      max: 10000,
      min_desc: '',
      max_desc: ''
    };
  },
  onShow() {
    this.getData();
  },
  methods: {
    handlerInput(e) {
      let { value: v } = e.detail;
      if (isNaN(Number(v))) {
        wx.showToast({
          title: '请输入正确的数字',
          icon: 'none'
        });
        return '';
      }
      v = v.replace(/^[0]+/g, '0');
      // if (v.length >= String(this.min).length && this.min > Number(v)) {
      //   v = String(this.min);
      // }
      // if (Number(this.balance) < Number(v)) {
      //   v = this.balance;
      // }
      if (/\.[0-9]{2,}/g.test(v)) {
        const [_int, _float] = v.split('.');
        v = `${_int}.${_float.slice(0, 2)}`;
      }
      this.withdrawValue = v;
      return v;
    },
    async openWithdraw() {
      if (this.allow_authentication != 1 || Number(this.balance) === 0) {
        return;
      }
      await this.$refs.popupRef.open('bottom');
    },
    closeWithdraw() {
      this.$refs.popupRef.close('bottom');
    },
    withdrawAll() {
      this.withdrawValue = this.balance;
    },
    showUseTip() {
      this.popupText = this.notice;
      this.popupStatus = true;
    },
    // 获取页面数据
    async getData() {
      if (this.page === 1) {
        this.list = [];
      }
      const data = await getAccountListApi({
        page: this.page++
      });
      if (data) {
        // const data = res.data.responseData;
        // this.avatar = data.avatar;
        // const data = res.data.data
        this.min = data.min;
        this.max = data.max;
        this.min_desc = data.min_desc;
        this.max_desc = data.max_desc;
        this.balance = data.balance;
        this.list.push(...(data.detailrecords || []));
        this.notice = data.notice;
        this.detailrecords = this.list;
        this.loadmore.more = +data.has_more;
        this.allow_authentication = data.allow_authentication;
        this.not_allow_authentication = data.not_allow_authentication;
        if (+data.is_first == 1) {
          this.popupStatus = true;
          this.popupText =
            '新氧青春钱包已升级，截止2022年5月28日10:30的钱包余额和明细，请前往【新氧APP】-【钱包】查看';
          // wx.showModal({
          //   title: '提示',
          //   content: '新氧优享钱包已升级，截止2022年5月28日10:30的钱包余额和明细，请前往【新氧APP】-【钱包】查看',
          //   showCancel: false,
          //   confirmText: '知道了',
          //   success () {
          //   }
          // })
        }
        if (
          data.not_allow_authentication &&
          +data.not_allow_authentication.have_see === 0 &&
          data.not_allow_authentication.reason !== ''
        ) {
          // wx.showModal({
          //   title: '提示',
          //   content: data.not_allow_authentication.reason,
          //   showCancel: false,
          //   confirmText: '知道了',
          //   success () {
          //   }
          // })
          this.popupStatus = true;
          this.popupText = data.not_allow_authentication.reason;
          this.doUpHaveSee(data.not_allow_authentication.id);
        }
      }
    },
    // 点击提现
    async handlerWithdraw() {
      if (Number(this.withdrawValue) > Number(this.balance)) {
        uni.showToast({
          title: '输入金额需小于余额',
          icon: 'none'
        });
        return;
      }
      if (Number(this.withdrawValue) < this.min) {
        uni.showToast({
          title: this.min_desc,
          icon: 'none'
        });
        return;
      }
      if (Number(this.withdrawValue) > this.max) {
        uni.showToast({
          title: this.max_desc,
          icon: 'none'
        });
        return;
      }
      if (+this.allow_authentication === 1) {
        const res = await applyWithdrawalApi({
          account: this.withdrawValue
        });
        const { errorCode, errorMsg, responseData } = res || {};
        if (+errorCode === 200 || +errorCode === 0) {
          uni.showToast({
            title: '提现申请成功等待审核',
            icon: 'none'
          });
          const subscribe = await this.createSubscribe({
            '6DRqNnpzmYuWDLCQiniij05FQSpYur-gV3YOMyVEyqE': [7]
          });
          subscribe?.({ draw_cash_apply_id: responseData.id });
          setTimeout(() => {
            this.page = 1;
            this.getData();
            this.closeWithdraw();
          }, 1000);
        } else {
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      }
    },
    handleAuth() {
      this.$bridge({
        url: '/packageMy/auth'
      });
    },
    handleUseTip() {},
    async doUpHaveSee(id) {
      const res = await this.$request({
        url: '/groupBuy/account/UpHaveSee',
        isLoading: false,
        data: {
          id: id
        }
      });
      const { status, message } = res.data;
      if (status !== 200) {
        uni.showToast({
          title: message,
          icon: 'none'
        });
      }
    },
    handlerPopupChange({ show }) {
      console.log(show);
      if (!show) {
        this.withdrawFocus = false;
        this.withdrawValue = '';
        return;
      }
      setTimeout(() => {
        this.$queryFirstNode('.input').then((res) => {
          const { windowHeight } = uni.getSystemInfoSync();
          const { bottom } = res;
          this.cursorSpacing = windowHeight - bottom;
          console.log(this.cursorSpacing, windowHeight, bottom);
          setTimeout(() => {
            this.withdrawFocus = true;
          }, 100);
        });
      }, 350);
    }
  },
  // 滚动到底部
  onReachBottom() {
    if (this.loadmore.more) {
      this.getData();
    }
  }
};
</script>

<style lang="less" scoped>
.wallet {
  padding-inline: 30rpx;
  &.isios {
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
  &__top {
    position: relative;
    padding: 64rpx 20rpx 0 40rpx;
    height: 300rpx;
    background-color: #f8f8f8;
    box-sizing: border-box;
    .use-tip {
      position: absolute;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      bottom: 40rpx;
      right: 30rpx;
      font-size: 24rpx;
      color: @text-color;
      font-weight: 400;
      z-index: 1;
      img {
        margin-right: 5rpx;
        height: 24rpx;
        width: 24rpx;
      }
    }
    &--title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFangSC-Regular;
      font-size: 30rpx;
      color: #222222;
      .right {
        display: inline-flex;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        // color: #5448EE;
        color: @text-color;
        letter-spacing: 0;
        font-weight: 400;
        &:after {
          display: inline-block;
          content: '';
          background-image: url('https://static.soyoung.com/sy-pre/30p278sehb837-1726812600633.png');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          width: 21rpx;
          height: 30rpx;
          margin-left: 10rpx;
          transform: rotate(270deg);
        }
      }
    }
    &--price {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &--num {
      font-family: Outfit-Medium;
      font-size: 90rpx;
      color: #030303;
      font-weight: 500;
    }
    &--btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 144rpx;
      height: 56rpx;
      color: #fff;
      background: #bababa;
      font-size: 26rpx;
      &.active {
        // background: #5448EE;
        background: @border-color;
      }
    }
  }
  &__list {
    padding: 0 20rpx;
    &--title {
      padding: 0 20rpx;
      height: 124rpx;
      line-height: 124rpx;
      font-family: PingFangSC-Medium;
      font-size: 32rpx;
      color: #222222;
      background: #ffffff;
      border-bottom: 1px solid #f9f9f9;
    }
    &--empty {
      padding-top: 220rpx;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #aaabb3;
      text-align: center;
      img {
        width: 70rpx;
        height: 70rpx;
        margin-bottom: 20rpx;
      }
    }
    &--li {
      padding: 30rpx 20rpx;
      border-bottom: 1px solid #f9f9f9;
      .wallet__li {
        &--type {
          display: flex;
          margin-bottom: 6rpx;
          justify-content: space-between;
          align-items: center;
          font-family: PingFangSC-Regular;
          font-size: 30rpx;
          color: #222222;
        }
        &--num {
          font-family: Outfit-Medium;
          font-size: 30rpx;
          // color: #5448EE;
          color: @text-color;
        }
        &--count {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-family: Outfit-Regular;
          font-size: 24rpx;
          color: #aaabb3;
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
.withdraw-box {
  position: relative;
  box-sizing: border-box;
  padding: 0 50rpx;
  background: #ffffff;
  padding-bottom: env(safe-area-inset-bottom);

  .withdraw-close {
    position: absolute;
    right: 0;
    top: 0;
    height: 104rpx;
    width: 104rpx;
    background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1744709336227.png)
      no-repeat center center transparent;
    background-size: 40rpx 40rpx;
    z-index: 1;
  }
  .withdraw-header {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 104rpx;
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
  .withdraw-form {
    margin-bottom: 30rpx;
    height: 120rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dce0e9;
    .prefix {
      margin-right: 20rpx;
      font-family: PingFangSC-Regular;
      font-size: 44rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
    }
    .input {
      flex: 1;
      height: 60rpx;
      line-height: 60rpx;
      width: 200 * 2rpx;
      font-family: PingFangSC-Regular;
      font-size: 44rpx;
      color: #bababa;
      letter-spacing: 0;
      color: #030303;
      font-weight: 400;
    }
    .withdraw-all {
      font-family: PingFangSC-Regular;
      font-size: 30rpx;
      color: #61b43e;
      letter-spacing: 0;
      text-align: right;
      line-height: 40rpx;
      font-weight: 400;
    }
  }
  .withdraw-total {
    margin-bottom: 68rpx;
    font-family: PingFangSC-Regular;
    font-size: 30rpx;
    color: #999999;
    text-align: left;
    line-height: 40rpx;
    font-weight: 400;
  }
  .withdraw-footer {
    box-sizing: border-box;
    padding-bottom: env(safe-area-inset-bottom);
    height: 124rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    div {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 310rpx;
      height: 84rpx;
      font-family: PingFangSC-Medium;
      font-size: 13px;
      color: #333333;
      text-align: center;
      font-weight: 500;
    }
    .btn-cancel {
      border: 1px solid #333333;
      color: #333333;
    }
    .btn-ok {
      border: 1px solid #bababa;
      background-color: #bababa;
      color: #fff;
      &.active {
        background-color: #333;
      }
    }
  }
}
</style>
