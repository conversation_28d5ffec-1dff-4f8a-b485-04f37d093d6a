<script>
import Homecard from '@/components/homecard/appt.vue';

export default {
  name: 'HomecardDemo',
  components: {
    Homecard
  },
  data() {
    return {
      cardList: [],
      uidArray: [19852365570, 1, 3, 6, 7, 10, 11, 13, 15, 18]
    };
  },
  methods: {
    async getData(uid) {
      const res = await this.$request({
        url: '/syGroupBuy/chain/index/home',
        data: {
          customer_id: 1,
          city_id: 1,
          uid: uid
        }
      });

      const { errorCode, responseData } = res.data;
      if ([0, 200].includes(errorCode)) {
        // 是个对象
        this.cardList.push({
          renderUid: uid,
          ...responseData.guide_card_list
        });
        console.log('this.cardList', this.cardData);
      }
    }
  },
  async mounted() {
    for (const uid of this.uidArray) {
      await this.getData(uid);
    }
  }
};
</script>

<template>
  <view class="card-page">
    <view class="card-wrapper">
      <view
        v-for="(cardDataItem, index) in cardList"
        :key="`${cardDataItem.card_key}-${index}`"
      >
        uid: {{ cardDataItem.renderUid }}
        <Homecard v-if="cardDataItem.card_key" :cardData="cardDataItem" />
      </view>
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.card-page {
  display: flex;
  justify-content: center;
  flex: 1;
  min-height: 100vh;
  height: 100%;
  padding: 20px 0;

  background-color: #00a77d;
}

.card-wrapper {
  width: 345 * @px;
  display: flex;
  flex-direction: column;
  gap: 20 * @px;
}
</style>
