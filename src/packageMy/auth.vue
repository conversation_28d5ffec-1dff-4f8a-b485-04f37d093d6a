<template>
  <div>
    <div class="auth-wr">
      <div class="input-wr">
        <label class="">真实姓名</label>
        <input
          type="text"
          placeholder="请输入姓名"
          v-model="form.name"
          maxlength="15"
          :disabled="is_authed"
        />
      </div>
      <div class="input-wr">
        <label class="">身份证号</label>
        <input
          type="text"
          placeholder="请输入身份证号码"
          v-model="form.card_num"
          maxlength="18"
          :disabled="is_authed"
        />
      </div>

      <template v-if="is_authed">
        <!-- <div class="btn active">已认证</div> -->
        <div class="btn-link" @click="resetAuth">取消实名</div>
      </template>
      <template v-else>
        <div :class="['btn', isActive ? 'active' : '']" @click="onSubmit">
          开始认证
        </div>

        <div class="agreement">
          <!-- <img
            @click="change_select"
            class="select-img"
            :src="is_select_img[is_select]"
          /> -->
          <span class="empty-circle" @click="change_select">
            <span v-if="is_select" class="s-icon"></span>
          </span>
          我已阅读并同意<span class="item" @click.stop="toH5(agreement_url)">{{
            agreement_text
          }}</span>
        </div>
      </template>

      <p class="tip">
        实名认证如遇到问题请<span
          class="em"
          @click="toConsult"
          style="
            text-decoration: underline;
            color: #030303;
            font-family: PingFangSC-Medium;
          "
          >《联系客服》</span
        >
        <!-- <br />
        或拨打<span @click="phone">************</span>（每天09:00-22:00) -->
      </p>
    </div>

    <customPopup
      title="提示"
      class="custom-popup-modal"
      v-model="customPopupVisible"
      width="300px"
    >
      <template #body>
        <div class="auth-tip-modal">
          您申请实名认证的信息需要与<span class="em"
            >当前微信绑定的实名信息</span
          >保持一致，否则可能导致钱包提现失败，如果遇到问题请您联系客服处理
        </div>
      </template>
      <template #footer>
        <div class="custom-popup-modal-btns">
          <div class="btn" @click="customPopupVisible = false">取消认证</div>
          <div class="btn confrim" @click="onConfrim">继续认证</div>
        </div>
      </template>
    </customPopup>
  </div>
</template>

<script>
import SyEncrypt from '@soyoung/SyEncrypt';
import customPopup from '@/components/popup';
import {
  getAuthenticationApi,
  addAuthenticationApi,
  cancelAuthenticationApi
} from '@/api/my';
export default {
  components: { customPopup },
  name: 'Auth',
  data() {
    return {
      form: {
        name: '',
        card_num: ''
      },
      is_select_img: {
        1: 'https://static.soyoung.com/sy-pre/5hh8gx04yvgt-1652681400682.png',
        0: 'https://static.soyoung.com/sy-pre/1dq8lm94cleso-1657530600725.png'
      },
      is_select: 0,
      agreement_text: '', // 协议文案
      agreement_url: '', // 协议链接
      fetching: false,
      is_authed: false, // 是否已经认证通过
      customPopupVisible: false, // 确认弹窗
      auth_id: '' // 上一次的认证id,取消的时候用
    };
  },
  computed: {
    isActive() {
      return (
        this.form.name !== '' && this.form.card_num && this.is_select !== 0
      );
    }
  },
  onLoad() {
    this.getAuthentication();
  },
  onShow() {},
  methods: {
    onSubmit() {
      if (this.onbeforeSubmit()) {
        this.customPopupVisible = true;
      }
    },
    onConfrim() {
      this.customPopupVisible = false;
      this.doCreate();
    },
    onbeforeSubmit() {
      let flag = true;
      if (!this.form.name) {
        uni.showToast({
          title: '姓名不能为空，请填写',
          icon: 'none'
        });
        flag = false;
        return;
      }
      const regName = /^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/;
      if (!regName.test(this.form.name)) {
        uni.showToast({
          title: '请姓名填写有误，请重新填写',
          icon: 'none'
        });
        flag = false;
        return;
      }
      if (!this.form.card_num) {
        uni.showToast({
          title: '身份证号不能为空，请填写',
          icon: 'none'
        });
        flag = false;
        return;
      }
      const regIdNo =
        /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/;
      if (!regIdNo.test(this.form.card_num)) {
        uni.showToast({
          title: '身份证号填写有误，请重新填写',
          icon: 'none'
        });
        flag = false;
        return;
      }
      if (!this.is_select) {
        uni.showToast({
          title: '请确认已阅读并同意《个人信息授权声明》',
          icon: 'none'
        });
        flag = false;
        return;
      }
      if (!this.isActive) {
        flag = false;
        return;
      }
      return flag;
    },
    async doCreate() {
      if (this.fetching) return;
      this.fetching = true;
      const syEncrypt = new SyEncrypt({
        key: 'ec5peDsPVAaW4PKr' // 默认秘钥，当不传或者key不合法时使用 ec5peDsPVAaW4PKr
      });
      let card_num = '';
      // 加密
      const encrypted = syEncrypt.encrypt(this.form.card_num);
      if (encrypted) {
        card_num = encrypted;
      }
      const { errorMsg, errorCode } = await addAuthenticationApi(
        Object.assign({}, this.form, {
          card_num: card_num
        })
      );
      this.fetching = false;
      if (+errorCode === 200 || +errorCode === 0) {
        uni.showToast({
          title: '提交成功',
          icon: 'none'
        });
        this.getAuthentication();
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    doReset() {
      this.form = {
        name: '',
        card_num: ''
      };
      this.is_select = 0;
    },
    toConsult() {
      this.$bridge({
        url: `/packageAccount/consult?type=1&qzychannel=4`
      });
    },
    toH5(url) {
      this.$toH5(url);
    },
    async getAuthentication() {
      const data = await getAuthenticationApi();
      if (data) {
        this.agreement_text = data.agreement_text;
        this.agreement_url = data.agreement_url;
        this.is_authed = +data.authentication.status === 1;
        if (this.is_authed) {
          this.form.name = data.authentication.name;
          this.form.card_num = data.authentication.card_num;
          this.auth_id = data.authentication.id;
        } else {
          this.doReset();
        }
      }
    },
    // 取消认证
    async cancelAuthentication() {
      const { errorMsg, errorCode } = await cancelAuthenticationApi({
        id: this.auth_id
      });
      if (+errorCode === 200 || +errorCode === 0) {
        this.getAuthentication();
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    change_select() {
      this.is_select = this.is_select === 0 ? 1 : 0;
    },
    phone() {
      wx.makePhoneCall({
        phoneNumber: '************' // 仅为示例，并非真实的电话号码
      });
    },
    // 取消实名认证，清空认证信息
    resetAuth() {
      uni.showModal({
        content: '取消实名认证将清空认证记录，并影响提现等功能，是否继续？',
        cancelText: '我再想想',
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            this.cancelAuthentication();
          }
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.auth-empty {
  width: 100%;
  text-align: center;
}
.auth-wr {
  height: 100vh;
  background-color: #fff;
  .input-wr {
    display: flex;
    align-items: center;
    // padding: 30rpx 0;
    margin: 0 50rpx;
    height: 122rpx;
    line-height: 122rpx;
    border-bottom: 1rpx solid #f0f0f0;
    box-sizing: border-box;
    label {
      font-family: PingFangSC-Medium;
      font-size: 30rpx;
      color: #030303;
      font-weight: 500;
    }
    input {
      flex: 1;
      font-family: PingFangSC-Regular;
      font-size: 30rpx;
      color: #222222;
      font-weight: 400;
      height: 42rpx;
      line-height: 42rpx;
      margin-left: 40rpx;
      &::placeholder {
        font-family: PingFangSC-Regular;
        font-size: 30rpx;
        color: #bababa;
        font-weight: 400;
      }
    }
  }
  .tip {
    width: 100%;
    position: fixed;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
    bottom: 40px;
    height: 34rpx;
    line-height: 34rpx;
    margin-top: 40rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    .em {
      // color: #5448ee;
      color: @text-color;
    }
  }
  .btn {
    width: 690rpx;
    height: 88rpx;
    line-height: 88rpx;
    margin: 0 auto;
    background: #bababa;
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    margin-top: 60rpx;
    &.active {
      // background: #5448EE;;
      background: @border-color;
    }
    &.cancel {
      background: #333333;
      color: #ffffff;
    }
  }
  .btn-link {
    text-align: center;
    line-height: 88rpx;
    margin-top: 50rpx;
    font-family: PingFangSC-Medium;
    font-size: 24rpx;
    letter-spacing: 0;
    text-align: center;
    color: #030303;
    font-weight: 500;
    text-decoration: underline;
  }
}

.agreement {
  display: flex;
  height: 18 * 2rpx;
  font-family: PingFangSC-Regular;
  font-size: 12 * 2rpx;
  color: #030303;
  letter-spacing: 0;
  line-height: 18 * 2rpx;
  font-weight: 400;
  justify-content: center;
  margin-top: 20 * 2rpx;
  align-items: center;
  // .select-img {
  //   width: 16 * 2rpx;
  //   height: 16 * 2rpx;
  //   margin-right: 5 * 2rpx;
  // }
  .empty-circle {
    display: inline-block;
    width: 24rpx;
    height: 24rpx;
    border: 2rpx solid #333333;
    position: relative;
    margin-right: 10rpx;
    margin-left: 0rpx;
    box-sizing: border-box;
    .s-icon {
      display: inline-block;
      width: 16rpx;
      height: 16rpx;
      background-color: #333333;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      margin: auto;
    }
  }
  .item {
    font-family: PingFangSC-Medium;
    font-size: 12px;
    color: @text-color;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: 500;
    text-decoration: underline;
  }
}
.auth-tip-modal {
  text-align: left;
  line-height: 26px;
  .em {
    font-weight: bold;
  }
}
.custom-popup-modal-btns {
  text-align: center;
  margin-top: 25 * 2rpx;
  .btn {
    box-sizing: border-box;
    display: inline-block;
    padding: 0px;
    width: 118 * 2rpx;
    // height: 30 * 2rpx;
    font-size: 14 * 2rpx;
    border: 0.5px solid @border-color;
    border-radius: 20px;
    color: @text-color;
    margin-right: 18 * 2rpx;
    line-height: 30 * 2rpx;
    &:last-child {
      margin-right: 0px;
    }
    &.confrim {
      background-color: @text-color;
      color: #fff;
    }
  }
}
</style>
