<template>
  <div class="socket-page">我是一个socket测试页面</div>
</template>

<script>
import { getConfig } from '@/api/home';
import store from '@/store';
export default {
  data() {
    return {
      newWs: null,
      ws: null
    };
  },
  methods: {
    async onCreateSocket() {
      if (
        (this.ws &&
          this.ws?.socketTask &&
          this.ws?.socketTask.readyState === 1) ||
        (this.ws?.socketTask && this.ws?.socketTask.readyState === 1)
      ) {
        return false;
      }
      // const { PublicWS } = await require.async('../packageHospital/chat/index');
      try {
        const res = await getConfig();
        const conf = res.responseData;
        console.log(conf, '自研socket链接配置');
        if (conf && conf.newWs) {
          this.newWs = 1;
          if (this.ws) {
            return false;
          }
          let _uid = store.state.global.userInfo.uid; //获取公共登录uid
          // 动态加载 PublicWS
          const { default: PublicWS } = await import(
            '@/packageHospital/components/chat/index'
          );
          this.ws = new PublicWS({
            config: {
              roomID: 'soyoungLive://1315',
              senderUid: _uid,
              receiverUid: _uid,
              token: conf.chat_oauth_token,
              accepts: [86245575],
              debug: 1,
              currentPages: getCurrentPages()

              // roomID: 'soyoungLive://1315',
              // senderUid: 19852364833, // _uid,
              // receiverUid: 19852364833, // _uid,
              // token: 'b44dbfe44cf350a9ec254132bccb3f9f', // conf.chat_oauth_token,
              // accepts: [28012352],
              // debug: 1,
              // currentPages: getCurrentPages()
            },
            socketTask: {
              onClose: (res) => {
                console.log('ws:关闭', res);
              },
              onError: (res) => {
                console.log('ws:error', res);
              },
              onMessage: (res) => {
                console.log(res, '接受消息啦');
                this.sendMessage(res);
              },
              onOpen: (res) => {
                console.log('ws:打开', res);
              }
            }
          });
        }
      } catch (error) {
        console.log('error', error);
      }
    },
    sendMessage(res) {
      console.log(res, '*** socket res ***');
      const body = res.body;
      console.log(body, '*** socket body ***');
      try {
        const bd = JSON.parse(body || '{}');
        const ext = bd.ext;
        if (!ext) {
          return;
        }
        console.log(bd, ext, '*** socket bd ext ***');
      } catch (error) {
        console.log(error);
      }
    }
  },
  async onShow() {
    if (this.newWs) {
      this.ws.socketTask.readyState && this.ws.socketTask.readyState === 3
        ? this.ws.socketEvent.reConnect()
        : void 0;
    }
    if (this.ws?.socketTask) {
      console.log('*** socketTask已建立链接 ***');
    }
    this.onCreateSocket();
  },
  onHide() {
    if (this.newWs) {
      this.ws.socketEvent.close();
    }
    if (this.ws?.socketTask) {
      this.ws.socketTask.close();
    }
  },
  onUnload() {
    if (this.newWs) {
      this.ws.socketEvent.close();
    }
    if (this.ws?.socketTask) {
      this.ws.socketTask.close();
    }
  }
};
</script>
