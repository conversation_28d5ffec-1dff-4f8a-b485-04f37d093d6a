import Vue from 'vue';
Vue.filter('dateFormat', function (value, fmt) {
  value = value && value.replace(/-/g, '/');
  const getDate = new Date(value);
  const o = {
    'M+': getDate.getMonth() + 1,
    'd+': getDate.getDate(),
    'h+': getDate.getHours(),
    'm+': getDate.getMinutes(),
    's+': getDate.getSeconds(),
    'q+': Math.floor((getDate.getMonth() + 3) / 3),
    S: getDate.getMilliseconds()
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (getDate.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
});

Vue.filter('titleFormat', function (title) {
  let str = title;
  if ((title || '').indexOf('【') === 0) {
    str = `<span style="margin-left: -7px;">【</span>${title.substr(1)}`;
  }
  return str;
});
