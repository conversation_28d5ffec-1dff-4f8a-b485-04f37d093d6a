import Vue from 'vue';

/**
 * 私域 + c
 * @param {number} limit
 * @param {number} page
 * @returns {Promise<Object>}
 */
export async function apiGetAcodeUrl(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/getAcodeUrl',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 私域 + c 绑定
 * @param {number} limit
 * @param {number} page
 * @returns {Promise<Object>}
 */
export async function apiUserUnionIdRelation(data) {
  const res = await Vue.$request({
    url: '/xinyang/PrivateDomain/UserUnionIdRelation',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 保存资料
 * @param {number} limit
 * @param {number} page
 * @returns {Promise<Object>}
 */
export async function apiSaveLiuziCustomer(data) {
  return Vue.$request({
    url: '/syGroupBuy/chain/customer/saveLiuziCustomer',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

/**
 * 获取门店
 * @param {number} limit
 * @param {number} page
 * @returns {Promise<Object>}
 */
export async function apiGetUserNearestTenant(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/customer/getUserNearestTenant',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

export async function apiGetUserPhone(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/customer/getPhoneNumber',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

export async function getChannelCodeUrl(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/getChannelCodeUrl',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
