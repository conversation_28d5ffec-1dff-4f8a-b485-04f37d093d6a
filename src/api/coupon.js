import Vue from 'vue';

/**
 * 权益中心获取红包接口
 * @returns Promise<Array>
 */
export async function apiGetCoupon4CouponCenter(discount_status, cancelToken) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/userCouponList',
    data: {
      // 券状态1：有效 2：失效
      discount_status
    },
    cancelToken
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}
/**
 * 权益中心获取津贴接口
 * @returns Promise<Array>
 */
export async function apiGetAllowance4CouponCenter(
  discount_status,
  cancelToken
) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/userAllowanceList',
    data: {
      // 券状态1：有效 2：失效
      discount_status
    },
    cancelToken
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}
/**
 * 权益中心根据couponid获取适用的商品列表
 * @returns Promise<Object|null>
 */
export async function apiGetSkuByCoupon(coupon_id, page) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/couponProductList',
    data: {
      coupon_id,
      page,
      page_size: 10
    }
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取商品详情页的红包或者津贴
 * @param {number} material_id
 * @param {number} material_type
 * @returns Promise<Object|null>
 */
export async function apiGetDiscountList4Spu(material_id, material_type) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/productDiscountList',
    data: {
      material_id,
      material_type
    }
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 领取红包
 * @param {string} coupon_id
 * @returns Promise<Boolean>
 */
export async function apiGetReceiveCoupon(coupon_id) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/receiveCoupon',
    data: {
      coupon_id
    }
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}

/**
 * 领取津贴
 * @param {string} stock_id
 * @returns Promise<Boolean>
 */
export async function apiGetReceiveAllowance(stock_id) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/receiveCoupon',
    data: {
      stock_id
    }
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}
/**
 * 获取确认订单页-可用红包列表
 * @param {number} pid
 * @param {number} price
 * @returns  Promise<Array>
 */
export async function apiGetOrderCouponList(pid, price) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/orderCouponList',
    data: {
      pid,
      price
    }
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}

/**
 * 获取确认订单页-可用津贴列表
 * @param {number} allowance_id
 * @param {number} price
 * @returns Promise<Array>
 */
export async function apiGetOrderAllowanceList(allowance_id, price) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/discount/orderAllowanceList',
    data: {
      allowance_id,
      price
    }
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}
