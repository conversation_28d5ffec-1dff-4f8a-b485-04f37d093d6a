import Vue from 'vue';

/**
 * 获取用户数据数据
 * @returns {Promise<Object|null>}
 */
export async function getUserInfoApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/user/getUserInfo',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    if (
      errorMsg !== '请登录' &&
      errorMsg !== '用户未登录' &&
      errorCode !== 77777 &&
      errorCode !== 789
    ) {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 获取我的页面显示数据
 * @param {number} page
 * @returns {Promise<Object|null>}
 */
export async function getUserPageInfoApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/user/getUserHomepage',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取钱包页面数据 & 账单记录
 * @returns {Promise<Object|null>}
 */
export async function getAccountListApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/account/getAccountList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 申请提现
 * @param {number|string} account
 * @returns {Promise<Object|null>}
 */
export async function applyWithdrawalApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/account/applyWithdrawal',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 获取关于我们页面数据
 * @returns {Promise<Object|null>}
 */
export async function getAboutUsApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/user/getAboutUs',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

export async function ViewPolicy(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/user/viewPolicy',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取实名认证数据
 * @returns {Promise<Object|null>}
 */
export async function getAuthenticationApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/account/getAuthentication',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 申请实名认证
 * @param {number|string} name
 * @param {number|string} card_num
 * @returns {Promise<Object|null>}
 */
export async function addAuthenticationApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/account/addAuthentication',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 取消实名认证
 * @param {number|string} id
 * @returns {Promise<Object|null>}
 */
export async function cancelAuthenticationApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/account/cancelAuthentication',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 进群加C拦截页页面数据获取
 * @returns {Promise<Object|null>}
 */
export async function checkCrowdApi({ data, catchFn }) {
  const res = await Vue.$request({
    url: '/syGroupBuy/account/checkCrowd',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      catchFn && catchFn();
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 权益中心接口
 * @returns {Promise<Object|null>}
 */
export async function apiGetUserEquity() {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/account/getUserEquity',
    data: {}
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}
