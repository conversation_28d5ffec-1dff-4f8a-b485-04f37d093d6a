import Vue from 'vue';

/**
 * 获取线下沙龙数据
 * @param {number|string} activity_id
 * @returns {Promise<Object|null>}
 */
export async function apiJoinGroupDetail(data) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syChainTrade/wxapp/group/detail',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (+errorCode === 0 && responseData) {
    return { errorMsg, responseData };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return { errorMsg, responseData };
  }
}

// * 小程序分享接口
export async function getGroupShareInfo(data) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syChainTrade/wxapp/share/getGroupShareInfo',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (+errorCode === 0) {
    return { errorMsg, responseData };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return { errorMsg, responseData };
  }
}

/**
 * 领取红包接口
 * @param {number|string} activity_id
 * @returns {Promise<Array>}
 */
export async function apiReceiveCoupon(activity_id, source, coupon_id) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/activity/receiveCoupon',
    data: { activity_id, source, coupon_id } //source, 1:分享人 2：助力人
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg } = res;
  if (+errorCode === 200 || +errorCode === 0) {
    return true;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return false;
  }
}

/**
 * 老带新首页
 */
export async function getaHome(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/invite/aHomePage',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新b页
 */
export async function getbHome(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/help/helpHome',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新首页
 */
export async function getInviteList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/invite/getInviteList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新首页
 */
export async function getQwImg(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/invite/getQwImg',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新首页
 */
export async function getRewardList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/invite/getRewardList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新b页
 * 成功情况接口
 */
export async function getHelpSucceedInfo(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/help/helpSucceed',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新b页
 * 助力接口
 */
export async function apiUserhelp(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/help/help',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新b页
 * 失败页面接口
 */
export async function getHelpFailInfo(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/help/helpFail',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 老带新b页
 * 去使用时检查优惠券是否已发放接口
 */
export async function getCouponInfo(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/help/getCoupon',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 青春自由卡裂变活动
 */

/**
 * 邀请成功的奖品页
 * @param {number|string} activity_id
 * @returns {Promise<Array>}
 */
export async function apiGetInfectSuccessSkus(activity_id, source, from_uid) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/activity/invitationSuc',
    data: { activity_id, source, from_uid } //source, 1:分享人 2：助力人
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 生成二维码
 * @param {number|string} applets_scheme
 * @returns {Promise}
 */
export async function apiGetShareInfectImg(
  applets_scheme,
  activity_id,
  from_uid
) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/activity/shareFission',
    data: { applets_scheme, activity_id, from_uid }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 助力
 * @param {number|string}
 * @returns {Promise<Boolean>}
 */
export async function apiInfectActivityDoHelp({
  activity_id,
  from_uid,
  city_id,
  from_qw_entry
}) {
  return Vue.$request({
    url: '/syGroupBuy/chain/activity/fissionActivityDoHelp',
    data: { activity_id, from_uid, city_id, from_qw_entry }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

/**
 * 初始化查询
 * @param {number|string}
 * @returns {Promise<Boolean>}
 */
export async function apiInfectActivityInit(
  activity_id,
  from_uid,
  from_qw_entry,
  uid
) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/activity/fissionActivityInit',
    data: { activity_id, from_uid, from_qw_entry }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else if (errorCode !== `${uid}${from_uid}${activity_id}`) {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  } else {
    return null;
  }
}

/**
 * 订阅
 * @param {number|string} scene
 * @returns {Promise<Boolean>}
 */
export async function apiSubscribe(scene) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/activity/addSubscribe',
    data: { scene }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg } = res;
  if (+errorCode === 200 || +errorCode === 0) {
    return true;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return false;
  }
}

export async function getReportData(data) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/yearReport/getReportData',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (+errorCode === 0 && responseData) {
    return { errorCode, errorMsg, responseData };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return { errorCode, errorMsg, responseData };
  }
}

export async function getShareQrcode(data) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/yearReport/getShareQrcode',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (+errorCode === 0 && responseData) {
    return { errorMsg, responseData };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return { errorMsg, responseData };
  }
}

/**
 * 根据序列号查询礼品卡信息
 */
export async function apiGiftCardDetail(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/giftCardDetail',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 礼品卡激活记录
 */
export async function apiGiftCardDList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/giftCardList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 礼品卡激活
 */
export async function apiActiveGiftCard(psd) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/giftCardActive',
    data: {
      card_password: psd
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

export async function inviteUser(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/oldBringNew/invite/inviteNewUser',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}
