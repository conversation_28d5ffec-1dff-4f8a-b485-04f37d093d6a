import Vue from 'vue';

/**
 * 获取退款信息
 * @param {*} order_id
 * @returns {Promse<Object|null>}
 */
// https://wxapi85.sy.soyoung.com/syGroupBuy/chain/refund/getRefundInfo?order_id=1221111111111215369
export function GetRefundInfo(params) {
  return Vue.$request({
    url: '/syChainTrade/wxapp/refund/getRefundInfo',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

/**
 * 提交退款
 * @param {*} order_id
 * @param {*} uid
 * @param {*} sku_id
 * @param {*} reason
 * @param {*} other_reason
 * @returns {Promse<Object|null>}
 */
export function SubmitRefund(params) {
  return Vue.$request({
    url: '/syChainTrade/wxapp/refund/submitRefund',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}
