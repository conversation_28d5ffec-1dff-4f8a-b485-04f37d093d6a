import Vue from 'vue';

/**
 * 获取商品信息
 * @param {string} sku_id
 * @returns {Promse<Object|null>}
 */
export async function apiGetSpuInfo(params) {
  const res = await Vue.$request({
    url: '/xinyang/sku/detail',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 获取拼团楼层信息
 * @param {string} sku_id
 * @returns {Promse<Object|null>}
 */
export async function apiGetGroupFloor(params) {
  const res = await Vue.$request({
    url: '/syChainTrade/app/group/groupFloor',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 获取优惠券+津贴列表
 * @param {string} sku_id
 * @returns {Promse<Object|null>}
 */
export async function apiGetCouponList(params) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syChainTrade/wxapp/sku/skuCouponList',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.$log('优惠券列表接口', params, errorMsg, 'error');
    return null;
  }
}

/**
 * 领取优惠券
 * @param {string} id // 优惠券id
 * @returns {Promse<Object|null>}
 */
export async function apiGetYuYueCoupon(params) {
  const res = await Vue.$request({
    url: '/xinyang/product/getYuYueCoupon',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });

  return res;
}

/**
 * 领取津贴
 * @param {string} id // 津贴id
 * @returns {Promse<Object|null>}
 */
export async function apiReceiveAllowance(params) {
  const res = await Vue.$request({
    url: '/xinyang/product/receiveAllowance',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 商品详情页 分享二维码生成
 * @param {Object} param0
 * @returns {Promse<Object|null>}
 */
export async function apiGetShareQrCode(
  { title, cover, sku_id },
  extData = {}
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/product/getSkuDetailShareQrcodeV2',
    data: {
      title,
      cover,
      sku_id,
      ...extData
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取适用城市-城市列表
 * @param {*} sku_id
 * @param {*} select_city_id
 * @param {*} ingredients_id
 * @returns {Promse<Object|null>}
 */

export function GetChainCityList(params) {
  return Vue.$request({
    url: '/syGroupBuy/chain/reservation/chainCityList',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}
/**
 * 获取适用城市-机构列表
 * @param {*} sku_id
 * @param {*} city_id
 * @param {*} lat
 * @param {*} lon
 * @param {*} sort
 * @param {*} ingredients_id
 * @returns {Promse<Object|null>}
 */
export function GetHospitalList(params) {
  return Vue.$request({
    url: '/syGroupBuy/chain/reservation/hospitalList',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

/**
 * 获取可预约原料列表
 * @param {*} sku_id
 * @returns {Promse<Object|null>}
 */
export function GetChainIngredientsList(params) {
  return Vue.$request({
    url: '/syGroupBuy/chain/reservation/chainIngredientsList',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

/**
 * 未授权定位时，根据id获取当前所在位置
 * @returns {Promse<Object|null>}
 */
export function GetDistrictInfoByIp() {
  return Vue.$request({
    url: '/syGroupBuy/reservation/getDistrictInfoByIp',
    method: 'GET',
    data: {}
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

// 检查是否能拼团
export async function apiGroupCheckCanJoin(data) {
  const { errorCode, errorMsg } = await Vue.$request({
    url: '/syChainTrade/wxapp/group/checkCanJoinGroup',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  /**
  * 1 此商品无拼团活动
    2 拼团活动未开始
    3 拼团活动已结束
    4 此拼团活动未找到
    5 该团已满员，无法参加
    6 您参加的拼团未成功
    7 参团时间已经超过了哦
    8 拼团仅能新用户才能参加哦
    9 您已经参加了此团，快去预约医院吧！
    10 您已经参加了此团，快去订单列表支付吧！
    11 很抱歉，该拼团不可群内参与 您可以点击上方商品发起拼团，并私聊邀请好友参团哦~
  */
  return { errorCode, errorMsg };
}

/**
 * 解密
 * @param {string} encryptedData
 * @param {string} iv
 * @returns
 */
export async function apiDecryptedData(sessionKey, encryptedData, iv) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/groupBuy/User/DecodeWxData',
    data: {
      sessionKey,
      encryptedData,
      iv
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      uni.$log('/groupBuy/User/DecodeWxData', errorMsg, 'error');
      return {
        errorCode: -100,
        errorMsg: error,
        responseData: null
      };
    });
  const result =
    (errorCode === 200 || errorCode === 0) && responseData
      ? responseData
      : null;
  // if (!result) {
  // }
  return result;
}

/**
 * 商品详情页红包领取接口
 * */
export function couponReceiveCoupon(params) {
  return Vue.$request({
    url: '/syChainTrade/wxapp/coupon/receiveCoupon',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}

/**
 * 商品详情页获取商品分期数据接口
 * */
export function loanInfo(params) {
  return Vue.$request({
    url: '/syChainTrade/wxapp/sku/loanInfo',
    method: 'GET',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
}
