import Vue from 'vue';

/**
 * 日历基础数据集合
 * @param {Object} param
 * @returns {Promise<Array>} 日历列表
 */
export async function apiCalendarForTime({
  reserve_id = '',
  city_id = '',
  group_buy_id = '',
  package_id = '',
  card_num = '',
  pid = ''
}) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/calendarForTime',
    data: {
      pid,
      city_id,
      card_num,
      source: 1,
      reserve_id,
      package_id,
      group_buy_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (errorCode === 200 || errorCode === 0) {
    return responseData?.list || [];
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}

/**
 * 城市列表+机构信息+推荐城市
 * @param {string} city_id
 * @param {string} pid
 * @param {string} package_id
 * @returns {Promise<Object|null>} 信息对象 或者 null
 */
export async function apiApptCityList(city_id, pid, package_id) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/cityList',
    data: {
      package_id,
      city_id,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 是否可以预约
 * @param {*} order_id
 * @param {*} pid
 * @param {*} package_id
 * @returns {Promise<Object|null>} 是否可以预约对象 或者 null
 */
export async function apiCheckReserve(pid, package_id) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/isCanReserve',
    data: {
      package_id,
      source: 2,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 预约弹窗是否可以预约
 * @param {*} order_id
 * @param {*} pid
 * @param {*} package_id
 * @returns {Promise<Object|null>} 是否可以预约对象 或者 null
 */
export async function apiCheckRes(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/isCanReserve',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 预约基础信息
 * @param {*} param
 * @returns {Promise<Object|null>} 预约基础信息 或者 null
 */
export async function apiApptInfo({
  order_id = '',
  card_num = '',
  package_id = '',
  pid = '',
  page_type = 1
}) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/getReserveBaseInfo',
    data: {
      order_id,
      card_num,
      package_id,
      pid,
      page_type
    }
  })
    .then((res) => res.data)
    .catch((error) => ({
      errorCode: -100,
      errorMsg: error || '网络错误,稍后再试',
      responseData: null
    }));
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 商品预约信息预拉取,不带机构的城市列表
 * @param {*} order_id
 * @param {*} city_id
 * @param {*} pid
 * @param {*} package_id
 * @returns {Promise<Array>} 城市列表
 */
export async function apiCityListWithoutHospital(city_id, pid, package_id) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/getReserveCityList',
    data: {
      package_id,
      city_id,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}
/**
 * 获取服务城市
 * @param {*} order_id
 * @param {*} city_id
 * @param {*} pid
 * @param {*} package_id
 * @returns {Promise<Array>} 城市列表
 */
export async function apiCityListWithoutHos(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/getReserveCityList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

export async function apiChainReservationChainCityList(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/chainCityList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 * 是否开启强制预约
 * @param {*} city_id
 * @param {*} material_id
 * @param {*} material_type
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function checkForceReserve(city_id, material_id, material_type) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/isPowerReserve',
    data: {
      city_id,
      [material_type === 3 ? 'package_id' : 'spu_id']: material_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 是否开启强制预约
 * @param {*} city_id
 * @param {*} package_id
 * @param {*} pid
 * @param {*} spu_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function checkForceRes(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/isPowerReserve',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return {};
  }
}
/**
 * 获取预约模式列表
 * @param {*} pid
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function reserveMode(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/reserveMode',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 获取间隔提示文案
 * @param {*} pid
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function checkCommodityCycleTxt(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/checkCommodityCycle',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 获取预约基本信息
 * @param {*} order_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function getReserveBaseInfo(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getReserveBaseInfo',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 请求机构列表，和机构对应的时间切片
 * @param {*} order_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function calendarForTimeDetail(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/calendarForTimeDetail',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

// /syGroupBuy/chain/reservation/reserveCalendarForTimeDetail
export async function apiChainReservationReserveCalendarForTimeDetail(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/reserveCalendarForTimeDetail',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 请求日期列表
 * @param {*} order_id
 * @param {*} package_id
 * @param {*} card_num
 * @param {*} pid
 * @param {*} city_id
 * @param {*} request_times
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function calendarForTime(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/calendarForTime',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

// /syGroupBuy/chain/reservation/reserveCalendarForTime
export async function apiChainReservationReserveCalendarForTime(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/reserveCalendarForTime',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取服务机构
 * @param {*} order_id
 * @param {*} package_id
 * @param {*} hospital_id
 * @param {*} pid
 * @param {*} city_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function getReserveHospitalList(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/getReserveHospitalList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

export async function apiChainReservationHospitalList(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/hospitalList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取机构下的日期和时间
 * @param {*} order_id
 * @param {*} city_id
 * @param {*} card_num
 * @param {*} hospital_id
 * @param {*} ingredients_id
 * @param {*} sku_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function getCalendar(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getCalendar',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 获取医生列表
 * @param {*} order_id
 * @param {*} city_id
 * @param {*} package_id
 * @param {*} pid
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function calendarForDoctor(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/calendarForDoctor',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 获取医生下的日期和时间切片
 * @param {*} order_id
 * @param {*} city_id
 * @param {*} card_num
 * @param {*} package_id
 * @param {*} pid
 * @param {*} doctor_id
 * @param {*} hos_list
 * @param {*} request_times
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function calendarForDoctorDetail(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/calendarForDoctorDetail',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 预约提交校验
 * @returns {Promise<Object|null>}
 */
export async function checkReserveStatus(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/checkReserve',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  return res;
}
/**
 * 获取套餐商品列表
 * @param {*} order_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function packageReserveList(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/packageReserveList',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 获取改约原因
 * @param {*} order_id
 * @returns {Promise<Object|null>} 是否强制预约对象 或者 null
 */
export async function getReasonList(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/getReason',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 取消预约
 * @param {*} order_id
 * @param {*} reservation_id
 * @returns {Promise<Object|null>}
 */
export async function toCancelReserve(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/cancelReserve',
    data
  })
    .then((res) => res)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 取消预约
 *  syGroupBuy/chain/reservation/cancelReserve
 * @param {*} order_id
 * @param {*} reservation_id
 * @returns {Promise<Object|null>}
 */
export async function apiChainReservationCancelReserve(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/cancelReserve',
    data
  })
    .then((res) => res)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 获取根据ip获取城市信息
 * @returns {Promise<Object|null>}
 */
export async function getDistrictInfoByIp(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/getDistrictInfoByIp',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return {};
  }
}

/**
 * 选择就诊人
 * @param {*} order_id
 * @param {*} reservation_id
 * @returns {Promise<Object|null>}
 */
export async function setReserveProfile(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/setReserveProfile',
    data
  })
    .then((res) => res)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 全局取消预约弹窗提示
 */
export async function getPopupWindow(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/popup/getPopupWindow',
    data
  })
    .then((res) => res.data)
    .catch(() => {
      return {
        errorCode: -100,
        errorMsg: '',
        responseData: null
      };
    });
  return res;
}

/**
 * 全局预约取消  反馈
 */
export async function confirmPopupWindow(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/popup/confirmPopupWindow',
    data
  })
    .then((res) => res.data)
    .catch(() => {
      return {
        errorCode: -100,
        errorMsg: '',
        responseData: null
      };
    });
  return res;
}

/**
 * 获取优享日的信息
 */
export async function getYxDayConfig(data) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/reservation/getYxDayConfig',
    data
  })
    .then((res) => res.data)
    .catch(() => {
      return {
        errorCode: -100,
        errorMsg: '',
        responseData: null
      };
    });
  return (errorCode === 200 || errorCode === 0) && responseData
    ? responseData
    : null;
}

// syGroupBuy/chain/reservation/getCityInfoByCityId?city_id=175
export async function getCityInfoByCityId(city_id) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getCityInfoByCityId',
    data: {
      city_id: city_id || 0
    }
  })
    .then((res) => res.data)
    .catch(() => {
      return {
        errorCode: -100,
        errorMsg: '',
        responseData: null
      };
    });
  return res;
}
