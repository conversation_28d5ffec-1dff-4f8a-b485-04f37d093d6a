import Vue from 'vue';

/**
 * 连锁预约头部数据
 * @returns {Promise<Object|null>}
 * */
export async function getReserveIndexHeader(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getReserveIndexHeader',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 连锁预约列表
 * @params { string } uid
 * @params { number } index
 * @params { number } limit
 * */
export async function getReserveIndexList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getReserveIndexList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 待到店列表
 * */
export async function getWaitArriveList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/visit/getWaitArriveList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 服务记录列表
 *
 * */
export async function getServiceRecordList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/visit/getServiceRecordList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}
