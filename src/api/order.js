import Vue from 'vue';
/**
 * 获取确认订单页数据接口
 * @param {Object[]} skuList
 * @param {string} allowance_id
 * @returns {Promise<Object|null>} 确认订单页数据 或者 null
 */
export async function apiPayInfo(skuList, allowance_id = '', use_points) {
  const products = skuList.map(
    ({
      material_id,
      material_type,
      pid,
      code_id,
      times = 0,
      use_code,
      is_group,
      group_id
    }) => ({
      material_id,
      material_type,
      pid,
      code_id,
      times,
      use_code,
      is_group,
      group_id
    })
  );
  let skuJson = '';
  const { qrcode_id = '' } = skuList[0];
  try {
    skuJson = JSON.stringify(products);
  } catch (e) {
    uni.$log('apiPayInfo', e, 'error');
  }
  const res = await Vue.$request({
    url: '/syGroupBuy/order/confirmOrder',
    data: {
      products: skuJson,
      qrcode_id,
      allowance_id,
      use_points
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 申请接口
 * @param {string} order_id
 * @param {string} period
 * @returns {Promise<Object|null>} 申请接口 或者 null
 */
export async function apiCreateOrderLoan(order_id, period) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/createOrderLoan',
    data: {
      order_id,
      period
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取拼团信息
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} group_id
 * @returns {Promise<Object|null>} 拼团信息 或者 null
 */
export async function apiGroupInfo(material_id, material_type, group_id) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/groupConfirmInfo',
    data: {
      material_id,
      material_type,
      group_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 是否有加入拼团权限
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} group_id
 * @returns {Promise<Object>} 是否可以参团
 */
export async function apiGroupCheckCanJoin(data) {
  const { errorCode, errorMsg } = await Vue.$request({
    url: '/syChainTrade/wxapp/group/checkCanJoinGroup',
    data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  /**
  * 1 此商品无拼团活动
    2 拼团活动未开始
    3 拼团活动已结束
    4 此拼团活动未找到
    5 该团已满员，无法参加
    6 您参加的拼团未成功
    7 参团时间已经超过了哦
    8 拼团仅能新用户才能参加哦
    9 您已经参加了此团，快去预约医院吧！
    10 您已经参加了此团，快去订单列表支付吧！
    11 很抱歉，该拼团不可群内参与 您可以点击上方商品发起拼团，并私聊邀请好友参团哦~
  */
  return { errorCode, errorMsg };
}

/**
 * 解密
 * @param {string} encryptedData
 * @param {string} iv
 * @returns
 */
export async function apiDecryptedData(sessionKey, encryptedData, iv) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/groupBuy/User/DecodeWxData',
    data: {
      sessionKey,
      encryptedData,
      iv
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      uni.$log('/groupBuy/User/DecodeWxData', errorMsg, 'error');
      return {
        errorCode: -100,
        errorMsg: error,
        responseData: null
      };
    });
  const result =
    (errorCode === 200 || errorCode === 0) && responseData
      ? responseData
      : null;
  // if (!result) {
  // }
  return result;
}

/**
 * 支付成功页,订单和支付信息获取
 * @param {*} order_id
 * @returns {Promise<Object|null>} 支付成功的信息 或者 null
 */
export async function apiOrderSuccessInfo(order_id) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/order/orderFinish',
    data: {
      order_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络异常,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取三个协议
 * @returns {Promise<Object|null>} 协议信息 或者 null
 */
export async function apiUserAgreement() {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/userAgreement'
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 签署协议
 * @returns {Promise<Boolean>} 是否成功
 */
export async function apiSignAgreement(selected) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/setUserAgreement',
    data: {
      selected
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode } = res;
  return errorCode === 200 || errorCode === 0;
}

/**
 * 获取订单列表数据接口
 * @param {number} page
 * @param {number} limit
 * @param {number} type
 * @returns {Promise<Object[]|null>} 订单列表数据 或者 null
 */
export async function getOrderListDataApi({
  page,
  limit,
  type,
  order_id,
  catchFn
}) {
  const res = await Vue.$request({
    url: '/syChainTrade/wxapp/order/getOrderList',
    data: {
      page,
      limit,
      type,
      order_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      catchFn && catchFn();
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  return res;
}

/**
 * 申请退款接口，获取退款拦截信息
 * @param {string} order_id
 * @returns {Promise<Object|null>} 申请接口 或者 null
 */
export async function getApplyBackTipApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/applyBackTip',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取服务电话
 * @param {string} order_id
 * @returns {Promise<Object|null>} 电话信息 或者 null
 */
export async function getResidentTelApi(data) {
  const res = await Vue.$request({
    url: '/groupBuy/Guide/GetResidentTel',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取评价服务列表
 * @param {string} order_id
 * @returns {Promise<Object|null>} 列表 或者 null
 */
export async function getOrderPostListApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/orderPostList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取核销码
 * @param {string} order_id
 * @returns {Promise<Object|null>}
 */
export async function getOrderQrCodeApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/showOrderQrCode',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取订单详情
 * @param {string} order_id
 * @returns {Promise<Object|null>}
 */
export async function getOrderDetailApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/orderDetail',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: data.order_id + errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取服务进程详情
 * @param {string} order_id
 * @returns {Promise<Object|null>}
 */
export async function getServiceProcessApi(data) {
  const res = await Vue.$request({
    url: '/groupBuy/Guide/ServiceProcess',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取预约信息
 * @param {string|number} order_id
 * @param {string|number} card_num
 * @param {string|number} hospital_id
 * @param {string|number} package_id
 * @param {string|number} pid
 * @returns {Promise<Object|null>}
 */
export async function getReserveBaseInfoApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/getReserveBaseInfo',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取二维码核销情况
 * @param {string|number} reserve_id
 * @returns {Promise<Object|null>}
 */
export async function getVerifyStatusApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/getVerifyStatus',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取问诊信息
 * @param {string|number} order_id
 * @returns {Promise<Object|null>}
 */
export async function getInquiryEntranceApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/inquiry/entrance',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取加C情况
 * @param {null}
 * @returns {Promise<Object|null>}
 */
export async function getStewardInfoApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/user/getStewardInfo',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 未加C时获取二维码
 * @param {string|number} city_id
 * @param {string|number} pid
 * @param {string|number} scene
 * @param {string|number} market_activity_id
 * @returns {Promise<Object|null>}
 */
export async function getAcodeUrlApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/getAcodeUrl',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 取消订单
 * @param {string|number} order_id
 * @returns {Promise<Object|null>}
 */
export async function cancelOrderApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/cancelOrder',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg } = res;
  if (+errorCode === 200 || +errorCode === 0) {
    return {
      errorCode
    };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 取消分期
 * @param {string|number} order_id
 * @returns {Promise<Object|null>}
 */
export async function cancelLoanApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/cancelLoan',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg } = res;
  if (+errorCode === 200 || +errorCode === 0) {
    return {
      errorCode
    };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 商品订单列表
 * @param {string} material_id
 * @param {string} material_type
 * @returns {Promise<Array>} 订单列表
 */
export async function apiGetOrderListByMaterial(material_id, material_type) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/materialOrderList',
    data: {
      material_id,
      material_type
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}

/**
 * 限购，检查该商品是否可购买(登录状态下)
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} pid
 * @returns {Promise<Object>}
 * {
      pass_flag: 1, // 1为可购买，0为限购
      not_pass_reason: "", // 限购原因
      not_pass_case: "purchase_limit_obj", // 限购触发的场景值
      black_user_obj: {
        pass_flag: 0,
        not_pass_reason: ""
      },
      purchase_limit_obj: {
        pass_flag: 0,
        not_pass_reason: "",
        tip: "", // 限购原因，用于按钮上的文案显示
        explain: "" // 限购详细原因，用于 toast 提示
      }
    }
 */
export async function canBuyCheckApi(material_id, material_type, pid) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/canBuyCheck',
    data: {
      material_id,
      material_type,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.$log('canBuyCheckApi', res, 'error');
    return null;
  }
}

/**
 * 获取到店提醒列表
 * @returns {Promise<Object>} 订单列表
 */
export async function getUserThreeDaysReserveApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/getUserThreeDaysReserve',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 支付成功后提示
 */
export async function getPayTanKuangInfo(pid) {
  const res = await Vue.$request({
    url: '/syGroupBuy/order/getPayTanKuang',
    data: {
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
