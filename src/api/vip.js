import Vue from 'vue';

export async function getVipHome(data = {}) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/level/levelHome',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

// 获取二维码信息
// /syGroupBuy/chain/user/myQrCode
export async function getQrCodeInfo(data = {}) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/user/myQrCode',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
