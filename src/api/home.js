import config from '@/config';
import store from '@/store';
import Vue from 'vue';

/**
 * 获取首页基础数据
 * @returns {Promise<Object|null>}
 */
export async function getHomeIndexV2(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/detailV2',
    // url: '/syGroupBuy/index/indexV2',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return {};
  }
}
/**
 * 获取首页基础数据
 * @returns {Promise<Object|null>}
 */
export async function getIndexFeedList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/productCardList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}
/**
 * 获取根据ip获取城市信息
 * @returns {Promise<Object|null>}
 */
export async function getDistrictInfoByIp(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/reservation/getDistrictInfoByIp',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return {};
  }
}
/**
 * 获取服务城市列表
 * @returns {Promise<Object|null>}
 */
export async function getCityList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/cityList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 *  反馈弹窗状态
 * @param {string} popup_id 弹窗ID
 * @param {number} popup_type 弹窗类型 1：常规弹窗 2：红包弹窗
 * @returns
 */
export async function apiHomePopupCallBack(popup_id, popup_type = 1) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/popupCallBack',
    data: {
      popup_id,
      popup_type
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 首页获取常规弹窗
 * @returns {Promise<Object|null>}
 */
export async function apiGetNormalPopup(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/normalPopup',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 首页获取红包弹窗
 * @returns {Promise<Object|null>}
 */
export async function apiGetDiscountPopup(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/discountPopup',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取距离最近的机构
 * @returns {Promise<Object|null>}
 */
export async function apiGetMapHospitalGroupCity(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/getMapHospitalGroupCity',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

export async function apiSetSubscribe(data) {
  return new Promise((resolve, reject) => {
    const { unionId, openId, uid } = store.state.global.userInfo;

    uni.request({
      url: config.baseApi + '/syGroupBuy/wx-subscription/subscription',
      data: {
        uid,
        union_id: unionId,
        open_id: openId,
        app_id: config.appId,
        ...data
      },
      header: { 'content-Type': 'application/json' },
      method: 'POST',
      success(res) {
        resolve(res);
      },
      fail(error) {
        reject(error);
      }
    });
  });
}

export async function getConfig() {
  const res = await Vue.$request({
    url: '/xinyang/message/getMessageConfig',
    data: {}
  });
  const { statusCode, data } = res;
  if ((+statusCode === 200 || +statusCode === 0) && data) {
    return data;
  } else {
    return null;
  }
}
