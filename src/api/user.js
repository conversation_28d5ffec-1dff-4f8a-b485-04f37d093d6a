import Vue from 'vue';
import config from '@/config';

/**
 * 获取用户是否已加C
 * @returns {Promise<Object|null>}
 */
export async function checkUserJoinCJumpUrlApi(data = {}) {
  const res = await Vue.$request({
    url: '/syChainTrade/wxapp/order/checkUserJoinCJumpUrl',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取跨境隐私协议内容
 * @returns {Promise<Object|null>}
 */
export async function getCrossBorderPrivacy(data = {}) {
  const res = await Vue.$request({
    host: config.passportApi,
    url: '/Apiuser/GetCrossBorderPrivacy',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 同意跨境隐私协议内容
 * @returns {Promise<Object|null>}
 */
export async function setCrossBorderPrivacy(data = {}) {
  const res = await Vue.$request({
    host: config.passportApi,
    url: '/Apiuser/SetCrossBorderPrivacy',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 校验xytoken是否有效
 * @returns {Promise<Object|null>}
 */
export async function checkXyToken(data = {}) {
  const res = await Vue.$request({
    url: '/Wxpassport/checkXyToken',
    method: 'post',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}

export async function getCouponRed() {
  const res = await Vue.$request({
    url: '/syChainTrade/app/coupon/couponRed',
    data: {}
  })
    .then((res) => res.data)
    .catch(() => {
      return {
        errorCode: -100,
        errorMsg: '',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}
