import Vue from 'vue';

/**
 * 加c领券页基础数据
 * activity_id
 * @returns {Promise<Object|null>}
 */
export async function joinCIndex(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/joinCIndex',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 校验是否加c
 * activity_id
 * @returns {Promise<Object|null>}
 */
export async function checkUserJoinC(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/user/checkUserJoinC',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 获取活动规则文案
 * @returns {Promise<Object|null>}
 */
export async function joinCRule(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/joinCRule',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 领取红包
 * activity_id
 * @returns {Promise<Object|null>}
 */
export async function joinCReceive(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/activity/joinCReceive',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: {}
      };
    });
  return res;
}
