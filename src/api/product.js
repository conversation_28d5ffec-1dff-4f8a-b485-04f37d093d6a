import Vue from 'vue';

/**
 * 秒杀频道页面过来，需要根据id查询商品的card信息
 * @param {Object} param0
 * @returns {Promse<Object|null>} sku的信息 或者 null
 */
export async function apiGetMaterialForOrder({
  material_type,
  material_id,
  hospital_id,
  start_time,
  times,
  pid
}) {
  const res = await Vue.$request({
    url: '/syGroupBuy/product/getMaterialInfoForCreateOrder',
    data: {
      material_type,
      material_id,
      hospital_id,
      start_time,
      times,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (errorCode === 200 || errorCode === 0) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取商品信息
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} sku_id
 * @param {string} qrcode_id
 * @returns {Promse<Object|null>}
 */
export async function apiGetMaterialInfo(
  material_id,
  material_type,
  sku_id,
  qrcode_id
) {
  const params = {
    material_id,
    material_type,
    qrcode_id
  };
  if (sku_id) {
    params.sku_id = sku_id;
  }
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url:
      material_type === 1
        ? '/syGroupBuy/product/spuDetail'
        : '/syGroupBuy/product/packageDetail',
    data: params
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.$log('商品主接口', params, errorMsg, 'error');
    return null;
  }
}
/**
 * 获取套餐内包含的sku详情信息
 * @param {*} material_id
 * @param {*} material_type
 * @returns {Promse<Array>}
 */
export async function apiGetPackageList(material_id, material_type) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/packageProductDetail',
    data: { material_id, material_type }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}

/**
 * 获取拼团楼层信息(2条)
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} pid
 * @returns {Promse<Object|null>} sku的信息 或者 null
 */
export async function apiProductGroupOpenView(material_id, material_type, pid) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/productGroupOpenView',
    data: {
      material_id,
      material_type,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (errorCode === 200 || errorCode === 0) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取拼团楼层信息(所有)
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} pid
 * @returns {Promse<Object|null>} sku的信息 或者 null
 */
export async function apiProductGroupOpenList(
  material_id,
  material_type,
  pid,
  page,
  page_size = 10
) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/productGroupOpenList',
    data: {
      material_id,
      material_type,
      pid,
      page,
      page_size
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (errorCode === 200 || errorCode === 0) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 机构品专用-获取sku信息
 * @param {string} pid
 * @param {string} qrcode_id
 * @returns {Promse<Object|null>} sku的信息 或者 null
 */
export async function apiGetHospitalSkuInfo(pid, qrcode_id) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/hospitalProductDetail',
    data: { qrcode_id, pid }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 活动信息
 * @param {string} material_id
 * @param {string} material_type
 * @param {string} pid
 * @param {string} times
 * @param {string} group_id
 * @returns {Promse<Object|null>} ActivityData 或者 null
 */
export async function apiGetMaterialActivityData(
  material_id,
  material_type,
  pid,
  times,
  group_id
) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/getMaterialActivityData',
    data: {
      material_type,
      material_id,
      group_id,
      pid,
      times
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 商品详情页 活动入口
 * @param {string} material_id
 * @param {string} material_type
 * @returns {Promse<Object|null>}
 */
export async function apiActivityEntrance(material_id, material_type) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/activityEntrance',
    data: {
      material_type,
      material_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 商品详情页 分享二维码生成
 * @param {Object} param0
 * @returns {Promse<Object|null>}
 */
export async function apiGetShareQrCode({
  title,
  cover,
  pusher_id,
  material_id,
  material_type
}) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/getMaterialDetailShareQrcode',
    data: {
      title,
      cover,
      material_id,
      material_type,
      pusher_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}
/**
 * 添加推手
 * @param {string} pusher_id
 * @returns
 */
export async function apiAddPusher(pusher_id) {
  return Vue.$request({
    url: '/syGroupBuy/product/addPusher',
    data: {
      pusher_id
    }
  }).catch((error) => error);
}
/**
 * 获取商品的分期信息
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @param {*} times
 * @returns {Promse<Object|null>}
 */
export async function apiGetProductLoan(
  material_id,
  material_type,
  pid,
  times = 0,
  qrcode_id = '',
  price
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/getProductLoan',
    data: {
      material_type,
      material_id,
      times,
      pid,
      qrcode_id,
      price
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取商品的加购信息
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @param {*} times
 * @returns {Promse<Object|null>}
 */
export async function apiGetPlusProductList(material_id, material_type) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/getPlusProductList',
    data: {
      material_type,
      material_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 * 获取sku券后价
 * @param {*} pid
 * @param {*} times
 * @returns {Promse<Object|null>}
 */
export async function apiGetSkuDiscountInfo(pid, times) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/productOptimalDiscountInfo',
    data: {
      pid,
      times
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.$log(
      'productOptimalDiscountInfo',
      {
        params: {
          pid,
          times
        },
        res: { errorCode, errorMsg, responseData }
      },
      'error'
    );
    return null;
  }
}

/**
 * 获取问答信息
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @returns {Promse<Object|null>}
 */
export async function apiGetSkuQa(material_id, material_type, pid) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/questionData',
    data: {
      material_id,
      material_type,
      pid
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取升级套餐信息
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @returns {Promse<Object|null>}
 */
export async function apiGetRecommendPackage(
  material_id,
  material_type,
  pid,
  times
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/recommendPackage',
    data: {
      material_id,
      material_type,
      pid,
      times
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: []
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 * 获取项目卡片信息
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @returns {Promse<Object|null>}
 */
export async function apiGetCommodityCard(
  material_id,
  material_type,
  pid,
  times
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/commodityCard',
    data: {
      material_id,
      material_type,
      pid,
      times
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: []
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 * 获取打板日记
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @returns {Promse<Object|null>}
 */
export async function apiGetRecommendPost(
  material_id,
  material_type,
  pid,
  times
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/recommendPost',
    data: {
      material_id,
      material_type,
      pid,
      times
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: []
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 * 获取日记列表
 * @param {Object} params0
 * @returns {Promse<Object|null>} PostList 或者 null
 */
export async function apiGetPostList(
  material_id,
  material_type,
  theme_ids,
  page = 1
) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/getThemePostListByTopicIds',
    data: {
      material_type,
      material_id,
      theme_ids,
      page
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return {
      ...responseData,
      data: responseData.data || []
    };
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取气泡
 * @param {*} material_id
 * @param {*} material_type
 * @returns {Promse<Object|null>}
 */
export async function apiGetBubbleData(material_id, material_type) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/materialBubbleData',
    data: {
      material_id,
      material_type
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: []
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}
// import json from '@/components/spu/spu-text.json';
/**
 * 获取气泡
 * @param {*} material_id
 * @param {*} material_type
 * @returns {Promse<Object|null>}
 */
const noticeMap = new Map();
export async function apiGetNoticeMap(url) {
  if (noticeMap.has(url)) {
    return noticeMap.get(url);
  } else {
    const fetchingPromise = new Promise((resolve, reject) => {
      apiGetNoticeMap.fetching = true;
      uni.request({
        url,
        data: {},
        success(res) {
          resolve(res.data);
        },
        fail() {
          reject(null);
        }
      });
    });
    noticeMap.set(url, fetchingPromise);
    return fetchingPromise;
  }
}

/**
 * 获取打板日记对应的帖子列表
 * @param {*} material_id
 * @param {*} material_type
 * @param {*} pid
 * @returns {Promse<Object|null>}
 */
export async function apiGetRecommendPostDetail(
  material_id,
  material_type,
  pid,
  times
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/product/recommendPostDetail',
    data: {
      material_id,
      material_type,
      pid,
      times
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: []
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}
