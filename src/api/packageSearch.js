import Vue from 'vue';

/**
 * 搜索Sug
 * @param {string} keyword
 * @returns {Promise<Object|null>}
 * */
export async function getSearchSugListApi(data) {
  const res = await Vue.$request({
    url: '/apiconnecthub/zy/search/sugg',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 搜索起始页
 * @param {Number} source 1: 商品 2: 活动
 * */
export async function getSearchStartApi(data) {
  const res = await Vue.$request({
    url: '/apiconnecthub/zy/search/start',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 搜索结果页
 * @param {Number} source 1: 商品 2: 活动
 * @param {String} keyword 搜索关键词
 * */
export async function getSearchResultApi(data) {
  const res = await Vue.$request({
    url: '/apiconnecthub/zy/search/feed',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}
