import Vue from 'vue';
/**
 * 保存用户信息
 * @returns {Promise<Object|null>}
 */
export async function apiSaveUserInInfo(data) {
  const { errorCode, errorMsg } = await Vue.$request({
    url: '/syGroupBuy/crm/userSave',
    data
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (errorCode === 200 || errorCode === 0) {
    return true;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return false;
  }
}
/**
 * 保存签到信息
 * @returns {Promise<Object|null>}
 */
export async function apiSaveSignInInfo(data) {
  const { errorCode, errorMsg } = await Vue.$request({
    // url: '/syGroupBuy/crm/visitSave',
    url: '/syGroupBuy/chain/visit/visitSave',
    data
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if (errorCode === 200 || errorCode === 0) {
    return true;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return false;
  }
}
/**
 * 获取签到信息
 * @returns {Promise<Object|null>}
 */
export async function apiGetSignInInfo(data) {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    // url: '/syGroupBuy/crm/visitInfo',
    url: '/syGroupBuy/chain/visit/visitInfo',
    data
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}
/**
 * 获取组织机构
 * @returns {Promise<Object|null>}
 */
export async function apiGetCityList() {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/crm/cityTree ',
    data: {}
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}

/**
 * 获取组织机构
 * @returns {Promise<Object|null>}
 */
export async function apiGetCityTree() {
  const { errorCode, errorMsg, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/visit/cityTree ',
    data: {}
  })
    .then((res) => res.data)
    .catch((err) => {
      return {
        errorCode: -1000,
        errorMsg: err || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return [];
  }
}
