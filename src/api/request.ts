import Taro from '@tarojs/taro'

// API基础配置
const BASE_URL =
  process.env.NODE_ENV === 'development' ? 'https://api-dev.example.com' : 'https://api.example.com'

// 请求拦截器
const interceptors = {
  request: (config: any) => {
    // 添加token等通用配置
    const token = Taro.getStorageSync('token')
    if (token) {
      config.header = {
        ...config.header,
        Authorization: `Bear<PERSON> ${token}`,
      }
    }

    // 显示loading
    Taro.showLoading({
      title: '加载中...',
    })

    return config
  },

  response: (response: any) => {
    // 隐藏loading
    Taro.hideLoading()

    // 统一处理响应
    if (response.statusCode === 200) {
      const { code, data, message } = response.data

      if (code === 0) {
        return data
      } else {
        Taro.showToast({
          title: message || '请求失败',
          icon: 'none',
        })
        return Promise.reject(new Error(message))
      }
    } else {
      Taro.showToast({
        title: '网络错误',
        icon: 'none',
      })
      return Promise.reject(new Error('网络错误'))
    }
  },
}

// 封装请求方法
export const request = {
  get: (url: string, data?: any) => {
    const config = interceptors.request({
      url: BASE_URL + url,
      method: 'GET',
      data,
      header: {
        'Content-Type': 'application/json',
      },
    })

    return Taro.request(config).then(interceptors.response)
  },

  post: (url: string, data?: any) => {
    const config = interceptors.request({
      url: BASE_URL + url,
      method: 'POST',
      data,
      header: {
        'Content-Type': 'application/json',
      },
    })

    return Taro.request(config).then(interceptors.response)
  },

  put: (url: string, data?: any) => {
    const config = interceptors.request({
      url: BASE_URL + url,
      method: 'PUT',
      data,
      header: {
        'Content-Type': 'application/json',
      },
    })

    return Taro.request(config).then(interceptors.response)
  },

  delete: (url: string, data?: any) => {
    const config = interceptors.request({
      url: BASE_URL + url,
      method: 'DELETE',
      data,
      header: {
        'Content-Type': 'application/json',
      },
    })

    return Taro.request(config).then(interceptors.response)
  },
}

export default request
