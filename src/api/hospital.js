import Vue from 'vue';

/**
 * 获取机构列表
 * @param {number} city_id
 * @param {number} pid
 * @param {number} package_id
 * @param {number} activity_id
 * @returns {Promise<Object|null>}
 */
export async function getHospitalListApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/hospital/mapHospitalList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 200 || +errorCode === 0) {
    return responseData;
  } else {
    if (errorMsg !== 'ok') {
      uni.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
    return null;
  }
}

/**
 * 获取城市列表
 * @param {number} city_id
 * @param {number} pid
 * @param {number} package_id
 * @param {number} activity_id
 * @returns {Promise<Object|null>}
 */
export async function getCityListApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/hospital/mapCityList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if (+errorCode === 200 || +errorCode === 0) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取tag_id标签详细数据
 * @param {number} tag_id
 * @returns {Promise<Object|null>}
 */
export async function getDoctorTagInfoApi(tag_id) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/hospital/doctorTagInfo',
    data: {
      tag_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return false;
  }
}

/**
 * 获取医生详细数据
 * @param {number} doctor_id
 * @returns {Promise<Object|null>}
 */
export async function getDoctorInfoApi(doctor_id) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/hospital/doctorInfo',
    data: {
      doctor_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return false;
  }
}

/**
 * 获取机构详细数据
 * @param {number} hospital_id
 * @returns {Promise<Object|null>}
 */
export async function getHospitalInfoApi(hospital_id) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/hospital/homePage',
    data: {
      hospital_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return false;
  }
}
/**
 * 获取机构资质信息
 * @param {number} hospital_id
 * @returns {Promise<Object|null>}
 */
export async function getHospitalSafeGuard(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/hospital/hospitalGuarantee',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, errorMsg, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    uni.showToast({
      title: errorMsg,
      icon: 'none'
    });
    return null;
  }
}

/**
 * 获取机构热卖商品数据
 * @param {number} hospital_id
 * @param {number} tag_id
 * @returns {Promise<Object|null>}
 */
export async function getHotProductApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/index/productCardList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    // uni.showToast({
    //   title: errorMsg,
    //   icon: 'none'
    // });
    return null;
  }
}

/**
 * 获取机构主页最近浏览商品数据
 * @param {number} material_id
 * @param {number} material_type
 * @returns {Promise<Object|null>}
 */
export async function getLastviewProductApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/hospital/recentViewProduct',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    // uni.showToast({
    //   title: errorMsg,
    //   icon: 'none'
    // });
    return null;
  }
}

/**
 * 获取机构主页最近浏览商品数据
 * @param {number} activity_id
 * @param {number} hospital_id
 * @param {number} page
 * @returns {Promise<Object|null>}
 */
export async function geActivityProductApi(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/hospital/activityProduct',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if ((+errorCode === 200 || +errorCode === 0) && responseData) {
    return responseData;
  } else {
    // uni.showToast({
    //   title: errorMsg,
    //   icon: 'none'
    // });
    return null;
  }
}

/**
 * 获取机构加C页面客服二维码
 * @param {number} hospital_id
 * @returns {Promise<Object|null>}
 */
export async function getConsultantInfo(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/hospital/consultantInfo',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return res;
  } else {
    return null;
  }
}

// 获取所有门店列表及城市
export async function getTenantCityList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/hospital/getTenantCityList',
    data: data,
    method: 'post'
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}

// 获取城市下的所有门店列表
export async function getTenantOneCityList(data) {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/hospital/getTenantList',
    data: data
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取机构列表接口
 * @returns {Promise<Object|null>}
 */
export async function getHospitalListInChoosePage(
  location_city_id,
  select_city_id,
  position
) {
  console.log(location_city_id, select_city_id, position);
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/project/getGroupBuyChainProjectCityHospital',
    data: Object.assign(
      {
        location_city_id,
        select_city_id
      },
      position || {}
    )
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}

/**
 * 获取推荐城市
 * @returns {Promise<Object|null>}
 */
export async function getRecommendCityId() {
  const res = await Vue.$request({
    url: '/syGroupBuy/chain/project/getRecommendCityId',
    data: {}
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  const { errorCode, responseData } = res;
  if (+errorCode === 0 && responseData) {
    return responseData;
  } else {
    return null;
  }
}
