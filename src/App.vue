<script>
import { getYxDayConfig } from '@/api/reservation';
import { checkXyToken, getCouponRed } from '@/api/user';
import config from '@/config';
import * as Sentry from '@/utils/sentry-uniapp.min';
import { createNamespacedHelpers, mapState } from 'vuex';
const { mapActions } = createNamespacedHelpers('reserve');
export default {
  async onLaunch(opts) {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync();
    // topBarHeight:系统信息中添加右侧胶囊（含胶囊）以上的高度(包括胶囊)
    const menuRect = uni.getMenuButtonBoundingClientRect();
    if (menuRect) {
      systemInfo.topBarHeight = menuRect.top + menuRect.height + 2;
      systemInfo.menuBtnTop = menuRect.top;
      systemInfo.menuBtnHeight = menuRect.height;
    }
    this.$store.commit('global/setSystemInfo', systemInfo);
    await this.$uniLogin();
    this.startup(opts);
    // 清空订单列表翻页缓存，记录离开订单列表页时当前页面页数
    uni.setStorageSync('order_list_page_num', 1);

    uni.loadFontFace({
      family: 'OutFit-Regular',
      global: true,
      desc: {
        weight: 400
      },
      source:
        'url(https://static.soyoung.com/sy-pre/outfit-variablefont_wght-1725869400650.ttf)',
      success: () => {
        console.log('OutFit-Regular字体加载成功');
      }
    });
    uni.loadFontFace({
      family: 'OutFit-Medium',
      global: true,
      desc: {
        weight: 500
      },
      source:
        'url(https://static.soyoung.com/sy-pre/outfit-variablefont_wght-1725869400650.ttf)',
      success: () => {
        console.log('OutFit-Regular字体加载成功');
      }
    });
    uni.loadFontFace({
      family: 'DfKing-Regular',
      global: true,
      desc: {
        weight: 400
      },
      source:
        'url(https://static.soyoung.com/sy-pre/24dnu5dnfwi22-1745910600621.ttf)',
      success: () => {
        console.log('DfKing-Regular字体加载成功');
      }
    });
    uni.loadFontFace({
      family: 'DfKing-Medium',
      global: true,
      desc: {
        weight: 400
      },
      source:
        'url(https://static.soyoung.com/sy-pre/iyd4e0z2qt55-1745910600621.ttf)',
      success: () => {
        console.log('DfKing-Medium字体加载成功');
      }
    });
  },
  onShow: function (opts) {
    const reg = new RegExp('(^|&)psid=([^&]*)(&|$)');
    const userInfo = uni.getStorageSync('user_info') || {};
    const scene = decodeURIComponent(
      opts.query && opts.query.scene ? opts.query.scene : ''
    );
    let r = null;
    if (scene && scene.indexOf('psid') > -1) {
      r = scene.match(reg);
    }
    if (opts.query && opts.query.psid) {
      userInfo.psid = decodeURIComponent(opts.query.psid);
    } else {
      if (r != null && unescape(r[2])) {
        userInfo.psid = unescape(r[2]);
      } else {
        userInfo.psid = '0';
      }
    }
    userInfo.marketActivityId =
      opts.query && opts.query.market_activity_id
        ? parseInt(opts.query.market_activity_id)
        : 0;
    if (opts.query && opts.query.channel_id) {
      userInfo.channel_id = opts.query.channel_id;
    }

    if (opts.query && opts.query.market_activity_id) {
      uni.setStorageSync('market_activity_id', opts.query.market_activity_id);
    } else {
      const c_market_activity_id = uni.getStorageSync('market_activity_id');
      if (c_market_activity_id) {
        userInfo.marketActivityId = parseInt(c_market_activity_id);
      }
    }
    /**
     * 获取落地页URL中的click_id，
     * 对于腾讯广告非微信流量为URL中的参数qz_gdt的值，对于微信流量为URL中的参数gdt_vid的值。
     * 后续上报转化数据的时候click_id为必传值
     */
    if (opts.query && (opts.query.gdt_vid || opts.query.qz_gdt)) {
      const clickId = opts.query.gdt_vid || opts.query.qz_gdt;
      const time = new Date();
      uni.setStorageSync('wx_ad_data', {
        clickId,
        time
      });
    }
    this.$setUserInfoToStorage(userInfo);
    uni.setStorageSync('sys', config.sys);
    // 每次小程序打开的时候上报当前小程序地理位置
    this.$saveWxLastLocation(userInfo.cityId);
    // 小程序更新机制
    this.updateManager();
    this.checkScopeOfUserLocation().then((res) => {
      if (res === false) {
        this.$setUserInfoToStorage({
          lat: '',
          lng: '',
          cityId: '',
          cityName: ''
        });
      } else {
        this.$getCityId('gcj02').catch(() => ({}));
      }
    });

    // 检查用户是否有xyToken
    if (this.userInfo.uid && this.userInfo.xyToken) {
      checkXyToken().then((res) => {
        if (res.isValid !== 1) {
          this.$setUserInfoToStorage({
            uid: '',
            xyToken: ''
          });
          const user_info = uni.getStorageSync('user_info');
          uni.removeStorage({
            key: `history_${user_info.unionId}`
          });
        }
      });
    }
    this.getCouponInfo();
  },
  onHide: function () {
    // 小程序退出时修改隐藏“我的页面”中的预约提示
    uni.setStorageSync('my_page_hide_order_tip', false);
    console.log('App Hide');
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  methods: {
    ...mapActions(['setYxDayASync']),
    startup(opts) {
      // 埋点添加 wx_app_startup  https://soyoung.feishu.cn/docs/doccnWnMutcVxuKFyUDVzznmvkf#
      try {
        if (wx.syTrack) {
          wx.syTrack.initSyTrackSessionObj(1, opts);
        }
        const { path, query, scene, referrerInfo, forwardMaterials, chatType } =
          wx.getLaunchOptionsSync();
        const _trackObj = {
          info: 'wx_app_startup',
          ext: {
            path,
            query,
            scene,
            referrerInfo,
            forwardMaterials,
            chatType,
            appid: (opts && opts.referrerInfo && opts.referrerInfo.appId) || 0
          }
        };
        // this是全局sendEvent方法，也可能是uni.$track等其他挂载的位置
        this.$reportData(_trackObj);
        getYxDayConfig().then((res) => {
          if (res) this.setYxDayASync(res);
        });
      } catch (e) {
        console.log(e);
      }
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    },
    async updateManager() {
      // 检查更新
      const updateManager = uni.getUpdateManager();
      updateManager.onCheckForUpdate(() => {});
      updateManager.onUpdateReady(() => {
        updateManager.applyUpdate();
      });
      updateManager.onUpdateFailed((error) => {
        uni.$log(`更新失败了`, error, 'error');
      });
    },
    // 获取红包的信息
    async getCouponInfo() {
      const res = await getCouponRed();
      if (res?.has_red_dot) {
        uni.$emit('__global_coupon_bar_red_toggle__', true);
      }
    }
  },
  created() {
    uni.$on('__global_coupon_bar_red_toggle__', (visible = false) => {
      if (visible) {
        wx.showTabBarRedDot({
          index: 2 // 红包
        });
      } else {
        wx.hideTabBarRedDot({
          index: 2 // 红包
        });
      }
    });
  },
  onPageNotFound() {
    uni.redirectTo({
      url: '/pages/index/index'
    });
  },
  onError(err) {
    // 优化错误记录，只记录关键信息
    const simplifiedUserInfo = {
      id: this.userInfo.openId || this.userInfo.unionId,
      username: this.userInfo.uid || this.userInfo.unionId
    };

    // 设置用户信息，但不包含过多细节
    Sentry.setUser({
      ...simplifiedUserInfo,
      ip_address: '{{auto}}'
    });

    // 使用优化的错误记录
    uni.$logError(err, 'AppOnError');

    // 简化错误对象后再发送
    if (typeof err === 'object' && err !== null) {
      try {
        // 创建简化版错误对象
        const simplifiedError = {
          message: err.message || String(err).substring(0, 100),
          name: err.name || 'AppError'
        };

        // 只保留堆栈的重要部分
        if (err.stack) {
          const stackLines = err.stack.split('\n');
          simplifiedError.stack = stackLines.slice(0, 5).join('\n');
        }

        Sentry.captureException(simplifiedError);
      } catch (e) {
        // 如果处理失败，发送简单消息
        Sentry.captureMessage('应用错误: ' + String(err).substring(0, 200));
      }
    } else {
      // 简单字符串错误
      Sentry.captureMessage('应用错误: ' + String(err).substring(0, 200));
    }
  },

  onUnhandledRejection(err) {
    // 使用优化的错误记录
    uni.$logError(err, 'UnhandledRejection');

    // 简化Promise错误后再发送
    if (err && typeof err === 'object') {
      const simplifiedError = {
        message: err.message || err.reason || String(err).substring(0, 100),
        name: 'UnhandledRejection'
      };

      Sentry.captureException(simplifiedError);
    } else {
      Sentry.captureMessage(
        '未处理的Promise拒绝: ' + String(err).substring(0, 200)
      );
    }
  }
};
</script>

<style lang="less">
::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none !important;
  background: transparent !important;
  color: transparent !important;
}
.translucent-opacity-cover {
  position: relative;
  &::after {
    content: '';
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.03);
    position: absolute;
  }
}
</style>
