import config from '../config.js';
/**
 * <AUTHOR>
 * 日志上报
 * @param {Object} options
 */
uni.$log = function (...args) {
  if (!uni.getRealtimeLogManager) return;
  const logManager = uni.getRealtimeLogManager();
  const lastArg = args[args.length - 1];
  let type = 'info';

  // 处理日志类型
  if (['info', 'error', 'warn'].includes(lastArg)) {
    type = lastArg;
    args = Array.prototype.slice.call(args, 0, args.length - 1);
  }

  // 获取用户信息
  const user_info = uni.getStorageSync('user_info');

  // 准备日志内容
  const logData = [`[${config.version}][${config.env}]`];

  // 处理和限制日志参数大小
  for (const arg of args) {
    try {
      if (typeof arg === 'object') {
        // 对象类型数据进行字符串化并限制大小
        const stringified = JSON.stringify(arg);
        if (stringified.length > 500) {
          // 限制单个对象大小
          logData.push(stringified.substring(0, 500) + '...[truncated]');
        } else {
          logData.push(stringified);
        }
      } else {
        // 简单类型直接添加
        logData.push(arg);
      }
    } catch (e) {
      logData.push('[无法序列化的数据]');
    }
  }

  // 添加简化的用户信息，而不是完整对象
  if (user_info) {
    const { uid } = user_info;
    logData.push(`uid:${`${uid}`?.substring(0, 10) || 'none'}`);
  }

  // 发送日志
  logManager?.[type](...logData);

  // 添加过滤条件
  logManager.addFilterMsg(config.version);
  logManager.addFilterMsg(config.env);

  // 添加用户ID作为过滤条件
  if (user_info && user_info.uid) {
    logManager.addFilterMsg(user_info.uid);
  }
};

// 添加可以记录简单错误的辅助函数
uni.$logError = function (err, context = '') {
  if (!uni.getRealtimeLogManager) return;

  // 如果是Error对象，提取关键信息
  let errorMsg = '';
  if (err instanceof Error) {
    errorMsg = `${err.name}: ${err.message}`;
    if (err.stack) {
      // 只取堆栈前200个字符，避免过大
      const stackSummary = err.stack.split('\n').slice(0, 3).join('\n');
      errorMsg += ` | ${stackSummary}`;
    }
  } else if (typeof err === 'object') {
    try {
      errorMsg = JSON.stringify(err).substring(0, 300);
    } catch (e) {
      errorMsg = '[无法序列化的错误对象]';
    }
  } else {
    errorMsg = String(err);
  }

  uni.$log(`错误(${context}):`, errorMsg, 'error');
};
