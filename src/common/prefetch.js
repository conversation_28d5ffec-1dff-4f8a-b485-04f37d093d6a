import { apiGetMaterialInfo } from '../api/product';
import {
  getHospitalListInChoosePage,
  getRecommendCityId
} from '@/api/hospital.js';

// 详情页预拉取方法
uni.navigateToSpu = function ({
  material_id,
  material_type,
  sku_id = '',
  times = '',
  group_id = '',
  pusher_id = '',
  auto_share_pull = '',
  share_page = '',
  qrcode_id = ''
}) {
  const promise = apiGetMaterialInfo(material_id, material_type, sku_id);
  const query = [
    `material_id=${material_id}`,
    `material_type=${material_type}`,
    `sku_id=${sku_id}`,
    `times=${times}`,
    `group_id=${group_id}`,
    `pusher_id=${pusher_id}`,
    `auto_share_pull=${auto_share_pull}`,
    `share_page=${share_page}`,
    `qrcode_id=${qrcode_id}`,
    `prefetch=1`
  ];
  uni.navigateTo({
    url: `/pages/product?${query.join('&')}`,
    events: {
      onSpuReached(data) {
        console.log('跳转成功！', data);
      }
    },
    success(res) {
      res.eventChannel.emit('prefetchSpu', { promise });
    }
  });
};

// 机构选择预拉取方法
uni.navigateToHospitalChoosePage = async function (cityId, wantCityId) {
  let needServerRecommend = !!cityId && !wantCityId; // 是否需要推荐城市
  console.log(
    'navigateToHospitalChoosePage',
    `cityId:${cityId},wantCityId:${wantCityId}是否推荐城市：${needServerRecommend}`
  );
  uni.$log(
    'navigateToHospitalChoosePage',
    `cityId:${cityId},wantCityId:${wantCityId}是否推荐城市：${needServerRecommend}`
  );
  if (needServerRecommend) {
    const res = await getRecommendCityId();
    uni.$log(cityId, wantCityId, '推荐' + res?.recommend_city_id);
    wantCityId = res?.recommend_city_id;
  } else if (cityId && wantCityId) {
    // nothting to do
  } else if (!cityId && wantCityId) {
    // nothting to do
  } else {
    wantCityId = 1;
  }
  uni.navigateTo({
    url: `/packageHospital/hospital-choose?prefetch=1&want_city_id=${wantCityId}`,
    events: {
      afterSelectHospital: () => {}
    },
    success: (res) => {
      res.eventChannel.emit(
        'hospitalMapListData',
        getHospitalListInChoosePage(cityId, wantCityId)
      );
    }
  });
};
