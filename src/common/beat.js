export class Beat {
  constructor() {
    this.beatTimer = null;
    this.beatQueue = new Map();
    this.isBeating = false;
  }
  add(cb) {
    if (!cb) return this;
    this.beatQueue.set(cb, 1);
    return this;
  }
  remove(cb) {
    if (!cb) return this;
    this.beatQueue.delete(cb);
    return this;
  }
  clear() {
    this.stop();
    for (let [cb] of this.beatQueue) {
      this.beatQueue.delete(cb);
    }
    this.stop();
    return this;
  }
  stop() {
    clearTimeout(this.beatTimer);
    this.isBeating = false;
    return this;
  }
  destroy() {
    this.clear();
    this.beatTimer = null;
    this.isBeating = false;
    this.beatQueue = null;
  }
  run(ms = 100) {
    if (this.isBeating) return this;
    this.isBeating = true;
    const beat = () => {
      for (let [cb] of this.beatQueue) {
        cb();
      }
      this.beatTimer = setTimeout(beat, ms);
    };
    beat();
    return this;
  }
}

export function beatFactory(vm) {
  // 一个页面内所有组件 共用一个 beat
  let beat = new Beat();
  vm.$once('hook:beforeDestroy', () => {
    beat.destroy();
    beat = null;
  });
  return beat;
}

export function getCurrentPageId(vm) {
  // console.log(vm.$mp, (vm.$mp.page || vm.$mp.component)?.getPageId());
  return (vm.$mp.page || vm.$mp.component)?.getPageId();
}
