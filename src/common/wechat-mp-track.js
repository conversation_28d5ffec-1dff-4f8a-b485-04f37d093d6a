/* eslint-disable */

/**
 *  迭代记录
 *  20230330 为啥下载这个文件,而不是用npm包;sendAjax 的 return promise 是一个无意义的promise,
 *   曾经尝试重写 wx.request,(釜底抽薪那就无视这个文件内部的实现了)但是wx.request在iOS上无法重写,
 *   没办法了,我只能copy一份,wx.request => uni.request;
 *   最小化改动,实现我能拿到 sendAjax 的返回时机,来实现队列的有序上报!
 *  20210907 - [小程序埋点上报丢失优化] - https://wiki.sy.soyoung.com/pages/viewpage.action?pageId=203002563
 *  20210830 - [埋点JS，插件化] - https://wiki.sy.soyoung.com/pages/viewpage.action?pageId=200013694
 *  20211117 - [支持UTM] - https://wiki.sy.soyoung.com/pages/viewpage.action?pageId=205727393
 *  20211227 - [支持代报] - https://wiki.sy.soyoung.com/pages/viewpage.action?pageId=203003043
 */

import { cloneDeep } from 'lodash-es';
import queue from 'queue';
const packageInfo = {
  version: '1.0.100'
};

const md5 = require('md5');

const SessionArray = [
  'article_id', // 公众号文章投放
  'launch_type', // 启动类型
  'launch_id',
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content'
];

const wxReportMonitor = {
  PageNotFound: '0',
  report: (id, value) => {
    if (wx && wx.reportMonitor) {
      wx.reportMonitor(id, value);
    }
  }
};

export default class SyTrack {
  /**
   * @constructor
   * @param {object} configObj - 传入初始化配置对象参数
   * @param {string} configObj.mpVersion - 小程序版本
   * @param {string} configObj.syAppId - 新氧业务定义的sys
   * @param {string} configObj.isTest - 是否上报到线上测试表
   */
  constructor(configObj = {}) {
    this.configObj = configObj;
    const { mpVersion, syAppId } = this.configObj;

    if (!mpVersion || !syAppId || mpVersion === '0' || syAppId === '0') {
      throw new Error('syTrack异常，请检查初始化参数');
    }

    this._initAppEventlistener();
    this._sendQueue = queue({
      concurrency: 1,
      autostart: true,
      timeout: 100
    });
    this.sessionObj = {};
  }

  // 生成会话id
  static _createSessionID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  _initAppEventlistener() {
    const _this = this;
    wx.onAppShow((opts) => {
      const _trackObj = {
        info: 'wx_app_awake',
        ext: {
          sdk_auto: 1
        }
      };

      if (wx && wx.getEnterOptionsSync) {
        const { path, query, scene, referrerInfo, forwardMaterials, chatType } =
          wx.getEnterOptionsSync();

        // 有新参数且和会话不一致的时候 需要重置session
        if (!_this.sessionObj.isOnLaunch) {
          const _findFlag = SessionArray.find((item) => {
            return query[item] && query[item] !== _this.sessionObj[item];
          });
          _findFlag &&
            this.initSyTrackSessionObj(0, {
              scene,
              query
            });
        }

        _trackObj.ext = {
          path,
          query,
          scene,
          referrerInfo,
          forwardMaterials,
          chatType,
          appid: (opts && opts.referrerInfo && opts.referrerInfo.appId) || 0,
          sdk_auto: 1
        };
      }
      _this.SendReqTrack(_trackObj);
      _this.sessionObj && (_this.sessionObj.isOnLaunch = 0);
      _this._log('onAppShow');
    });
    wx.onAppHide(() => {
      const s = {
        info: 'wx_app_hide'
      };
      s.ext = {
        sdk_auto: 1
      };

      if (getCurrentPages && getCurrentPages().length > 0) {
        const _pagesArr = getCurrentPages();
        const _page = _pagesArr[_pagesArr.length - 1];
        ['route', 'options'].forEach((item) => {
          _page &&
            _page[item] &&
            (s.ext[item === 'route' ? 'path' : item] = _page[item]);
        });
      }
      _this.SendReqTrack(s);
      _this._log('onAppHide');
    });
    wx.onPageNotFound((res) => {
      const p = {
        info: 'sy_wx_other_page_not_found_sys'
      };
      p.ext = res || {};
      _this.SendReqTrack(p);

      try {
        wxReportMonitor.report(wxReportMonitor.PageNotFound, 1);
      } catch (e) {
        _this._log('onPageNotFound');
      }
    });
  }

  _log_seq_plus(time) {
    const _this = this;
    return new Promise((resolve) => {
      const _session = _this.sessionObj;
      _session.createTime = time;
      _session.log_seq += 1;
      _this.sessionObj = _session;
      resolve(_session);
    });
  }

  // 30分钟会话内判断是否有效字段
  _diffSession() {
    const _this = this;
    return new Promise(async (resolve) => {
      const { createTime, syTrackIsDebug, is_rsync_bp } = _this.sessionObj;
      const _n = new Date().getTime();
      const _t = 30;
      const _validTime = Math.abs(_n - createTime) <= _t * 60 * 1000;
      if (!_validTime) {
        _this._log(`会话过期,session重置`);
        _this._sendQueue.stop();
        await _this.initSyTrackSessionObj(0, {
          query: {
            is_rsync_bp,
            syTrackIsDebug,
            expire: 1
          }
        });
        _this._sendQueue.start();
      }
      await _this._log_seq_plus(_n);
      resolve();
    });
  }

  // 初始化或者重置sessionObj
  initSyTrackSessionObj(isOnLaunch = 0, { scene, query } = {}) {
    const _this = this;
    return new Promise((resolve) => {
      const _obj = {
        env: 'unknown',
        systemInfo: wx.getSystemInfoSync() || {}, // https://developers.weixin.qq.com/miniprogram/dev/api/base/system/wx.getSystemInfoSync.html
        id: this.constructor._createSessionID(), // 当前会话id
        createTime: new Date().getTime(), // 创建时间
        log_seq: 0, // 当前会话id内发送的第几个埋点,
        scene,
        isOnLaunch
      };
      const _array = [
        'is_rsync_bp', // 埋点测试平台使用
        'syTrackIsDebug'
      ];

      if (!query.expire) {
        SessionArray.forEach((item) => _array.push(item));
      }

      _array.forEach((item) => {
        _obj[item] = query[item] || undefined;
      });
      __wxConfig.envVersion && (_obj.env = __wxConfig.envVersion);

      if (query.launch_type) {
        if (wx) {
          const { path, query: launch_query } = wx.getLaunchOptionsSync();
          _obj.utm_url = path;
          const _queryString = _this._serialize(launch_query);
          _queryString && (_obj.utm_url += `?${_queryString}`);

          _obj.utm_url = encodeURIComponent(_obj.utm_url);
        }
        _obj.launch_type = query.launch_type || null;
        _obj.launch_id = query.launch_id || null;
      }
      _this.sessionObj = _obj;
      _this._log(`init session`);
      resolve();
    });
  }

  // 新埋点发送方法
  SendReqTrack(...info) {
    const _this = this;
    _this._sendQueue.push(() => {
      return new Promise(async (resolve) => {
        await _this._diffSession().catch((err) => {
          _this._log(err);
        });
        await _this.sendAjax(...info);
        resolve();
      });
    });
  }

  sendAjax(...info) {
    const _this = this;
    return new Promise(async (resolve) => {
      try {
        // 官方爬虫不发送埋点 https://developers.weixin.qq.com/miniprogram/dev/framework/sitemap.html
        if (wx.getStorageSync('scene') === 1129) return;
        // 字段含义-wiki地址:http://wiki.lavion.com.cn:8098/index.php?title=%E6%97%A5%E5%BF%97%E7%BB%93%E6%9E%84

        const mark = 'buitdtku.hkyfudigu6';
        const unionID = wx.getStorageSync('unionID') || 0;
        const openID = wx.getStorageSync('openID') || 0;
        const timestamp = Date.now();
        const data = cloneDeep(info[0]);
        const sign = md5(mark + openID + unionID + timestamp);
        // let sign = mark + openID + unionID + timestamp
        _this._log(`log_seq_before_${sign}:${_this.sessionObj.log_seq}`);
        // 判断会话是否过期
        const _session = _this.sessionObj;
        const {
          id,
          log_seq,
          is_rsync_bp,
          article_id,
          isOnLaunch,
          utm_source,
          utm_medium,
          utm_campaign,
          utm_term,
          utm_content,
          utm_url,
          launch_type,
          launch_id,
          scene
        } = _session;
        _this._log(`log_seq_after_${sign}:${log_seq}`);

        const datas = {};
        datas.data = {
          sdktype: 4,
          unionid: unionID || 0,
          openid: openID || 0,
          sys: wx.getStorageSync('stat_sys') || 0,
          client_time: timestamp,
          type: data.type || 1, // 1为from_action;2为page_hide;3为page_show;4为page_unload（ 目前数据只存 1、3 ）
          from_action: data.info,
          ext: data.ext,
          lng: wx.getStorageSync('lng') || 0,
          lat: wx.getStorageSync('lat') || 0,
          wxa_ver: this.configObj.mpVersion,
          uid: wx.getStorageSync('uid') || 0,
          city_id: wx.getStorageSync('cityId') || 0,
          is_h5: data.is_h5 || 0,
          sign: sign,
          stack_length: data.stack_length || 0,
          appid: this.configObj.syAppId || 0,
          market_activity_id:
            wx.getStorageSync('user_info').marketActivityId || 0,
          session_id: id || 0,
          log_seq: log_seq || 0,
          article_id: article_id || 0,
          session_scene: scene || null,
          old_scene: wx.getStorageSync('scene') || null,
          system_info: {},
          mp_env: 'unknown',
          sdk_version: packageInfo.version,
          utm_source,
          utm_medium,
          utm_campaign,
          utm_term,
          utm_content,
          utm_url,
          launch_type,
          launch_id
        };

        const _storageData = _session;
        _storageData.env && (datas.data.mp_env = _storageData.env);

        if (_storageData.systemInfo) {
          [
            'brand',
            'model',
            'system',
            'platform',
            'host',
            'enableDebug',
            'language',
            'version'
          ].forEach((item) => {
            _storageData.systemInfo[item] &&
              (datas.data.system_info[item] = _storageData.systemInfo[item]);
          });
        }

        if (!isOnLaunch && getCurrentPages && getCurrentPages().length > 0) {
          const _pagesArr = getCurrentPages();
          const _page = _pagesArr[_pagesArr.length - 1];
          const _obj = {
            page_stack_length: _pagesArr.length
          };
          ['route', 'options'].forEach((item) => {
            _page && _page[item] && (_obj[item] = _page[item]);
          });
          datas.data.current_page_info = _obj;
        }

        let _url = ['release', 'trial'].includes(_storageData.env)
          ? 'https://st3.soyoung.com'
          : 'https://testst3.soyoung.com';
        if (_this.configObj.isTest) {
          _url += '/sy_mp';
        } else {
          _url += '/wxa/uplog.php';
        }

        uni.request({
          url: _url,
          data: datas,
          method: 'POST',
          success() {
            _this._log(`${datas.data.from_action}上报成功`);
          },
          fail(err) {
            wx.getNetworkType({
              success(resNetwork) {
                uni.$log('埋点', err, datas, resNetwork, 'error');
              }
            });
          }
        });
        // 埋点管理平台使用的标识，有标识就双报
        let _isBp = false;
        try {
          if (!is_rsync_bp && getApp) {
            const { globalData } = getApp();
            _isBp = globalData?.syTrackSession?.is_rsync_bp;
            _isBp && (_session.is_rsync_bp = 1);
          }
        } catch (e) {
          _this._log(e);
        }
        if (is_rsync_bp || _isBp) {
          // let _url = ['release', 'trial'].includes(_storageData.env)
          //   ? 'https://dataknower.soyoung.com'
          //   : 'https://data-test.sy.soyoung.com';
          let _url = 'https://dataknower.soyoung.com/bp/api/h5-add';
          // _url += '/bp/api/h5-add';
          uni.request({
            url: _url,
            data: datas,
            method: 'POST'
          });
        }
      } catch (e) {
        _this._log(e);
      } finally {
        resolve();
      }
    });
  }

  _serialize(obj) {
    var str = [];
    for (var p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  }

  _log(logString) {
    this.sessionObj.syTrackIsDebug &&
      logString &&
      console.log(`%cLOG_TRACK:${logString}`, 'color:green;font-size:16px;');
  }
}
