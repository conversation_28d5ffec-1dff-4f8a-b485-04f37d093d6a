export class SyExposure {
  constructor(selector, callback, vm, options) {
    this.callback = callback;
    this.selector = selector;
    this.lockTimer = null;
    if (options) {
      this.bottom = options.bottom || 0;
      this.top = options.top || 0;
    }
    this.init(vm); // 为什么把vm传来传去而不用this.vm：因为会出现循环引用导致报错
    this.getRootPageComponent(vm)?.exposureObservers.push(this); // push 到当前页面组件的 pageAndExposureObservers 数组中
  }

  /**
   * 初始化
   * @param vm 组件的 $vm 实例 (vue 实例)
   */
  init(vm) {
    // 创建 IntersectionObserver 的实例
    this.ob = vm
      ? vm.createIntersectionObserver({ observeAll: true })
      : uni.createIntersectionObserver(null, { observeAll: true });
    // 开始监听，当露出时触发回调
    // 开始监听，当露出时触发回调
    this.ob
      .relativeToViewport({
        bottom: this.bottom,
        top: this.top
      })
      .observe(this.selector, (result) => {
        if (result.intersectionRatio > 0) {
          this.callback(result);
        }
      });
    // 切断循环引用，防止报错（此语句必须放在.observe之后，否则会找不到组件内的元素）
    this.ob._component = null;
  }

  /**
   * 销毁监听 （onShow 时还会被重新激活）
   */
  disconnect() {
    this.ob.disconnect();
  }

  /**
   * 销毁监听并从页面组件的 exposureObservers 中移除当前实例（onShow时不会再激活）
   * @param vm
   */
  remove(vm) {
    this.ob.disconnect();
    const obList = this.getRootPageComponent(vm).exposureObservers;
    const index = obList.indexOf(this);
    if (index !== -1) {
      obList.splice(index, 1);
    }
  }

  /**
   * 重新监听所有曝光埋点
   * @param vm
   */
  reInit(vm) {
    this.init(vm);
  }

  /**
   * 获取组件所属的最上层组件(页面组件)
   * @param vm
   * @returns {*}
   */
  getRootPageComponent(vm) {
    if (vm) {
      if (vm.mpType && vm.mpType === 'component') {
        return this.getRootPageComponent(vm.$parent);
      } else if (vm.mpType === 'page' || vm.$parent === undefined) {
        return vm;
      } else {
        return vm;
      }
    } else {
      return vm;
    }
  }
}
