const VERSION = '2.0.0'; const SUBVERSION = '2.0.12'; const SMID = 'smidV2'; const defaultAuthConf = { location: !1 }; let smConf = { appId: 'default', channel: '', organization: '', apiHost: 'fp-it.fengkongcloud.com', apiPath: '/v3/profile/weapp', authConf: JSON.parse(JSON.stringify(defaultAuthConf)) }; const randomDeviceId = getDefaultDeviceId(); const sdkDefaultConf = { key: getOriginWords('%5B%A0%C0%DB%DD%E2%D2%CE'), deviceId: randomDeviceId, timestamp: getOriginWords('%3Eelollnmmnhck') }; const smResult = { sign: '', timestamp: '', deviceId: '', length: 0 }; const jsLoaded = +new Date(); const sessionId = getSessionId(); function getSessionId() { const a = +new Date(); const b = Math.floor(1E8 * Math.random()); return a + '-' + b }function safeAdd(a, b) { const c = (65535 & a) + (65535 & b); return(a >> 16) + (b >> 16) + (c >> 16) << 16 | 65535 & c }function bitRotateLeft(a, b) { return a << b | a >>> 32 - b }function md5cmn(c, d, a, b, e, f) { return safeAdd(bitRotateLeft(safeAdd(safeAdd(d, c), safeAdd(b, f)), e), a) }function md5ff(e, a, b, c, d, f, g) { return md5cmn(a & b | ~a & c, e, a, d, f, g) }function md5gg(e, a, b, c, d, f, g) { return md5cmn(a & c | b & ~c, e, a, d, f, g) }function md5hh(e, a, b, c, d, f, g) { return md5cmn(a ^ b ^ c, e, a, d, f, g) }function md5ii(e, a, b, c, d, f, g) { return md5cmn(b ^ (a | ~c), e, a, d, f, g) }function binlMD5(e, f) { e[f >> 5] |= 128 << f % 32, e[(f + 64 >>> 9 << 4) + 14] = f; let g; let h; let j; let k; let l; let m = 1732584193; let n = -271733879; let o = -1732584194; let p = 271733878; for(g = 0; g < e.length; g += 16)h = m, j = n, k = o, l = p, m = md5ff(m, n, o, p, e[g], 7, -680876936), p = md5ff(p, m, n, o, e[g + 1], 12, -389564586), o = md5ff(o, p, m, n, e[g + 2], 17, 606105819), n = md5ff(n, o, p, m, e[g + 3], 22, -1044525330), m = md5ff(m, n, o, p, e[g + 4], 7, -176418897), p = md5ff(p, m, n, o, e[g + 5], 12, 1200080426), o = md5ff(o, p, m, n, e[g + 6], 17, -1473231341), n = md5ff(n, o, p, m, e[g + 7], 22, -45705983), m = md5ff(m, n, o, p, e[g + 8], 7, 1770035416), p = md5ff(p, m, n, o, e[g + 9], 12, -1958414417), o = md5ff(o, p, m, n, e[g + 10], 17, -42063), n = md5ff(n, o, p, m, e[g + 11], 22, -1990404162), m = md5ff(m, n, o, p, e[g + 12], 7, 1804603682), p = md5ff(p, m, n, o, e[g + 13], 12, -40341101), o = md5ff(o, p, m, n, e[g + 14], 17, -1502002290), n = md5ff(n, o, p, m, e[g + 15], 22, 1236535329), m = md5gg(m, n, o, p, e[g + 1], 5, -165796510), p = md5gg(p, m, n, o, e[g + 6], 9, -1069501632), o = md5gg(o, p, m, n, e[g + 11], 14, 643717713), n = md5gg(n, o, p, m, e[g], 20, -373897302), m = md5gg(m, n, o, p, e[g + 5], 5, -701558691), p = md5gg(p, m, n, o, e[g + 10], 9, 38016083), o = md5gg(o, p, m, n, e[g + 15], 14, -660478335), n = md5gg(n, o, p, m, e[g + 4], 20, -405537848), m = md5gg(m, n, o, p, e[g + 9], 5, 568446438), p = md5gg(p, m, n, o, e[g + 14], 9, -1019803690), o = md5gg(o, p, m, n, e[g + 3], 14, -187363961), n = md5gg(n, o, p, m, e[g + 8], 20, 1163531501), m = md5gg(m, n, o, p, e[g + 13], 5, -1444681467), p = md5gg(p, m, n, o, e[g + 2], 9, -51403784), o = md5gg(o, p, m, n, e[g + 7], 14, 1735328473), n = md5gg(n, o, p, m, e[g + 12], 20, -1926607734), m = md5hh(m, n, o, p, e[g + 5], 4, -378558), p = md5hh(p, m, n, o, e[g + 8], 11, -2022574463), o = md5hh(o, p, m, n, e[g + 11], 16, 1839030562), n = md5hh(n, o, p, m, e[g + 14], 23, -35309556), m = md5hh(m, n, o, p, e[g + 1], 4, -1530992060), p = md5hh(p, m, n, o, e[g + 4], 11, 1272893353), o = md5hh(o, p, m, n, e[g + 7], 16, -155497632), n = md5hh(n, o, p, m, e[g + 10], 23, -1094730640), m = md5hh(m, n, o, p, e[g + 13], 4, 681279174), p = md5hh(p, m, n, o, e[g], 11, -358537222), o = md5hh(o, p, m, n, e[g + 3], 16, -722521979), n = md5hh(n, o, p, m, e[g + 6], 23, 76029189), m = md5hh(m, n, o, p, e[g + 9], 4, -640364487), p = md5hh(p, m, n, o, e[g + 12], 11, -421815835), o = md5hh(o, p, m, n, e[g + 15], 16, 530742520), n = md5hh(n, o, p, m, e[g + 2], 23, -995338651), m = md5ii(m, n, o, p, e[g], 6, -198630844), p = md5ii(p, m, n, o, e[g + 7], 10, 1126891415), o = md5ii(o, p, m, n, e[g + 14], 15, -1416354905), n = md5ii(n, o, p, m, e[g + 5], 21, -57434055), m = md5ii(m, n, o, p, e[g + 12], 6, 1700485571), p = md5ii(p, m, n, o, e[g + 3], 10, -1894986606), o = md5ii(o, p, m, n, e[g + 10], 15, -1051523), n = md5ii(n, o, p, m, e[g + 1], 21, -2054922799), m = md5ii(m, n, o, p, e[g + 8], 6, 1873313359), p = md5ii(p, m, n, o, e[g + 15], 10, -30611744), o = md5ii(o, p, m, n, e[g + 6], 15, -1560198380), n = md5ii(n, o, p, m, e[g + 13], 21, 1309151649), m = md5ii(m, n, o, p, e[g + 4], 6, -145523070), p = md5ii(p, m, n, o, e[g + 11], 10, -1120210379), o = md5ii(o, p, m, n, e[g + 2], 15, 718787259), n = md5ii(n, o, p, m, e[g + 9], 21, -343485551), m = safeAdd(m, h), n = safeAdd(n, j), o = safeAdd(o, k), p = safeAdd(p, l); return[m, n, o, p] }function binl2rstr(a) { let b; let c = ''; const d = 32 * a.length; for(b = 0; b < d; b += 8)c += String.fromCharCode(255 & a[b >> 5] >>> b % 32); return c }function rstr2binl(a) { let b; const c = []; for(c[(a.length >> 2) - 1] = void 0, b = 0; b < c.length; b += 1)c[b] = 0; const d = 8 * a.length; for(b = 0; b < d; b += 8)c[b >> 5] |= (255 & a.charCodeAt(b / 8)) << b % 32; return c }function rstrMD5(a) { return binl2rstr(binlMD5(rstr2binl(a), 8 * a.length)) }function rstr2hex(a) { let b; let c; const d = '0123456789abcdef'; let e = ''; for(c = 0; c < a.length; c += 1)b = a.charCodeAt(c), e += d.charAt(15 & b >>> 4) + d.charAt(15 & b); return e }function str2rstrUTF8(a) { return unescape(encodeURIComponent(a)) }function rawMD5(a) { return rstrMD5(str2rstrUTF8(a)) }function hexMD5(a) { return rstr2hex(rawMD5(a)) }function md5(a) { return hexMD5(a) }function getOriginWords(a) { a = unescape(a); const b = String.fromCharCode; let c = b(a.charCodeAt(0) - a.length); for(let d = 1; d < a.length; d++)c += b(a.charCodeAt(d) - c.charCodeAt(d - 1)); return c }function getDateTime() { const a = new Date(); const b = a.getFullYear().toString(); let c = (a.getMonth() + 1).toString(); let d = a.getDate().toString(); let e = a.getHours().toString(); let f = a.getMinutes().toString(); let g = a.getSeconds().toString(); return c = c <= 9 ? '0' + c : c, d = d <= 9 ? '0' + d : d, e = e <= 9 ? '0' + e : e, f = f <= 9 ? '0' + f : f, g = g <= 9 ? '0' + g : g, b + c + d + e + f + g }function getUid() { return'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(a) { const b = 0 | 16 * Math.random(); const c = a == 'x' ? b : 8 | 3 & b; return c.toString(16) }) }function getDefaultDeviceId() { const a = getDateTime(); const b = getUid(); const c = a + md5(b) + '00'; const d = md5('smsk_weapp_' + c).substr(0, 14); return c + d + 0 }function getSystemInfoSync() { try{ return wx.getSystemInfoSync() }catch(a) { return{} } }function getAllInfo() { const a = getSystemInfoSync(); const b = +new Date(); const c = getSmid(); const d = a.version; const e = getLaunchOptions(); return delete a.version, Object.assign({ appId: smConf.appId, channel: smConf.channel, deviceId: c, time: b - jsLoaded, sessionId, version: VERSION, subVersion: SUBVERSION, weAppVerion: d, res: `${a.screenWidth}_${a.screenHeight}`, launchOptions: e }, a) }function formatJsonpParams(a) { const b = []; for(const c in a)if(a.hasOwnProperty(c)) { let d = a[c]; typeof a[c] === 'object' && (d = JSON.stringify(d)), b.push(c + '=' + encodeURIComponent(d)) }return b.join('&') }function initConf(a) { smConf = Object.assign({}, smConf, a), smConf.authConf = Object.assign({}, defaultAuthConf, a.authConf || {}) }function getDeviceId() { const a = getMainInfo(); const b = formatJsonpParams(a); const c = getSmResult(); const d = c.sign; return encrypt(d, b) }function getMainInfo() { const a = getSmid(); const b = +new Date(); return{ deviceId: a, version: VERSION } }function getSmid() { let a; try{ const b = `${wx.env.USER_DATA_PATH}/SMFP.txt`; const c = wx.getFileSystemManager(); a = c.readFileSync(b, 'utf8') }catch(a) {}try{ a = wx.getStorageSync(SMID, null) }catch(a) {}return a || (a = randomDeviceId), setSmid(a), a }function setSmid(a) { try{ const b = `${wx.env.USER_DATA_PATH}/SMFP.txt`; const c = wx.getFileSystemManager(); wx.setStorageSync(SMID, a), c.writeFileSync(b, a, 'utf8') }catch(a) {} }function getLaunchOptions() { let a = {}; try{ a = wx.getLaunchOptionsSync() }catch(a) {}return a }function getConnectedWifi() { return new Promise(function(a) { try{ try{ wx.startWifi({ success: function() { wx.getConnectedWifi({ complete: function(b) { try{ b.wifi ? a(b.wifi) : a({}) }catch(b) { a({}) } } }) }, fail: function() { a([]) } }) }catch(b) { a([]) } }catch(b) { a({}) } }) }function getLocation() { return new Promise(function(a) { try{ wx.getLocation({ altitude: !0, success: function(b) { try{ delete b.errMsg, a(b) }catch(b) { a({}) } }, fail: function() { a({}) } }) }catch(b) { a({}) } }) }function getAccelerometer() { return new Promise(function(a) { const b = []; try{ wx.onAccelerometerChange(function(c) { try{ b.push(c), setTimeout(function() { try{}catch(a) {} }, 6e3), a(b) }catch(c) { a(b) } }), setTimeout(function() { a(b) }, 1e3) }catch(c) { a(b) } }) }function getCompass() { return new Promise(function(a) { const b = []; try{ wx.onCompassChange(function(c) { try{ b.push(c.direction), setTimeout(function() { try{}catch(a) {} }, 6e3), a(b) }catch(c) { a(b) } }), setTimeout(function() { a(b) }, 1e3) }catch(c) { a(b) } }) }function getWifiList() { return new Promise(function(a) { const b = getAllInfo(); try{ wx.startWifi({ success: function() { if(b.platform == 'android')try{ wx.getWifiList({ success: function() { try{ wx.onGetWifiList(function(b) { try{ a(b.wifiList) }catch(b) { a([]) } }) }catch(a) {}setTimeout(function() { a([]) }, 1e3) }, fail: function() { a([]) } }) }catch(b) { a([]) }else a([]) }, fail: function() { a([]) } }) }catch(b) { a([]) } }) }function getScreenBrightness() { return new Promise(function(a) { try{ wx.getScreenBrightness({ success: function(b) { try{ a(b.value) }catch(b) { a(-1) } }, fail: function() { a(-1) } }) }catch(b) { a(-1) } }) }function getNetworkType() { return new Promise(function(a) { try{ wx.getNetworkType({ success: function(b) { try{ a(b.networkType) }catch(b) { a('') } }, fail: function() { a('') } }) }catch(b) { a('') } }) }function setSmResult(a, b, c, d) { smResult.deviceId = a, smResult.sign = b, smResult.timestamp = c, smResult.length = 1 * d }function getSmResult() { return{ deviceId: smResult.deviceId, sign: smResult.sign, timestamp: smResult.timestamp, length: smResult.length } }function getDefaultResult() { const a = getSmid(); const b = smConf.timestamp || sdkDefaultConf.timestamp; return{ deviceId: a, sign: null, timestamp: b } }function loadScript(a, b, c) { try{ wx.request({ url: a, data: b, method: 'POST', header: { 'content-type': 'application/json' }, success: function(a) { const b = { code: 1902, detail: {}, deviceId: '', message: '\u6570\u636E\u89E3\u6790\u5F02\u5E38', requestId: '', riskLevel: '' }; try{ const d = a.data; const e = d.match(/smCB\((.*)\)/); if(e) { const a = e[1]; try{ c(JSON.parse(a)) }catch(a) { c(b) } }else c(b) }catch(a) { c(b) } } }) }catch(a) {} }function getEncryptResult(a, b) { try{ const c = getSmResult(); const d = c.timestamp; const e = a(b); return base64Encode(e) + d }catch(a) { const c = getDefaultResult(); const d = c.timestamp; const e = smEncrypt(DES, b); return base64Encode(e) + d } }function smEncrypt(a, b, c, d, e, g) { return a = a || DES, c = c || smConf.key || sdkDefaultConf.key, d = d == 0 ? 0 : 1, e = e == 2 ? 1 : 0, c += '', e == 0 ? a(c, b, d) : a(c, b, d, e, g) }function createDesKeys(a) { let b; let c; let d; const e = [0, 4, 536870912, 536870916, 65536, 65540, 536936448, 536936452, 512, 516, 536871424, 536871428, 66048, 66052, 536936960, 536936964]; const f = [0, 1, 1048576, 1048577, 67108864, 67108865, 68157440, 68157441, 256, 257, 1048832, 1048833, 67109120, 67109121, 68157696, 68157697]; const g = [0, 8, 2048, 2056, 16777216, 16777224, 16779264, 16779272, 0, 8, 2048, 2056, 16777216, 16777224, 16779264, 16779272]; const h = [0, 2097152, 134217728, 136314880, 8192, 2105344, 134225920, 136323072, 131072, 2228224, 134348800, 136445952, 139264, 2236416, 134356992, 136454144]; const j = [0, 262144, 16, 262160, 0, 262144, 16, 262160, 4096, 266240, 4112, 266256, 4096, 266240, 4112, 266256]; const k = [0, 1024, 32, 1056, 0, 1024, 32, 1056, 33554432, 33555456, 33554464, 33555488, 33554432, 33555456, 33554464, 33555488]; const l = [0, 268435456, 524288, 268959744, 2, 268435458, 524290, 268959746, 0, 268435456, 524288, 268959744, 2, 268435458, 524290, 268959746]; const o = [0, 65536, 2048, 67584, 536870912, 536936448, 536872960, 536938496, 131072, 196608, 133120, 198656, 537001984, 537067520, 537004032, 537069568]; const p = [0, 262144, 0, 262144, 2, 262146, 2, 262146, 33554432, 33816576, 33554432, 33816576, 33554434, 33816578, 33554434, 33816578]; const q = [0, 268435456, 8, 268435464, 0, 268435456, 8, 268435464, 1024, 268436480, 1032, 268436488, 1024, 268436480, 1032, 268436488]; const r = [0, 32, 0, 32, 1048576, 1048608, 1048576, 1048608, 8192, 8224, 8192, 8224, 1056768, 1056800, 1056768, 1056800]; const s = [0, 16777216, 512, 16777728, 2097152, 18874368, 2097664, 18874880, 67108864, 83886080, 67109376, 83886592, 69206016, 85983232, 69206528, 85983744]; const t = [0, 4096, 134217728, 134221824, 524288, 528384, 134742016, 134746112, 16, 4112, 134217744, 134221840, 524304, 528400, 134742032, 134746128]; const u = [0, 4, 256, 260, 0, 4, 256, 260, 1, 5, 257, 261, 1, 5, 257, 261]; const i = a.length > 8 ? 3 : 1; const v = Array(32 * i); const w = [0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0]; let x = 0; let y = 0; for(let m = 0; m < i; m++) { let m = a.charCodeAt(x++) << 24 | a.charCodeAt(x++) << 16 | a.charCodeAt(x++) << 8 | a.charCodeAt(x++); let n = a.charCodeAt(x++) << 24 | a.charCodeAt(x++) << 16 | a.charCodeAt(x++) << 8 | a.charCodeAt(x++); d = 252645135 & (m >>> 4 ^ n), n ^= d, m ^= d << 4, d = 65535 & (n >>> -16 ^ m), m ^= d, n ^= d << -16, d = 858993459 & (m >>> 2 ^ n), n ^= d, m ^= d << 2, d = 65535 & (n >>> -16 ^ m), m ^= d, n ^= d << -16, d = 1431655765 & (m >>> 1 ^ n), n ^= d, m ^= d << 1, d = 16711935 & (n >>> 8 ^ m), m ^= d, n ^= d << 8, d = 1431655765 & (m >>> 1 ^ n), n ^= d, m ^= d << 1, d = m << 8 | 240 & n >>> 20, m = n << 24 | 16711680 & n << 8 | 65280 & n >>> 8 | 240 & n >>> 24, n = d; for(let a = 0; a < w.length; a++)w[a] ? (m = m << 2 | m >>> 26, n = n << 2 | n >>> 26) : (m = m << 1 | m >>> 27, n = n << 1 | n >>> 27), m &= -15, n &= -15, b = e[m >>> 28] | f[15 & m >>> 24] | g[15 & m >>> 20] | h[15 & m >>> 16] | j[15 & m >>> 12] | k[15 & m >>> 8] | l[15 & m >>> 4], c = o[n >>> 28] | p[15 & n >>> 24] | q[15 & n >>> 20] | r[15 & n >>> 16] | s[15 & n >>> 12] | t[15 & n >>> 8] | u[15 & n >>> 4], d = 65535 & (c >>> 16 ^ b), v[y++] = b ^ d, v[y++] = c ^ d << 16 }return v }function DES(a, b, c, d, e, f) { var g = String.fromCharCode; let h; let k; let l; let n; let o; let p; let q; let r; let s; let t; let u; let v; let w; let x; const y = [16843776, 0, 65536, 16843780, 16842756, 66564, 4, 65536, 1024, 16843776, 16843780, 1024, 16778244, 16842756, 16777216, 4, 1028, 16778240, 16778240, 66560, 66560, 16842752, 16842752, 16778244, 65540, 16777220, 16777220, 65540, 0, 1028, 66564, 16777216, 65536, 16843780, 4, 16842752, 16843776, 16777216, 16777216, 1024, 16842756, 65536, 66560, 16777220, 1024, 4, 16778244, 66564, 16843780, 65540, 16842752, 16778244, 16777220, 1028, 66564, 16843776, 1028, 16778240, 16778240, 0, 65540, 66560, 0, 16842756]; const z = [-2146402272, -2147450880, 32768, 1081376, 1048576, 32, -2146435040, -2147450848, -2147483616, -2146402272, -2146402304, -2147483648, -2147450880, 1048576, 32, -2146435040, 1081344, 1048608, -2147450848, 0, -2147483648, 32768, 1081376, -2146435072, 1048608, -2147483616, 0, 1081344, 32800, -2146402304, -2146435072, 32800, 0, 1081376, -2146435040, 1048576, -2147450848, -2146435072, -2146402304, 32768, -2146435072, -2147450880, 32, -2146402272, 1081376, 32, 32768, -2147483648, 32800, -2146402304, 1048576, -2147483616, 1048608, -2147450848, -2147483616, 1048608, 1081344, 0, -2147450880, 32800, -2147483648, -2146435040, -2146402272, 1081344]; const A = [520, 134349312, 0, 134348808, 134218240, 0, 131592, 134218240, 131080, 134217736, 134217736, 131072, 134349320, 131080, 134348800, 520, 134217728, 8, 134349312, 512, 131584, 134348800, 134348808, 131592, 134218248, 131584, 131072, 134218248, 8, 134349320, 512, 134217728, 134349312, 134217728, 131080, 520, 131072, 134349312, 134218240, 0, 512, 131080, 134349320, 134218240, 134217736, 512, 0, 134348808, 134218248, 131072, 134217728, 134349320, 8, 131592, 131584, 134217736, 134348800, 134218248, 520, 134348800, 131592, 8, 134348808, 131584]; const B = [8396801, 8321, 8321, 128, 8396928, 8388737, 8388609, 8193, 0, 8396800, 8396800, 8396929, 129, 0, 8388736, 8388609, 1, 8192, 8388608, 8396801, 128, 8388608, 8193, 8320, 8388737, 1, 8320, 8388736, 8192, 8396928, 8396929, 129, 8388736, 8388609, 8396800, 8396929, 129, 0, 0, 8396800, 8320, 8388736, 8388737, 1, 8396801, 8321, 8321, 128, 8396929, 129, 1, 8192, 8388609, 8193, 8396928, 8388737, 8193, 8320, 8388608, 8396801, 128, 8388608, 8192, 8396928]; const C = [256, 34078976, 34078720, 1107296512, 524288, 256, 1073741824, 34078720, 1074266368, 524288, 33554688, 1074266368, 1107296512, 1107820544, 524544, 1073741824, 33554432, 1074266112, 1074266112, 0, 1073742080, 1107820800, 1107820800, 33554688, 1107820544, 1073742080, 0, 1107296256, 34078976, 33554432, 1107296256, 524544, 524288, 1107296512, 256, 33554432, 1073741824, 34078720, 1107296512, 1074266368, 33554688, 1073741824, 1107820544, 34078976, 1074266368, 256, 33554432, 1107820544, 1107820800, 524544, 1107296256, 1107820800, 34078720, 0, 1074266112, 1107296256, 524544, 33554688, 1073742080, 524288, 0, 1074266112, 34078976, 1073742080]; const D = [536870928, 541065216, 16384, 541081616, 541065216, 16, 541081616, 4194304, 536887296, 4210704, 4194304, 536870928, 4194320, 536887296, 536870912, 16400, 0, 4194320, 536887312, 16384, 4210688, 536887312, 16, 541065232, 541065232, 0, 4210704, 541081600, 16400, 4210688, 541081600, 536870912, 536887296, 16, 541065232, 4210688, 541081616, 4194304, 16400, 536870928, 4194304, 536887296, 536870912, 16400, 536870928, 541081616, 4210688, 541065216, 4210704, 541081600, 0, 541065232, 16, 16384, 541065216, 4210704, 16384, 4194320, 536887312, 0, 541081600, 536870912, 4194320, 536887312]; const E = [2097152, 69206018, 67110914, 0, 2048, 67110914, 2099202, 69208064, 69208066, 2097152, 0, 67108866, 2, 67108864, 69206018, 2050, 67110912, 2099202, 2097154, 67110912, 67108866, 69206016, 69208064, 2097154, 69206016, 2048, 2050, 69208066, 2099200, 2, 67108864, 2099200, 67108864, 2099200, 2097152, 67110914, 67110914, 69206018, 69206018, 2, 2097154, 67108864, 67110912, 2097152, 69208064, 2050, 2099202, 69208064, 2050, 67108866, 69208066, 69206016, 2099200, 0, 2, 69208066, 0, 2099202, 69206016, 2048, 67108866, 67110912, 2048, 2097154]; const F = [268439616, 4096, 262144, 268701760, 268435456, 268439616, 64, 268435456, 262208, 268697600, 268701760, 266240, 268701696, 266304, 4096, 64, 268697600, 268435520, 268439552, 4160, 266240, 262208, 268697664, 268701696, 4160, 0, 0, 268697664, 268435520, 268439552, 266304, 262144, 266304, 262144, 268701696, 4096, 64, 268697664, 4096, 266304, 268439552, 64, 268435520, 268697600, 268697664, 268435456, 262144, 268439616, 0, 268701760, 262208, 268435520, 268697600, 268439552, 268439616, 0, 268701760, 266240, 266240, 4160, 4160, 262208, 268435456, 268701696]; const G = createDesKeys(a); let H = 0; let I = b.length; let J = 0; const K = G.length == 32 ? 3 : 9; r = K == 3 ? c ? [0, 32, 2] : [30, -2, -2] : c ? [0, 32, 2, 62, 30, -2, 64, 96, 2] : [94, 62, -2, 32, 64, 2, 30, -2, -2], f == 2 ? b += '        ' : f == 1 ? (l = 8 - I % 8, b += g(l, l, l, l, l, l, l, l), l == 8 && (I += 8)) : !f && (b += '\0\0\0\0\0\0\0\0'); let L = ''; let M = ''; for(d == 1 && (s = e.charCodeAt(H++) << 24 | e.charCodeAt(H++) << 16 | e.charCodeAt(H++) << 8 | e.charCodeAt(H++), u = e.charCodeAt(H++) << 24 | e.charCodeAt(H++) << 16 | e.charCodeAt(H++) << 8 | e.charCodeAt(H++), H = 0); H < I;) { for(p = b.charCodeAt(H++) << 24 | b.charCodeAt(H++) << 16 | b.charCodeAt(H++) << 8 | b.charCodeAt(H++), q = b.charCodeAt(H++) << 24 | b.charCodeAt(H++) << 16 | b.charCodeAt(H++) << 8 | b.charCodeAt(H++), d == 1 && (c ? (p ^= s, q ^= u) : (t = s, v = u, s = p, u = q)), l = 252645135 & (p >>> 4 ^ q), q ^= l, p ^= l << 4, l = 65535 & (p >>> 16 ^ q), q ^= l, p ^= l << 16, l = 858993459 & (q >>> 2 ^ p), p ^= l, q ^= l << 2, l = 16711935 & (q >>> 8 ^ p), p ^= l, q ^= l << 8, l = 1431655765 & (p >>> 1 ^ q), q ^= l, p ^= l << 1, p = p << 1 | p >>> 31, q = q << 1 | q >>> 31, k = 0; k < K; k += 3) { for(w = r[k + 1], x = r[k + 2], h = r[k]; h != w; h += x)n = q ^ G[h], o = (q >>> 4 | q << 28) ^ G[h + 1], l = p, p = q, q = l ^ (z[63 & n >>> 24] | B[63 & n >>> 16] | D[63 & n >>> 8] | F[63 & n] | y[63 & o >>> 24] | A[63 & o >>> 16] | C[63 & o >>> 8] | E[63 & o]); l = p, p = q, q = l }p = p >>> 1 | p << 31, q = q >>> 1 | q << 31, l = 1431655765 & (p >>> 1 ^ q), q ^= l, p ^= l << 1, l = 16711935 & (q >>> 8 ^ p), p ^= l, q ^= l << 8, l = 858993459 & (q >>> 2 ^ p), p ^= l, q ^= l << 2, l = 65535 & (p >>> 16 ^ q), q ^= l, p ^= l << 16, l = 252645135 & (p >>> 4 ^ q), q ^= l, p ^= l << 4, d == 1 && (c ? (s = p, u = q) : (p ^= t, q ^= v)), M += g(p >>> 24, 255 & p >>> 16, 255 & p >>> 8, 255 & p, q >>> 24, 255 & q >>> 16, 255 & q >>> 8, 255 & q), J += 8, J == 512 && (L += M, M = '', J = 0) }return L + M }function base64Encode(a) { let b; let c; let d; let e; let f; let g; const h = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'; for(d = a.length, c = 0, b = ''; c < d;) { if(e = 255 & a.charCodeAt(c++), c == d) { b += h.charAt(e >> 2), b += h.charAt((3 & e) << 4), b += '=='; break }if(f = a.charCodeAt(c++), c == d) { b += h.charAt(e >> 2), b += h.charAt((3 & e) << 4 | (240 & f) >> 4), b += h.charAt((15 & f) << 2), b += '='; break }g = a.charCodeAt(c++), b += h.charAt(e >> 2), b += h.charAt((3 & e) << 4 | (240 & f) >> 4), b += h.charAt((15 & f) << 2 | (192 & g) >> 6), b += h.charAt(63 & g) }return b }function base64Decode(a) { var b = String.fromCharCode; let c; let d; let e; let f; let g; let h; let j; const k = [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1]; for(h = a.length, g = 0, j = ''; g < h;) { do c = k[255 & a.charCodeAt(g++)]; while(g < h && c == -1); if(c == -1)break; do d = k[255 & a.charCodeAt(g++)]; while(g < h && d == -1); if(d == -1)break; j += b(c << 2 | (48 & d) >> 4); do{ if(e = 255 & a.charCodeAt(g++), e == 61)return j; e = k[e] }while(g < h && e == -1); if(e == -1)break; j += b((15 & d) << 4 | (60 & e) >> 2); do{ if(f = 255 & a.charCodeAt(g++), f == 61)return j; f = k[f] }while(g < h && f == -1); if(f == -1)break; j += b((3 & e) << 6 | f) }return j }function encrypt(a, b) { const c = 'W'; const d = getSmResult(); const e = d.length; try{ const d = smEncrypt(DES, base64Decode(a), '', 0); const f = d.substr(0, e).split(','); const g = formatEncryptFunc(f[0]); let h = null; return typeof g === 'function' && (h = function(a) { return smEncrypt(g, a, f[2], f[1], f[3]) }), c + getEncryptResult(h, b) }catch(a) { return c + getEncryptResult(DES, b) } }function formatEncryptFunc(a) { const b = [null, DES]; return b[a] ? b[a] : null }function sendFpData(a) { return new Promise(function(b) { const c = formatJsonpParams(a); const d = 'https://' + smConf.apiHost + smConf.apiPath; const e = getDefaultResult(); setSmResult(e.deviceId, e.sign, e.timestamp, 0), loadScript(d, { organization: smConf.organization, smdata: encrypt(e.sign, c), os: 'weapp', version: VERSION }, function(a) { try{ if(a.code == 1100) { const b = a.deviceId || getSmid() || smConf.deviceId || sdkDefaultConf.deviceId; const c = a.detail ? a.detail.timestamp : ''; const d = a.detail ? a.detail.sign : ''; const e = a.detail ? a.detail.len : 0; b && d && c && e && (setSmid(b), setSmResult(b, d, c, e)) } }catch(a) {}b() }) }) }function signalFilter(a, c) { let b = a; try{ return c && (b = a.sort(function(d, a) { return d[c] - a[c] < 0 })), b.slice(0, 10) }catch(a) { return b.slice(0, 10) } }function sendBehaviorData() { try{ let a; const b = smConf.authConf && !0 === smConf.authConf.location; const c = getAllInfo(); const d = [getAccelerometer(), getCompass(), getScreenBrightness(), getNetworkType(), getConnectedWifi()]; b && (d.push(getWifiList()), d.push(getLocation())), Promise.all(d).then(function(d) { try{ const e = { accelerometer: signalFilter(d[0]), compass: signalFilter(d[1]), screenBright: d[2], networkType: d[3], connectedWifi: d[4] }; b && (e.wifiList = signalFilter(d[5], 'signalStrength'), e.location = d[6]), a = Object.assign(c, e), sendFpData(a) }catch(a) { sendFpData(c) } }, function() { sendFpData(c) }) }catch(a) {} }setTimeout(function() { const a = getAllInfo(); sendFpData(a).then(function() { try{ setTimeout(sendBehaviorData, 2e3) }catch(a) {} }); try{ wx.startAccelerometer && wx.startAccelerometer(), wx.startCompass && wx.startCompass() }catch(a) {} }, 0), module.exports = { initConf, getDeviceId };
