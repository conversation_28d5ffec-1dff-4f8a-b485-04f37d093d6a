export function setDataToOtherPage(options) {
  uni.$storage.crossPageToast = JSON.stringify({
    options,
    time: new Date().getTime() + 1000
  });
}

export function getDataFromOtherPage() {
  const crossPageToast = JSON.parse(uni.$storage.crossPageToast || '{}');
  console.log(crossPageToast, 'crossPageToast', new Date().getTime());
  if (crossPageToast.time > new Date().getTime()) {
    console.log(crossPageToast.options, 'sfa');
    return uni.showToast(crossPageToast.options);
  }
}
