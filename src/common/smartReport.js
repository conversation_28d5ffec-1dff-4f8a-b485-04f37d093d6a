import Vue from 'vue';
import config from '@/config';
import SyTrack from './wechat-mp-track';
import { SyExposure } from '@/common/exposure';
import { uniLogin } from '@/plugins/login';
import { cloneDeep } from 'lodash-es';

let requestCount = 0;
function noop() {}
function waitFactory() {
  let complete = noop;
  const defered = new Promise((resolve) => {
    complete = resolve;
  });
  return {
    complete,
    defered
  };
}
const immediateReportList = [
  'wx_app_startup',
  'sy_wxtuan_other_webview_page',
  'sy_wxtuan_tuan_home_page:tab_click',
  'sy_wxtuan_tuan_wechat:service_exposure',
  'sy_chain_store_other_member_info:more_click',
  'sy_chain_store_other_home:member_popup_exposure',
  'sy_chain_store_other_home:general_popup_exposure'
];
const immediateReport4Test = process.env.NODE_ENV === 'development' && false;
/**
 * 修改 SmartReport 类 请联系 包龙星, 里面用到了大量的 for、while 循环 和 协程 来 暂停、恢复埋点上报功能,
 * 你的改动可能造成死循环,或者对恢复上报造成影响
 *
 * 以下参数的判断位置很重要,不要轻易修改
 * flushing
 * waiting
 * active
 * sy_chain_store_other_home:member_popup_exposure
 */
class SmartReport {
  constructor() {
    // 等待空闲阈值,当请求通道占用大于 waitIdleThreshold 时,暂停埋点上报
    this.waitIdleThreshold = 2;
    // 等待空闲阈值,当请求通道占用大于 waitIdleThreshold 时,暂停埋点上报,恢复时报的时间,一般我司接口timeout 就是 1500毫秒
    this.waitIdleTime = 1500;
    this.flushing = false;
    this.flushTimer = null;
    this.queueMap = new Map();
    this.waiting = false;
    this.active = true;
    this.queueList = [];
    this.reportId = 0;
    this.requestingMap = new Map();
    // https://developers.weixin.qq.com/miniprogram/dev/framework/runtime/operating-mechanism.html
    this.backgroundExecTime = 4500;
    wx.syTrack = this.syTrack = new SyTrack({
      mpVersion: config.version,
      syAppId: __PRIME_APPID__
    });
    this.bindGlobalEvent();
  }
  enableReportAnalyze(enable = true) {
    this.syTrack.sessionObj.is_rsync_bp = enable ? 1 : 0;
  }
  reportData(params, vm) {
    console.log('reportData', params);
    if (!params.type) params.type = 1;
    params = cloneDeep(params);
    if (immediateReport4Test) {
      console.log('immediateReport4Test', params, params.ext);
      this.syTrack.SendReqTrack(params);
      return;
    }
    if (immediateReportList.includes(params?.info)) {
      this.queueList.push([params]);
    } else {
      this.queueReport(params, this.getPageId(vm));
    }
    this.flush();
  }
  getPageId(vm) {
    // console.log(vm.$mp, (vm.$mp.page || vm.$mp.component)?.getPageId());
    return (vm.$mp.page || vm.$mp.component)?.getPageId();
  }
  /**
   * 注册全局事件
   */
  bindGlobalEvent() {
    let backgroundTimer = null;
    uni.onAppShow(() => {
      clearTimeout(backgroundTimer);
      this.active = true;
      Promise.all([this.getQueueFromStorage(), uniLogin()]).then(([data]) => {
        this.queueList = data;
        this.flush();
      });
    });
    uni.onAppHide(() => {
      // 为了等页面的onhide埋点加入到队列
      Promise.resolve().then(() => {
        // console.log(
        //   this.queueList
        //     .map((queue) => {
        //       return `\n==========上报队列===========\n${queue
        //         .map(({ type, info }) => `${type}:${info}`)
        //         .join('\n')}`;
        //     })
        //     .join('\n')
        // );
        this.setQueueIntoStorage(this.queueList);
        // const time = Date.now();
        backgroundTimer = setTimeout(() => {
          // console.log('后台运行了' + (Date.now() - time));
          this.active = false;
          this.waiting = false;
          this.flushing = false;
          clearTimeout(this.flushTimer);
          this.setQueueIntoStorage(this.queueList);
        }, this.backgroundExecTime);
      });
    });
    uni.$on('__prime_report_complete__', (id) => {
      // console.log('__prime_report_complete__', id, this.requestingMap.get(id));
      if (id === undefined) return;
      const complete = this.requestingMap.get(id);
      complete?.();
    });
  }
  getCurrentPage() {
    const pages = getCurrentPages();
    return pages[pages.length - 1];
  }
  /**
   * 给页面的一个生命周期,排队:
   * onshow -> doing event -> onhide -> onshow -> doing event -> onhide - unload
   * @param {*} type 类型: 1->事件;2->PageHide;3->PageShow;4->PageUnload
   * @param {*} params
   */
  queueReport(params, pageKey) {
    const type = Number(params.type);
    let path = this.queueMap.get(pageKey);
    if (!path) {
      path = {
        queue: [],
        startPath: false,
        closePath: false
      };
    }
    switch (type) {
      case 3: // 页面出现
        path.startPath = true;
        path.queue.unshift(params);
        break;
      case 1: // 事件埋点
        path.queue.push(params);
        break;
      case 2: // 页面隐藏
      case 4: // 页面销毁
        path.closePath = true;
        path.queue.push(params);
        break;
    }
    this.queueMap.set(pageKey, path);
    if (path.startPath && path.closePath) {
      this.queueList.push(path.queue);
      path = null;
      this.queueMap.delete(pageKey);
    }
  }
  /**
   * 冲刷队列
   */
  async flush() {
    if (this.flushing || this.waiting) return;
    this.flushing = true;
    let retry = 10;
    for (;;) {
      if (!this.active) break;
      if (!this.queueList.length) break;
      if (retry <= 0) break;
      // 请求并发大约需要的通道数,说明在紧锣密鼓的请求业务接口
      if (getRequestCount() >= this.waitIdleThreshold) {
        this.waiting = true;
        this.flushTimer = setTimeout(() => {
          this.waiting = false;
          this.flush();
        }, this.waitIdleTime);
        break;
      }
      const [queue] = this.queueList;
      if (!queue || queue.length === 0) break;
      let clean = true;
      try {
        clean = await this.flushCapsule(queue);
      } catch (error) {
        retry--;
        clean = false;
      } finally {
        if (clean) {
          this.queueList.splice(0, 1);
          this.setQueueIntoStorage(this.queueList);
        }
      }
    }
    this.flushing = false;
  }
  async flushCapsule(queue) {
    for (;;) {
      if (queue.length === 0) break;
      if (!this.active) break;
      if (getRequestCount() >= this.waitIdleThreshold) break;
      const target = queue.shift();
      try {
        await this.syTrackReport(target);
      } catch (_) {
        queue.unshift(target);
      }
    }
    return queue.length === 0;
  }
  syTrackReport(params) {
    const __prime_report_id__ = this.reportId++;
    if (params.ext) {
      params.ext.__prime_report_id__ = __prime_report_id__;
    } else {
      params.ext = {
        __prime_report_id__: __prime_report_id__
      };
    }
    const { complete, defered } = waitFactory();
    this.requestingMap.set(__prime_report_id__, complete);
    this.syTrack.SendReqTrack(params);
    return defered;
  }
  getQueueFromStorage() {
    return new Promise((resole) => {
      uni.getStorage({
        key: '__APP_REPORT_QUEUE__',
        success: ({ data, errMsg }) => {
          if (errMsg !== 'getStorage:ok') {
            resole([]);
            return;
          }
          // getStorage 一个未知的key会返回空字符串 ''
          if (Array.isArray(data)) {
            resole(data);
          } else {
            resole([]);
          }
        },
        fail: () => resole([])
      });
    });
  }
  setQueueIntoStorage(queue) {
    uni.setStorageSync('__APP_REPORT_QUEUE__', queue);
  }
}

/**
 *
 * 重新请求上报方法,来获取请求并发, wx.request 在iOS上不支持重写,所以只能重写 uni,
 * 为什么不检测 wechat-mp-track sendAjax 的promise 返回,而是自己构建一套 event bus
 */

function overideRequest() {
  const wxOriginRequest = uni.request;
  uni.request = function (options) {
    options.enableHttp2 = true;
    // options.timeout = 10000;
    const businessRequest = /^https:\/\/wxapi(\d*)(.sy)?.soyoung.com/g.test(
      options.url
    );
    const reportRequest = /soyoung.com\/wxa\/uplog/g.test(options.url);
    let __prime_report_id__ = '';
    if (reportRequest) {
      __prime_report_id__ = options.data.data.ext.__prime_report_id__;
      delete options.data.data.ext.__prime_report_id__;
    }
    const originCompleteFunc = options.complete;
    options.complete = function (...args) {
      if (reportRequest) {
        uni.$emit('__prime_report_complete__', __prime_report_id__);
      }
      businessRequest && requestCount--;
      if (originCompleteFunc) {
        originCompleteFunc.apply(this, args);
      }
    };
    businessRequest && requestCount++;
    return wxOriginRequest(options);
  };
}

export function getRequestCount() {
  return requestCount;
}

const smartReport = new SmartReport();
overideRequest();

export default smartReport;

/**
 * mixins 代码copy之前张亮的实现
 * TODO: 重构
 */
Vue.mixin({
  data() {
    return {
      exposureObservers: [], // 用于存储曝光埋点
      isPageFirstLoad: true // 标记页面是否第一次加载
    };
  },
  async onShow() {
    if (
      this.isSwitchingTab === undefined ||
      this['__route__'] === this.isSwitchingTab
    ) {
      // 为了处理 ios switch tab 的bug，记录当前正在切换的页面
      setTimeout(() => (this.isSwitchingTab = undefined), 2000);
      // 【页面埋点】首次和之后每次显示页面。发送页面埋点（从页面组件的 pageTrackConfig 中读取数据）
      const dataForReport = this.generateDataForReport(
        await this.$options.pageTrackConfig,
        3
      );
      // 防止重复曝光
      if (dataForReport && this.CurrentPageForMaidian !== dataForReport.info) {
        dataForReport && this.$reportData(dataForReport);
        this.CurrentPageForMaidian = dataForReport.info;
        setTimeout(() => {
          this.CurrentPageForMaidian = undefined;
        }, 2000);
      }
      // 【曝光埋点】如果是从其他页面切换，重新初始化曝光埋点
      if (!this.isPageFirstLoad) {
        this.exposureObservers.forEach((item) => item.reInit(this));
      }
    }
    this.isPageFirstLoad = false;
    // 异步曝光 --》处理
    if (this.$options.pageTrackConfig && this.$options.pageTrackConfig.async) {
      this.isPageFirstLoad = true;
    }
  },
  async onHide() {
    // 【页面埋点】页面隐藏时，触发页面隐藏埋点
    const dataForReport = this.generateDataForReport(
      await this.$options.pageTrackConfig,
      2
    );
    dataForReport && this.$reportData(dataForReport);
    // 【曝光埋点】页面隐藏时，销毁所有曝光
    this.exposureObservers.forEach((item) => item.disconnect(this));
  },
  async onUnload() {
    // 【页面埋点】页面销毁时，触发 type=4 埋点
    const dataForReport = this.generateDataForReport(
      await this.$options.pageTrackConfig,
      4
    );
    dataForReport && this.$reportData(dataForReport);
  },
  methods: {
    $reportData(data) {
      return smartReport.reportData(data, this);
    },
    $reportPageUnload(data) {
      data.type = 4;
      this.$reportData(data);
    },
    $reportPageShow(data) {
      data.type = 3;
      this.$reportData(data);
    },
    $reportPageHide(data) {
      data.type = 2;
      this.$reportData(data);
    },
    $registerExposure() {
      return new SyExposure(...arguments);
    },
    generateDataForReport(pageTrackConfig, type) {
      let dataForReport;
      if (typeof pageTrackConfig === 'function') {
        dataForReport = { type: type, ...pageTrackConfig.call(this) };
      } else if (typeof pageTrackConfig === 'object' && pageTrackConfig.info) {
        dataForReport = { type: type, ...pageTrackConfig };
      } else if (typeof pageTrackConfig === 'string') {
        dataForReport = { type: type, info: pageTrackConfig };
      }
      return dataForReport;
    }
  }
});
