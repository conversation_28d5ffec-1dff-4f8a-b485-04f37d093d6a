module.exports=function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=38)}([function(t,e,n){"use strict";n.r(e),n.d(e,"__extends",(function(){return i})),n.d(e,"__assign",(function(){return o})),n.d(e,"__rest",(function(){return a})),n.d(e,"__decorate",(function(){return s})),n.d(e,"__param",(function(){return u})),n.d(e,"__metadata",(function(){return c})),n.d(e,"__awaiter",(function(){return f})),n.d(e,"__generator",(function(){return d})),n.d(e,"__createBinding",(function(){return p})),n.d(e,"__exportStar",(function(){return l})),n.d(e,"__values",(function(){return h})),n.d(e,"__read",(function(){return _})),n.d(e,"__spread",(function(){return v})),n.d(e,"__spreadArrays",(function(){return g})),n.d(e,"__await",(function(){return b})),n.d(e,"__asyncGenerator",(function(){return y})),n.d(e,"__asyncDelegator",(function(){return m})),n.d(e,"__asyncValues",(function(){return O})),n.d(e,"__makeTemplateObject",(function(){return j})),n.d(e,"__importStar",(function(){return E})),n.d(e,"__importDefault",(function(){return S})),n.d(e,"__classPrivateFieldGet",(function(){return w})),n.d(e,"__classPrivateFieldSet",(function(){return x}));
  /*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
  
  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
  
  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */
  var r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)};function i(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return(o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function a(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n}function s(t,e,n,r){var i,o=arguments.length,a=o<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(o<3?i(a):o>3?i(e,n,a):i(e,n))||a);return o>3&&a&&Object.defineProperty(e,n,a),a}function u(t,e){return function(n,r){e(n,r,t)}}function c(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function f(t,e,n,r){return new(n||(n=Promise))((function(i,o){function a(t){try{u(r.next(t))}catch(t){o(t)}}function s(t){try{u(r.throw(t))}catch(t){o(t)}}function u(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}u((r=r.apply(t,e||[])).next())}))}function d(t,e){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function p(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}function l(t,e){for(var n in t)"default"===n||e.hasOwnProperty(n)||(e[n]=t[n])}function h(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function _(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function v(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(_(arguments[e]));return t}function g(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var r=Array(t),i=0;for(e=0;e<n;e++)for(var o=arguments[e],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r}function b(t){return this instanceof b?(this.v=t,this):new b(t)}function y(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(t,e||[]),o=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(t){i[t]&&(r[t]=function(e){return new Promise((function(n,r){o.push([t,e,n,r])>1||s(t,e)}))})}function s(t,e){try{(n=i[t](e)).value instanceof b?Promise.resolve(n.value.v).then(u,c):f(o[0][2],n)}catch(t){f(o[0][3],t)}var n}function u(t){s("next",t)}function c(t){s("throw",t)}function f(t,e){t(e),o.shift(),o.length&&s(o[0][0],o[0][1])}}function m(t){var e,n;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,i){e[r]=t[r]?function(e){return(n=!n)?{value:b(t[r](e)),done:"return"===r}:i?i(e):e}:i}}function O(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=h(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,i){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,i,(e=t[n](e)).done,e.value)}))}}}function j(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function E(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}function S(t){return t&&t.__esModule?t:{default:t}}function w(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function x(t,e,n){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,n),n}},function(t,e,n){"use strict";n.d(e,"d",(function(){return i})),n.d(e,"e",(function(){return a})),n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return u})),n.d(e,"l",(function(){return c})),n.d(e,"j",(function(){return f})),n.d(e,"i",(function(){return d})),n.d(e,"f",(function(){return p})),n.d(e,"c",(function(){return l})),n.d(e,"k",(function(){return h})),n.d(e,"n",(function(){return _})),n.d(e,"m",(function(){return v})),n.d(e,"h",(function(){return g})),n.d(e,"g",(function(){return b}));var r=Object.prototype.toString;function i(t){switch(r.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return b(t,Error)}}function o(t,e){return r.call(t)==="[object "+e+"]"}function a(t){return o(t,"ErrorEvent")}function s(t){return o(t,"DOMError")}function u(t){return o(t,"DOMException")}function c(t){return o(t,"String")}function f(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function d(t){return o(t,"Object")}function p(t){return"undefined"!=typeof Event&&b(t,Event)}function l(t){return"undefined"!=typeof Element&&b(t,Element)}function h(t){return o(t,"RegExp")}function _(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function v(t){return d(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t}function g(t){return"number"==typeof t&&t!=t}function b(t,e){try{return t instanceof e}catch(t){return!1}}},function(t,e,n){"use strict";function r(t){t.then(null,(function(t){console.error(t)}))}n.r(e),n.d(e,"forget",(function(){return r})),n.d(e,"htmlTreeAsString",(function(){return i.b})),n.d(e,"getLocationHref",(function(){return i.a})),n.d(e,"dsnToString",(function(){return o.a})),n.d(e,"makeDsn",(function(){return o.b})),n.d(e,"SeverityLevels",(function(){return a})),n.d(e,"SentryError",(function(){return s.a})),n.d(e,"getGlobalObject",(function(){return u.a})),n.d(e,"getGlobalSingleton",(function(){return u.b})),n.d(e,"addInstrumentationHandler",(function(){return P})),n.d(e,"isError",(function(){return d.d})),n.d(e,"isErrorEvent",(function(){return d.e})),n.d(e,"isDOMError",(function(){return d.a})),n.d(e,"isDOMException",(function(){return d.b})),n.d(e,"isString",(function(){return d.l})),n.d(e,"isPrimitive",(function(){return d.j})),n.d(e,"isPlainObject",(function(){return d.i})),n.d(e,"isEvent",(function(){return d.f})),n.d(e,"isElement",(function(){return d.c})),n.d(e,"isRegExp",(function(){return d.k})),n.d(e,"isThenable",(function(){return d.n})),n.d(e,"isSyntheticEvent",(function(){return d.m})),n.d(e,"isNaN",(function(){return d.h})),n.d(e,"isInstanceOf",(function(){return d.g})),n.d(e,"CONSOLE_LEVELS",(function(){return p.a})),n.d(e,"consoleSandbox",(function(){return p.b})),n.d(e,"logger",(function(){return p.c})),n.d(e,"memoBuilder",(function(){return F.a})),n.d(e,"uuid4",(function(){return L.i})),n.d(e,"parseUrl",(function(){return L.g})),n.d(e,"getEventDescription",(function(){return L.e})),n.d(e,"addExceptionTypeValue",(function(){return L.c})),n.d(e,"addExceptionMechanism",(function(){return L.b})),n.d(e,"parseSemver",(function(){return L.f})),n.d(e,"addContextToFrame",(function(){return L.a})),n.d(e,"stripUrlQueryAndFragment",(function(){return L.h})),n.d(e,"checkOrSetAlreadyCaught",(function(){return L.d})),n.d(e,"isNodeEnv",(function(){return U.b})),n.d(e,"dynamicRequire",(function(){return U.a})),n.d(e,"loadModule",(function(){return U.c})),n.d(e,"normalize",(function(){return B.a})),n.d(e,"normalizeToSize",(function(){return B.b})),n.d(e,"walk",(function(){return B.c})),n.d(e,"fill",(function(){return l.e})),n.d(e,"addNonEnumerableProperty",(function(){return l.a})),n.d(e,"markFunctionWrapped",(function(){return l.g})),n.d(e,"getOriginalFunction",(function(){return l.f})),n.d(e,"urlEncode",(function(){return l.i})),n.d(e,"convertToPlainObject",(function(){return l.b})),n.d(e,"extractExceptionKeysForMessage",(function(){return l.d})),n.d(e,"dropUndefinedKeys",(function(){return l.c})),n.d(e,"objectify",(function(){return l.h})),n.d(e,"resolve",(function(){return W})),n.d(e,"relative",(function(){return Y})),n.d(e,"normalizePath",(function(){return V})),n.d(e,"isAbsolute",(function(){return J})),n.d(e,"join",(function(){return $})),n.d(e,"dirname",(function(){return X})),n.d(e,"basename",(function(){return Z})),n.d(e,"makePromiseBuffer",(function(){return Q.a})),n.d(e,"severityFromString",(function(){return et})),n.d(e,"createStackParser",(function(){return h.a})),n.d(e,"stripSentryFramesAndReverse",(function(){return h.c})),n.d(e,"getFunctionName",(function(){return h.b})),n.d(e,"eventStatusFromHttpCode",(function(){return nt.a})),n.d(e,"truncate",(function(){return rt.e})),n.d(e,"snipLine",(function(){return rt.d})),n.d(e,"safeJoin",(function(){return rt.c})),n.d(e,"isMatchingPattern",(function(){return rt.b})),n.d(e,"escapeStringForRegex",(function(){return rt.a})),n.d(e,"supportsErrorEvent",(function(){return _})),n.d(e,"supportsDOMError",(function(){return v})),n.d(e,"supportsDOMException",(function(){return g})),n.d(e,"supportsFetch",(function(){return b})),n.d(e,"isNativeFetch",(function(){return y})),n.d(e,"supportsNativeFetch",(function(){return m})),n.d(e,"supportsReportingObserver",(function(){return O})),n.d(e,"supportsReferrerPolicy",(function(){return j})),n.d(e,"supportsHistory",(function(){return E})),n.d(e,"resolvedSyncPromise",(function(){return it.c})),n.d(e,"rejectedSyncPromise",(function(){return it.b})),n.d(e,"SyncPromise",(function(){return it.a})),n.d(e,"dateTimestampInSeconds",(function(){return ot.c})),n.d(e,"timestampInSeconds",(function(){return ot.d})),n.d(e,"timestampWithMs",(function(){return ot.e})),n.d(e,"usingPerformanceAPI",(function(){return ot.f})),n.d(e,"_browserPerformanceTimeOriginMode",(function(){return ot.a})),n.d(e,"browserPerformanceTimeOrigin",(function(){return ot.b})),n.d(e,"TRACEPARENT_REGEXP",(function(){return at})),n.d(e,"extractTraceparentData",(function(){return st})),n.d(e,"isBrowserBundle",(function(){return ut.a})),n.d(e,"createEnvelope",(function(){return ct.b})),n.d(e,"addItemToEnvelope",(function(){return ct.a})),n.d(e,"getEnvelopeType",(function(){return ct.c})),n.d(e,"serializeEnvelope",(function(){return ct.d})),n.d(e,"createClientReportEnvelope",(function(){return ft})),n.d(e,"DEFAULT_RETRY_AFTER",(function(){return dt.a})),n.d(e,"parseRetryAfterHeader",(function(){return dt.d})),n.d(e,"disabledUntil",(function(){return dt.b})),n.d(e,"isRateLimited",(function(){return dt.c})),n.d(e,"updateRateLimits",(function(){return dt.e}));var i=n(18),o=n(27),a=["fatal","error","warning","log","info","debug","critical"],s=n(7),u=n(3),c=n(0),f=n(6),d=n(1),p=n(9),l=n(4),h=n(11);function _(){try{return new ErrorEvent(""),!0}catch(t){return!1}}function v(){try{return new DOMError(""),!0}catch(t){return!1}}function g(){try{return new DOMException(""),!0}catch(t){return!1}}function b(){if(!("fetch"in Object(u.a)()))return!1;try{return new Headers,new Request(""),new Response,!0}catch(t){return!1}}function y(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function m(){if(!b())return!1;var t=Object(u.a)();if(y(t.fetch))return!0;var e=!1,n=t.document;if(n&&"function"==typeof n.createElement)try{var r=n.createElement("iframe");r.hidden=!0,n.head.appendChild(r),r.contentWindow&&r.contentWindow.fetch&&(e=y(r.contentWindow.fetch)),n.head.removeChild(r)}catch(t){f.a&&p.c.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return e}function O(){return"ReportingObserver"in Object(u.a)()}function j(){if(!b())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(t){return!1}}function E(){var t=Object(u.a)(),e=t.chrome,n=e&&e.app&&e.app.runtime,r="history"in t&&!!t.history.pushState&&!!t.history.replaceState;return!n&&r}var S,w=Object(u.a)(),x={},k={};function T(t){if(!k[t])switch(k[t]=!0,t){case"console":!function(){if(!("console"in w))return;p.a.forEach((function(t){t in w.console&&Object(l.e)(w.console,t,(function(e){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];I("console",{args:n,level:t}),e&&e.apply(w.console,n)}}))}))}();break;case"dom":!function(){if(!("document"in w))return;var t=I.bind(null,"dom"),e=C(t,!0);w.document.addEventListener("click",e,!1),w.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach((function(e){var n=w[e]&&w[e].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(Object(l.e)(n,"addEventListener",(function(e){return function(n,r,i){if("click"===n||"keypress"==n)try{var o=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},a=o[n]=o[n]||{refCount:0};if(!a.handler){var s=C(t);a.handler=s,e.call(this,n,s,i)}a.refCount+=1}catch(t){}return e.call(this,n,r,i)}})),Object(l.e)(n,"removeEventListener",(function(t){return function(e,n,r){if("click"===e||"keypress"==e)try{var i=this.__sentry_instrumentation_handlers__||{},o=i[e];o&&(o.refCount-=1,o.refCount<=0&&(t.call(this,e,o.handler,r),o.handler=void 0,delete i[e]),0===Object.keys(i).length&&delete this.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,e,n,r)}})))}))}();break;case"xhr":!function(){if(!("XMLHttpRequest"in w))return;var t=XMLHttpRequest.prototype;Object(l.e)(t,"open",(function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=this,i=e[1],o=r.__sentry_xhr__={method:Object(d.l)(e[0])?e[0].toUpperCase():e[0],url:e[1]};Object(d.l)(i)&&"POST"===o.method&&i.match(/sentry_key/)&&(r.__sentry_own_request__=!0);var a=function(){if(4===r.readyState){try{o.status_code=r.status}catch(t){}I("xhr",{args:e,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:r})}};return"onreadystatechange"in r&&"function"==typeof r.onreadystatechange?Object(l.e)(r,"onreadystatechange",(function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return a(),t.apply(r,e)}})):r.addEventListener("readystatechange",a),t.apply(r,e)}})),Object(l.e)(t,"send",(function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return this.__sentry_xhr__&&void 0!==e[0]&&(this.__sentry_xhr__.body=e[0]),I("xhr",{args:e,startTimestamp:Date.now(),xhr:this}),t.apply(this,e)}}))}();break;case"fetch":!function(){if(!m())return;Object(l.e)(w,"fetch",(function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r={args:e,fetchData:{method:M(e),url:N(e)},startTimestamp:Date.now()};return I("fetch",Object(c.__assign)({},r)),t.apply(w,e).then((function(t){return I("fetch",Object(c.__assign)(Object(c.__assign)({},r),{endTimestamp:Date.now(),response:t})),t}),(function(t){throw I("fetch",Object(c.__assign)(Object(c.__assign)({},r),{endTimestamp:Date.now(),error:t})),t}))}}))}();break;case"history":!function(){if(!E())return;var t=w.onpopstate;function e(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=e.length>2?e[2]:void 0;if(r){var i=S,o=String(r);S=o,I("history",{from:i,to:o})}return t.apply(this,e)}}w.onpopstate=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=w.location.href,i=S;if(S=r,I("history",{from:i,to:r}),t)try{return t.apply(this,e)}catch(t){}},Object(l.e)(w.history,"pushState",e),Object(l.e)(w.history,"replaceState",e)}();break;case"error":A=w.onerror,w.onerror=function(t,e,n,r,i){return I("error",{column:r,error:i,line:n,msg:t,url:e}),!!A&&A.apply(this,arguments)};break;case"unhandledrejection":H=w.onunhandledrejection,w.onunhandledrejection=function(t){return I("unhandledrejection",t),!H||H.apply(this,arguments)};break;default:return void(f.a&&p.c.warn("unknown instrumentation type:",t))}}function P(t,e){x[t]=x[t]||[],x[t].push(e),T(t)}function I(t,e){var n,r;if(t&&x[t])try{for(var i=Object(c.__values)(x[t]||[]),o=i.next();!o.done;o=i.next()){var a=o.value;try{a(e)}catch(e){f.a&&p.c.error("Error while triggering instrumentation handler.\nType: "+t+"\nName: "+Object(h.b)(a)+"\nError:",e)}}}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}function M(t){return void 0===t&&(t=[]),"Request"in w&&Object(d.g)(t[0],Request)&&t[0].method?String(t[0].method).toUpperCase():t[1]&&t[1].method?String(t[1].method).toUpperCase():"GET"}function N(t){return void 0===t&&(t=[]),"string"==typeof t[0]?t[0]:"Request"in w&&Object(d.g)(t[0],Request)?t[0].url:String(t[0])}var R,D;function C(t,e){return void 0===e&&(e=!1),function(n){if(n&&D!==n&&!function(t){if("keypress"!==t.type)return!1;try{var e=t.target;if(!e||!e.tagName)return!0;if("INPUT"===e.tagName||"TEXTAREA"===e.tagName||e.isContentEditable)return!1}catch(t){}return!0}(n)){var r="keypress"===n.type?"input":n.type;(void 0===R||function(t,e){if(!t)return!0;if(t.type!==e.type)return!0;try{if(t.target!==e.target)return!0}catch(t){}return!1}(D,n))&&(t({event:n,name:r,global:e}),D=n),clearTimeout(R),R=w.setTimeout((function(){R=void 0}),1e3)}}}var A=null;var H=null;var F=n(19),L=n(26),U=n(10),B=n(28);function q(t,e){for(var n=0,r=t.length-1;r>=0;r--){var i=t[r];"."===i?t.splice(r,1):".."===i?(t.splice(r,1),n++):n&&(t.splice(r,1),n--)}if(e)for(;n--;n)t.unshift("..");return t}var G=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^/]+?|)(\.[^./]*|))(?:[/]*)$/;function z(t){var e=G.exec(t);return e?e.slice(1):[]}function W(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n="",r=!1,i=t.length-1;i>=-1&&!r;i--){var o=i>=0?t[i]:"/";o&&(n=o+"/"+n,r="/"===o.charAt(0))}return(r?"/":"")+(n=q(n.split("/").filter((function(t){return!!t})),!r).join("/"))||"."}function K(t){for(var e=0;e<t.length&&""===t[e];e++);for(var n=t.length-1;n>=0&&""===t[n];n--);return e>n?[]:t.slice(e,n-e+1)}function Y(t,e){t=W(t).substr(1),e=W(e).substr(1);for(var n=K(t.split("/")),r=K(e.split("/")),i=Math.min(n.length,r.length),o=i,a=0;a<i;a++)if(n[a]!==r[a]){o=a;break}var s=[];for(a=o;a<n.length;a++)s.push("..");return(s=s.concat(r.slice(o))).join("/")}function V(t){var e=J(t),n="/"===t.substr(-1),r=q(t.split("/").filter((function(t){return!!t})),!e).join("/");return r||e||(r="."),r&&n&&(r+="/"),(e?"/":"")+r}function J(t){return"/"===t.charAt(0)}function $(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return V(t.join("/"))}function X(t){var e=z(t),n=e[0],r=e[1];return n||r?(r&&(r=r.substr(0,r.length-1)),n+r):"."}function Z(t,e){var n=z(t)[2];return e&&n.substr(-1*e.length)===e&&(n=n.substr(0,n.length-e.length)),n}var Q=n(29),tt=n(14);function et(t){return"warn"===t?tt.a.Warning:function(t){return-1!==a.indexOf(t)}(t)?t:tt.a.Log}var nt=n(31),rt=n(8),it=n(12),ot=n(16),at=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function st(t){var e=t.match(at);if(e){var n=void 0;return"1"===e[3]?n=!0:"0"===e[3]&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}}var ut=n(17),ct=n(20);function ft(t,e,n){var r=[{type:"client_report"},{timestamp:n||Object(ot.c)(),discarded_events:t}];return Object(ct.b)(e?{dsn:e}:{},[r])}var dt=n(30)},function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return a}));var r=n(10),i={};function o(){return Object(r.b)()?t:"undefined"!=typeof window?window:"undefined"!=typeof self?self:i}function a(t,e,n){var r=n||o(),i=r.__SENTRY__=r.__SENTRY__||{};return i[t]||(i[t]=e())}}).call(this,n(25))},function(t,e,n){"use strict";n.d(e,"e",(function(){return s})),n.d(e,"a",(function(){return u})),n.d(e,"g",(function(){return c})),n.d(e,"f",(function(){return f})),n.d(e,"i",(function(){return d})),n.d(e,"b",(function(){return p})),n.d(e,"d",(function(){return _})),n.d(e,"c",(function(){return v})),n.d(e,"h",(function(){return g}));var r=n(0),i=n(18),o=n(1),a=n(8);function s(t,e,n){if(e in t){var r=t[e],i=n(r);if("function"==typeof i)try{c(i,r)}catch(t){}t[e]=i}}function u(t,e,n){Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}function c(t,e){var n=e.prototype||{};t.prototype=e.prototype=n,u(t,"__sentry_original__",e)}function f(t){return t.__sentry_original__}function d(t){return Object.keys(t).map((function(e){return encodeURIComponent(e)+"="+encodeURIComponent(t[e])})).join("&")}function p(t){var e=t;if(Object(o.d)(t))e=Object(r.__assign)({message:t.message,name:t.name,stack:t.stack},h(t));else if(Object(o.f)(t)){var n=t;e=Object(r.__assign)({type:n.type,target:l(n.target),currentTarget:l(n.currentTarget)},h(n)),"undefined"!=typeof CustomEvent&&Object(o.g)(t,CustomEvent)&&(e.detail=n.detail)}return e}function l(t){try{return Object(o.c)(t)?Object(i.b)(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}}function h(t){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}function _(t,e){void 0===e&&(e=40);var n=Object.keys(p(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return Object(a.e)(n[0],e);for(var r=n.length;r>0;r--){var i=n.slice(0,r).join(", ");if(!(i.length>e))return r===n.length?i:Object(a.e)(i,e)}return""}function v(t){var e,n;if(Object(o.i)(t)){var i={};try{for(var a=Object(r.__values)(Object.keys(t)),s=a.next();!s.done;s=a.next()){var u=s.value;void 0!==t[u]&&(i[u]=v(t[u]))}}catch(t){e={error:t}}finally{try{s&&!s.done&&(n=a.return)&&n.call(a)}finally{if(e)throw e.error}}return i}return Array.isArray(t)?t.map(v):t}function g(t){var e;switch(!0){case null==t:e=new String(t);break;case"symbol"==typeof t||"bigint"==typeof t:e=Object(t);break;case Object(o.j)(t):e=new t.constructor(t);break;default:e=t}return e}},function(t,e,n){"use strict";n.r(e),n.d(e,"addBreadcrumb",(function(){return I})),n.d(e,"captureException",(function(){return x})),n.d(e,"captureEvent",(function(){return T})),n.d(e,"captureMessage",(function(){return k})),n.d(e,"configureScope",(function(){return P})),n.d(e,"startTransaction",(function(){return F})),n.d(e,"setContext",(function(){return M})),n.d(e,"setExtra",(function(){return D})),n.d(e,"setExtras",(function(){return N})),n.d(e,"setTag",(function(){return C})),n.d(e,"setTags",(function(){return R})),n.d(e,"setUser",(function(){return A})),n.d(e,"withScope",(function(){return H})),n.d(e,"addGlobalEventProcessor",(function(){return _})),n.d(e,"getCurrentHub",(function(){return O})),n.d(e,"getHubFromCarrier",(function(){return E})),n.d(e,"Hub",(function(){return b})),n.d(e,"makeMain",(function(){return m})),n.d(e,"Scope",(function(){return l})),n.d(e,"Session",(function(){return g})),n.d(e,"API",(function(){return U})),n.d(e,"getEnvelopeEndpointWithUrlEncodedAuth",(function(){return Y})),n.d(e,"getStoreEndpointWithUrlEncodedAuth",(function(){return K})),n.d(e,"getRequestHeaders",(function(){return V})),n.d(e,"initAPIDetails",(function(){return B})),n.d(e,"getReportDialogEndpoint",(function(){return J})),n.d(e,"BaseClient",(function(){return it})),n.d(e,"BaseBackend",(function(){return pt})),n.d(e,"eventToSentryRequest",(function(){return ft})),n.d(e,"sessionToSentryRequest",(function(){return ct})),n.d(e,"initAndBind",(function(){return lt})),n.d(e,"NoopTransport",(function(){return dt})),n.d(e,"createTransport",(function(){return gt})),n.d(e,"SDK_VERSION",(function(){return mt})),n.d(e,"Integrations",(function(){return r}));var r={};n.r(r),n.d(r,"FunctionToString",(function(){return Ot})),n.d(r,"InboundFilters",(function(){return Et}));var i=n(0),o=n(26),a=n(16),s=n(9),u=n(3),c=n(10),f="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,d=n(1),p=n(12),l=function(){function t(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={}}return t.clone=function(e){var n=new t;return e&&(n._breadcrumbs=Object(i.__spread)(e._breadcrumbs),n._tags=Object(i.__assign)({},e._tags),n._extra=Object(i.__assign)({},e._extra),n._contexts=Object(i.__assign)({},e._contexts),n._user=e._user,n._level=e._level,n._span=e._span,n._session=e._session,n._transactionName=e._transactionName,n._fingerprint=e._fingerprint,n._eventProcessors=Object(i.__spread)(e._eventProcessors),n._requestSession=e._requestSession),n},t.prototype.addScopeListener=function(t){this._scopeListeners.push(t)},t.prototype.addEventProcessor=function(t){return this._eventProcessors.push(t),this},t.prototype.setUser=function(t){return this._user=t||{},this._session&&this._session.update({user:t}),this._notifyScopeListeners(),this},t.prototype.getUser=function(){return this._user},t.prototype.getRequestSession=function(){return this._requestSession},t.prototype.setRequestSession=function(t){return this._requestSession=t,this},t.prototype.setTags=function(t){return this._tags=Object(i.__assign)(Object(i.__assign)({},this._tags),t),this._notifyScopeListeners(),this},t.prototype.setTag=function(t,e){var n;return this._tags=Object(i.__assign)(Object(i.__assign)({},this._tags),((n={})[t]=e,n)),this._notifyScopeListeners(),this},t.prototype.setExtras=function(t){return this._extra=Object(i.__assign)(Object(i.__assign)({},this._extra),t),this._notifyScopeListeners(),this},t.prototype.setExtra=function(t,e){var n;return this._extra=Object(i.__assign)(Object(i.__assign)({},this._extra),((n={})[t]=e,n)),this._notifyScopeListeners(),this},t.prototype.setFingerprint=function(t){return this._fingerprint=t,this._notifyScopeListeners(),this},t.prototype.setLevel=function(t){return this._level=t,this._notifyScopeListeners(),this},t.prototype.setTransactionName=function(t){return this._transactionName=t,this._notifyScopeListeners(),this},t.prototype.setTransaction=function(t){return this.setTransactionName(t)},t.prototype.setContext=function(t,e){var n;return null===e?delete this._contexts[t]:this._contexts=Object(i.__assign)(Object(i.__assign)({},this._contexts),((n={})[t]=e,n)),this._notifyScopeListeners(),this},t.prototype.setSpan=function(t){return this._span=t,this._notifyScopeListeners(),this},t.prototype.getSpan=function(){return this._span},t.prototype.getTransaction=function(){var t=this.getSpan();return t&&t.transaction},t.prototype.setSession=function(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this},t.prototype.getSession=function(){return this._session},t.prototype.update=function(e){if(!e)return this;if("function"==typeof e){var n=e(this);return n instanceof t?n:this}return e instanceof t?(this._tags=Object(i.__assign)(Object(i.__assign)({},this._tags),e._tags),this._extra=Object(i.__assign)(Object(i.__assign)({},this._extra),e._extra),this._contexts=Object(i.__assign)(Object(i.__assign)({},this._contexts),e._contexts),e._user&&Object.keys(e._user).length&&(this._user=e._user),e._level&&(this._level=e._level),e._fingerprint&&(this._fingerprint=e._fingerprint),e._requestSession&&(this._requestSession=e._requestSession)):Object(d.i)(e)&&(e=e,this._tags=Object(i.__assign)(Object(i.__assign)({},this._tags),e.tags),this._extra=Object(i.__assign)(Object(i.__assign)({},this._extra),e.extra),this._contexts=Object(i.__assign)(Object(i.__assign)({},this._contexts),e.contexts),e.user&&(this._user=e.user),e.level&&(this._level=e.level),e.fingerprint&&(this._fingerprint=e.fingerprint),e.requestSession&&(this._requestSession=e.requestSession)),this},t.prototype.clear=function(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this},t.prototype.addBreadcrumb=function(t,e){var n="number"==typeof e?Math.min(e,100):100;if(n<=0)return this;var r=Object(i.__assign)({timestamp:Object(a.c)()},t);return this._breadcrumbs=Object(i.__spread)(this._breadcrumbs,[r]).slice(-n),this._notifyScopeListeners(),this},t.prototype.clearBreadcrumbs=function(){return this._breadcrumbs=[],this._notifyScopeListeners(),this},t.prototype.applyToEvent=function(t,e){if(this._extra&&Object.keys(this._extra).length&&(t.extra=Object(i.__assign)(Object(i.__assign)({},this._extra),t.extra)),this._tags&&Object.keys(this._tags).length&&(t.tags=Object(i.__assign)(Object(i.__assign)({},this._tags),t.tags)),this._user&&Object.keys(this._user).length&&(t.user=Object(i.__assign)(Object(i.__assign)({},this._user),t.user)),this._contexts&&Object.keys(this._contexts).length&&(t.contexts=Object(i.__assign)(Object(i.__assign)({},this._contexts),t.contexts)),this._level&&(t.level=this._level),this._transactionName&&(t.transaction=this._transactionName),this._span){t.contexts=Object(i.__assign)({trace:this._span.getTraceContext()},t.contexts);var n=this._span.transaction&&this._span.transaction.name;n&&(t.tags=Object(i.__assign)({transaction:n},t.tags))}return this._applyFingerprint(t),t.breadcrumbs=Object(i.__spread)(t.breadcrumbs||[],this._breadcrumbs),t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,t.sdkProcessingMetadata=this._sdkProcessingMetadata,this._notifyEventProcessors(Object(i.__spread)(h(),this._eventProcessors),t,e)},t.prototype.setSDKProcessingMetadata=function(t){return this._sdkProcessingMetadata=Object(i.__assign)(Object(i.__assign)({},this._sdkProcessingMetadata),t),this},t.prototype._notifyEventProcessors=function(t,e,n,r){var o=this;return void 0===r&&(r=0),new p.a((function(a,s){var u=t[r];if(null===e||"function"!=typeof u)a(e);else{var c=u(Object(i.__assign)({},e),n);Object(d.n)(c)?c.then((function(e){return o._notifyEventProcessors(t,e,n,r+1).then(a)})).then(null,s):o._notifyEventProcessors(t,c,n,r+1).then(a).then(null,s)}}))},t.prototype._notifyScopeListeners=function(){var t=this;this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((function(e){e(t)})),this._notifyingListeners=!1)},t.prototype._applyFingerprint=function(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this._fingerprint&&(t.fingerprint=t.fingerprint.concat(this._fingerprint)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint},t}();function h(){return Object(u.b)("globalEventProcessors",(function(){return[]}))}function _(t){h().push(t)}var v=n(4),g=function(){function t(t){this.errors=0,this.sid=Object(o.i)(),this.duration=0,this.status="ok",this.init=!0,this.ignoreDuration=!1;var e=Object(a.d)();this.timestamp=e,this.started=e,t&&this.update(t)}return t.prototype.update=function(t){if(void 0===t&&(t={}),t.user&&(!this.ipAddress&&t.user.ip_address&&(this.ipAddress=t.user.ip_address),this.did||t.did||(this.did=t.user.id||t.user.email||t.user.username)),this.timestamp=t.timestamp||Object(a.d)(),t.ignoreDuration&&(this.ignoreDuration=t.ignoreDuration),t.sid&&(this.sid=32===t.sid.length?t.sid:Object(o.i)()),void 0!==t.init&&(this.init=t.init),!this.did&&t.did&&(this.did=""+t.did),"number"==typeof t.started&&(this.started=t.started),this.ignoreDuration)this.duration=void 0;else if("number"==typeof t.duration)this.duration=t.duration;else{var e=this.timestamp-this.started;this.duration=e>=0?e:0}t.release&&(this.release=t.release),t.environment&&(this.environment=t.environment),!this.ipAddress&&t.ipAddress&&(this.ipAddress=t.ipAddress),!this.userAgent&&t.userAgent&&(this.userAgent=t.userAgent),"number"==typeof t.errors&&(this.errors=t.errors),t.status&&(this.status=t.status)},t.prototype.close=function(t){t?this.update({status:t}):"ok"===this.status?this.update({status:"exited"}):this.update()},t.prototype.toJSON=function(){return Object(v.c)({sid:""+this.sid,init:this.init,started:new Date(1e3*this.started).toISOString(),timestamp:new Date(1e3*this.timestamp).toISOString(),status:this.status,errors:this.errors,did:"number"==typeof this.did||"string"==typeof this.did?""+this.did:void 0,duration:this.duration,attrs:{release:this.release,environment:this.environment,ip_address:this.ipAddress,user_agent:this.userAgent}})},t}(),b=function(){function t(t,e,n){void 0===e&&(e=new l),void 0===n&&(n=4),this._version=n,this._stack=[{}],this.getStackTop().scope=e,t&&this.bindClient(t)}return t.prototype.isOlderThan=function(t){return this._version<t},t.prototype.bindClient=function(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()},t.prototype.pushScope=function(){var t=l.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t},t.prototype.popScope=function(){return!(this.getStack().length<=1)&&!!this.getStack().pop()},t.prototype.withScope=function(t){var e=this.pushScope();try{t(e)}finally{this.popScope()}},t.prototype.getClient=function(){return this.getStackTop().client},t.prototype.getScope=function(){return this.getStackTop().scope},t.prototype.getStack=function(){return this._stack},t.prototype.getStackTop=function(){return this._stack[this._stack.length-1]},t.prototype.captureException=function(t,e){var n=this._lastEventId=e&&e.event_id?e.event_id:Object(o.i)(),r=e;if(!e){var a=void 0;try{throw new Error("Sentry syntheticException")}catch(t){a=t}r={originalException:t,syntheticException:a}}return this._invokeClient("captureException",t,Object(i.__assign)(Object(i.__assign)({},r),{event_id:n})),n},t.prototype.captureMessage=function(t,e,n){var r=this._lastEventId=n&&n.event_id?n.event_id:Object(o.i)(),a=n;if(!n){var s=void 0;try{throw new Error(t)}catch(t){s=t}a={originalException:t,syntheticException:s}}return this._invokeClient("captureMessage",t,e,Object(i.__assign)(Object(i.__assign)({},a),{event_id:r})),r},t.prototype.captureEvent=function(t,e){var n=e&&e.event_id?e.event_id:Object(o.i)();return"transaction"!==t.type&&(this._lastEventId=n),this._invokeClient("captureEvent",t,Object(i.__assign)(Object(i.__assign)({},e),{event_id:n})),n},t.prototype.lastEventId=function(){return this._lastEventId},t.prototype.addBreadcrumb=function(t,e){var n=this.getStackTop(),r=n.scope,o=n.client;if(r&&o){var u=o.getOptions&&o.getOptions()||{},c=u.beforeBreadcrumb,f=void 0===c?null:c,d=u.maxBreadcrumbs,p=void 0===d?100:d;if(!(p<=0)){var l=Object(a.c)(),h=Object(i.__assign)({timestamp:l},t),_=f?Object(s.b)((function(){return f(h,e)})):h;null!==_&&r.addBreadcrumb(_,p)}}},t.prototype.setUser=function(t){var e=this.getScope();e&&e.setUser(t)},t.prototype.setTags=function(t){var e=this.getScope();e&&e.setTags(t)},t.prototype.setExtras=function(t){var e=this.getScope();e&&e.setExtras(t)},t.prototype.setTag=function(t,e){var n=this.getScope();n&&n.setTag(t,e)},t.prototype.setExtra=function(t,e){var n=this.getScope();n&&n.setExtra(t,e)},t.prototype.setContext=function(t,e){var n=this.getScope();n&&n.setContext(t,e)},t.prototype.configureScope=function(t){var e=this.getStackTop(),n=e.scope,r=e.client;n&&r&&t(n)},t.prototype.run=function(t){var e=m(this);try{t(this)}finally{m(e)}},t.prototype.getIntegration=function(t){var e=this.getClient();if(!e)return null;try{return e.getIntegration(t)}catch(e){return f&&s.c.warn("Cannot retrieve integration "+t.id+" from the current Hub"),null}},t.prototype.startSpan=function(t){return this._callExtensionMethod("startSpan",t)},t.prototype.startTransaction=function(t,e){return this._callExtensionMethod("startTransaction",t,e)},t.prototype.traceHeaders=function(){return this._callExtensionMethod("traceHeaders")},t.prototype.captureSession=function(t){if(void 0===t&&(t=!1),t)return this.endSession();this._sendSessionUpdate()},t.prototype.endSession=function(){var t=this.getStackTop(),e=t&&t.scope,n=e&&e.getSession();n&&n.close(),this._sendSessionUpdate(),e&&e.setSession()},t.prototype.startSession=function(t){var e=this.getStackTop(),n=e.scope,r=e.client,o=r&&r.getOptions()||{},a=o.release,s=o.environment,c=(Object(u.a)().navigator||{}).userAgent,f=new g(Object(i.__assign)(Object(i.__assign)(Object(i.__assign)({release:a,environment:s},n&&{user:n.getUser()}),c&&{userAgent:c}),t));if(n){var d=n.getSession&&n.getSession();d&&"ok"===d.status&&d.update({status:"exited"}),this.endSession(),n.setSession(f)}return f},t.prototype._sendSessionUpdate=function(){var t=this.getStackTop(),e=t.scope,n=t.client;if(e){var r=e.getSession&&e.getSession();r&&n&&n.captureSession&&n.captureSession(r)}},t.prototype._invokeClient=function(t){for(var e,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var o=this.getStackTop(),a=o.scope,s=o.client;s&&s[t]&&(e=s)[t].apply(e,Object(i.__spread)(n,[a]))},t.prototype._callExtensionMethod=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=y(),i=r.__SENTRY__;if(i&&i.extensions&&"function"==typeof i.extensions[t])return i.extensions[t].apply(this,e);f&&s.c.warn("Extension method "+t+" couldn't be found, doing nothing.")},t}();function y(){var t=Object(u.a)();return t.__SENTRY__=t.__SENTRY__||{extensions:{},hub:void 0},t}function m(t){var e=y(),n=E(e);return S(e,t),n}function O(){var t=y();return j(t)&&!E(t).isOlderThan(4)||S(t,new b),Object(c.b)()?function(t){try{var e=y().__SENTRY__,n=e&&e.extensions&&e.extensions.domain&&e.extensions.domain.active;if(!n)return E(t);if(!j(n)||E(n).isOlderThan(4)){var r=E(t).getStackTop();S(n,new b(r.client,l.clone(r.scope)))}return E(n)}catch(e){return E(t)}}(t):E(t)}function j(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function E(t){return Object(u.b)("hub",(function(){return new b}),t)}function S(t,e){return!!t&&((t.__SENTRY__=t.__SENTRY__||{}).hub=e,!0)}function w(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=O();if(r&&r[t])return r[t].apply(r,Object(i.__spread)(e));throw new Error("No hub defined or "+t+" was not found on the hub, please open a bug report.")}function x(t,e){return w("captureException",t,{captureContext:e,originalException:t,syntheticException:new Error("Sentry syntheticException")})}function k(t,e){var n=new Error(t),r="string"!=typeof e?{captureContext:e}:void 0;return w("captureMessage",t,"string"==typeof e?e:void 0,Object(i.__assign)({originalException:t,syntheticException:n},r))}function T(t){return w("captureEvent",t)}function P(t){w("configureScope",t)}function I(t){w("addBreadcrumb",t)}function M(t,e){w("setContext",t,e)}function N(t){w("setExtras",t)}function R(t){w("setTags",t)}function D(t,e){w("setExtra",t,e)}function C(t,e){w("setTag",t,e)}function A(t){w("setUser",t)}function H(t){w("withScope",t)}function F(t,e){return w("startTransaction",Object(i.__assign)({},t),e)}var L=n(27),U=function(){function t(t,e,n){void 0===e&&(e={}),this.dsn=t,this._dsnObject=Object(L.b)(t),this.metadata=e,this._tunnel=n}return t.prototype.getDsn=function(){return this._dsnObject},t.prototype.forceEnvelope=function(){return!!this._tunnel},t.prototype.getBaseApiEndpoint=function(){return q(this._dsnObject)},t.prototype.getStoreEndpoint=function(){return W(this._dsnObject)},t.prototype.getStoreEndpointWithUrlEncodedAuth=function(){return K(this._dsnObject)},t.prototype.getEnvelopeEndpointWithUrlEncodedAuth=function(){return Y(this._dsnObject,this._tunnel)},t}();function B(t,e,n){return{initDsn:t,metadata:e||{},dsn:Object(L.b)(t),tunnel:n}}function q(t){var e=t.protocol?t.protocol+":":"",n=t.port?":"+t.port:"";return e+"//"+t.host+n+(t.path?"/"+t.path:"")+"/api/"}function G(t,e){return""+q(t)+t.projectId+"/"+e+"/"}function z(t){return Object(v.i)({sentry_key:t.publicKey,sentry_version:"7"})}function W(t){return G(t,"store")}function K(t){return W(t)+"?"+z(t)}function Y(t,e){return e||function(t){return G(t,"envelope")}(t)+"?"+z(t)}function V(t,e,n){var r=["Sentry sentry_version=7"];return r.push("sentry_client="+e+"/"+n),r.push("sentry_key="+t.publicKey),t.pass&&r.push("sentry_secret="+t.pass),{"Content-Type":"application/json","X-Sentry-Auth":r.join(", ")}}function J(t,e){var n=Object(L.b)(t),r=q(n)+"embed/error-page/",i="dsn="+Object(L.a)(n);for(var o in e)if("dsn"!==o)if("user"===o){if(!e.user)continue;e.user.name&&(i+="&name="+encodeURIComponent(e.user.name)),e.user.email&&(i+="&email="+encodeURIComponent(e.user.email))}else i+="&"+encodeURIComponent(o)+"="+encodeURIComponent(e[o]);return r+"?"+i}var $=n(28),X=n(8),Z=n(7),Q="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,tt=[];function et(t){return t.reduce((function(t,e){return t.every((function(t){return e.name!==t.name}))&&t.push(e),t}),[])}function nt(t){var e={};return function(t){var e=t.defaultIntegrations&&Object(i.__spread)(t.defaultIntegrations)||[],n=t.integrations,r=Object(i.__spread)(et(e));Array.isArray(n)?r=Object(i.__spread)(r.filter((function(t){return n.every((function(e){return e.name!==t.name}))})),et(n)):"function"==typeof n&&(r=n(r),r=Array.isArray(r)?r:[r]);var o=r.map((function(t){return t.name}));return-1!==o.indexOf("Debug")&&r.push.apply(r,Object(i.__spread)(r.splice(o.indexOf("Debug"),1))),r}(t).forEach((function(t){e[t.name]=t,function(t){-1===tt.indexOf(t.name)&&(t.setupOnce(_,O),tt.push(t.name),Q&&s.c.log("Integration installed: "+t.name))}(t)})),Object(v.a)(e,"initialized",!0),e}var rt="Not capturing exception because it's already been captured.",it=function(){function t(t,e){this._integrations={},this._numProcessing=0,this._backend=new t(e),this._options=e,e.dsn&&(this._dsn=Object(L.b)(e.dsn))}return t.prototype.captureException=function(t,e,n){var r=this;if(!Object(o.d)(t)){var i=e&&e.event_id;return this._process(this._getBackend().eventFromException(t,e).then((function(t){return r._captureEvent(t,e,n)})).then((function(t){i=t}))),i}Q&&s.c.log(rt)},t.prototype.captureMessage=function(t,e,n,r){var i=this,o=n&&n.event_id,a=Object(d.j)(t)?this._getBackend().eventFromMessage(String(t),e,n):this._getBackend().eventFromException(t,n);return this._process(a.then((function(t){return i._captureEvent(t,n,r)})).then((function(t){o=t}))),o},t.prototype.captureEvent=function(t,e,n){if(!(e&&e.originalException&&Object(o.d)(e.originalException))){var r=e&&e.event_id;return this._process(this._captureEvent(t,e,n).then((function(t){r=t}))),r}Q&&s.c.log(rt)},t.prototype.captureSession=function(t){this._isEnabled()?"string"!=typeof t.release?Q&&s.c.warn("Discarded session because of missing or non-string release"):(this._sendSession(t),t.update({init:!1})):Q&&s.c.warn("SDK not enabled, will not capture session.")},t.prototype.getDsn=function(){return this._dsn},t.prototype.getOptions=function(){return this._options},t.prototype.getTransport=function(){return this._getBackend().getTransport()},t.prototype.flush=function(t){var e=this;return this._isClientDoneProcessing(t).then((function(n){return e.getTransport().close(t).then((function(t){return n&&t}))}))},t.prototype.close=function(t){var e=this;return this.flush(t).then((function(t){return e.getOptions().enabled=!1,t}))},t.prototype.setupIntegrations=function(){this._isEnabled()&&!this._integrations.initialized&&(this._integrations=nt(this._options))},t.prototype.getIntegration=function(t){try{return this._integrations[t.id]||null}catch(e){return Q&&s.c.warn("Cannot retrieve integration "+t.id+" from the current Client"),null}},t.prototype._updateSessionFromEvent=function(t,e){var n,r,o=!1,a=!1,s=e.exception&&e.exception.values;if(s){a=!0;try{for(var u=Object(i.__values)(s),c=u.next();!c.done;c=u.next()){var f=c.value.mechanism;if(f&&!1===f.handled){o=!0;break}}}catch(t){n={error:t}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}}var d="ok"===t.status;(d&&0===t.errors||d&&o)&&(t.update(Object(i.__assign)(Object(i.__assign)({},o&&{status:"crashed"}),{errors:t.errors||Number(a||o)})),this.captureSession(t))},t.prototype._sendSession=function(t){this._getBackend().sendSession(t)},t.prototype._isClientDoneProcessing=function(t){var e=this;return new p.a((function(n){var r=0,i=setInterval((function(){0==e._numProcessing?(clearInterval(i),n(!0)):(r+=1,t&&r>=t&&(clearInterval(i),n(!1)))}),1)}))},t.prototype._getBackend=function(){return this._backend},t.prototype._isEnabled=function(){return!1!==this.getOptions().enabled&&void 0!==this._dsn},t.prototype._prepareEvent=function(t,e,n){var r=this,s=this.getOptions(),u=s.normalizeDepth,c=void 0===u?3:u,f=s.normalizeMaxBreadth,d=void 0===f?1e3:f,h=Object(i.__assign)(Object(i.__assign)({},t),{event_id:t.event_id||(n&&n.event_id?n.event_id:Object(o.i)()),timestamp:t.timestamp||Object(a.c)()});this._applyClientOptions(h),this._applyIntegrationsMetadata(h);var _=e;n&&n.captureContext&&(_=l.clone(_).update(n.captureContext));var v=Object(p.c)(h);return _&&(v=_.applyToEvent(h,n)),v.then((function(t){return t&&(t.sdkProcessingMetadata=Object(i.__assign)(Object(i.__assign)({},t.sdkProcessingMetadata),{normalizeDepth:Object($.a)(c)+" ("+typeof c+")"})),"number"==typeof c&&c>0?r._normalizeEvent(t,c,d):t}))},t.prototype._normalizeEvent=function(t,e,n){if(!t)return null;var r=Object(i.__assign)(Object(i.__assign)(Object(i.__assign)(Object(i.__assign)(Object(i.__assign)({},t),t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((function(t){return Object(i.__assign)(Object(i.__assign)({},t),t.data&&{data:Object($.a)(t.data,e,n)})}))}),t.user&&{user:Object($.a)(t.user,e,n)}),t.contexts&&{contexts:Object($.a)(t.contexts,e,n)}),t.extra&&{extra:Object($.a)(t.extra,e,n)});return t.contexts&&t.contexts.trace&&(r.contexts.trace=t.contexts.trace),r.sdkProcessingMetadata=Object(i.__assign)(Object(i.__assign)({},r.sdkProcessingMetadata),{baseClientNormalized:!0}),r},t.prototype._applyClientOptions=function(t){var e=this.getOptions(),n=e.environment,r=e.release,i=e.dist,o=e.maxValueLength,a=void 0===o?250:o;"environment"in t||(t.environment="environment"in e?n:"production"),void 0===t.release&&void 0!==r&&(t.release=r),void 0===t.dist&&void 0!==i&&(t.dist=i),t.message&&(t.message=Object(X.e)(t.message,a));var s=t.exception&&t.exception.values&&t.exception.values[0];s&&s.value&&(s.value=Object(X.e)(s.value,a));var u=t.request;u&&u.url&&(u.url=Object(X.e)(u.url,a))},t.prototype._applyIntegrationsMetadata=function(t){var e=Object.keys(this._integrations);e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=Object(i.__spread)(t.sdk.integrations||[],e))},t.prototype._sendEvent=function(t){this._getBackend().sendEvent(t)},t.prototype._captureEvent=function(t,e,n){return this._processEvent(t,e,n).then((function(t){return t.event_id}),(function(t){Q&&s.c.error(t)}))},t.prototype._processEvent=function(t,e,n){var r=this,i=this.getOptions(),o=i.beforeSend,a=i.sampleRate,s=this.getTransport();function u(t,e){s.recordLostEvent&&s.recordLostEvent(t,e)}if(!this._isEnabled())return Object(p.b)(new Z.a("SDK not enabled, will not capture event."));var c="transaction"===t.type;return!c&&"number"==typeof a&&Math.random()>a?(u("sample_rate","event"),Object(p.b)(new Z.a("Discarding event because it's not included in the random sample (sampling rate = "+a+")"))):this._prepareEvent(t,n,e).then((function(n){if(null===n)throw u("event_processor",t.type||"event"),new Z.a("An event processor returned null, will not send event.");return e&&e.data&&!0===e.data.__sentry__||c||!o?n:function(t){var e="`beforeSend` method has to return `null` or a valid event.";if(Object(d.n)(t))return t.then((function(t){if(!Object(d.i)(t)&&null!==t)throw new Z.a(e);return t}),(function(t){throw new Z.a("beforeSend rejected with "+t)}));if(!Object(d.i)(t)&&null!==t)throw new Z.a(e);return t}(o(n,e))})).then((function(e){if(null===e)throw u("before_send",t.type||"event"),new Z.a("`beforeSend` returned `null`, will not send event.");var i=n&&n.getSession&&n.getSession();return!c&&i&&r._updateSessionFromEvent(i,e),r._sendEvent(e),e})).then(null,(function(t){if(t instanceof Z.a)throw t;throw r.captureException(t,{data:{__sentry__:!0},originalException:t}),new Z.a("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: "+t)}))},t.prototype._process=function(t){var e=this;this._numProcessing+=1,t.then((function(t){return e._numProcessing-=1,t}),(function(t){return e._numProcessing-=1,t}))},t}();var ot=n(20);function at(t){if(t.metadata&&t.metadata.sdk){var e=t.metadata.sdk;return{name:e.name,version:e.version}}}function st(t,e){return e?(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=Object(i.__spread)(t.sdk.integrations||[],e.integrations||[]),t.sdk.packages=Object(i.__spread)(t.sdk.packages||[],e.packages||[]),t):t}function ut(t,e){var n=at(e),r=Object(i.__assign)(Object(i.__assign)({sent_at:(new Date).toISOString()},n&&{sdk:n}),!!e.tunnel&&{dsn:Object(L.a)(e.dsn)}),o="aggregates"in t?"sessions":"session",a=[{type:o},t];return[Object(ot.b)(r,[a]),o]}function ct(t,e){var n=Object(i.__read)(ut(t,e),2),r=n[0],o=n[1];return{body:Object(ot.d)(r),type:o,url:Y(e.dsn,e.tunnel)}}function ft(t,e){var n,r=at(e),o=t.type||"event",a="transaction"===o||!!e.tunnel,s=(t.sdkProcessingMetadata||{}).transactionSampling||{},u=s.method,c=s.rate;st(t,e.metadata.sdk),t.tags=t.tags||{},t.extra=t.extra||{},t.sdkProcessingMetadata&&t.sdkProcessingMetadata.baseClientNormalized||(t.tags.skippedNormalization=!0,t.extra.normalizeDepth=t.sdkProcessingMetadata?t.sdkProcessingMetadata.normalizeDepth:"unset"),delete t.sdkProcessingMetadata;try{n=JSON.stringify(t)}catch(e){t.tags.JSONStringifyError=!0,t.extra.JSONStringifyError=e;try{n=JSON.stringify(Object($.a)(t))}catch(t){var f=t;n=JSON.stringify({message:"JSON.stringify error after renormalization",extra:{message:f.message,stack:f.stack}})}}var d={body:n,type:o,url:a?Y(e.dsn,e.tunnel):K(e.dsn)};if(a){var p=Object(i.__assign)(Object(i.__assign)({event_id:t.event_id,sent_at:(new Date).toISOString()},r&&{sdk:r}),!!e.tunnel&&{dsn:Object(L.a)(e.dsn)}),l=[{type:o,sample_rates:[{id:u,rate:c}]},d.body],h=Object(ot.b)(p,[l]);d.body=Object(ot.d)(h)}return d}var dt=function(){function t(){}return t.prototype.sendEvent=function(t){return Object(p.c)({reason:"NoopTransport: Event has been skipped because no Dsn is configured.",status:"skipped"})},t.prototype.close=function(t){return Object(p.c)(!0)},t}(),pt=function(){function t(t){this._options=t,this._options.dsn||Q&&s.c.warn("No DSN provided, backend will not do anything."),this._transport=this._setupTransport()}return t.prototype.eventFromException=function(t,e){throw new Z.a("Backend has to implement `eventFromException` method")},t.prototype.eventFromMessage=function(t,e,n){throw new Z.a("Backend has to implement `eventFromMessage` method")},t.prototype.sendEvent=function(t){if(this._newTransport&&this._options.dsn&&this._options._experiments&&this._options._experiments.newTransport){var e=function(t,e){var n=at(e),r=t.type||"event",o=(t.sdkProcessingMetadata||{}).transactionSampling||{},a=o.method,s=o.rate;st(t,e.metadata.sdk),t.tags=t.tags||{},t.extra=t.extra||{},t.sdkProcessingMetadata&&t.sdkProcessingMetadata.baseClientNormalized||(t.tags.skippedNormalization=!0,t.extra.normalizeDepth=t.sdkProcessingMetadata?t.sdkProcessingMetadata.normalizeDepth:"unset"),delete t.sdkProcessingMetadata;var u=Object(i.__assign)(Object(i.__assign)({event_id:t.event_id,sent_at:(new Date).toISOString()},n&&{sdk:n}),!!e.tunnel&&{dsn:Object(L.a)(e.dsn)}),c=[{type:r,sample_rates:[{id:a,rate:s}]},t];return Object(ot.b)(u,[c])}(t,B(this._options.dsn,this._options._metadata,this._options.tunnel));this._newTransport.send(e).then(null,(function(t){Q&&s.c.error("Error while sending event:",t)}))}else this._transport.sendEvent(t).then(null,(function(t){Q&&s.c.error("Error while sending event:",t)}))},t.prototype.sendSession=function(t){if(this._transport.sendSession)if(this._newTransport&&this._options.dsn&&this._options._experiments&&this._options._experiments.newTransport){var e=B(this._options.dsn,this._options._metadata,this._options.tunnel),n=Object(i.__read)(ut(t,e),1)[0];this._newTransport.send(n).then(null,(function(t){Q&&s.c.error("Error while sending session:",t)}))}else this._transport.sendSession(t).then(null,(function(t){Q&&s.c.error("Error while sending session:",t)}));else Q&&s.c.warn("Dropping session because custom transport doesn't implement sendSession")},t.prototype.getTransport=function(){return this._transport},t.prototype._setupTransport=function(){return new dt},t}();function lt(t,e){!0===e.debug&&(Q?s.c.enable():console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle."));var n=O(),r=n.getScope();r&&r.update(e.initialScope);var i=new t(e);n.bindClient(i)}var ht=n(29),_t=n(30),vt=n(31);function gt(t,e,n){void 0===n&&(n=Object(ht.a)(t.bufferSize||30));var r={};return{send:function(t){var i=Object(ot.c)(t),o="event"===i?"error":i,a={category:o,body:Object(ot.d)(t)};return Object(_t.c)(r,o)?Object(p.b)({status:"rate_limit",reason:bt(r,o)}):n.add((function(){return e(a).then((function(t){var e=t.body,n=t.headers,i=t.reason,a=t.statusCode,s=Object(vt.a)(a);return n&&(r=Object(_t.e)(r,n)),"success"===s?Object(p.c)({status:s,reason:i}):Object(p.b)({status:s,reason:i||e||("rate_limit"===s?bt(r,o):"Unknown transport error")})}))}))},flush:function(t){return n.drain(t)}}}function bt(t,e){return"Too many "+e+" requests, backing off until: "+new Date(Object(_t.b)(t,e)).toISOString()}var yt,mt="6.19.7",Ot=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){yt=Function.prototype.toString,Function.prototype.toString=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Object(v.f)(this)||this;return yt.apply(n,t)}},t.id="FunctionToString",t}(),jt=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],Et=function(){function t(e){void 0===e&&(e={}),this._options=e,this.name=t.id}return t.prototype.setupOnce=function(e,n){e((function(e){var r=n();if(r){var a=r.getIntegration(t);if(a){var u=r.getClient(),c=u?u.getOptions():{};return function(t,e){if(e.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t))return Q&&s.c.warn("Event dropped due to being internal Sentry Error.\nEvent: "+Object(o.e)(t)),!0;if(function(t,e){if(!e||!e.length)return!1;return function(t){if(t.message)return[t.message];if(t.exception)try{var e=t.exception.values&&t.exception.values[0]||{},n=e.type,r=void 0===n?"":n,i=e.value,a=void 0===i?"":i;return[""+a,r+": "+a]}catch(e){return Q&&s.c.error("Cannot extract message for event "+Object(o.e)(t)),[]}return[]}(t).some((function(t){return e.some((function(e){return Object(X.b)(t,e)}))}))}(t,e.ignoreErrors))return Q&&s.c.warn("Event dropped due to being matched by `ignoreErrors` option.\nEvent: "+Object(o.e)(t)),!0;if(function(t,e){if(!e||!e.length)return!1;var n=wt(t);return!!n&&e.some((function(t){return Object(X.b)(n,t)}))}(t,e.denyUrls))return Q&&s.c.warn("Event dropped due to being matched by `denyUrls` option.\nEvent: "+Object(o.e)(t)+".\nUrl: "+wt(t)),!0;if(!function(t,e){if(!e||!e.length)return!0;var n=wt(t);return!n||e.some((function(t){return Object(X.b)(n,t)}))}(t,e.allowUrls))return Q&&s.c.warn("Event dropped due to not being matched by `allowUrls` option.\nEvent: "+Object(o.e)(t)+".\nUrl: "+wt(t)),!0;return!1}(e,function(t,e){void 0===t&&(t={});void 0===e&&(e={});return{allowUrls:Object(i.__spread)(t.whitelistUrls||[],t.allowUrls||[],e.whitelistUrls||[],e.allowUrls||[]),denyUrls:Object(i.__spread)(t.blacklistUrls||[],t.denyUrls||[],e.blacklistUrls||[],e.denyUrls||[]),ignoreErrors:Object(i.__spread)(t.ignoreErrors||[],e.ignoreErrors||[],jt),ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(a._options,c))?null:e}}return e}))},t.id="InboundFilters",t}();function St(t){void 0===t&&(t=[]);for(var e=t.length-1;e>=0;e--){var n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}function wt(t){try{if(t.stacktrace)return St(t.stacktrace.frames);var e;try{e=t.exception.values[0].stacktrace.frames}catch(t){}return e?St(e):null}catch(e){return Q&&s.c.error("Cannot extract url for event "+Object(o.e)(t)),null}}},function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__},function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var r=n(0),i=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){return t.__proto__=e,t}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n]);return t});var o=function(t){function e(e){var n=this.constructor,r=t.call(this,e)||this;return r.message=e,r.name=n.prototype.constructor.name,i(r,n.prototype),r}return Object(r.__extends)(e,t),e}(Error)},function(t,e,n){"use strict";n.d(e,"e",(function(){return i})),n.d(e,"d",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return u}));var r=n(1);function i(t,e){return void 0===e&&(e=0),"string"!=typeof t||0===e||t.length<=e?t:t.substr(0,e)+"..."}function o(t,e){var n=t,r=n.length;if(r<=150)return n;e>r&&(e=r);var i=Math.max(e-60,0);i<5&&(i=0);var o=Math.min(i+140,r);return o>r-5&&(o=r),o===r&&(i=Math.max(o-140,0)),n=n.slice(i,o),i>0&&(n="'{snip} "+n),o<r&&(n+=" {snip}"),n}function a(t,e){if(!Array.isArray(t))return"";for(var n=[],r=0;r<t.length;r++){var i=t[r];try{n.push(String(i))}catch(t){n.push("[value cannot be serialized]")}}return n.join(e)}function s(t,e){return!!Object(r.l)(t)&&(Object(r.k)(e)?e.test(t):"string"==typeof e&&-1!==t.indexOf(e))}function u(t){return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}},function(t,e,n){"use strict";n.d(e,"a",(function(){return u})),n.d(e,"b",(function(){return c})),n.d(e,"c",(function(){return r}));var r,i=n(0),o=n(6),a=n(3),s=Object(a.a)(),u=["debug","info","warn","error","log","assert"];function c(t){var e=Object(a.a)();if(!("console"in e))return t();var n=e.console,r={};u.forEach((function(t){var i=n[t]&&n[t].__sentry_original__;t in e.console&&i&&(r[t]=n[t],n[t]=i)}));try{return t()}finally{Object.keys(r).forEach((function(t){n[t]=r[t]}))}}function f(){var t=!1,e={enable:function(){t=!0},disable:function(){t=!1}};return o.a?u.forEach((function(n){e[n]=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];t&&c((function(){var t;(t=s.console)[n].apply(t,Object(i.__spread)(["Sentry Logger ["+n+"]:"],e))}))}})):u.forEach((function(t){e[t]=function(){}})),e}r=o.a?Object(a.b)("logger",f):f()},function(t,e,n){"use strict";(function(t,r){n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return s}));var i=n(17);function o(){return!Object(i.a)()&&"[object process]"===Object.prototype.toString.call(void 0!==t?t:0)}function a(t,e){return t.require(e)}function s(t){var e;try{e=a(r,t)}catch(t){}try{var n=a(r,"process").cwd;e=a(r,n()+"/node_modules/"+t)}catch(t){}return e}}).call(this,n(39),n(24)(t))},function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"b",(function(){return a}));var r=n(0);function i(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t.sort((function(t,e){return t[0]-e[0]})).map((function(t){return t[1]}));return function(t,e){var i,a,s,u;void 0===e&&(e=0);var c=[];try{for(var f=Object(r.__values)(t.split("\n").slice(e)),d=f.next();!d.done;d=f.next()){var p=d.value;try{for(var l=(s=void 0,Object(r.__values)(n)),h=l.next();!h.done;h=l.next()){var _=(0,h.value)(p);if(_){c.push(_);break}}}catch(t){s={error:t}}finally{try{h&&!h.done&&(u=l.return)&&u.call(l)}finally{if(s)throw s.error}}}}catch(t){i={error:t}}finally{try{d&&!d.done&&(a=f.return)&&a.call(f)}finally{if(i)throw i.error}}return o(c)}}function o(t){if(!t.length)return[];var e=t,n=e[0].function||"",i=e[e.length-1].function||"";return-1===n.indexOf("captureMessage")&&-1===n.indexOf("captureException")||(e=e.slice(1)),-1!==i.indexOf("sentryWrapped")&&(e=e.slice(0,-1)),e.slice(0,50).map((function(t){return Object(r.__assign)(Object(r.__assign)({},t),{filename:t.filename||e[0].filename,function:t.function||"?"})})).reverse()}function a(t){try{return t&&"function"==typeof t&&t.name||"<anonymous>"}catch(t){return"<anonymous>"}}},function(t,e,n){"use strict";n.d(e,"c",(function(){return i})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return a}));var r=n(1);function i(t){return new a((function(e){e(t)}))}function o(t){return new a((function(e,n){n(t)}))}var a=function(){function t(t){var e=this;this._state=0,this._handlers=[],this._resolve=function(t){e._setResult(1,t)},this._reject=function(t){e._setResult(2,t)},this._setResult=function(t,n){0===e._state&&(Object(r.n)(n)?n.then(e._resolve,e._reject):(e._state=t,e._value=n,e._executeHandlers()))},this._executeHandlers=function(){if(0!==e._state){var t=e._handlers.slice();e._handlers=[],t.forEach((function(t){t[0]||(1===e._state&&t[1](e._value),2===e._state&&t[2](e._value),t[0]=!0)}))}};try{t(this._resolve,this._reject)}catch(t){this._reject(t)}}return t.prototype.then=function(e,n){var r=this;return new t((function(t,i){r._handlers.push([!1,function(n){if(e)try{t(e(n))}catch(t){i(t)}else t(n)},function(e){if(n)try{t(n(e))}catch(t){i(t)}else i(e)}]),r._executeHandlers()}))},t.prototype.catch=function(t){return this.then((function(t){return t}),t)},t.prototype.finally=function(e){var n=this;return new t((function(t,r){var i,o;return n.then((function(t){o=!1,i=t,e&&e()}),(function(t){o=!0,i=t,e&&e()})).then((function(){o?r(i):t(i)}))}))},t}()},,function(t,e,n){"use strict";var r;n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return i})),function(t){t.Fatal="fatal",t.Error="error",t.Warning="warning",t.Log="log",t.Info="info",t.Debug="debug",t.Critical="critical"}(r||(r={}));var i=["fatal","error","warning","log","info","debug","critical"]},function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.appName=e.sdk=void 0;var n={request:function(){},httpRequest:function(){},getSystemInfoSync:function(){},getSystemInfo:function(){}},r=("object"==typeof uni?n=uni:"object"==typeof wx?n=wx:"object"==typeof my?n=my:"object"==typeof tt?n=tt:"object"==typeof dd?n=dd:"object"==typeof qq?n=qq:"object"==typeof swan?n=swan:console.log("sentry-uniapp æš‚ä¸æ”¯æŒæ­¤å¹³å°, å¿«åº”ç”¨è¯·ä½¿ç”¨ sentry-quickapp"),n);e.sdk=r;var i,o=(i="unknown","object"==typeof uni?i="uniapp":"object"==typeof wx?i="wechat":"object"==typeof my?i="alipay":"object"==typeof tt?i="bytedance":"object"==typeof dd?i="dingtalk":"object"==typeof qq?i="qq":"object"==typeof swan&&(i="swan"),i);e.appName=o},function(t,e,n){"use strict";(function(t){n.d(e,"c",(function(){return c})),n.d(e,"d",(function(){return f})),n.d(e,"e",(function(){return d})),n.d(e,"f",(function(){return p})),n.d(e,"a",(function(){return a})),n.d(e,"b",(function(){return l}));var r=n(3),i=n(10),o={nowSeconds:function(){return Date.now()/1e3}};var a,s=Object(i.b)()?function(){try{return Object(i.a)(t,"perf_hooks").performance}catch(t){return}}():function(){var t=Object(r.a)().performance;if(t&&t.now)return{now:function(){return t.now()},timeOrigin:Date.now()-t.now()}}(),u=void 0===s?o:{nowSeconds:function(){return(s.timeOrigin+s.now())/1e3}},c=o.nowSeconds.bind(o),f=u.nowSeconds.bind(u),d=f,p=void 0!==s,l=function(){var t=Object(r.a)().performance;if(t&&t.now){var e=t.now(),n=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+e-n):36e5,o=i<36e5,s=t.timing&&t.timing.navigationStart,u="number"==typeof s?Math.abs(s+e-n):36e5;return o||u<36e5?i<=u?(a="timeOrigin",t.timeOrigin):(a="navigationStart",s):(a="dateNow",n)}a="none"}()}).call(this,n(24)(t))},function(t,e,n){"use strict";function r(){return"undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&!!__SENTRY_BROWSER_BUNDLE__}n.d(e,"a",(function(){return r}))},function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return s}));var r=n(3),i=n(1);function o(t,e){try{for(var n=t,r=[],i=0,o=0,s=" > ".length,u=void 0;n&&i++<5&&!("html"===(u=a(n,e))||i>1&&o+r.length*s+u.length>=80);)r.push(u),o+=u.length,n=n.parentNode;return r.reverse().join(" > ")}catch(t){return"<unknown>"}}function a(t,e){var n,r,o,a,s,u=t,c=[];if(!u||!u.tagName)return"";c.push(u.tagName.toLowerCase());var f=e&&e.length?e.filter((function(t){return u.getAttribute(t)})).map((function(t){return[t,u.getAttribute(t)]})):null;if(f&&f.length)f.forEach((function(t){c.push("["+t[0]+'="'+t[1]+'"]')}));else if(u.id&&c.push("#"+u.id),(n=u.className)&&Object(i.l)(n))for(r=n.split(/\s+/),s=0;s<r.length;s++)c.push("."+r[s]);var d=["type","name","title","alt"];for(s=0;s<d.length;s++)o=d[s],(a=u.getAttribute(o))&&c.push("["+o+'="'+a+'"]');return c.join("")}function s(){var t=Object(r.a)();try{return t.document.location.href}catch(t){return""}}},function(t,e,n){"use strict";function r(){var t="function"==typeof WeakSet,e=t?new WeakSet:[];return[function(n){if(t)return!!e.has(n)||(e.add(n),!1);for(var r=0;r<e.length;r++){if(e[r]===n)return!0}return e.push(n),!1},function(n){if(t)e.delete(n);else for(var r=0;r<e.length;r++)if(e[r]===n){e.splice(r,1);break}}]}n.d(e,"a",(function(){return r}))},function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"d",(function(){return u}));var r=n(0),i=n(1);function o(t,e){return void 0===e&&(e=[]),[t,e]}function a(t,e){var n=Object(r.__read)(t,2),i=n[0],o=n[1];return[i,Object(r.__spread)(o,[e])]}function s(t){var e=Object(r.__read)(t,2),n=Object(r.__read)(e[1],1);return Object(r.__read)(n[0],1)[0].type}function u(t){var e=Object(r.__read)(t,2),n=e[0],o=e[1],a=JSON.stringify(n);return o.reduce((function(t,e){var n=Object(r.__read)(e,2),o=n[0],a=n[1],s=Object(i.j)(a)?String(a):JSON.stringify(a);return t+"\n"+JSON.stringify(o)+"\n"+s}),a)}},function(t,e,n){"use strict";n.r(e);var r=n(14);n.d(e,"Severity",(function(){return r.a})),n.d(e,"SeverityLevels",(function(){return r.b}))},function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.SDK_VERSION=e.SDK_NAME=void 0,e.SDK_NAME="sentry.javascript.miniapp",e.SDK_VERSION="1.0.10"},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.computeStackTrace=void 0;var r=n(0),i=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|[-a-z]+:|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,o=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js))(?::(\d+))?(?::(\d+))?\s*$/i,a=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,s=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,u=/\((\S*)(?::(\d+))(?::(\d+))\)/,c=/^\s*at (\w.*) \((\w*.js):(\d*):(\d*)/i;function f(t,e){try{return r.__assign(r.__assign({},t),{stack:t.stack.slice(e)})}catch(e){return t}}function d(t){var e=t&&t.message;return e?e.error&&"string"==typeof e.error.message?e.error.message:e:"No error message"}e.computeStackTrace=function(t){var e=null,n=t&&t.framesToPop;try{if(e=function(t){if(!t||!t.stacktrace)return null;for(var e,n=t.stacktrace,r=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,i=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^\)]+))\((.*)\))? in (.*):\s*$/i,o=n.split("\n"),a=[],s=0;s<o.length;s+=2){var u=null;(e=r.exec(o[s]))?u={url:e[2],func:e[3],args:[],line:+e[1],column:null}:(e=i.exec(o[s]))&&(u={url:e[6],func:e[3]||e[4],args:e[5]?e[5].split(","):[],line:+e[1],column:+e[2]}),u&&(!u.func&&u.line&&(u.func="?"),a.push(u))}if(!a.length)return null;return{message:d(t),name:t.name,stack:a}}(t))return f(e,n)}catch(t){}try{if(e=function(t){if(!t||!t.stack)return null;for(var e,n,r,f=[],p=t.stack.split("\n"),l=0;l<p.length;++l){if(n=i.exec(p[l])){var h=n[2]&&0===n[2].indexOf("native");n[2]&&0===n[2].indexOf("eval")&&(e=u.exec(n[2]))&&(n[2]=e[1],n[3]=e[2],n[4]=e[3]),r={url:n[2],func:n[1]||"?",args:h?[n[2]]:[],line:n[3]?+n[3]:null,column:n[4]?+n[4]:null}}else if(n=a.exec(p[l]))r={url:n[2],func:n[1]||"?",args:[],line:+n[3],column:n[4]?+n[4]:null};else if(n=o.exec(p[l]))n[3]&&n[3].indexOf(" > eval")>-1&&(e=s.exec(n[3]))?(n[1]=n[1]||"eval",n[3]=e[1],n[4]=e[2],n[5]=""):0!==l||n[5]||void 0===t.columnNumber||(f[0].column=t.columnNumber+1),r={url:n[3],func:n[1]||"?",args:n[2]?n[2].split(","):[],line:n[4]?+n[4]:null,column:n[5]?+n[5]:null};else{if(!(n=c.exec(p[l])))continue;r={url:n[2],func:n[1]||"?",args:[],line:n[3]?+n[3]:null,column:n[4]?+n[4]:null}}!r.func&&r.line&&(r.func="?"),f.push(r)}if(!f.length)return null;return{message:d(t),name:t.name,stack:f}}(t))return f(e,n)}catch(t){}return{message:d(t),name:t&&t.name,stack:[],failed:!0}}},function(t,e){t.exports=function(t){if(!t.webpackPolyfill){var e=Object.create(t);e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),Object.defineProperty(e,"exports",{enumerable:!0}),e.webpackPolyfill=1}return e}},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){"use strict";n.d(e,"i",(function(){return s})),n.d(e,"g",(function(){return u})),n.d(e,"e",(function(){return f})),n.d(e,"c",(function(){return d})),n.d(e,"b",(function(){return p})),n.d(e,"f",(function(){return h})),n.d(e,"a",(function(){return _})),n.d(e,"h",(function(){return v})),n.d(e,"d",(function(){return g}));var r=n(0),i=n(3),o=n(4),a=n(8);function s(){var t=Object(i.a)(),e=t.crypto||t.msCrypto;if(void 0!==e&&e.getRandomValues){var n=new Uint16Array(8);e.getRandomValues(n),n[3]=4095&n[3]|16384,n[4]=16383&n[4]|32768;var r=function(t){for(var e=t.toString(16);e.length<4;)e="0"+e;return e};return r(n[0])+r(n[1])+r(n[2])+r(n[3])+r(n[4])+r(n[5])+r(n[6])+r(n[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))}function u(t){if(!t)return{};var e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};var n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],relative:e[5]+n+r}}function c(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function f(t){var e=t.message,n=t.event_id;if(e)return e;var r=c(t);return r?r.type&&r.value?r.type+": "+r.value:r.type||r.value||n||"<unknown>":n||"<unknown>"}function d(t,e,n){var r=t.exception=t.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{};o.value||(o.value=e||""),o.type||(o.type=n||"Error")}function p(t,e){var n=c(t);if(n){var i=n.mechanism;if(n.mechanism=Object(r.__assign)(Object(r.__assign)(Object(r.__assign)({},{type:"generic",handled:!0}),i),e),e&&"data"in e){var o=Object(r.__assign)(Object(r.__assign)({},i&&i.data),e.data);n.mechanism.data=o}}}var l=/^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;function h(t){var e=t.match(l)||[],n=parseInt(e[1],10),r=parseInt(e[2],10),i=parseInt(e[3],10);return{buildmetadata:e[5],major:isNaN(n)?void 0:n,minor:isNaN(r)?void 0:r,patch:isNaN(i)?void 0:i,prerelease:e[4]}}function _(t,e,n){void 0===n&&(n=5);var r=e.lineno||0,i=t.length,o=Math.max(Math.min(i,r-1),0);e.pre_context=t.slice(Math.max(0,o-n),o).map((function(t){return Object(a.d)(t,0)})),e.context_line=Object(a.d)(t[Math.min(i-1,o)],e.colno||0),e.post_context=t.slice(Math.min(o+1,i),o+1+n).map((function(t){return Object(a.d)(t,0)}))}function v(t){return t.split(/[\?#]/,1)[0]}function g(t){if(t&&t.__sentry_captured__)return!0;try{Object(o.a)(t,"__sentry_captured__",!0)}catch(t){}return!1}},function(t,e,n){"use strict";n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return c}));var r=n(0),i=n(7),o=n(6),a=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/;function s(t,e){void 0===e&&(e=!1);var n=t.host,r=t.path,i=t.pass,o=t.port,a=t.projectId;return t.protocol+"://"+t.publicKey+(e&&i?":"+i:"")+"@"+n+(o?":"+o:"")+"/"+(r?r+"/":r)+a}function u(t){return"user"in t&&!("publicKey"in t)&&(t.publicKey=t.user),{user:t.publicKey||"",protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function c(t){var e="string"==typeof t?function(t){var e=a.exec(t);if(!e)throw new i.a("Invalid Sentry Dsn: "+t);var n=Object(r.__read)(e.slice(1),6),o=n[0],s=n[1],c=n[2],f=void 0===c?"":c,d=n[3],p=n[4],l=void 0===p?"":p,h="",_=n[5],v=_.split("/");if(v.length>1&&(h=v.slice(0,-1).join("/"),_=v.pop()),_){var g=_.match(/^\d+/);g&&(_=g[0])}return u({host:d,pass:f,path:h,projectId:_,port:l,protocol:o,publicKey:s})}(t):u(t);return function(t){if(o.a){var e=t.port,n=t.projectId,r=t.protocol;if(["protocol","publicKey","host","projectId"].forEach((function(e){if(!t[e])throw new i.a("Invalid Sentry Dsn: "+e+" missing")})),!n.match(/^\d+$/))throw new i.a("Invalid Sentry Dsn: Invalid projectId "+n);if(!function(t){return"http"===t||"https"===t}(r))throw new i.a("Invalid Sentry Dsn: Invalid protocol "+r);if(e&&isNaN(parseInt(e,10)))throw new i.a("Invalid Sentry Dsn: Invalid port "+e)}}(e),e}},function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return u})),n.d(e,"b",(function(){return c})),n.d(e,"c",(function(){return f}));var r=n(0),i=n(1),o=n(19),a=n(4),s=n(11);function u(t,e,n){void 0===e&&(e=1/0),void 0===n&&(n=1/0);try{return f("",t,e,n)}catch(t){return{ERROR:"**non-serializable** ("+t+")"}}}function c(t,e,n){void 0===e&&(e=3),void 0===n&&(n=102400);var r,i=u(t,e);return r=i,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(r))>n?c(t,e-1,n):i}function f(e,n,u,c,d){void 0===u&&(u=1/0),void 0===c&&(c=1/0),void 0===d&&(d=Object(o.a)());var p=Object(r.__read)(d,2),l=p[0],h=p[1],_=n;if(_&&"function"==typeof _.toJSON)try{return _.toJSON()}catch(t){}if(null===n||["number","boolean","string"].includes(typeof n)&&!Object(i.h)(n))return n;var v=function(e,n){try{return"domain"===e&&n&&"object"==typeof n&&n._events?"[Domain]":"domainEmitter"===e?"[DomainEmitter]":void 0!==t&&n===t?"[Global]":"undefined"!=typeof window&&n===window?"[Window]":"undefined"!=typeof document&&n===document?"[Document]":Object(i.m)(n)?"[SyntheticEvent]":"number"==typeof n&&n!=n?"[NaN]":void 0===n?"[undefined]":"function"==typeof n?"[Function: "+Object(s.b)(n)+"]":"symbol"==typeof n?"["+String(n)+"]":"bigint"==typeof n?"[BigInt: "+String(n)+"]":"[object "+Object.getPrototypeOf(n).constructor.name+"]"}catch(t){return"**non-serializable** ("+t+")"}}(e,n);if(!v.startsWith("[object "))return v;if(0===u)return v.replace("object ","");if(l(n))return"[Circular ~]";var g=Array.isArray(n)?[]:{},b=0,y=Object(i.d)(n)||Object(i.f)(n)?Object(a.b)(n):n;for(var m in y)if(Object.prototype.hasOwnProperty.call(y,m)){if(b>=c){g[m]="[MaxProperties ~]";break}var O=y[m];g[m]=f(m,O,u-1,c,d),b+=1}return h(n),g}}).call(this,n(25))},function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var r=n(7),i=n(12);function o(t){var e=[];function n(t){return e.splice(e.indexOf(t),1)[0]}return{$:e,add:function(o){if(!(void 0===t||e.length<t))return Object(i.b)(new r.a("Not adding Promise due to buffer limit reached."));var a=o();return-1===e.indexOf(a)&&e.push(a),a.then((function(){return n(a)})).then(null,(function(){return n(a).then(null,(function(){}))})),a},drain:function(t){return new i.a((function(n,r){var o=e.length;if(!o)return n(!0);var a=setTimeout((function(){t&&t>0&&n(!1)}),t);e.forEach((function(t){Object(i.c)(t).then((function(){--o||(clearTimeout(a),n(!0))}),r)}))}))}}}},function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"e",(function(){return u}));var r=n(0),i=6e4;function o(t,e){void 0===e&&(e=Date.now());var n=parseInt(""+t,10);if(!isNaN(n))return 1e3*n;var r=Date.parse(""+t);return isNaN(r)?i:r-e}function a(t,e){return t[e]||t.all||0}function s(t,e,n){return void 0===n&&(n=Date.now()),a(t,e)>n}function u(t,e,n){var i,a,s,u;void 0===n&&(n=Date.now());var c=Object(r.__assign)({},t),f=e["x-sentry-rate-limits"],d=e["retry-after"];if(f)try{for(var p=Object(r.__values)(f.trim().split(",")),l=p.next();!l.done;l=p.next()){var h=l.value.split(":",2),_=parseInt(h[0],10),v=1e3*(isNaN(_)?60:_);if(h[1])try{for(var g=(s=void 0,Object(r.__values)(h[1].split(";"))),b=g.next();!b.done;b=g.next()){c[b.value]=n+v}}catch(t){s={error:t}}finally{try{b&&!b.done&&(u=g.return)&&u.call(g)}finally{if(s)throw s.error}}else c.all=n+v}}catch(t){i={error:t}}finally{try{l&&!l.done&&(a=p.return)&&a.call(p)}finally{if(i)throw i.error}}else d&&(c.all=n+o(d,n));return c}},function(t,e,n){"use strict";function r(t){return t>=200&&t<300?"success":429===t?"rate_limit":t>=400&&t<500?"invalid":t>=500?"failed":"unknown"}n.d(e,"a",(function(){return r}))},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.MiniappClient=void 0;var r=n(0),i=n(5),o=n(41),a=n(22),s=function(t){function e(e){return void 0===e&&(e={}),t.call(this,o.MiniappBackend,e)||this}return r.__extends(e,t),e.prototype._prepareEvent=function(e,n,i){return e.platform=e.platform||"javascript",e.sdk=r.__assign(r.__assign({},e.sdk),{name:a.SDK_NAME,packages:r.__spread(e.sdk&&e.sdk.packages||[],[{name:"npm:sentry-uniapp",version:a.SDK_VERSION}]),version:a.SDK_VERSION}),t.prototype._prepareEvent.call(this,e,n,i)},e.prototype.showReportDialog=function(t){void 0===t&&(t={}),console.log("sentry-uniapp æš‚æœªå®žçŽ°è¯¥æ–¹æ³•",t)},e}(i.BaseClient);e.MiniappClient=s},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.prepareFramesForEvent=e.eventFromStacktrace=e.eventFromPlainObject=e.exceptionFromStacktrace=void 0;var r=n(2),i=n(23);function o(t){var e=a(t.stack),n={type:t.name,value:t.message};return e&&e.length&&(n.stacktrace={frames:e}),void 0===n.type&&""===n.value&&(n.value="Unrecoverable error caught"),n}function a(t){if(!t||!t.length)return[];var e=t,n=e[0].func||"",r=e[e.length-1].func||"";return-1===n.indexOf("captureMessage")&&-1===n.indexOf("captureException")||(e=e.slice(1)),-1!==r.indexOf("sentryWrapped")&&(e=e.slice(0,-1)),e.map((function(t){return{colno:null===t.column?void 0:t.column,filename:t.url||e[0].url,function:t.func||"?",in_app:!0,lineno:null===t.line?void 0:t.line}})).slice(0,100).reverse()}e.exceptionFromStacktrace=o,e.eventFromPlainObject=function(t,e,n){var o={exception:{values:[{type:r.isEvent(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:"Non-Error "+(n?"promise rejection":"exception")+" captured with keys: "+r.extractExceptionKeysForMessage(t)}]},extra:{__serialized__:r.normalizeToSize(t)}};if(e){var s=a(i.computeStackTrace(e).stack);o.stacktrace={frames:s}}return o},e.eventFromStacktrace=function(t){return{exception:{values:[o(t)]}}},e.prepareFramesForEvent=a},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0});var r=n(35);Object.defineProperty(e,"BaseTransport",{enumerable:!0,get:function(){return r.BaseTransport}});var i=n(43);Object.defineProperty(e,"XHRTransport",{enumerable:!0,get:function(){return i.XHRTransport}})},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.BaseTransport=void 0;var r=n(5),i=n(2),o=function(){function t(t){this.options=t,this._buffer=i.makePromiseBuffer(30),this.url=new r.API(this.options.dsn).getStoreEndpointWithUrlEncodedAuth()}return t.prototype.sendEvent=function(t){throw new i.SentryError("Transport Class has to implement `sendEvent` method")},t.prototype.close=function(t){return this._buffer.drain(t)},t}();e.BaseTransport=o},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.keypressEventHandler=e.breadcrumbEventHandler=e.wrap=e.ignoreNextOnError=e.shouldIgnoreOnError=void 0;var r,i,o=n(0),a=n(5),s=n(2),u=0;function c(){u+=1,setTimeout((function(){u-=1}))}e.shouldIgnoreOnError=function(){return u>0},e.ignoreNextOnError=c,e.wrap=function t(e,n,r){if(void 0===n&&(n={}),"function"!=typeof e)return e;try{if(e.__sentry__)return e;if(e.__sentry_wrapped__)return e.__sentry_wrapped__}catch(t){return e}var i=function(){r&&"function"==typeof r&&r.apply(this,arguments);var i=Array.prototype.slice.call(arguments);try{var u=i.map((function(e){return t(e,n)}));return e.handleEvent?e.handleEvent.apply(this,u):e.apply(this,u)}catch(t){throw c(),a.withScope((function(e){e.addEventProcessor((function(t){var e=o.__assign({},t);return n.mechanism&&(s.addExceptionTypeValue(e,void 0,void 0),s.addExceptionMechanism(e,n.mechanism)),e.extra=o.__assign(o.__assign({},e.extra),{arguments:s.normalize(i,3)}),e})),a.captureException(t)})),t}};try{for(var u in e)Object.prototype.hasOwnProperty.call(e,u)&&(i[u]=e[u])}catch(t){}e.prototype=e.prototype||{},i.prototype=e.prototype,Object.defineProperty(e,"__sentry_wrapped__",{enumerable:!1,value:i}),Object.defineProperties(i,{__sentry__:{enumerable:!1,value:!0},__sentry_original__:{enumerable:!1,value:e}});try{Object.getOwnPropertyDescriptor(i,"name").configurable&&Object.defineProperty(i,"name",{get:function(){return e.name}})}catch(t){}return i};var f=0;function d(t,e){return void 0===e&&(e=!1),function(n){if(r=void 0,n&&i!==n){i=n;var o=function(){var e;try{e=n.target?s.htmlTreeAsString(n.target):s.htmlTreeAsString(n)}catch(t){e="<unknown>"}0!==e.length&&a.getCurrentHub().addBreadcrumb({category:"ui."+t,message:e},{event:n,name:t})};f&&clearTimeout(f),e?f=setTimeout(o):o()}}}e.breadcrumbEventHandler=d,e.keypressEventHandler=function(){return function(t){var e;try{e=t.target}catch(t){return}var n=e&&e.tagName;n&&("INPUT"===n||"TEXTAREA"===n||e.isContentEditable)&&(r||d("input")(t),clearTimeout(r),r=setTimeout((function(){r=void 0}),1e3))}}},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0});var r=n(44);Object.defineProperty(e,"GlobalHandlers",{enumerable:!0,get:function(){return r.GlobalHandlers}});var i=n(45);Object.defineProperty(e,"TryCatch",{enumerable:!0,get:function(){return i.TryCatch}});var o=n(46);Object.defineProperty(e,"LinkedErrors",{enumerable:!0,get:function(){return o.LinkedErrors}});var a=n(47);Object.defineProperty(e,"System",{enumerable:!0,get:function(){return a.System}});var s=n(48);Object.defineProperty(e,"Router",{enumerable:!0,get:function(){return s.Router}});var u=n(49);Object.defineProperty(e,"IgnoreMpcrawlerErrors",{enumerable:!0,get:function(){return u.IgnoreMpcrawlerErrors}})},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.Transports=e.Integrations=void 0;var r=n(21);Object.defineProperty(e,"Severity",{enumerable:!0,get:function(){return r.Severity}});var i=n(5);Object.defineProperty(e,"addGlobalEventProcessor",{enumerable:!0,get:function(){return i.addGlobalEventProcessor}}),Object.defineProperty(e,"addBreadcrumb",{enumerable:!0,get:function(){return i.addBreadcrumb}}),Object.defineProperty(e,"captureException",{enumerable:!0,get:function(){return i.captureException}}),Object.defineProperty(e,"captureEvent",{enumerable:!0,get:function(){return i.captureEvent}}),Object.defineProperty(e,"captureMessage",{enumerable:!0,get:function(){return i.captureMessage}}),Object.defineProperty(e,"configureScope",{enumerable:!0,get:function(){return i.configureScope}}),Object.defineProperty(e,"getHubFromCarrier",{enumerable:!0,get:function(){return i.getHubFromCarrier}}),Object.defineProperty(e,"getCurrentHub",{enumerable:!0,get:function(){return i.getCurrentHub}}),Object.defineProperty(e,"Hub",{enumerable:!0,get:function(){return i.Hub}}),Object.defineProperty(e,"Scope",{enumerable:!0,get:function(){return i.Scope}}),Object.defineProperty(e,"setContext",{enumerable:!0,get:function(){return i.setContext}}),Object.defineProperty(e,"setExtra",{enumerable:!0,get:function(){return i.setExtra}}),Object.defineProperty(e,"setExtras",{enumerable:!0,get:function(){return i.setExtras}}),Object.defineProperty(e,"setTag",{enumerable:!0,get:function(){return i.setTag}}),Object.defineProperty(e,"setTags",{enumerable:!0,get:function(){return i.setTags}}),Object.defineProperty(e,"setUser",{enumerable:!0,get:function(){return i.setUser}}),Object.defineProperty(e,"withScope",{enumerable:!0,get:function(){return i.withScope}});var o=n(22);Object.defineProperty(e,"SDK_NAME",{enumerable:!0,get:function(){return o.SDK_NAME}}),Object.defineProperty(e,"SDK_VERSION",{enumerable:!0,get:function(){return o.SDK_VERSION}});var a=n(40);Object.defineProperty(e,"defaultIntegrations",{enumerable:!0,get:function(){return a.defaultIntegrations}}),Object.defineProperty(e,"init",{enumerable:!0,get:function(){return a.init}}),Object.defineProperty(e,"lastEventId",{enumerable:!0,get:function(){return a.lastEventId}}),Object.defineProperty(e,"showReportDialog",{enumerable:!0,get:function(){return a.showReportDialog}}),Object.defineProperty(e,"flush",{enumerable:!0,get:function(){return a.flush}}),Object.defineProperty(e,"close",{enumerable:!0,get:function(){return a.close}}),Object.defineProperty(e,"wrap",{enumerable:!0,get:function(){return a.wrap}});var s=n(32);Object.defineProperty(e,"MiniappClient",{enumerable:!0,get:function(){return s.MiniappClient}});var u=n(37);e.Integrations=u;var c=n(34);e.Transports=c},function(t,e){var n,r,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(t){if(n===setTimeout)return setTimeout(t,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(t){n=o}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var u,c=[],f=!1,d=-1;function p(){f&&u&&(f=!1,u.length?c=u.concat(c):d=-1,c.length&&l())}function l(){if(!f){var t=s(p);f=!0;for(var e=c.length;e;){for(u=c,c=[];++d<e;)u&&u[d].run();d=-1,e=c.length}u=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(t)}}function h(t,e){this.fun=t,this.array=e}function _(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];c.push(new h(t,e)),1!==c.length||f||s(l)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=_,i.addListener=_,i.once=_,i.off=_,i.removeListener=_,i.removeAllListeners=_,i.emit=_,i.prependListener=_,i.prependOnceListener=_,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.wrap=e.close=e.flush=e.lastEventId=e.showReportDialog=e.init=e.defaultIntegrations=void 0;var r=n(5),i=n(2),o=n(32),a=n(36),s=n(37);e.defaultIntegrations=[new r.Integrations.InboundFilters,new r.Integrations.FunctionToString,new s.TryCatch,new s.GlobalHandlers,new s.LinkedErrors,new s.System,new s.Router,new s.IgnoreMpcrawlerErrors],e.init=function(t){void 0===t&&(t={}),void 0===t.defaultIntegrations&&(t.defaultIntegrations=e.defaultIntegrations),t.normalizeDepth=t.normalizeDepth||5,t.defaultIntegrations&&t.defaultIntegrations[3].setExtraOptions(t.extraOptions),r.initAndBind(o.MiniappClient,t)},e.showReportDialog=function(t){void 0===t&&(t={}),t.eventId||(t.eventId=r.getCurrentHub().lastEventId());var e=r.getCurrentHub().getClient();e&&e.showReportDialog(t)},e.lastEventId=function(){return r.getCurrentHub().lastEventId()},e.flush=function(t){var e=r.getCurrentHub().getClient();return e?e.flush(t):i.resolvedSyncPromise(!1)},e.close=function(t){var e=r.getCurrentHub().getClient();return e?e.close(t):i.resolvedSyncPromise(!1)},e.wrap=function(t){return a.wrap(t)()}},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.MiniappBackend=void 0;var r=n(0),i=n(5),o=n(21),a=n(2),s=n(42),u=n(34),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(e,t),e.prototype._setupTransport=function(){if(!this._options.dsn)return t.prototype._setupTransport.call(this);var e=r.__assign(r.__assign({},this._options.transportOptions),{dsn:this._options.dsn});return this._options.transport?new this._options.transport(e):new u.XHRTransport(e)},e.prototype.eventFromException=function(t,e){var n=e&&e.syntheticException||void 0,r=s.eventFromUnknownInput(t,n,{attachStacktrace:this._options.attachStacktrace});return a.addExceptionMechanism(r,{handled:!0,type:"generic"}),r.level=o.Severity.Error,e&&e.event_id&&(r.event_id=e.event_id),a.resolvedSyncPromise(r)},e.prototype.eventFromMessage=function(t,e,n){void 0===e&&(e=o.Severity.Info);var r=n&&n.syntheticException||void 0,i=s.eventFromString(t,r,{attachStacktrace:this._options.attachStacktrace});return i.level=e,n&&n.event_id&&(i.event_id=n.event_id),a.resolvedSyncPromise(i)},e}(i.BaseBackend);e.MiniappBackend=c},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.eventFromString=e.eventFromUnknownInput=void 0;var r=n(2),i=n(33),o=n(23);function a(t,e,n){void 0===n&&(n={});var r={message:t};if(n.attachStacktrace&&e){var a=o.computeStackTrace(e),s=i.prepareFramesForEvent(a.stack);r.stacktrace={frames:s}}return r}e.eventFromUnknownInput=function(t,e,n){var s;if(void 0===n&&(n={}),r.isErrorEvent(t)&&t.error)return t=t.error,s=i.eventFromStacktrace(o.computeStackTrace(t));if(r.isDOMError(t)||r.isDOMException(t)){var u=t,c=u.name||(r.isDOMError(u)?"DOMError":"DOMException"),f=u.message?c+": "+u.message:c;return s=a(f,e,n),r.addExceptionTypeValue(s,f),s}if(r.isError(t))return s=i.eventFromStacktrace(o.computeStackTrace(t));if(r.isPlainObject(t)||r.isEvent(t)){var d=t;return s=i.eventFromPlainObject(d,e,n.rejection),r.addExceptionMechanism(s,{synthetic:!0}),s}return s=a(t,e,n),r.addExceptionTypeValue(s,""+t,void 0),r.addExceptionMechanism(s,{synthetic:!0}),s},e.eventFromString=a},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.XHRTransport=void 0;var r=n(0),i=n(2),o=n(15),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r.__extends(e,t),e.prototype.sendEvent=function(t){var e=this,n=o.sdk.request||o.sdk.httpRequest;return this._buffer.add((function(){return new Promise((function(r,o){n({url:e.url,method:"POST",data:JSON.stringify(t),header:{"content-type":"application/json"},success:function(t){r({status:i.eventStatusFromHttpCode(t.statusCode)})},fail:function(t){o(t)}})}))}))},e}(n(35).BaseTransport);e.XHRTransport=a},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.GlobalHandlers=void 0;var r=n(0),i=n(5),o=n(2),a=n(15),s=function(){function t(e){this.name=t.id,this._onErrorHandlerInstalled=!1,this._onUnhandledRejectionHandlerInstalled=!1,this._onPageNotFoundHandlerInstalled=!1,this._onMemoryWarningHandlerInstalled=!1,this._options=r.__assign({onerror:!0,onunhandledrejection:!0,onpagenotfound:!0,onmemorywarning:!0},e)}return t.prototype.setExtraOptions=function(t){t&&(this._options.onerror=!!t.onerror,this._options.onunhandledrejection=!!t.onunhandledrejection,this._options.onpagenotfound=!!t.onpagenotfound,this._options.onmemorywarning=!!t.onmemorywarning)},t.prototype.setupOnce=function(){Error.stackTraceLimit=50,this._options.onerror&&(o.logger.log("Global Handler attached: onError"),this._installGlobalOnErrorHandler()),this._options.onunhandledrejection&&(o.logger.log("Global Handler attached: onunhandledrejection"),this._installGlobalOnUnhandledRejectionHandler()),this._options.onpagenotfound&&(o.logger.log("Global Handler attached: onPageNotFound"),this._installGlobalOnPageNotFoundHandler()),this._options.onmemorywarning&&(o.logger.log("Global Handler attached: onMemoryWarning"),this._installGlobalOnMemoryWarningHandler())},t.prototype._installGlobalOnErrorHandler=function(){if(!this._onErrorHandlerInstalled){if(a.sdk.onError){var t=i.getCurrentHub();a.sdk.onError((function(e){var n="string"==typeof e?new Error(e):e;t.captureException(n)}))}this._onErrorHandlerInstalled=!0}},t.prototype._installGlobalOnUnhandledRejectionHandler=function(){if(!this._onUnhandledRejectionHandlerInstalled){if(a.sdk.onUnhandledRejection){var t=i.getCurrentHub();a.sdk.onUnhandledRejection((function(e){var n=e.reason,r=e.promise,i="string"==typeof n?new Error(n):n;t.captureException(i,{data:r})}))}this._onUnhandledRejectionHandlerInstalled=!0}},t.prototype._installGlobalOnPageNotFoundHandler=function(){if(!this._onPageNotFoundHandlerInstalled){if(a.sdk.onPageNotFound){var t=i.getCurrentHub();a.sdk.onPageNotFound((function(e){var n=e.path.split("?")[0];t.setTag("pagenotfound",n),t.setExtra("message",JSON.stringify(e)),t.captureMessage("é¡µé¢æ— æ³•æ‰¾åˆ°: "+n)}))}this._onPageNotFoundHandlerInstalled=!0}},t.prototype._installGlobalOnMemoryWarningHandler=function(){if(!this._onMemoryWarningHandlerInstalled){if(a.sdk.onMemoryWarning){var t=i.getCurrentHub();a.sdk.onMemoryWarning((function(e){var n=e.level,r=void 0===n?-1:n,i="æ²¡æœ‰èŽ·å–åˆ°å‘Šè­¦çº§åˆ«ä¿¡æ¯";switch(r){case 5:i="TRIM_MEMORY_RUNNING_MODERATE";break;case 10:i="TRIM_MEMORY_RUNNING_LOW";break;case 15:i="TRIM_MEMORY_RUNNING_CRITICAL";break;default:return}t.setTag("memory-warning",String(r)),t.setExtra("message",i),t.captureMessage("å†…å­˜ä¸è¶³å‘Šè­¦")}))}this._onMemoryWarningHandlerInstalled=!0}},t.id="GlobalHandlers",t}();e.GlobalHandlers=s},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.TryCatch=void 0;var r=n(2),i=n(36),o=function(){function t(){this._ignoreOnError=0,this.name=t.id}return t.prototype._wrapTimeFunction=function(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=e[0];return e[0]=i.wrap(r,{mechanism:{data:{function:a(t)},handled:!0,type:"instrument"}}),t.apply(this,e)}},t.prototype._wrapRAF=function(t){return function(e){return t(i.wrap(e,{mechanism:{data:{function:"requestAnimationFrame",handler:a(t)},handled:!0,type:"instrument"}}))}},t.prototype._wrapEventTarget=function(t){var e=r.getGlobalObject(),n=e[t]&&e[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(r.fill(n,"addEventListener",(function(e){return function(n,r,o){try{"function"==typeof r.handleEvent&&(r.handleEvent=i.wrap(r.handleEvent.bind(r),{mechanism:{data:{function:"handleEvent",handler:a(r),target:t},handled:!0,type:"instrument"}}))}catch(t){}return e.call(this,n,i.wrap(r,{mechanism:{data:{function:"addEventListener",handler:a(r),target:t},handled:!0,type:"instrument"}}),o)}})),r.fill(n,"removeEventListener",(function(t){return function(e,n,r){var i=n;try{i=i&&(i.__sentry_wrapped__||i)}catch(t){}return t.call(this,e,i,r)}})))},t.prototype.setupOnce=function(){this._ignoreOnError=this._ignoreOnError;var t=r.getGlobalObject();r.fill(t,"setTimeout",this._wrapTimeFunction.bind(this)),r.fill(t,"setInterval",this._wrapTimeFunction.bind(this)),r.fill(t,"requestAnimationFrame",this._wrapRAF.bind(this)),["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"].forEach(this._wrapEventTarget.bind(this))},t.id="TryCatch",t}();function a(t){try{return t&&t.name||"<anonymous>"}catch(t){return"<anonymous>"}}e.TryCatch=o},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.LinkedErrors=void 0;var r=n(0),i=n(5),o=n(33),a=n(23),s=function(){function t(e){void 0===e&&(e={}),this.name=t.id,this._key=e.key||"cause",this._limit=e.limit||5}return t.prototype.setupOnce=function(){i.addGlobalEventProcessor((function(e,n){var r=i.getCurrentHub().getIntegration(t);return r?r._handler(e,n):e}))},t.prototype._handler=function(t,e){if(!(t.exception&&t.exception.values&&e&&e.originalException instanceof Error))return t;var n=this._walkErrorTree(e.originalException,this._key);return t.exception.values=r.__spread(n,t.exception.values),t},t.prototype._walkErrorTree=function(t,e,n){if(void 0===n&&(n=[]),!(t[e]instanceof Error)||n.length+1>=this._limit)return n;var i=a.computeStackTrace(t[e]),s=o.exceptionFromStacktrace(i);return this._walkErrorTree(t[e],e,r.__spread([s],n))},t.id="LinkedErrors",t}();e.LinkedErrors=s},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.System=void 0;var r=n(0),i=n(5),o=n(15),a=n(22),s=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){i.addGlobalEventProcessor((function(e){if(i.getCurrentHub().getIntegration(t))try{var n=o.sdk.getSystemInfoSync(),s=n.batteryLevel,u=n.currentBattery,c=n.battery,f=n.brand,d=n.language,p=n.model,l=n.pixelRatio,h=n.platform,_=n.screenHeight,v=n.screenWidth,g=n.statusBarHeight,b=n.system,y=n.version,m=n.windowHeight,O=n.windowWidth,j=n.app,E=n.appName,S=n.fontSizeSetting,w=a.SDK_VERSION,x=r.__read(b.split(" "),2),k=x[0],T=x[1];return r.__assign(r.__assign({},e),{contexts:r.__assign(r.__assign({},e.contexts),{device:{brand:f,battery_level:s||u||c,model:p,screen_dpi:l},os:{name:k||b,version:T||b},extra:r.__assign({SDKVersion:w,language:d,platform:h,screenHeight:_,screenWidth:v,statusBarHeight:g,version:y,windowHeight:m,windowWidth:O,fontSizeSetting:S,app:j||E||o.appName},n)})})}catch(t){console.warn("sentry-uniapp get system info fail: "+t)}return e}))},t.id="System",t}();e.System=s},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.Router=void 0;var r=n(0),i=n(5),o=function(){function t(e){this.name=t.id,this._options=r.__assign({enable:!0},e)}return t.prototype.setupOnce=function(){var e=this;i.addGlobalEventProcessor((function(n){if(i.getCurrentHub().getIntegration(t)&&e._options.enable)try{var o=getCurrentPages().map((function(t){return{route:t.route,options:t.options}}));return r.__assign(r.__assign({},n),{extra:r.__assign(r.__assign({},n.extra),{routers:o})})}catch(t){console.warn("sentry-uniapp get router info fail: "+t)}return n}))},t.id="Router",t}();e.Router=o},function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.IgnoreMpcrawlerErrors=void 0;var r=n(5),i=n(15),o=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){r.addGlobalEventProcessor((function(e){if(r.getCurrentHub().getIntegration(t)&&"wechat"===i.appName&&i.sdk.getLaunchOptionsSync&&1129===i.sdk.getLaunchOptionsSync().scene)return null;return e}))},t.id="IgnoreMpcrawlerErrors",t}();e.IgnoreMpcrawlerErrors=o}]);
  //# sourceMappingURL=sentry-uniapp.min.js.map