import config from '@/config';
import Vue from 'vue';
import md5 from './md5';

const modulesFiles = require.context('./common-package', true, /\.js$/)
export default modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})


/**
 * 判断是否包含事件
 * 
 * 不区分是否为native
 * 
 */
export const hasEvent = ($vm,name)=>{
  
  return !!($vm.$mp.component.dataset.eventOpts || []).find((item)=>{
    
    // item == array
    if(item && item.length > 1 && item[1] && item[1][0] && item[1][0][0][0]){
      return item[1][0][0] == name
    }
  })
}
/**
 * 倒计时
 * 缺少生命周期停止
 */
export const countDown = (date)=>{
  let time = getTime(date)
  
  let o = Vue.observable({
    time:time,
    get date(){
      return new Date(this.time)
    },
    set date(v){
      this.time = getTime(v)
    },
    // 天
    get d(){
      return Math.floor(this.time/(24*3600*1000))
    },
    // 时
    get h(){
      return this.date.getHours()
    },
    // 分
    get m(){
      return this.date.getMinutes()
    },
    // 秒
    get s(){
      return this.date.getSeconds()
    }
  })

  
  let si = setInterval(()=>{
    
    o.time -= 1000
    if (time <= 0) {
      clearInterval(si)
    }
  },1000)

  // 停止，摧毁时使用
  o.clear = ()=>{
    clearInterval(si)
  }
  return o
}

const getTime = (date)=>{
  if(!isNaN(parseInt(date))){
    date = parseInt(date) * 1000
  }
  date = new Date(date)
  let time = date.getTime() + new Date().getTimezoneOffset() * 60  * 1000
  if(isNaN(time)){
    time = 0
  }
  return time 
}


// 根据参数获取签名
export const getParamsSign = (extData)=>{
  const keyArr = [];
  const temp = [];
  for (const key in extData) {
    keyArr.push(key);
  }
  keyArr.sort();
  for (let i = 0; i < keyArr.length; i++) {
    temp[keyArr[i]] = extData[keyArr[i]];
  }
  let paramStr = '';
  for (const i in temp) {
    if (typeof temp[i] === 'object') {
      if (
        JSON.stringify(temp[i]) !== '{}' &&
        temp[i] &&
        temp[i].length !== 0
      ) {
        paramStr += i + '=Array&';
      }
    } else if (temp[i] !== undefined) {
      paramStr += i + '=' + temp[i] + '&';
    }
  }
  const _sign = md5(
    '_sydd=EISisewwIJJfssleiwwofja9ewzvwtytXCZBNMJTG' +
      config.version +
      '&' +
      paramStr.substring(0, paramStr.length - 1)
  );
  return _sign
}
