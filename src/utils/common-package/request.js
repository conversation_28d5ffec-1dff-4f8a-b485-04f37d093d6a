// const config = require('../../config')
import Vue from 'vue';
// const md5 = require('../md5')
import config from '@/config';
// import md5 from '../md5';
import qs from 'qs';
import store from '@/store';
import * as Sentry from '@/utils/sentry-uniapp.min';
import { getParamsSign } from '@/utils/common';
import JWTPayload from './../jwtPayload';

function generateRequestId() {
  let result = '';
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < 32; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

const requestTaskMap = new Map();
export default async ({
  host = config.baseApi,
  url,
  data = {},
  header = {},
  method = 'POST',
  complete = () => {},
  isLoading = false,
  cancelToken = ''
}) => {
  // if (/sy.soyoung.com/g.test(host) && /^\/syGroupBuy/g.test(url)) {
  //   host = 'https://wxapi.sy.soyoung.com';
  // } else
  if (/^\/Wxpassport/g.test(url)) {
    host = config.passportApi;
  }

  return new Promise((resolve, reject) => {
    if (isLoading) {
      uni.showLoading({
        title: '加载中'
      });
    }

    console.log(header);
    if (method.toLocaleUpperCase() === 'POST' || !header['content-type']) {
      header['content-Type'] = 'application/x-www-form-urlencoded';
    }
    const {
      unionId,
      openId,
      lng,
      lat,
      uid,
      cityId,
      marketActivityId,
      psid,
      xyToken
    } = store.state.global.userInfo;
    const extData = Object.assign(
      {},
      {
        sys: config.sys,
        app_id: config.appId,
        lver: config.version,
        union_id: unionId || 0,
        open_id: openId || 0,
        uid: uid || 0,
        xy_token: xyToken || 0,
        market_activity_id: marketActivityId || 0,
        psid: psid || '',
        cityId: cityId || 0,
        city_id: cityId || 0,
        lat: lat || 0,
        lng: lng || 0,
        deviceId: Vue.prototype.SMSdk.getDeviceId(),
        device_id: Vue.prototype.SMSdk.getDeviceId(),
        sm_device_id: Vue.prototype.SMSdk.getDeviceId(),
        _time: new Date().getTime(),
        request_id: generateRequestId() // 随机生成一个request_id
      },
      Vue.prototype.$deepClone(data)
    );
    if (extData.xy_token) {
      let jwt = new JWTPayload(extData.xy_token);
      if (!jwt.isValid()) {
        console.log('xyTokenIsExpired过期了');
        // Vue.$setUserInfoToStorage({
        //   uid: '',
        //   xyToken: ''
        // });
        // extData.uid = 0;
        // extData.xy_token = 0;
        Vue.prototype.$signout();
      }
    }

    // const keyArr = [];
    // const temp = [];
    // for (const key in extData) {
    //   keyArr.push(key);
    // }
    // keyArr.sort();
    // for (let i = 0; i < keyArr.length; i++) {
    //   temp[keyArr[i]] = extData[keyArr[i]];
    // }
    // let paramStr = '';
    // for (const i in temp) {
    //   if (typeof temp[i] === 'object') {
    //     if (
    //       JSON.stringify(temp[i]) !== '{}' &&
    //       temp[i] &&
    //       temp[i].length !== 0
    //     ) {
    //       paramStr += i + '=Array&';
    //     }
    //   } else if (temp[i] !== undefined) {
    //     paramStr += i + '=' + temp[i] + '&';
    //   }
    // }
    // extData._sign = md5(
    //   '_sydd=EISisewwIJJfssleiwwofja9ewzvwtytXCZBNMJTG' +
    //     config.version +
    //     '&' +
    //     paramStr.substring(0, paramStr.length - 1)
    // );

    extData._sign = getParamsSign(extData);

    if (method.toLocaleUpperCase() === 'GET') {
      url += '?' + qs.stringify(extData);
    }
    // #ifdef H5
    host = '';
    // #endif
    console.log('请求URL', host + url, '请求参数', extData);
    const requestTask = uni.request({
      url: host + url,
      data: qs.stringify(extData),
      method: method.toLocaleUpperCase(),
      header,
      success(res) {
        if (res.data.errorCode === 404) {
          Sentry.captureException(res);
        }
        console.log('请求返回值', res.data);
        if (
          res.data.code === 77777 ||
          res.data.errorCode === 77777 ||
          res.data.errorCode === 789
        ) {
          // 服务端返回未登录
          // Vue.prototype.$login()
          // uni.showToast('请稍后再试')
          if (
            url.indexOf('/activityEntrance') === -1 &&
            url.indexOf('user/getUserInfo') === -1 &&
            url.indexOf('order/getUserThreeDaysReserve') === -1 &&
            url.indexOf('order/orderList') === -1
          ) {
            uni.showToast({
              title: '登录态过期，请重新登录',
              icon: 'none'
            });
          }

          Vue.$setUserInfoToStorage({
            uid: '',
            xyToken: ''
          });
          Sentry.captureMessage(
            `登录态过期，请重新登录,server返回状态码${
              res.data.code || res.data.errorCode
            }`
          );
          // store.state.global.userInfo.uid = ''
          // store.state.global.userInfo.xyToken = ''
          reject('用户未登录');
        } else {
          resolve(res);
        }
      },
      fail(err) {
        if (!host.includes('st3.soyoung.com')) {
          wx.getNetworkType({
            success(resNetwork) {
              uni.$log('网络请求错误', url, err, extData, resNetwork, 'error');
              uni.$emit('networkWeak', {
                ...resNetwork,
                url
              });
            }
          });
        }
        Sentry.captureException(err);
        reject(err);
      },
      complete() {
        if (isLoading) {
          uni.hideLoading();
        }
        complete();
        if (cancelToken) {
          requestTaskMap.delete(cancelToken);
        }
      }
    });
    if (cancelToken) {
      requestTaskMap.set(cancelToken, requestTask);
    }
    return requestTask;
  });
};
export function aboutRequestTask(cancelToken) {
  if (!cancelToken) return;
  const requestTask = requestTaskMap.get(cancelToken);
  requestTaskMap.delete(cancelToken);
  requestTask?.abort?.();
}

export function getCancelToken() {
  return `cancel_token_${new Date().getTime()}`;
}

//添加弱网监控
wx.onNetworkStatusChange(({ isConnected }) => {
  console.log(1111);
  uni.$emit('networkWeak', { isConnected });
});
