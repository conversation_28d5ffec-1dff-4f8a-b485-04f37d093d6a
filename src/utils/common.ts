import Taro from '@tarojs/taro'

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式化字符串
 */
export const formatDate = (
  date: Date | string | number,
  format = 'YYYY-MM-DD HH:mm:ss'
): string => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func.apply(null, args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (!timeout) {
      timeout = setTimeout(() => {
        timeout = null
        func.apply(null, args)
      }, wait)
    }
  }
}

/**
 * 深拷贝
 * @param obj 要拷贝的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 获取系统信息
 */
export const getSystemInfo = () => {
  return new Promise((resolve, reject) => {
    Taro.getSystemInfo({
      success: resolve,
      fail: reject,
    })
  })
}

/**
 * 显示消息提示
 * @param title 提示内容
 * @param icon 图标类型
 */
export const showToast = (
  title: string,
  icon: 'success' | 'error' | 'loading' | 'none' = 'none'
) => {
  Taro.showToast({
    title,
    icon,
    duration: 2000,
  })
}

/**
 * 显示确认对话框
 * @param content 提示内容
 * @param title 标题
 */
export const showConfirm = (content: string, title = '提示') => {
  return new Promise((resolve, reject) => {
    Taro.showModal({
      title,
      content,
      success: res => {
        if (res.confirm) {
          resolve(true)
        } else {
          reject(false)
        }
      },
      fail: reject,
    })
  })
}

/**
 * 存储数据
 * @param key 键名
 * @param data 数据
 */
export const setStorage = (key: string, data: any) => {
  return Taro.setStorage({
    key,
    data,
  })
}

/**
 * 获取存储数据
 * @param key 键名
 */
export const getStorage = (key: string) => {
  return Taro.getStorage({
    key,
  })
}

/**
 * 移除存储数据
 * @param key 键名
 */
export const removeStorage = (key: string) => {
  return Taro.removeStorage({
    key,
  })
}
