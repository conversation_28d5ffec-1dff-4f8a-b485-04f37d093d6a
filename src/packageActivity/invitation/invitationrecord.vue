<template>
  <page-meta background-color="#f8f8f8" page-style="">
    <NavBar :opacity="opacity" :title="'奖品记录'" :titleIsShow="true" />
    <div class="page">
      <div class="nav"></div>
      <div class="amount" v-if="successful">
        <div class="title">
          <div class="text">专属券奖励</div>
          <div class="right" style="white-space: nowrap">
            （ 线上可用
            <div class="line"></div>
            单张使用，可叠加其他优惠活动）
          </div>
        </div>
        <div
          class="rule"
          v-if="a_page_role_link"
          @click="open(a_page_role_link)"
        >
          规则
        </div>
        <div class="money">
          <div class="li">
            <div class="num">
              <div class="symbol">¥</div>
              {{ alreadyCouponAmount }}
              <div class="line"></div>
            </div>
            <div class="text" @click="alreadyHandler">
              已到账
              <img
                class="money-icon"
                v-if="
                  award_data.already_amount != '0' &&
                  award_data.already_coupon_amount != '0'
                "
                src="https://static.soyoung.com/sy-pre/wenhao-1744873800660.png"
              />
              <img
                class="money-icon"
                v-else-if="award_data.already_coupon_amount != '0'"
                src="https://static.soyoung.com/sy-pre/next-1744873800660.png"
              />
            </div>
          </div>
          <div class="li">
            <div class="num">
              <div class="symbol">¥</div>
              {{ award_data.used_amount }}
              <div class="line"></div>
            </div>
            <div class="text">已使用</div>
          </div>
          <div class="li">
            <div class="num">
              <div class="symbol">¥</div>
              {{ award_data.has_expired_amount }}
            </div>
            <div class="text">已失效</div>
          </div>
        </div>
        <div class="tip">
          {{ award_data.valid_date_str || '*已到账红包/专属券有效期为90天' }}
        </div>
        <div class="text_bottom">
          <div class="left">医美项目奖励</div>
          <div class="right">
            累计获得{{ award_data.medical_item_award_cnt }}次
          </div>
        </div>
      </div>
      <div class="list" v-if="list">
        <div class="item" v-for="(item, index) in list" :key="index">
          <div class="left">
            <div class="text">{{ getTitle(item.reward_type) }}</div>
            <div class="time">{{ item.create_date_trans }}</div>
          </div>
          <div class="right">
            <div
              class="reward_name"
              :class="item.reward_action === 1 ? 'green' : ''"
            >
              {{ item.reward_action === 1 ? '+' : '-' }}{{ item.reward_name }}
            </div>
            <img
              v-if="item.record_type === 3 && item.status_txt !== ''"
              :src="
                item.status_txt === '已使用'
                  ? 'https://static.soyoung.com/sy-design/1fgxrkb9nl1te1744883663374.png'
                  : 'https://static.soyoung.com/sy-design/1fgxp84m6kpq81744883663377.png'
              "
              alt=""
            />
          </div>
        </div>
      </div>

      <div class="bottom">
        <img
          src="https://static.soyoung.com/sy-design/2hca5b5fhk6c31736305943690.png"
          alt=""
        />
      </div>
    </div>
    <BottomModal
      :bottom="0"
      :showTitle="false"
      background="#fff"
      :visible="alreadyPopupVisible"
      @close="closeAlreadyPopup"
    >
      <div class="alreadyPopup">
        <div class="title">奖励金额明细</div>
        <div class="content">
          <div class="card">
            <div class="card-item">
              <div class="card-item-amount">
                <div class="symbol">¥</div>
                {{ award_data.already_amount }}
              </div>
              <div class="card-item-text">消费红包</div>
            </div>
            <div class="card-line"></div>
            <div class="card-item">
              <div class="card-item-amount">
                <div class="symbol">¥</div>
                {{ award_data.already_coupon_amount }}
              </div>
              <div class="card-item-text" @click="goToCoupon">
                专属券
                <img
                  src="https://static.soyoung.com/sy-design/1brn09jz3lef61744883663369.png"
                  alt=""
                />
              </div>
            </div>
          </div>
          <div class="rule">
            <div class="rule-title">使用规则：</div>
            <div class="rule-content">
              <div class="rule-item">消费红包仅可线下门店使用，单日使用1次</div>
              <div class="rule-item">
                专属券在单个商品单次仅可使用1次，可与其他优惠券叠加，具体使用范围以优惠券适用范围为准。
              </div>
              <div class="rule-link" @click="open(a_page_role_link)">
                详细规则
                <img
                  src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1744883663363.png"
                  alt=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </BottomModal>
  </page-meta>
</template>

<script>
import NavBar from './NavBar.vue';
import BottomModal from '@/components/bottomModal.vue';
import { getRewardList } from '@/api/activity';

export default {
  components: {
    NavBar,
    BottomModal
  },
  data() {
    return {
      alreadyPopupVisible: false,
      award_data: {},
      has_more: false,
      list: [],
      successful: false,
      page: 0,
      a_page_role_link: '',
      opacity: 0
    };
  },
  async onLoad() {
    await this.getList();
  },
  computed: {
    alreadyCouponAmount() {
      return (
        parseFloat(this.award_data.already_amount) +
        parseFloat(this.award_data.already_coupon_amount)
      );
    }
  },
  methods: {
    closeAlreadyPopup() {
      this.alreadyPopupVisible = false;
    },
    goToCoupon() {
      uni.switchTab({
        url: '/pages/coupon-center'
      });
    },
    alreadyHandler() {
      if (
        this.award_data.already_amount != '0' &&
        this.award_data.already_coupon_amount != '0'
      ) {
        this.alreadyPopupVisible = true;
      } else if (this.award_data.already_coupon_amount != '0') {
        this.goToCoupon();
      }
    },
    async getList() {
      const { errorCode, errorMsg, responseData } = await getRewardList({
        page: this.page
      });
      if (errorCode === 0) {
        this.successful = true;
        if (responseData.award_data) {
          this.award_data = responseData.award_data;
        }
        if (responseData.a_page_role_link) {
          this.a_page_role_link = responseData.a_page_role_link;
        }
        this.has_more = !!responseData.has_more;
        this.list = this.list.concat(responseData.list || []);
        this.page++;
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    getTitle(reward_type) {
      switch (reward_type) {
        case 1:
          return '好友到店奖励';
        case 2:
          return '好友消费奖励';
        case 3:
          return '到店使用';
        default:
          return '过期失效';
      }
    },
    open(url) {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_prize:rule_click',
        ext: {}
      });
      uni.navigateTo({
        url,
        fail: (res) => {
          console.log('跳转失败: QA请看', res.errMsg);
        }
      });
    }
  },
  onReachBottom() {
    console.log('触底了');
    if (this.has_more) {
      this.getList();
    }
  },
  onPageScroll(e) {
    if (e.scrollTop < 10) {
      this.opacity = 0;
    } else if (e.scrollTop <= 110 && e.scrollTop >= 10) {
      this.opacity = (e.scrollTop - 10) / 100;
    } else {
      this.opacity = 1;
    }
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_chain_store_tuan_old_brings_new_prize_page',
      ext: {}
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_old_brings_new_prize_page',
      ext: {}
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_old_brings_new_prize_page',
      ext: {}
    });
  }
};
</script>

<style lang="less" scoped>
.nav {
  height: 200rpx;
  // background-image: linear-gradient(
  //   180deg,
  //   #ffd8dd 0%,
  //   rgba(255, 238, 240, 0) 100%
  // );
}
.page {
  min-height: 100vh;
  padding-bottom: 80rpx;
  position: relative;
  box-sizing: border-box;
  background: #e2e7e9;
  .bottom {
    height: 80rpx;
    // margin-top: 120rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    image {
      width: 100%;
      height: 100%;
    }
  }
}

.amount {
  height: 360rpx;
  margin: 50rpx 20rpx 40rpx;
  // margin-top: 26rpx;
  margin-bottom: 0rpx;
  // background: url('https://static.soyoung.com/sy-pre/z63digynbs4d-1723615800631.png')
  //   no-repeat;
  // background-size: 100% 100%;
  background: #fff;
  box-sizing: border-box;
  border-bottom: 10rpx solid #a9ea6a;
  border-radius: 6rpx;
  position: relative;
  padding: 30rpx;
  .rule {
    position: absolute;
    right: 30rpx;
    top: 30rpx;
    // width: 46rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333;
    line-height: 40rpx;
  }
  .title {
    display: flex;
    align-items: center;
    line-height: 40rpx;
    // padding-top: 50rpx;
    // margin-left: 40rpx;
    .text {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #333333;
      font-weight: 400;
    }
    .right {
      font-family: PingFangSC-Light;
      font-size: 20rpx;
      color: #333333;
      font-weight: 200;
      display: flex;
      align-items: center;
      .line {
        width: 1rpx;
        height: 20rpx;
        background: #333;
        margin: 0 10rpx;
      }
    }
  }
  .money {
    display: flex;
    padding-top: 40rpx;
    .li {
      flex: 1;
      text-align: center;
      position: relative;
      .num {
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: OutFit-Regular;
        font-size: 34rpx;
        color: #333333;
        line-height: 42rpx;
        font-weight: 200;
        .symbol {
          padding-right: 2rpx;
          font-family: PingFangSC-Light;
          font-size: 14rpx;
          color: #333333;
          font-weight: 200;
        }
      }
      .text {
        font-family: PingFangSC-Light;
        font-size: 20rpx;
        color: #333333;
        font-weight: 200;
        margin-top: 6rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .money-icon {
        width: 20rpx;
        height: 20rpx;
        margin-left: 10rpx;
      }
      .line {
        // position: absolute;
        // background: #a6a6a6;
        // right: 0;
        // width: 1rpx;
        // height: 50rpx;
        // top: 20rpx;

        content: '';
        position: absolute;
        right: 0;
        height: 50rpx;
        top: 20rpx;
        width: 2rpx;
        background-color: #a6a6a6;
        transform: scaleX(0.5);
        transform-origin: 0 0;
      }
    }
  }
  .tip {
    padding-left: 12rpx;
    padding-top: 30rpx;
    font-family: PingFangSC-Light;
    font-size: 20rpx;
    color: #a6a6a6;
    font-weight: 200;
  }
  .text_bottom {
    display: flex;
    // border-top: #e6e6e6 solid 1rpx;
    position: relative;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333;
    padding-top: 20rpx;
    // margin: 0 40rpx;
    margin-top: 30rpx;
    justify-content: space-between;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2rpx;
      background-color: #e6e6e6;
      transform: scaleY(0.5);
      transform-origin: 0 0;
    }
  }
}
.list {
  margin: 0 20rpx;
  margin-top: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  .item {
    padding: 40rpx 0;
    margin: 0 30rpx;
    border-bottom: 1rpx solid #e6e6e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      width: 240rpx;
      .text {
        font-family: PingFangSC-Regular;
        color: #333333;
        letter-spacing: 0;
        font-size: 28rpx;
        line-height: 40rpx;
      }
      .time {
        font-size: 24rpx;
        margin-top: 2rpx;
        font-family: OutFit-Regular;
        color: #a6a6a6;
        font-weight: 200;
      }
    }
    .right {
      width: 400rpx;
      font-size: 28rpx;
      color: #333;
      text-align: right;
      font-family: Outfit-Regular;
      color: #333333;
      .reward_name {
        &.green {
          color: #61b43e;
        }
      }
      image {
        width: 56rpx;
        height: 22rpx;
      }
    }
  }
}
.alreadyPopup {
  height: 790rpx;
  .title {
    font-family: PingFangSC-Medium;
    font-size: 32rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    line-height: 104rpx;
  }
  .content {
    padding: 60rpx 20rpx 0;
  }
  .card {
    display: flex;
    align-items: center;
    width: 100%;
    background: #f2f2f2;
    border-radius: 6rpx;
    height: 220rpx;
    .card-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      &-amount {
        font-family: Outfit-Light;
        font-size: 34rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: justify;
        font-weight: 200;
        line-height: 42rpx;
        display: inline-flex;
        .symbol {
          font-family: PingFangSC-Light;
          font-size: 14rpx;
          color: #333333;
          letter-spacing: 0;
          text-align: justify;
          font-weight: 200;
          margin-right: 2rpx;
        }
      }
      &-text {
        margin-top: 6rpx;
        font-family: PingFangSC-Light;
        font-size: 20rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: justify;
        font-weight: 200;
        display: inline-flex;
        align-items: center;
        line-height: 28rpx;
        image {
          width: 10rpx;
          height: 18rpx;
          margin-left: 6rpx;
        }
      }
    }
    .card-line {
      width: 1rpx;
      background: #a6a6a6;
      height: 70rpx;
    }
  }
  .rule {
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: justify;
    font-weight: 400;
    line-height: 34rpx;
    margin: 90rpx 10rpx 0;
    &-title {
      margin-bottom: 20rpx;
    }
    &-item {
      margin-bottom: 10rpx;
    }
    &-link {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Regular;
      color: #61b43e;
      letter-spacing: 0;
      margin-top: 20rpx;
      text-align: justify;
      font-weight: 400;
      image {
        width: 12rpx;
        height: 22rpx;
        margin-left: 10rpx;
      }
    }
  }
}
</style>
