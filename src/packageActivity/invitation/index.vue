<template>
  <page-meta background-color="#E2E7E9" page-style="">
    <NavBar :opacity="opacity" />
    <div
      class="page"
      v-if="successful"
      :style="{
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        backgroundImage: `url(${config_data.a_page_head_img.u})`
      }"
    >
      <div class="rule" @click="goToRule" v-if="config_data.a_page_role_link">
        规则
      </div>
      <div class="main">
        <template v-if="activity_status === 1">
          <!-- <image
            mode="widthFix"
            class="img"
            @load="pageShow"
            @error="pageShow"
            :src="
              recommend_data.list &&
              recommend_data.list[0] &&
              recommend_data.list[0].type
                ? config_data.a_page_gift_c_img.u
                : config_data.a_page_gift_d_img.u
            "
          ></image> -->
          <div class="main-wrapper">
            <div class="main-box">
              <div class="main-box-top">
                <div class="main-box-title">会员邀请新客到店</div>
                <div class="main-box-subtitle">
                  {{ a_page_share_card_title_desc1 }}
                </div>
              </div>
              <div class="main-box-divide"></div>
              <div class="main-box-bottom">
                <div class="main-box-title">
                  {{ a_page_share_card_title_desc2 }}
                </div>
                <div class="main-box-subtitle">
                  {{ a_page_share_card_title_desc3 }}
                </div>
              </div>
            </div>
            <image
              mode="widthFix"
              class="img"
              @load="pageShow"
              @error="pageShow"
              src="https://static.soyoung.com/sy-design/2hca7nc2ykif91741659615199.png"
            ></image>
          </div>
          <div class="shareMain">
            <div class="shareBottom" @click="handleInvite">邀请好友</div>
            <!--            <template-->
            <!--              v-if="recommend_data.list && recommend_data.list[0].type === 1"-->
            <!--            >-->
            <!--              <div class="shareText">-->
            <!--                <div class="select" @click="isShareSwitch">-->
            <!--                  <image-->
            <!--                    class="icon"-->
            <!--                    v-if="isShare"-->
            <!--                    src="https://static.soyoung.com/sy-design/kgmpik5wigpe1723186292054.png"-->
            <!--                  />-->
            <!--                  <image-->
            <!--                    class="icon"-->
            <!--                    v-else-->
            <!--                    src="https://static.soyoung.com/sy-design/amhh4d59pgcj1723620384126.png"-->
            <!--                  />-->
            <!--                </div>-->
            <!--                <div class="right">-->
            <!--                  <div class="text1">同时分享我的到店体验</div>-->
            <!--                  <div-->
            <!--                    class="text2"-->
            <!--                    v-if="-->
            <!--                      recommend_data.list[0].data.item_cnt &&-->
            <!--                      recommend_data.list[0].data.item_cnt != '0'-->
            <!--                    "-->
            <!--                  >-->
            <!--                    共到访{{ recommend_data.list[0].data.visit_cnt }}次-->
            <!--                    <template-->
            <!--                      v-if="-->
            <!--                        recommend_data.list[0].data.item_cnt &&-->
            <!--                        recommend_data.list[0].data.item_cnt != '0'-->
            <!--                      "-->
            <!--                    >-->
            <!--                      <span class="line"></span>-->
            <!--                      共完成{{ recommend_data.list[0].data.item_cnt }}个项目-->
            <!--                    </template>-->
            <!--                    <template-->
            <!--                      v-if="recommend_data.list[0].data.good_evaluate_rate"-->
            <!--                    >-->
            <!--                      <span class="line"></span>-->
            <!--                      <image-->
            <!--                        class="icon"-->
            <!--                        src="https://static.soyoung.com/sy-design/1w3i3khp34hz81723186292056.png"-->
            <!--                      />好评率{{-->
            <!--                        recommend_data.list[0].data.good_evaluate_rate-->
            <!--                      }}%-->
            <!--                    </template>-->
            <!--                  </div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </template>-->
            <template
              v-if="recommend_data.list && recommend_data.list[0].type === 2"
            >
              <div class="shareText">
                <div class="select" @click="isShareSwitch">
                  <image
                    class="icon"
                    v-if="isShare"
                    src="https://static.soyoung.com/sy-design/kgmpik5wigpe1723186292054.png"
                  />
                  <image
                    class="icon"
                    v-else
                    src="https://static.soyoung.com/sy-design/amhh4d59pgcj1723620384126.png"
                  />
                </div>
                <div class="avatar">
                  <img
                    class="icon"
                    :src="recommend_data.list[0].data.user.avatar"
                  />
                </div>
                <div class="right">
                  <div class="text1">
                    同时推荐我的咨询师
                    {{ recommend_data.list[0].data.user.name }}
                  </div>
                  <div class="text2">
                    累计服务{{ recommend_data.list[0].data.visit_cnt }}人次<span
                      class="line"
                    ></span
                    >好评率{{ recommend_data.list[0].data.good_evaluate_rate }}%
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
        <template v-else>
          <div class="statusAbNormal">
            <image
              mode="widthFix"
              class="img"
              @load="pageShow"
              :src="getImageSrc(activity_status)"
            ></image>
            <div class="exceptionPrompt">
              {{ activity_status_desc }}
            </div>
            <div class="shareBottom" @click="gohome">去首页逛逛</div>
          </div>
          <!--
          <div class="shareMain">


          </div> -->
        </template>
      </div>
      <div
        class="flow"
        v-if="config_data.a_page_path_img && config_data.a_page_path_img.u"
      >
        <image
          mode="widthFix"
          class="img"
          @error="config_data.a_page_path_img.u = ''"
          :src="config_data.a_page_path_img.u"
        ></image>
      </div>
      <div class="invitationRecord" v-if="invite_record">
        <div class="title">
          <div class="left">邀请记录</div>
          <div class="right" @click="gotoRecord">
            奖品记录
            <img
              class="icon"
              mode="aspectFill"
              src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1736305944006.png"
            />
          </div>
        </div>
        <div
          class="amount"
          v-if="
            invite_record.award_data &&
            invite_record.award_data.wait_amount &&
            invite_record.award_data.already_amount &&
            invite_record.award_data.used_amount &&
            !(
              invite_record.award_data.wait_amount === '0' &&
              invite_record.award_data.already_amount === '0' &&
              invite_record.award_data.used_amount === '0'
            )
          "
        >
          <div class="money" @click="gotoRecord">
            <div class="li">
              <div class="num">
                <div class="symbol">¥</div>
                {{ invite_record.award_data.wait_amount || '0.00' }}
                <div class="line"></div>
              </div>
              <div class="text">待到账</div>
            </div>
            <div class="li">
              <div class="num">
                <div class="symbol">¥</div>
                {{ invite_record.award_data.already_amount || '0.00' }}
                <div class="line"></div>
              </div>
              <div class="text">已到账</div>
            </div>
            <div class="li">
              <div class="num">
                <div class="symbol">¥</div>
                {{ invite_record.award_data.used_amount || '0.00' }}
              </div>
              <div class="text">已使用</div>
            </div>
          </div>
          <div
            class="h-line"
            v-if="
              invite_record.progress_bar_data &&
              invite_record.progress_bar_data.is_show
            "
          ></div>
          <div
            class="progress"
            v-if="
              invite_record.progress_bar_data &&
              invite_record.progress_bar_data.is_show
            "
          >
            <div class="p-title">
              {{ invite_record.progress_bar_data.title }}
            </div>
            <image
              @click="handleClickToShowProgressTips"
              class="p-icon"
              mode="aspectFill"
              src="https://static.soyoung.com/sy-design/24reyewpfe1s01741659615337.png"
            ></image>
            <div class="p-bar">
              <div
                class="active"
                :style="{
                  width: `${
                    (invite_record.progress_bar_data.invite_num /
                      invite_record.progress_bar_data.invite_upper_limit) *
                    100
                  }%`
                }"
              ></div>
              <div
                class="button"
                :style="{
                  left: `${
                    (invite_record.progress_bar_data.invite_num /
                      invite_record.progress_bar_data.invite_upper_limit) *
                    100
                  }%`
                }"
              >
                <div class="b-icon"></div>
                <div class="b-num">
                  {{ invite_record.progress_bar_data.invite_num }}人
                </div>
              </div>
            </div>
            <div class="p-num">
              {{ invite_record.progress_bar_data.invite_upper_limit }}人
            </div>
          </div>
        </div>
        <div
          v-if="
            invite_record.award_config_v2 &&
            invite_record.award_config_v2.is_show
          "
          class="banner"
        >
          <image
            class="banner-image"
            mode="aspectFill"
            :src="invite_record.award_config_v2.background_img_url"
          ></image>
          <div class="banner-box">
            <div class="banner-title">
              {{ invite_record.award_config_v2.user_reward_day_msg }}
            </div>
            <div class="banner-subtitle">
              {{ invite_record.award_config_v2.user_reward_threshold_1 }}
            </div>
            <div class="banner-subtitle">
              {{ invite_record.award_config_v2.user_reward_threshold_2 }}
            </div>
          </div>
        </div>
        <div
          v-if="invite_record.award_rank && invite_record.award_rank.is_show"
          class="rank"
          :style="rankBackgroundStyle"
          @click="handleClickToRank"
        >
          <image
            class="rank-image"
            mode="aspectFill"
            :src="invite_record.award_rank.background_img_url"
          ></image>
        </div>
        <div
          v-if="
            invite_record && invite_record.list && invite_record.list.length > 0
          "
          class="list"
        >
          <div
            class="item"
            v-for="(item, index) in invite_record.list"
            :key="index"
          >
            <div class="left">
              <div class="avatar">
                <image mode="aspectFill" class="icon" :src="item.user.avatar" />
                <div v-if="item.notice_flag">
                  <button
                    class="tip"
                    open-type="share"
                    :data-name="item.user.name || 'kong'"
                    @click="tipShare(item.user.name)"
                  />
                </div>
              </div>
              <div class="text">
                <div class="ti">
                  {{ item.user.name }}
                  <div v-if="item.reward_status == 0" class="status status0">
                    待到店
                  </div>
                  <div
                    v-else-if="item.reward_status == 1"
                    class="status status1"
                  >
                    已到院
                  </div>
                  <div
                    v-else-if="item.reward_status == 2"
                    class="status status2"
                  >
                    已消费
                  </div>
                  <div
                    v-else-if="item.reward_status == 3"
                    class="status status3"
                  >
                    已失效
                  </div>
                  <div
                    v-else-if="item.reward_status == 100"
                    class="status status3"
                  >
                    已达上限
                  </div>
                </div>
                <div class="desc">
                  {{ item.title }}
                </div>
              </div>
            </div>
            <div class="line"></div>
            <div class="right" :class="item.get_status == 3 ? 'fff' : ''">
              <img
                class="icon"
                v-if="item.get_status == 0"
                src="https://static.soyoung.com/sy-design/dl1uyu2f7bjr1736319735598.png"
              />
              <img
                class="icon"
                v-else-if="item.get_status == 1 || item.get_status == 2"
                src="https://static.soyoung.com/sy-design/2hca637nb8ad51736319735403.png"
              />
              <img
                class="icon"
                v-else
                src="https://static.soyoung.com/sy-pre/3rjdzh0e0t3br-1736928600647.png"
              />
              <div class="money">{{ item.reward_name }}</div>
              <div class="text">
                <template v-if="item.get_status == 0">待到账</template>
                <template
                  v-else-if="item.get_status == 1 || item.get_status == 2"
                  >已到账</template
                >
                <template v-else>已失效</template>
              </div>
            </div>
          </div>
          <div class="tip">
            *已到账红包/专属券有效期为{{
              config_data.a_user_reward_coupon_effective_days || 0
            }}天
          </div>
        </div>
        <div class="nodata" v-else>
          <img
            class="icon"
            src="https://static.soyoung.com/sy-design/12vp7cocv16zj1736305942755.png"
          />
          <div class="text">暂无邀请记录</div>
        </div>
      </div>
      <div class="page-bottom">
        <img
          src="https://static.soyoung.com/sy-design/2hca5b5fhk6c31736305943690.png"
          alt=""
        />
      </div>
      <Popup type="bottom" ref="popup">
        <div class="share-box">
          <div class="title">分享到</div>
          <div class="close" @click="onBottomModalClose"></div>
          <div class="share-type-list">
            <button class="item" open-type="share" @click="onShare">
              <img
                class="cover"
                src="https://static.soyoung.com/sy-design/weixin-31726735613365.png"
              />
              <div class="text">微信好友</div>
            </button>
            <div
              class="item"
              :class="[qrsrc ? '' : 'disabled']"
              @click="onPosterGen"
            >
              <img
                class="cover"
                src="https://static.soyoung.com/sy-design/dn9yxsl49te91726735613368.png"
              />
              <div class="text">分享海报</div>
            </div>
          </div>
          <div class="share-button">
            <div class="button" @click="onBottomModalClose">取消</div>
          </div>
        </div>
      </Popup>
      <Poster
        :visible.sync="posterGenVisible"
        :params="posterParams"
        @afterPosterCreated="afterPosterCreated"
      />
    </div>
    <div v-if="!isload" class="skeleton">
      <image
        class="skeleton-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-design/32q76aat6dbg71723186292436.png"
      >
      </image>
    </div>
  </page-meta>
</template>
<script>
import NavBar from './NavBar.vue';
import Popup from '@/components/uni/popup.vue';
import Poster from './poster.vue';
import { getaHome, getInviteList, getQwImg, inviteUser } from '@/api/activity';
import { mapGetters } from 'vuex';
import qs from 'qs';

export default {
  components: {
    NavBar,
    Popup,
    Poster
  },
  data() {
    return {
      recommend_id: '',
      type: '',
      opacity: 0,
      invite_record: {},
      config_data: {},
      recommend_data: {},
      login_user: {},
      activity_status: 0,
      activity_status_desc: '',
      a_page_share_card_title_desc1: '',
      a_page_share_card_title_desc2: '',
      a_page_share_card_title_desc3: '',
      posterParams: {
        title: '',
        subtitle: '',
        cover: '',
        code: ''
      },
      qrsrc: '',
      isload: false,
      successful: false,
      isShare: false,
      page: 1,
      qrsrcArr: [],
      has_more: false,
      posterGenVisible: false
    };
  },
  methods: {
    onBottomModalClose() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:share_cancel_click',
        ext: {}
      });
      this.$refs.popup.close('bottom');
    },
    onShare() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:share_card_click',
        ext: {}
      });
      this.$refs.popup.close('bottom');
    },
    goToRule() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:rule_click',
        ext: {}
      });
      this.open(this.config_data.a_page_role_link);
    },
    tipShare(data) {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:remind_ta_click',
        ext: {}
      });
      wx.setClipboardData({
        data
      });
    },
    gotoRecord() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:prize_record_click',
        ext: {}
      });
      this.open('/packageActivity/invitation/invitationrecord');
    },
    onPosterGen() {
      if (this.qrsrc) {
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_a:share_pic_click',
          ext: {}
        });
        this.$refs.popup.close('bottom');
        setTimeout(() => {
          this.posterGenVisible = true;
        }, 100);
      }
    },
    isShareSwitch() {
      this.isShare = !this.isShare;
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:share_more_click',
        ext: {}
      });
    },
    handleJump(link) {
      console.log('handleJump', link);
      if (link.indexOf('.com') > -1 || link.indexOf('http') > -1) {
        this.$toH5(link);
      } else {
        this.$bridge({
          url: link
        });
      }
    },
    handleClickToBanner() {
      this.handleJump('https://m.soyoung.com/tmwap27267#/');
    },
    handleClickToShowProgressTips() {
      uni.showModal({
        title: '',
        content: this.invite_record.progress_bar_data.detail,
        showCancel: false
      });
    },
    handleClickToRank() {
      console.log(
        this.invite_record.award_rank,
        (this.invite_record.award_rank || {}).jump_url
      );
      this.handleJump((this.invite_record.award_rank || {}).jump_url || '');
    },
    afterPosterCreated(res) {
      res.then((imageUrl) => {
        console.log('afterPosterCreated', imageUrl);
        this.qrsrc = imageUrl;
        if (this.isShare) {
          this.qrsrcArr[1] = imageUrl;
        } else {
          this.qrsrcArr[0] = imageUrl;
        }
      });
    },
    gohome() {
      uni.switchTab({
        url: '/pages/index'
      });
    },
    open(url) {
      uni.navigateTo({
        url,
        fail: (res) => {
          console.log('跳转失败: QA请看', res.errMsg);
        }
      });
    },
    getImageSrc(status) {
      switch (status) {
        // case 2:
        // return 'https://static.soyoung.com/sy-pre/2hmp850uavc2k-1723702200624.png';
        case 3:
          return 'https://static.soyoung.com/sy-design/3c3yxmgi8qwpi1736319732599.png';
        // return 'https://static.soyoung.com/sy-pre/2khi7jsx1l2r9-1723702200624.png';
        case 4:
          return 'https://static.soyoung.com/sy-design/3c3yxmgi8qwpi1736319732062.png';
        default:
          return 'https://static.soyoung.com/sy-design/3c3yxmgi8qwpi1736319732394.png';
        // return 'https://static.soyoung.com/sy-pre/jye3mwfi9z5i-1723702200624.png';
      }
    },
    pageShow() {
      this.$nextTick(() => {
        this.isload = true;
      });
    },
    async handleInvite() {
      const { errorCode } = await inviteUser();
      if (errorCode === 0) {
        this.showPop();
      }
    },
    showPop() {
      this.$refs.popup.open('bottom');
      this.qrsrc = '';
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_a:share_click',
        ext: {}
      });
      if (!this.isShare && !this.qrsrcArr[0]) {
        this.getQr(0);
        return;
      }
      if (this.isShare) {
        this.qrsrc = this.qrsrcArr[1];
      } else {
        this.qrsrc = this.qrsrcArr[0];
      }
    },
    async getInfo() {
      const { errorCode, errorMsg, responseData } = await getaHome({
        recommend_id: this.recommend_id,
        type: this.type
      });

      if (errorCode === 0) {
        this.successful = true;
        this.activity_status = responseData.activity_status;
        this.activity_status_desc = responseData.activity_status_desc;
        this.a_page_share_card_title_desc1 =
          responseData.a_page_share_card_title_desc1;
        this.a_page_share_card_title_desc2 =
          responseData.a_page_share_card_title_desc2;
        this.a_page_share_card_title_desc3 =
          responseData.a_page_share_card_title_desc3;
        this.invite_record = responseData.invite_record;

        this.config_data = responseData.config_data;
        this.recommend_data = responseData.recommend_data;
        this.login_user = responseData.login_user;
        if (this.invite_record) {
          this.has_more = !!this.invite_record.has_more;
        }
        setTimeout(() => {
          this.isload = true;
        }, 2000);
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    async nextPage() {
      const { errorCode, errorMsg, responseData } = await getInviteList({
        page: this.page
      });
      if (errorCode === 0) {
        this.invite_record.list = this.invite_record.list.concat(
          responseData.list || []
        );
        this.has_more = !!responseData.has_more;
        this.page++;
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    async getQr(index) {
      if (!this.qrsrcArr[index]) {
        const { errorCode, errorMsg, responseData } = await getQwImg({
          type: this.isShare ? this.type : '',
          recommend_id: this.isShare ? this.recommend_id : ''
        });
        if (errorCode === 0) {
          this.posterParams = {
            cover:
              this.config_data && this.config_data.a_page_card_img
                ? this.config_data.a_page_card_img.u
                : 'https://static.soyoung.com/sy-pre/cg1xlkx8zzeg-1750299000617.jpg',
            title:
              this.login_user?.name.length > 3
                ? this.login_user.name.slice(0, 3) + '...'
                : this.login_user?.name,
            subtitle:
              this.config_data.a_page_share_card_text ||
              '会员邀请新客到店，享受更多优惠',
            code: responseData.img
          };
        } else {
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      }
    }
  },
  computed: {
    ...mapGetters(['isLogin'])
  },
  async onLoad(options) {
    console.log('onLoad', options);
    if (!this.isLogin) {
      await new Promise((resolve) => setTimeout(resolve, 300));
      const isAuth = await this.$login().catch((msg) => {
        if (msg !== '页面退回') {
          uni.showModal({
            title: '登录异常',
            content: msg,
            showCancel: false
          });
        }
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
            fail: () => {
              uni.switchTab({
                url: '/pages/index'
              });
            }
          });
        }, 1000);
        return null;
      });
      if (!isAuth) return;
    }
    const userInfo = uni.getStorageSync('user_info') || {};
    if (options.scene) {
      options = qs.parse(decodeURIComponent(options.scene));
      if (options.md) {
        userInfo.marketActivityId = parseInt(options.md);
        this.$setUserInfoToStorage(userInfo);
      }
      console.log('options.scene', options);
    }
    if (options.market_activity_id) {
      userInfo.marketActivityId = parseInt(options.market_activity_id);
      this.$setUserInfoToStorage(userInfo);
    }
    this.type = options.type || '';
    this.recommend_id = options.recommend_id || options.r_id || '';
    await this.getInfo();
    if (this.recommend_data && this.recommend_data.list) {
      this.isShare = !!this.recommend_data.list[0].type;
    }
    if (this.isShare) {
      this.getQr(1);
    } else {
      this.getQr(0);
    }
  },
  async onPullDownRefresh() {
    this.page = 1;
    await this.getInfo();
    uni.stopPullDownRefresh();
  },
  onPageScroll(e) {
    if (e.scrollTop < 10) {
      this.opacity = 0;
    } else if (e.scrollTop <= 110 && e.scrollTop >= 10) {
      this.opacity = (e.scrollTop - 10) / 100;
    } else {
      this.opacity = 1;
    }
  },
  onReachBottom() {
    console.log('触底了');
    if (this.has_more) {
      this.nextPage();
    }
  },
  onShareAppMessage(res) {
    let title = this.config_data.a_page_share_card_text;
    let imageUrl = this.config_data.a_page_share_card_img.u;
    let path =
      this.config_data.a_page_share_card_share_url +
      (this.isShare
        ? `&recommend_type=${this.type}&recommend_id=${this.recommend_id}`
        : '');
    console.log('onShareAppMessage', res, path);
    if (res.from === 'button') {
      console.log('来自页面内转发按钮', res.target.dataset);
      if (res.target.dataset.name) {
        title = this.config_data.a_page_friend_share_card_text;
        imageUrl = this.config_data.a_page_friend_share_card_img.u;
      }
    }
    console.log(path, 'path');
    return {
      title,
      imageUrl,
      path
    };
  },
  onShow() {
    setTimeout(() => {
      this.$reportPageShow({
        info: 'sy_chain_store_tuan_old_brings_new_a_page',
        ext: {
          status: this.activity_status,
          content: this.activity_status_desc
        }
      });
    }, 1500);
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_old_brings_new_a_page',
      ext: {
        status: this.activity_status,
        content: this.activity_status_desc
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_old_brings_new_a_page',
      ext: {
        status: this.activity_status,
        content: this.activity_status_desc
      }
    });
  }
};
</script>

<style lang="less" scoped>
.page {
  // padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  // padding-bottom: ~'max(env(safe-area-inset-bottom), 12rpx)';
  min-height: 100vh;
  background: #e2e7e9;
  box-sizing: border-box;
  padding-bottom: 200rpx;
  position: relative;
  .page-bottom {
    height: 80rpx;
    // margin-top: 120rpx;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.skeleton {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  .skeleton-img {
    width: 100%;
    display: block;
  }
}
.rule {
  position: absolute;
  right: 0;
  top: 200rpx;
  background: rgba(255, 255, 255, 0.61);
  border-radius: 4px 0 0 4px;
  width: 46rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #555555;
  line-height: 30rpx;
  box-sizing: border-box;
  padding: 10rpx 12rpx;
}
.main {
  padding-top: 460rpx;
  width: 660rpx;
  position: relative;
  margin: 0% auto;
  .main-wrapper {
    position: relative;
    .main-box {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 80rpx;
      box-sizing: border-box;
      .main-box-title {
        font-family: PingFangSC-Light;
        text-align: center;
        font-size: 48rpx;
        line-height: 66rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 200;
      }
      .main-box-subtitle {
        margin-top: 12rpx;
        font-family: PingFangSC-Light;
        font-size: 26rpx;
        line-height: 36rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        font-weight: 200;
        white-space: pre-wrap;
      }
      .main-box-divide {
        width: 342rpx;
        height: 2rpx;
        background: #e6e6e6;
        margin: 80rpx auto;
      }
    }
  }
  .img {
    display: block;
    width: 100%;
  }
  .shareBottom {
    margin: 0 auto;
    background: #222222;
    // background-image: linear-gradient(180deg, #39dcb7 0%, #00ab84 100%);
    // border-radius: 50rpx;
    width: 490rpx;
    height: 100rpx;
    font-family: PingFangSC-Semibold;
    font-size: 38rpx;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    // box-shadow: 0 2px 3px 0 rgba(134, 0, 25, 0.17);
  }
  .shareMain {
    position: absolute;
    bottom: 56rpx;
    left: 0;
    right: 0;
    .shareText {
      width: 560rpx;
      height: 84rpx;
      margin: 0 auto;
      margin-top: 30rpx;
      background-image: linear-gradient(
        -89deg,
        rgba(255, 151, 171, 0) 0%,
        rgba(255, 146, 167, 0.5) 18%,
        rgba(255, 166, 182, 0.75) 84%
      );
      border-radius: 8px;
      display: flex;
      align-items: center;
      .select {
        width: 36rpx;
        height: 36rpx;
        margin-left: 28rpx;
        .icon {
          width: 36rpx;
          height: 36rpx;
        }
      }
      .right {
        margin-left: 10rpx;
        .text1 {
          font-family: PingFangSC-Semibold;
          font-size: 26rpx;
          color: #ffffff;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .text2 {
          opacity: 0.75;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #ffffff;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          .line {
            display: inline-block;
            opacity: 0.47;
            background: #ffffff;
            width: 2rpx;
            height: 20rpx;
            margin: 0 10rpx;
          }
          .icon {
            width: 22rpx;
            height: 22rpx;
            margin-right: 10rpx;
          }
        }
      }
      .avatar {
        width: 60rpx;
        height: 60rpx;
        border: 1rpx solid #fff;
        border-radius: 50%;
        overflow: hidden;
        margin-left: 16rpx;
        flex-shrink: 0;
        .icon {
          width: 60rpx;
          height: 60rpx;
        }
      }
    }
  }
  .statusAbNormal {
    width: 100%;
    height: 772rpx;
    background: url(https://static.soyoung.com/sy-design/2hca7nc2ykif91736319732308.png)
      no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding-top: 80rpx;
    position: relative;
    image {
      width: 500rpx;
      height: 400rpx;
    }
    .exceptionPrompt {
      font-family: PingFangSC-Light;
      font-size: 30rpx;
      color: #333333;
      font-weight: 200;
      text-align: center;
      line-height: 42rpx;
      margin: 0 auto;
      margin-top: -42rpx;
      width: 480rpx;
    }
    .shareBottom {
      position: absolute;
      bottom: 42rpx;
    }
  }
}
.flow {
  margin: 20rpx;
  margin-bottom: 0;
  margin-top: 40rpx;
  overflow: hidden;
  border-radius: 8rpx;
  .img {
    width: 100%;
    display: block;
  }
}
.invitationRecord {
  margin: 40rpx 20rpx 0;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  .title {
    display: flex;
    padding: 30rpx;
    padding-top: 40rpx;
    justify-content: space-between;
    align-items: center;
    .left {
      font-family: PingFangSC-Regular;
      font-size: 36rpx;
      color: #222222;
      font-weight: 400;
    }
    .right {
      display: flex;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #333;
      align-items: center;
      .icon {
        width: 12rpx;
        height: 22rpx;
        margin-left: 16rpx;
      }
    }
  }
  .amount {
    max-height: 298rpx;
    margin: 0 20rpx;
    margin-bottom: 20rpx;
    background-color: #f1f1f1;
    padding-bottom: 58rpx;
    .money {
      display: flex;
      padding-top: 40rpx;
      .li {
        flex: 1;
        text-align: center;
        position: relative;
        .num {
          font-family: OutFit-Regular;
          font-size: 34rpx;
          color: #333333;
          font-weight: 200;
          font-size: 40rpx;
          display: flex;
          align-items: baseline;
          justify-content: center;
          .symbol {
            font-size: 14rpx;
            padding-right: 2rpx;
          }
        }
        .text {
          font-family: PingFangSC-Light;
          font-size: 20rpx;
          color: #333333;
          font-weight: 200;
        }
        .line {
          position: absolute;
          background: #a6a6a6;
          right: 0;
          width: 2rpx;
          height: 140rpx;
          top: -23rpx;
          transform: scale(0.5);
        }
      }
    }
    .h-line {
      width: 610rpx;
      height: 1rpx;
      margin-top: 50rpx;
      background: #d8d8d8;
      margin-inline: auto;
    }
    .progress {
      display: flex;
      align-items: center;
      line-height: 34rpx;
      margin-top: 31rpx;
      margin-inline: 30rpx;
      .p-title {
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        height: 34rpx;
        color: #646464;
        letter-spacing: 0;
        font-weight: 400;
        flex-shrink: 0;
      }
      .p-icon {
        width: 22rpx;
        height: 22rpx;
        flex-shrink: 0;
        margin-left: 6rpx;
      }
      .p-bar {
        width: 322rpx;
        height: 10rpx;
        background: #e6e6e6;
        margin-inline: 30rpx;

        position: relative;
        .active {
          background-color: #a9ea6a;
          height: 10rpx;
        }
        .button {
          position: absolute;
          top: -6rpx;
          .b-icon {
            width: 24rpx;
            height: 24rpx;
            box-sizing: border-box;
            background-color: #a9ea6a;
            border-radius: 50%;
            border: 8rpx solid #fff;
          }
          .b-num {
            min-width: 140rpx;
            font-family: Outfit-Regular;
            font-size: 20rpx;
            color: #a6a6a6;
            letter-spacing: 0;
            text-align: justify;
            font-weight: 400;
          }
        }
      }

      .p-num {
        font-family: Outfit-Regular;
        font-size: 20rpx;
        color: #222222;
        letter-spacing: 0;
        text-align: justify;
        font-weight: 400;
        flex-shrink: 0;
      }
    }
  }
  .banner {
    height: 168rpx;
    margin: 0 20rpx;
    background-size: 100% 100%;
    position: relative;
    .banner-image {
      width: 100%;
      height: 100%;
      position: absolute;
    }
    .banner-box {
      width: 100%;
      height: 100%;
      padding: 30rpx;
      box-sizing: border-box;
      .banner-title {
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        line-height: 34rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 500;
        margin-bottom: 12rpx;
        position: relative;
        z-index: 1;
      }
      .banner-subtitle {
        margin-top: 6rpx;
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        line-height: 28rpx;
        color: #646464;
        letter-spacing: 0;
        font-weight: 400;
        position: relative;
        z-index: 1;
      }
    }
  }
  .rank {
    height: 160rpx;
    margin: 20rpx;
    background-size: 100% 100%;
    position: relative;
    .rank-image {
      width: 100%;
      height: 100%;
      position: absolute;
    }
  }
  .nodata {
    width: 364rpx;
    margin: 0 auto;
    padding: 178rpx 0;
    .icon {
      width: 364rpx;
      height: 264rpx;
    }
    .text {
      font-size: 24rpx;
      color: #333;
      margin-top: 20rpx;
      text-align: center;
      font-family: PingFangSC-Light;
      font-weight: 200;
    }
  }
  .list {
    padding-top: 20rpx;
    margin: 0 20rpx;
    padding-bottom: 50rpx;
    .item {
      // background: url('https://static.soyoung.com/sy-pre/1rs5ka8622e4z-1723518600638.png')
      //   no-repeat;
      background: #f2f2f2;
      // background-size: 100% 100%;
      border-radius: 6rpx;
      margin-bottom: 16rpx;
      height: 150rpx;
      display: flex;
      .left {
        width: 554rpx;
        display: flex;
        align-items: center;
        padding: 26rpx 60rpx 30rpx 32rpx;
        .avatar {
          width: 88rpx;
          height: 88rpx;
          position: relative;
          margin-right: 20rpx;
          .icon {
            width: 88rpx;
            height: 88rpx;
            border-radius: 50%;
          }
          .tip {
            width: 62rpx;
            height: 22rpx;
            position: absolute;
            bottom: 0;
            left: 50%;
            background: url('https://static.soyoung.com/sy-design/1w40ytb2yww7s1736319735144.png')
              no-repeat;
            background-size: 100% 100%;
            margin-left: -31rpx;
            &::after {
              border: none;
            }
          }
        }
        .text {
          .ti {
            display: flex;
            align-items: center;
            font-family: PingFangTC-Medium;
            font-size: 24rpx;
            color: #333333;
            font-weight: 500;
            line-height: 34rpx;
            .status {
              margin-left: 10rpx;
              font-size: 16rpx;
              height: 22rpx;
              line-height: 22rpx;
              border-radius: 2rpx;
              min-width: 56rpx;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .status0 {
              background: #ff6c0e;
              color: #fff;
            }
            .status1 {
              background: #a9ea6a;
              color: #333;
            }
            .status2 {
              color: #a3e46b;
              background: #333333;
            }
            .status3 {
              background: #bababa;
              color: #646464;
            }
          }
          .desc {
            font-size: 24rpx;
            font-family: PingFangTC-Light;
            color: #333333;
            font-weight: 200;
            margin-top: 6rpx;
          }
        }
      }
      .line {
        width: 4rpx;
        height: 100%;
        background: url(https://static.soyoung.com/sy-pre/group1736319734931-1736320200626.png)
          no-repeat;
        background-size: 100% 100%;
      }
      .right {
        width: 100rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #222222;
        .icon {
          width: 26rpx;
          height: 26rpx;
          display: block;
        }
        .money {
          font-size: 20rpx;
          line-height: 26rpx;
          margin-top: 14rpx;
          font-family: OutFit-Regular;
          color: #333333;
          font-weight: 200;
        }
        .text {
          font-size: 20rpx;
          line-height: 28rpx;
          font-family: OutFit-Regular;
          color: #333333;
          font-weight: 200;
        }
        &.fff {
          color: #999999;
        }
      }
    }
    .tip {
      margin-top: 20rpx;
      display: flex;
      justify-content: flex-end;
      font-family: PingFangTC-Light;
      font-size: 20rpx;
      color: #a6a6a6;
      font-weight: 200;
    }
  }
}
.share-box {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: #fff;
  position: relative;
  .title {
    text-align: center;
    font-family: PingFangSC-Medium;
    padding: 30rpx 0;
    font-size: 32rpx;
    color: #030303;
    font-weight: 500;
  }
  .close {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    right: 20rpx;
    top: 28rpx;
    background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1726026826488.png)
      no-repeat center / 100%;
    z-index: 9;
  }
  .share-type-list {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding-top: 16rpx;
    padding-bottom: 16rpx;
  }
  .item {
    margin-left: 0;
    padding: 0;
    line-height: 1.2;
    flex-wrap: nowrap;
    background-color: transparent;
    &:first-child {
      margin-right: 80rpx;
    }
    &::after {
      border: none;
    }
  }
  .cover {
    margin-bottom: 20rpx;
    height: 45 * 2rpx;
    width: 45 * 2rpx;
  }
  .text {
    font-size: 24rpx;
    color: #666666;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
  .share-button {
    padding: 0 50rpx;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20rpx;
  }
  .button {
    position: relative;
    height: 124rpx;
    font-size: 30rpx;
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    border-radius: 0;
    border: none;
    background-color: #333333;
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 500;
    height: 84rpx;
    line-height: 84rpx;
    width: 100%;
  }
  .disabled {
    filter: grayscale(1);
    .text {
      color: #777;
    }
  }
}
</style>
