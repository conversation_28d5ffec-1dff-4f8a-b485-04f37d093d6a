<template>
  <div
    v-if="menuRect.top"
    class="pageTop"
    :style="{
      height: menuRect.bottom + 6 + 'px',
      background: `rgba(255, 255, 255, ${opacity})`
    }"
  >
    <div
      class="nav"
      @click="handleBack"
      :style="{
        top: menuRect.top + 'px',
        width: menuRect.height + 'px',
        height: menuRect.height + 'px'
      }"
    >
      <div class="backBox">
        <img
          class="back"
          src="https://static.soyoung.com/sy-pre/2sh0btiz2bbfy-1712715000686.png"
        />
      </div>
    </div>
    <div
      class="title"
      :style="{
        top: menuRect.top + 'px',
        height: menuRect.height + 'px',
        opacity: titleIsShow ? 1 : opacity
      }"
    >
      {{ title }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    opacity: {
      default: 0,
      type: Number
    },
    title: {
      type: String,
      default: '分享领无门槛优惠券'
    },
    titleIsShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFirst: false,
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      }
    };
  },
  computed: {},
  created() {
    this.isFirst = getCurrentPages().length === 1;
    this.menuRect = uni.getMenuButtonBoundingClientRect();
  },
  methods: {
    handleHome() {
      uni.switchTab({
        url: '/pages/index',
        fail: (res) => {
          console.log('跳转失败', res);
        }
      });
    },
    handleBack() {
      if (this.isFirst) {
        this.handleHome();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;
  .share {
    display: flex;
    position: fixed;
    align-items: center;
    height: 88rpx;
    right: 214rpx;
    background-color: transparent;
    padding: 0;
    &::after {
      border: none;
    }
    .shareIcon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  .title {
    position: fixed;
    left: 50%;
    font-family: PingFangSC-Medium;
    font-size: 36rpx;
    color: #333333;
    display: flex;
    align-items: center;
    transform: translateX(-50%);
  }
  .nav {
    border-radius: 80rpx;
    position: fixed;
    left: 0;
    padding-left: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    .back {
      width: 38rpx;
      height: 38rpx;
      display: block;
    }
    .index {
      width: 38rpx;
      height: 38rpx;
      display: block;
    }
    .line {
      width: 1rpx;
      height: 36rpx;
      background: rgba(0, 0, 0, 0.2);
    }
  }
}
</style>
