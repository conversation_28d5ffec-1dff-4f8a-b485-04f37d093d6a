<template>
  <page-meta background-color="#e2e7e9" :page-style="pageScrollStyle">
    <NavBar :opacity="opacity" :title="' '" />
    <!-- <div
      class="page"
      v-if="isload"
      :style="{
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        backgroundImage: `url(${config_data.a_page_head_img.u})`
      }"
    > -->
    <div v-if="isload && userData" class="page">
      <div class="rule" @click="ruleClick()">规则</div>
      <div class="main">
        <!-- 奖励模块未助力情况 -->
        <div
          v-if="pageStatus === 'normal'"
          class="giftBox"
          :style="{
            // backgroundImage: `url(${pageData.config_data.award_img.u})`
            backgroundImage: `url(https://static.soyoung.com/sy-design/2hca7nc2ykif91741659615199.png)`
          }"
        >
          <div class="giftBox-top">
            <div class="name">
              {{
                userData.invite_user
                  ? userData.invite_user.name.length > 5
                    ? `${userData.invite_user.name.slice(0, 5)}...`
                    : userData.invite_user.name
                  : ''
              }}
            </div>
            <div class="giftBox-title">请你免费做个脸</div>
          </div>
          <div class="giftBox-devide"></div>
          <div class="giftBox-bottom">
            <div class="giftBox-bottom-title">
              {{ pageData.page_share_card_title1 }}
            </div>
            <div class="giftBox-bottom-subtitle">
              {{ pageData.page_share_card_title2 }}
            </div>
            <div class="giftBox-bottom-price">
              <span class="giftBox-bottom-price-symbol">¥</span
              >{{ pageData.config_data.b_user_invite_gift_coupon_id_price }}
            </div>
          </div>
          <div class="greenBtn" @click="getGift">免费领取</div>
        </div>
        <!-- 助力成功 -->
        <div
          v-if="pageStatus === 'success'"
          class="longBox"
          :style="{
            backgroundImage: `url(https://static.soyoung.com/sy-design/2hca637nb8ad51749435736166.png)`
          }"
        >
          <div class="invite-box">
            <div class="invite-box-top">
              <div class="invite-box-title">
                {{ successPageData.page_invite_title1 }}
              </div>
              <div class="invite-box-subtitle">
                {{ successPageData.page_invite_title2 }}
              </div>
            </div>
            <div class="greenBtn shareButton" @click="successGoAPage">
              去分享
            </div>
          </div>
          <div class="long-divide">
            <div></div>
          </div>
          <div class="product-box">
            <div class="giftBox-top">
              <div class="name">
                {{
                  successPageData.invite_user
                    ? successPageData.invite_user.name
                    : ''
                }}
                请你免费做个脸
              </div>
              <div class="giftBox-title"></div>
            </div>

            <div class="giftBox-bottom">
              <div class="giftBox-bottom-title">
                {{ successPageData.page_share_card_title1 }}
              </div>
              <div class="giftBox-bottom-subtitle">
                {{ successPageData.page_share_card_title2 }}
              </div>
              <div class="giftBox-bottom-price">
                <span class="giftBox-bottom-price-symbol">¥</span
                >{{
                  successPageData.config_data.b_user_invite_gift_coupon_id_price
                }}
              </div>
              <div
                v-if="
                  successPageData.award_data &&
                  successPageData.award_data.expiry_date
                "
                class="tips"
              >
                请在{{ successPageData.award_data.expiry_date }}前兑换并到店体验
              </div>
            </div>
            <div class="greenBtn" @click="goProductPage">去使用</div>
          </div>
        </div>
        <!-- 助力失败之到过店 -->
        <div
          v-if="['failArrive', 'failNotArrive'].includes(pageStatus)"
          class="giftBox arriveBox"
        >
          <div class="main-box">
            <div class="main-box-top">
              <div class="main-box-title">会员邀请新客到店</div>
              <div class="main-box-subtitle">
                可享{{ pageData.page_share_card_num1 }}元无门槛优惠券
              </div>
            </div>
            <div class="main-box-divide"></div>
            <div class="main-box-bottom">
              <div class="main-box-title">新客到店消费，你可得</div>
              <div class="main-box-subtitle">
                至高{{ pageData.page_share_card_num2 }}元无门槛优惠券
              </div>
            </div>
          </div>
          <div class="greenBtn shareButton" @click="goApage">去分享</div>
        </div>

        <!-- 助力失败之到没到过店 -->
        <!-- <div
          v-if="pageStatus === 'failNotArrive' && failPageData.qc_code_info"
          class="notArriveBox"
        >
          <div class="title">
            {{ failPageData.qc_code_info.text || '' }}
          </div>
          <div class="kfName">
            {{ failPageData.qc_code_info.consultant_name || '' }}
          </div>
          <div class="qrCodeBox">
            <img
              show-menu-by-longpress
              :src="failPageData.qc_code_info.url || ''"
              alt=""
              class="qrCode"
            />
          </div>
          <div class="greenBtn">
            <img
              src="https://static.soyoung.com/sy-design/32q76aat6dbg71736319734225.png"
              alt=""
            />
          </div>
        </div> -->
        <!-- 用户反馈 -->
        <div
          v-if="needShowFK"
          :data-type="pageData.recommend_data.list[0].type"
          class="feedBack"
        >
          <div class="bgBox">
            <img
              src="https://static.soyoung.com/sy-design/2hca5b5fhk6c31723620383917.png"
              alt=""
              class="bg1"
            />
            <img
              src="https://static.soyoung.com/sy-design/feedback1723620383943.png"
              alt=""
              class="bg2"
            />
            <div class="content">
              <div class="top">
                <img
                  :src="pageData.invite_user && pageData.invite_user.avatar"
                  alt=""
                  class="avatar"
                />
                <div class="text">
                  {{ pageData.invite_user ? pageData.invite_user.name : ''
                  }}{{
                    pageData.recommend_data.list[0].type === 1
                      ? '的体验反馈'
                      : '推荐的咨询师'
                  }}{{
                    pageData.recommend_data.list[0].type === 1
                      ? ''
                      : pageData.recommend_data.list[0].data.user &&
                        `-${pageData.recommend_data.list[0].data.user.name}`
                  }}
                </div>
              </div>
              <div class="wrapper">
                <img
                  src="https://static.soyoung.com/sy-pre/17axvaesl2sda-1724987400624.png"
                  alt=""
                  class="jiao"
                />
                <div class="bottom">
                  <div class="line1">
                    <block v-if="pageData.recommend_data.list[0].type === 1">
                      共到访<span style="font-weight: bold"
                        >{{
                          pageData.recommend_data.list[0].data.visit_cnt
                        }}次</span
                      >，<block
                        v-if="pageData.recommend_data.list[0].data.item_cnt > 0"
                        >完成<span style="font-weight: bold"
                          >{{
                            pageData.recommend_data.list[0].data.item_cnt
                          }}个</span
                        >项目服务，</block
                      >好评率<span style="font-weight: bold"
                        >{{
                          pageData.recommend_data.list[0].data
                            .good_evaluate_rate
                        }}%</span
                      >
                    </block>
                    <block v-else>
                      累积服务<span style="font-weight: bold"
                        >{{
                          pageData.recommend_data.list[0].data.visit_cnt
                        }}人次</span
                      >，好评率<span style="font-weight: bold"
                        >{{
                          pageData.recommend_data.list[0].data
                            .good_evaluate_rate
                        }}%</span
                      >
                    </block>
                  </div>
                  <div
                    v-if="pageData.recommend_data.list[0].data.tenant"
                    class="line2"
                  >
                    <!-- （所属门店） -->
                    <div class="left">
                      {{
                        pageData.recommend_data.list[0].type === 1
                          ? '体验'
                          : '所属'
                      }}门店：
                    </div>
                    <div class="right">
                      {{
                        pageData.recommend_data.list[0].data.tenant.name || ''
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 体验流程 -->
        <div v-if="pageStatus === 'success'" class="processBox">
          <image
            mode="widthFix"
            src="https://static.soyoung.com/sy-design/u1tbn6dz8oz41736319733867.png"
            alt=""
            class="process"
          />
        </div>
      </div>
      <div
        v-if="
          userData.config_data &&
          (userData.config_data.tenant_img_list ||
            userData.config_data.award_info_img_list)
        "
        class="introList"
      >
        <!-- <image
          class="intro"
          mode="widthFix"
          src="https://static.soyoung.com/sy-pre/20240814-155439-1723619400630.jpeg"
        ></image>
        <image
          class="intro"
          mode="widthFix"
          src="https://static.soyoung.com/sy-pre/20240814-155439-1723619400630.jpeg"
        ></image> -->
        <image
          v-for="(item, index) in userData.config_data.tenant_img_list"
          :key="index"
          class="intro"
          mode="widthFix"
          :src="item.u"
        ></image>
        <image
          v-for="(item, index) in userData.config_data.award_info_img_list"
          :key="index"
          class="intro"
          mode="widthFix"
          :src="item.u"
        ></image>
      </div>
      <!-- 加c弹窗 -->
      <BottomModal
        :bottom="0"
        :showTitle="false"
        background="#f8f8f8"
        :visible="addVisible"
        @close="onSelectionClose"
      >
        <div
          class="spu-bottom-popup"
          :style="{
            backgroundImage: `url(${
              consultantInfo.bottom_img && consultantInfo.bottom_img.u
            })`
          }"
        >
          <!-- <div class="bigTitle">
            添加{{
              userData.invite_user
                ? userData.invite_user.name.length > 5
                  ? `${userData.invite_user.name.slice(0, 5)}...`
                  : userData.invite_user.name
                : ''
            }}的咨询师
          </div>
          <div class="subTitle">即可领取姐妹专享卡</div> -->
          <img
            show-menu-by-longpress
            :src="consultantInfo.qr_code"
            alt=""
            class="qrCode"
          />
        </div>
      </BottomModal>
    </div>
    <div v-else class="skeleton">
      <image
        class="skeleton-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-design/32q76aat6dbg71723186292436.png"
      >
      </image>
    </div>
    <div
      v-if="tipsVisible"
      class="mask"
      :style="{
        backgroundColor:
          pageStatus === 'failToHome' || pageStatus === 'failToAPage'
            ? '#000'
            : 'rgba(0, 0, 0, 0.8)'
      }"
    >
      <div class="tipsPop">
        <div class="top">
          <image
            mode="widthFix"
            src="https://static.soyoung.com/sy-design/bzsokyai5osd1749435736693.png"
            alt=""
            class="close"
            @click="closeTips"
          />
        </div>
        <div class="content">
          {{ tipsContent }}
        </div>
        <div
          class="btn shareButton"
          @click="popupGoAgage"
          v-if="['failNotArrive', 'failArrive'].includes(pageStatus)"
        >
          去分享
        </div>
        <div class="btn" @click="toFailStep2" v-else>知道了</div>
      </div>
    </div>
    <root-portal>
      <PageLoading :visible="pageLoading" />
    </root-portal>
  </page-meta>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import {
  getbHome,
  apiUserhelp,
  getHelpSucceedInfo,
  getHelpFailInfo,
  getCouponInfo
} from '@/api/activity';
import BottomModal from '@/components/bottomModal.vue';
import PageLoading from '@/components/pageLoading.vue';
import NavBar from './NavBar.vue';

export default {
  components: {
    NavBar,
    BottomModal,
    PageLoading
  },
  data() {
    return {
      opacity: 0,
      isload: false,
      addVisible: false, // 加c弹窗
      pageScrollStyle: '',
      tipsVisible: false,
      pageLoading: false,
      queryParams: {
        invite_code: '',
        market_activity_id: '',
        recommend_type: '',
        recommend_id: '',
        from: ''
      },
      // B页面第一步页面信息
      pageData: null,
      // 助力失败页面信息
      successPageData: null,
      // 助力失败页面信息
      failPageData: null,
      // 助力成功后咨询师信息
      consultantInfo: {},
      pageStatus: '',
      tipsContent: '',
      trackUseStatus: '' // 埋点用 记录页面初始状态 未助力：0 助力成功：1 助力失败：2
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin']),
    userData() {
      console.log(
        this.successPageData || this.failPageData || this.pageData,
        'this.successPageData || this.failPageData || this.pageData'
      );
      return this.successPageData || this.failPageData || this.pageData;
    },
    needShowFK() {
      let status = false;
      let type = '';
      const inviteInfoList = this.pageData?.recommend_data?.list;
      const inviteInfo =
        inviteInfoList && inviteInfoList.length && inviteInfoList[0];
      if (inviteInfo) {
        type = inviteInfo.type;
      }
      if (type && inviteInfo.data) {
        status =
          inviteInfo.data.visit_cnt > 0 &&
          inviteInfo.data.good_evaluate_rate >= 90;
      }
      return status;
    }
  },
  methods: {
    async goProductPage() {
      if (this.pageLoading) return;
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:go_use_click',
        ext: {}
      });
      this.pageLoading = true;
      const { errorCode, errorMsg } = await getCouponInfo();
      this.pageLoading = false;
      if (+errorCode === 0) {
        this.open(
          `/packageCoupon/applicable-product?id=${
            this.successPageData.config_data?.b_user_invite_gift_coupon_id || ''
          }`
        );
        // this.open(
        //   `/pages/product?id=${this.successPageData.award_data?.pid || ''}`
        // );
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    ruleClick() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:rule_click',
        ext: {}
      });
      this.open(this.userData.config_data.b_page_role_link);
    },
    initExposure() {
      console.log(this.$registerExposure, 'this.$registerExposure(');
      this.$registerExposure(
        '.feedBack',
        (res) => {
          console.log(res, 'ressssssssssss');
          const { type } = res?.dataset || {};
          console.log(type, 'type');
          this.$reportData({
            info: 'sy_chain_store_tuan_old_brings_new_b:share_info_exposure',
            ext: {
              type
            }
          });
        },
        this
      );
      this.$registerExposure(
        '.shareButton',
        () => {
          this.$reportData({
            info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
            ext: {}
          });
        },
        this
      );
    },
    successGoAPage() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:share_click',
        ext: {}
      });
      this.open(`/packageActivity/invitation/index`);
    },
    goApage() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:to_a_click',
        ext: {}
      });
      this.open(`/packageActivity/invitation/index`);
    },
    popupGoAgage() {
      this.tipsVisible = false;
      this.enablePageScroll();
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:share_click',
        ext: {}
      });
      this.open(`/packageActivity/invitation/index`);
    },
    // 格式化入参
    formatEnterOptions(options) {
      // 正常情况的入参
      if (!options.scene) {
        return options;
      }
      // 扫码进入
      const scene = decodeURIComponent(options.scene);
      console.log(scene, 'scene');
      const sceneArr = scene.split('&');
      const queryOption = {
        invite_code: sceneArr[0] || '',
        market_activity_id: sceneArr[1] || '',
        recommend_type: sceneArr[2] || '',
        recommend_id: sceneArr[3] || ''
      };
      console.log(queryOption, 'queryOption');
      return queryOption;
      // if (sign) {
      //   const splitArray = sign.split('_');
      //   const id = splitArray?.[0] || '';
      //   if (!id) {
      //     this.catchError('驻场码解析错误');
      //     return null;
      //   }
      //   return {
      //     id
      //   };
      // }
      // return null;
    },
    toFailStep2() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:fail_pop_click',
        ext: {
          content: this.tipsContent
        }
      });
      if (this.pageStatus === 'failToAPage') {
        this.open(`/packageActivity/invitation/index`);
      } else if (this.pageStatus === 'failToHome') {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        this.tipsVisible = false;
      }
    },
    async getSuccessInfo() {
      if (!this.isLogin) {
        // 校验登陆
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return false;
        });
        // 登陆异常，提示并重新登陆
        if (!isAuth) {
          this.pageLoading = false;
          return;
        }
        // 重新登陆之后 刷新接口
        await this.getSuccessInfo();
        // if (this.from_uid === +this.userInfo.uid) return;
      }
      let { errorCode, errorMsg, responseData } = await getHelpSucceedInfo(
        this.queryParams
      );
      if (+errorCode === 0) {
        this.successPageData = responseData;
        if (!this.isload) {
          this.$nextTick(() => {
            this.initExposure();
          });
        }

        // if (!onload) {
        //   this.showAddPop();
        // }
      } else if (+errorCode === 2) {
        this.pageStatus = 'failArrive';
        this.showTips(errorMsg);
        this.getFailInfo(errorMsg);
        this.fillPageData = responseData;
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
      } else if (+errorCode === 3) {
        this.pageStatus = 'failNotArrive';
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
        this.showTips(errorMsg);
        this.getFailInfo(errorMsg);
      } else if (+errorCode === 4) {
        this.pageStatus = 'failToHome';
        this.showTips(errorMsg);
      } else if (+errorCode === 5) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index'
          });
        }, 2000);
        return;
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
      this.isload = true;
    },
    async getFailInfo(failText) {
      const { errorCode, errorMsg, responseData } = await getHelpFailInfo({
        ...this.queryParams,
        help_error_type: this.pageStatus === 'failNotArrive' ? 3 : 2,
        fail_text: failText
      });
      if (+errorCode === 0) {
        this.failPageData = responseData;
        // if (!onload) {
        //   this.showAddPop();
        // }
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index'
          });
        }, 2000);
        return;
      }
    },
    async getGift() {
      if (this.pageLoading) return;
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:receive_click',
        ext: {}
      });
      this.pageLoading = true;
      if (!this.isLogin) {
        // 校验登陆
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return false;
        });
        // 登陆异常，提示并重新登陆
        if (!isAuth) {
          this.pageLoading = false;
          return;
        }
        // 重新登陆之后 刷新接口
        await this.getInfo();
        // if (this.from_uid === +this.userInfo.uid) return;
      }
      let { errorCode, errorMsg, responseData } = await apiUserhelp(
        this.queryParams
      );
      this.pageLoading = false;
      if (+errorCode === 0) {
        this.consultantInfo = responseData;
        this.showAddPop();
      } else if (+errorCode === 1) {
        // errorCode：1-已助力（跳转到助力成功页）2-助力失败（跳转到助力失败页-场景一）3-助力失败（跳转到助力失败页-场景二）4-助力失败（跳转到A邀请页面）
        this.pageStatus = 'success';
        this.getSuccessInfo();
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
      } else if (+errorCode === 2) {
        this.pageStatus = 'failArrive';
        this.showTips(errorMsg);
        this.getFailInfo(errorMsg);
        this.fillPageData = responseData;
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
      } else if (+errorCode === 3) {
        this.pageStatus = 'failNotArrive';
        this.showTips(errorMsg);
        this.getFailInfo(errorMsg);
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
      } else if (+errorCode === 4) {
        this.pageStatus = 'failToAPage';
        this.showTips(errorMsg);
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    showTips(str) {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:fail_pop_exposure',
        ext: {
          content: str
        }
      });
      this.tipsVisible = true;
      this.tipsContent = str;
      if (['failNotArrive', 'failArrive'].includes(this.pageStatus)) {
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
      }
    },
    closeTips() {
      this.tipsVisible = false;
      this.enablePageScroll();
    },
    async showAddPop() {
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:add_c_pop_exposure',
        ext: {}
      });
      this.addVisible = true;
      this.disablePageScroll();
    },
    disablePageScroll() {
      this.pageScrollStyle = 'overflow:hidden;height:100vh;';
    },
    enablePageScroll() {
      this.pageScrollStyle = '';
    },
    hideAdd() {
      this.addVisible = false;
      this.enablePageScroll();
    },
    onSelectionClose() {
      this.hideAdd();
      // this.reportStat('sy_wxtuan_tuan_product_info:guige_pop_close_click');
    },
    open(url) {
      uni.navigateTo({
        url,
        fail: (res) => {
          console.log('跳转失败: QA请看', res.errMsg);
        }
      });
    },
    async getInfo() {
      let { errorCode, errorMsg, responseData } = await getbHome(
        this.queryParams
      );
      if (+errorCode === 0) {
        this.pageStatus = 'normal';
        this.trackUseStatus = 0;
        this.pageData = responseData;
      } else if (+errorCode === 1) {
        //errorCode：1-已助力（跳转到助力成功页）4 助力自己 5-助力失败（跳转到A邀请页面）
        this.pageStatus = 'success';
        this.trackUseStatus = 1;
        this.getSuccessInfo();
        this.$reportData({
          info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
          ext: {}
        });
      } else if (+errorCode === 4) {
        this.trackUseStatus = 2;
        uni.redirectTo({
          url: `/packageActivity/invitation/index?market_activity_id=${
            this.queryParams.market_activity_id || ''
          }&type=${this.queryParams.recommend_type || ''}&recommend_id=${
            this.queryParams.recommend_id || ''
          }`
        });
      } else if (+errorCode === 5) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index'
          });
        }, 2000);
        return;
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
      if (!this.isload) {
        this.$nextTick(() => {
          this.initExposure();
        });
      }
      this.isload = true;
    }
  },
  onLoad(options) {
    this.queryParams = this.formatEnterOptions(options);
    console.log('queryParams', this.queryParams);
    if (!this.queryParams.from && !this.queryParams.invite_code) {
      console.log('没有 from 和 invite_code，重定向');
      uni.redirectTo({
        url: '/packageActivity/invitation/index'
      });
      return;
    }
    // this.getInfo();
    if (!this.queryParams) return;
    if (options.from === 'qywx') {
      this.pageStatus = 'success';
      this.getSuccessInfo();
      this.$reportData({
        info: 'sy_chain_store_tuan_old_brings_new_b:share_exposure',
        ext: {}
      });
    } else {
      this.getInfo();
    }
  },
  onPageScroll(e) {
    if (e.scrollTop < 10) {
      this.opacity = 0;
    } else if (e.scrollTop <= 110 && e.scrollTop >= 10) {
      this.opacity = (e.scrollTop - 10) / 100;
    } else {
      this.opacity = 1;
    }
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_chain_store_tuan_old_brings_new_b_page',
      ext: {
        status: this.trackUseStatus,
        from: this.queryParams.from === 'qywx' ? 'qywx' : 'share'
      }
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_old_brings_new_b_page',
      ext: {
        status: this.trackUseStatus,
        from: this.queryParams.from === 'qywx' ? 'qywx' : 'share'
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_old_brings_new_b_page',
      ext: {
        status: this.trackUseStatus,
        from: this.queryParams.from === 'qywx' ? 'qywx' : 'share'
      }
    });
  }
  // onShareAppMessage(res) {
  //   console.log(res);
  //   let title = this.config_data.a_page_share_card_text;
  //   let imageUrl = this.config_data.a_page_share_card_img.u;
  //   if (res.from === 'button') {
  //     console.log('来自页面内转发按钮');
  //     title = this.config_data.a_page_friend_share_card_text;
  //     imageUrl = this.config_data.a_page_friend_share_card_img.u;
  //   }
  //   return {
  //     title,
  //     imageUrl,
  //     path: this.config_data.a_page_share_card_share_url
  //   };
  // }
};
</script>

<style lang="scss" scoped>
.page {
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: max(env(safe-area-inset-bottom), 12rpx);
  min-height: 100vh;
  background: #e2e7e9;
  // background: #ffd8dd
  //   url('https://static.soyoung.com/sy-design/2hca637nb8ad51723620384742.png')
  //   no-repeat top center / 100%;
}
.skeleton {
  .skeleton-img {
    width: 100%;
    display: block;
  }
}
.rule {
  position: absolute;
  right: 0;
  top: 188rpx;
  background: rgba(255, 255, 255, 0.61);
  border-radius: 8rpx 0 0 8rpx;
  width: 46rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #555555;
  line-height: 30rpx;
  box-sizing: border-box;
  padding: 10rpx 12rpx;
}
.main {
  padding-top: 176rpx;
  // width: 660rpx;
  width: 100%;
  padding: 176rpx calc((100% - 660rpx) / 2) 0;
  position: relative;
  // margin: 0% auto;
  background: url(https://static.soyoung.com/sy-design/2hca79az1qgeq1736928314804.png)
    no-repeat;
  box-sizing: border-box;
  background-size: 100%;
}
.processBox {
  margin: 30rpx 0 0 -28rpx;
  .process {
    width: 355 * 2rpx;
    display: block;
  }
}
.slogan {
  width: 654rpx;
  margin: 0 auto;
  display: block;
}
.inviteText {
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 14rpx 0 10rpx;
  .avatarBox {
    display: flex;
    flex-direction: column;
    .avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 100%;
      border: 2rpx solid #fff;
    }
  }
  .nameBox {
    margin-top: -16rpx;
    width: 112rpx;
    height: 30rpx;
    line-height: 29rpx;
    background: url(https://static.soyoung.com/sy-pre/ct4tivhhsixn-1723792200637.png)
      no-repeat top center / 100% 100%;
    border-radius: 4rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #ffffff;
    letter-spacing: -0.54 * 2 rpx;
    text-align: center;
  }
  .textBox {
    width: 550rpx;
    padding: 30rpx 30rpx 30rpx 54rpx;
    margin-left: -22rpx;
    box-sizing: border-box;
    background: url(https://static.soyoung.com/sy-design/92fmuhyc8ix41723620384023.png)
      no-repeat top center / 100% 100%;
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: #ffffff;
    letter-spacing: -0.63 * 2 rpx;
    font-weight: 500;
  }
}
.giftBox {
  width: 660rpx;
  height: 772rpx;
  margin-top: 290rpx;
  padding: 80rpx 70rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-repeat: no-repeat;
  position: relative;
  background-size: 100% 100%;
  background-position: top center;
  .tips {
    font-family: PingFangSC-Medium;
    font-size: 24rpx;
    color: #fe6631;
    font-weight: 500;
  }
  .smoreLogo {
    width: 124rpx;
    margin-top: 22rpx;
  }
  .avatar {
    display: block;
    margin-top: -20rpx;
    width: 100rpx;
    height: 100rpx;
    border-radius: 100%;
    border: 2rpx solid #fff;
  }

  &-top {
    .name {
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: justify;
      line-height: 36rpx;
      font-weight: 200;
      text-align: center;
    }
    .giftBox-title {
      font-family: PingFangSC-Light;
      font-size: 48rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 200;
      margin-top: 10rpx;
      text-align: center;
      line-height: 66rpx;
    }
  }
  &-devide {
    width: 342rpx;
    height: 2rpx;
    background: #e6e6e6;
    margin: 80rpx auto;
  }
  &-bottom {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    &-title {
      font-family: PingFangSC-Light;
      text-align: center;
      font-size: 48rpx;
      line-height: 66rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 200;
    }
    &-subtitle {
      margin-top: 12rpx;
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      line-height: 36rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
      white-space: pre-wrap;
    }
    &-price {
      margin-top: 2rpx;
      font-size: 72rpx;
      line-height: 66rpx;
      letter-spacing: 0;
      text-align: center;
      line-height: 90rpx;
      font-family: Outfit-ExtraLight;
      color: #a3e46b;
      font-weight: 200;
      &-symbol {
        font-family: Outfit-ExtraLight;
        font-size: 30rpx;
        letter-spacing: 0;
        text-align: justify;
        font-weight: 200;
      }
    }
  }
  .line {
    width: 342rpx;
    height: 2rpx;
    background: #e6e6e6;
    margin-top: 80rpx;
  }
  .greenBtn {
    position: absolute;
    bottom: 42rpx;
    // margin-top: 332rpx;
  }
}
.longBox {
  height: 1234rpx;
  width: 660rpx;
  margin-top: 290rpx;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-repeat: no-repeat;
  position: relative;
  background-size: 100% 100%;
  background-position: top center;
  .invite-box {
    width: 100%;
    position: relative;
    padding: 80rpx 70rpx;
    box-sizing: border-box;
    .invite-box-title {
      font-family: PingFangSC-Light;
      text-align: center;
      font-size: 48rpx;
      line-height: 66rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 200;
    }
    .invite-box-subtitle {
      margin-top: 12rpx;
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      line-height: 36rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
      white-space: pre-wrap;
    }
    .greenBtn {
      margin-top: 60rpx;
    }
  }
  .long-divide {
    width: 342rpx;
    height: 2rpx;
    background: #e6e6e6;
  }
  .product-box {
    width: 100%;
    position: relative;
    padding: 80rpx 70rpx;
    box-sizing: border-box;
    .greenBtn {
      margin-top: 60rpx;
    }
  }
  .tips {
    font-family: Outfit-Regular;
    font-size: 22rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    width: 286rpx;
    margin: auto;
  }
  .smoreLogo {
    width: 124rpx;
    margin-top: 22rpx;
  }
  .avatar {
    display: block;
    margin-top: -20rpx;
    width: 100rpx;
    height: 100rpx;
    border-radius: 100%;
    border: 2rpx solid #fff;
  }

  &-top {
    .name {
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: justify;
      line-height: 36rpx;
      font-weight: 200;
      text-align: center;
    }
    .giftBox-title {
      font-family: PingFangSC-Light;
      font-size: 48rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 200;
      margin-top: 10rpx;
      text-align: center;
      line-height: 66rpx;
    }
  }
  &-devide {
  }
  &-bottom {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    &-title {
      font-family: PingFangSC-Light;
      text-align: center;
      font-size: 48rpx;
      line-height: 66rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 200;
    }
    &-subtitle {
      margin-top: 12rpx;
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      line-height: 36rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
      white-space: pre-wrap;
    }
    &-price {
      margin-top: 2rpx;
      font-size: 72rpx;
      line-height: 66rpx;
      letter-spacing: 0;
      text-align: center;
      line-height: 90rpx;
      font-family: Outfit-ExtraLight;
      color: #a3e46b;
      font-weight: 200;
      &-symbol {
        font-family: Outfit-ExtraLight;
        font-size: 30rpx;
        letter-spacing: 0;
        text-align: justify;
        font-weight: 200;
      }
    }
  }
  .line {
    width: 342rpx;
    height: 2rpx;
    background: #e6e6e6;
    margin-top: 80rpx;
  }
}
.greenBtn {
  width: 490rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #ffffff;
  background: #222222;
  margin-left: auto;
  margin-right: auto;
  font-family: PingFangSC-Regular;
  font-size: 32rpx;
  color: #ffffff;
  letter-spacing: 0;
  text-align: justify;
  font-weight: 400;
}
.feedBack {
  position: relative;
  width: 658rpx;
  margin-top: 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background-image: linear-gradient(90deg, #fc9fb0 1%, #fd9aa6 100%);
  padding: 1rpx;
  .bgBox {
    box-sizing: border-box;
    border-radius: 24rpx;
    background-image: linear-gradient(90deg, #ffc9d1 1%, #ffadbd 100%);
    box-shadow: inset 6rpx 10rpx 10rpx 0 rgba(255, 255, 255, 0.27);
    padding: 30rpx 20rpx;
  }
  .bg1 {
    position: absolute;
    top: 30rpx;
    left: 270rpx;
    width: 84rpx;
    height: 66rpx;
  }
  .bg2 {
    position: absolute;
    top: 40rpx;
    left: 494rpx;
    width: 131rpx;
    height: 18rpx;
  }
  .content {
    position: relative;
    z-index: 2;
  }
  .top {
    display: flex;
    align-items: center;
    .avatar {
      width: 50rpx;
      height: 50rpx;
      border-radius: 100%;
      border: 2rpx solid rgba(255, 255, 255, 0.8);
    }
    .text {
      margin-left: 10rpx;
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      color: #222222;
      letter-spacing: -0.72 * 2 rpx;
      font-weight: 600;
    }
  }
  .wrapper {
    position: relative;
    padding: 2rpx;
    background: linear-gradient(
      to bottom,
      rgba(255, 228, 231, 1) 0%,
      rgba(255, 255, 255, 1) 100%
    );
    border-radius: 0 14rpx 14rpx 14rpx;
    margin-top: 12rpx;
    margin-left: 60rpx;
    .jiao {
      position: absolute;
      top: -8rpx;
      left: 0;
      width: 35rpx;
      height: 10rpx;
    }
  }
  .bottom {
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #333333;
    letter-spacing: -0.63 * 2 rpx;
    padding: 24rpx 20rpx;
    box-sizing: border-box;
    width: calc(100%); /* Subtract double the wrapper's padding */
    height: calc(100%); /* Subtract double the wrapper's padding */
    background: linear-gradient(to bottom, #ffd8dd 0%, #feedef 100%);
    border-radius: 0 10rpx 10rpx 10rpx;
    .line1 {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #333333;
      letter-spacing: -0.63 * 2rpx;
      line-height: 40rpx;
      text-decoration: underline;
      text-decoration-color: #ffbfcf;
      text-underline-offset: 8rpx;
    }
    .line2 {
      display: flex;
      align-items: center;
      margin-top: 14rpx;
      .left {
        width: 140rpx;
      }
      .right {
        flex: 1;
        line-height: 40rpx;
        font-family: PingFangSC-Semibold;
        font-size: 28rpx;
        color: #333333;
        letter-spacing: -0.63 * 2rpx;
        font-weight: 600;
        text-decoration: underline;
        text-decoration-color: #ffbfcf;
        text-underline-offset: 8rpx;
      }
    }
  }
}
.introList {
  margin-top: 50rpx;
  width: 100%;
  .intro {
    width: 100%;
    display: block;
  }
}
.spu-bottom-popup {
  box-sizing: border-box;
  height: 76vh;
  background-color: #f8f8f8;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100%;
  overflow: hidden;
  .bigTitle {
    margin-top: 52rpx;
    font-size: 46rpx;
    line-height: 64rpx;
    text-align: center;
  }
  .subTitle {
    font-size: 32rpx;
    line-height: 46rpx;
    text-align: center;
    margin-top: 6rpx;
  }
  .qrCode {
    display: block;
    width: 290rpx;
    height: 290rpx;
    margin: 326rpx auto 0;
  }
}
.mask {
  background: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 4;
}
.tipsPop {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 814rpx;
  padding-bottom: 88rpx;
  background-color: #fff;
  .top {
    height: 104rpx;
    display: flex;
    justify-content: flex-end;
    padding: 32rpx;
    box-sizing: border-box;

    .close {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .content {
    margin: auto;
    box-sizing: border-box;
    width: 650rpx;
    font-family: PingFangSC-Light;
    font-size: 26rpx;
    color: #333333;
    letter-spacing: 0;
    line-height: 42rpx;
    font-weight: 200;
    margin-bottom: 80rpx;
  }
  .invite-box {
    margin-bottom: 80rpx;
    .invite-box-divide {
      width: 342rpx;
      height: 2rpx;
      background: #e6e6e6;
      margin: 0 auto 80rpx;
    }
    .invite-box-title {
      width: 384rpx;
      font-family: PingFangSC-Light;
      font-size: 48rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
      line-height: 66rpx;
      margin: 0 auto 12rpx;
    }
    .invite-box-subtitle {
      width: 308rpx;
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
      margin: 0 auto;
    }
  }
  .btn {
    width: 652rpx;
    height: 90rpx;
    margin: 0 auto;
    line-height: 90rpx;
    font-family: PingFangSC-Medium;
    background: #222;
    font-size: 32rpx;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 500;
    text-align: center;
  }
}
.arriveBox {
  width: 642rpx;
  height: 772rpx;

  // ????????????????!!!!!!
  background: url('https://static.soyoung.com/sy-design/2hca7nc2ykif91741659615199.png')
    no-repeat top center / 100% 100%;
  overflow: hidden;
  margin-top: 290rpx;
  position: relative;
  .main-box {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 80rpx;
    box-sizing: border-box;
    .main-box-title {
      font-family: PingFangSC-Light;
      text-align: center;
      font-size: 48rpx;
      line-height: 66rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 200;
    }
    .main-box-subtitle {
      margin-top: 12rpx;
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      line-height: 36rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 200;
      white-space: pre-wrap;
    }
    .main-box-divide {
      width: 342rpx;
      height: 2rpx;
      background: #e6e6e6;
      margin: 80rpx auto;
    }
  }
  .greenBtn {
    margin-top: 630rpx;
  }
}
.notArriveBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 650rpx;
  height: 772rpx;
  margin-top: 290rpx;
  background: url('https://static.soyoung.com/sy-design/2hca7nc2ykif91736319734413.png')
    no-repeat top center / 100% 100%;
  overflow: hidden;
  .greenBtn {
    position: relative;
    width: 480rpx;
    height: 142rpx;
    margin-top: 20rpx;
    background: #fff;
    image {
      width: 100%;
      height: 100%;
    }
    // .arrow {
    //   width: 38rpx;
    //   height: 14rpx;
    //   position: absolute;
    //   top: -14rpx;
    //   left: 50%;
    //   margin-left: -19rpx;
    //   background: url('https://static.soyoung.com/sy-pre/2kqiaoxxef9ok-1724047800620.png')
    //     no-repeat top center / 100% 100%;
    // }
    // .line1 {
    //   font-size: 36rpx;
    // }
    // .line2 {
    //   font-size: 26rpx;
    // }
  }
  .title {
    font-family: PingFangSC-Light;
    font-size: 26rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: justify;
    font-weight: 200;
    margin-top: 80rpx;
    line-height: 36rpx;
  }
  .kfName {
    margin-top: 10rpx;
    font-family: PingFangSC-Light;
    font-size: 48rpx;
    color: #333333;
    height: 66rpx;
    line-height: 66rpx;
    letter-spacing: 0;
    text-align: justify;
    font-weight: 200;
  }
  .qrCodeBox {
    width: 290rpx;
    height: 290rpx;
    box-sizing: border-box;
    margin-top: 50rpx;
    .qrCode {
      width: 290rpx;
      height: 290rpx;
      display: block;
    }
  }
}
</style>
