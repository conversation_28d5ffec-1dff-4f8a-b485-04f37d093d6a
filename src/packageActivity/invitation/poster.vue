<template>
  <div>
    <div
      class="poster-mask"
      :class="aniFadeIn ? 'poster-mask-show' : ''"
      :style="{
        display: aniVisible ? 'block' : 'none'
      }"
      @transitionend="ontransitionendMask"
    ></div>
    <div
      class="poster-wrap"
      :class="aniFadeIn ? 'poster-wrap-show' : ''"
      :style="{
        display: aniVisible ? 'block' : 'none'
      }"
    >
      <div class="canvas-wrap">
        <img :src="url" show-menu-by-longpress />
        <div class="close" @click="$emit('update:visible', false)"></div>
      </div>
      <div class="footer">
        <button class="save" @click="onSave">保存海报</button>
        <!--        <button class="share" @click="onSave">发送好友</button>-->
      </div>
    </div>
    <canvas
      type="2d"
      class="canvas"
      id="myCanvas"
      :style="{
        width: width * scale + 'px',
        height: height * scale + 'px'
      }"
    ></canvas>
  </div>
</template>
<script>
let ctx;
let canvas;
const dpr = 2;
const width = 540 * dpr;
const height = 1058 * dpr;
const fix = (n) => n * dpr;
const scale = 1 / dpr;
// 全局缓存加载的图片资源，不load两次
const catched = new Map();
export default {
  props: {
    visible: Boolean,
    params: {
      type: Object,
      default: () => ({
        title: '',
        cover: '',
        code: ''
      })
    }
  },
  data() {
    return {
      aniVisible: false,
      aniFadeIn: false,
      url: '',
      width,
      height,
      scale,
      queue: [],
      resourceUrls: []
    };
  },
  watch: {
    visible(value) {
      if (value) {
        this.$nextTick().then(() => {
          this.aniVisible = true;
          setTimeout(() => {
            this.aniFadeIn = true;
          }, 50);
        });
      } else {
        this.aniFadeIn = false;
      }
    },
    params: {
      handler: function (params) {
        const { cover, code } = params;
        if (cover && code) {
          this.createPoster(params);
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    ontransitionendMask() {
      if (!this.aniFadeIn) {
        this.aniVisible = false;
      }
    },
    onSave() {
      this.$emit(
        'reportStat',
        'sy_wxtuan_op_product_info:share_img_save_click'
      );
      uni.saveImageToPhotosAlbum({
        filePath: this.url,
        success: () => {
          uni.showToast({
            title: '保存成功,赶快去分享吧!',
            icon: 'none'
          });
          this.$emit('update:visible', false);
        },
        fail(err) {
          const msg = Number(err.errno) === 103 ? '没有权限' : '';
          uni.showToast({
            title: '保存失败!' + msg,
            icon: 'none'
          });
        }
      });
    },
    createPoster({ cover, code }) {
      const promise = new Promise((resolve) => {
        /**
         * 在draw之前调用，向绘制队列添加操作，如果存在图片url,也会发起一个不阻塞的图片fetch promise
         * 下面的顺序决定了绘制的顺序: 数组累加器 + promise调用链实现
         */
        this.resetCanvas();
        this.loadCoverImage(cover);
        this.drawCode(code);
        this.draw(resolve);
      });
      promise.then((url) => {
        this.url = url;
      });
      this.$emit('afterPosterCreated', promise);
    },
    async draw(resolve) {
      this.queue.push(function scaleCanvas() {
        // 不需要再次缩放，因为我们的坐标已经通过 fix() 函数处理过了
        //ctx.scale(this.scale, this.scale);
        uni.canvasToTempFilePath({
          canvas,
          x: 0,
          y: 0,
          width: width,
          height: height,
          fileType: 'png',
          quality: 1,
          destWidth: width,
          destHeight: height,
          success: (res) => {
            console.log('图片生成：', res.tempFilePath);
            resolve(res.tempFilePath);
          }
        });
      });
      // 所有的方法依赖ctx初始化
      await this.getCtxPromise();
      this.resourceUrls.forEach((url) => this.loadResource(url));
      console.log(
        '绘制操作队列：',
        this.queue.map((func) => func.name)
      );
      this.queue.reduce((prev, cur) => {
        return prev.then((...args) => {
          ctx.save();
          let isPromise = cur.apply(this, args);
          if (!isPromise) {
            isPromise = Promise.resolve(isPromise);
          }
          return isPromise.then((result) => {
            ctx.restore();
            return result;
          });
        });
      }, Promise.resolve());
    },
    resetCanvas() {
      this.queue = [];
      this.queue.unshift(function resetCanvas() {
        ctx.clearRect(0, 0, width, height);
      });
    },
    getCtxPromise() {
      return this.queryNode('#myCanvas').then((res) => {
        if (!res) {
          return Promise.reject('没有查询到canvas');
        }
        const [{ node }] = res;
        canvas = node;
        canvas.width = width;
        canvas.height = height;
        ctx = canvas.getContext('2d');
        ctx.strokeStyle = '#000';
        ctx.strokeRect(0, 0, width, height);
      });
    },
    drawBgImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawBgImage() {
        const image = await this.loadResource(url);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          0,
          0,
          width,
          height
        );
      });
    },
    loadCoverImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawBgImage() {
        const image = await this.loadResource(url);
        console.log(image, 'image');

        // 计算图像的缩放比例，保持背景图像不变形
        const imageAspectRatio = image.width / image.height;
        const canvasAspectRatio = width / height;

        let sourceX = 0,
          sourceY = 0,
          sourceWidth = image.width,
          sourceHeight = image.height;

        if (imageAspectRatio > canvasAspectRatio) {
          // 图像比 canvas 更宽，需要裁剪宽度
          sourceWidth = image.height * canvasAspectRatio;
          sourceX = (image.width - sourceWidth) / 2;
        } else if (imageAspectRatio < canvasAspectRatio) {
          // 图像比 canvas 更高，需要裁剪高度
          sourceHeight = image.width / canvasAspectRatio;
          sourceY = (image.height - sourceHeight) / 2;
        }

        ctx.drawImage(
          image,
          sourceX,
          sourceY,
          sourceWidth,
          sourceHeight,
          0,
          0,
          width,
          height
        );
      });
    },
    drawCode(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function loadCoverQr() {
        const image = await this.loadResource(url);
        const dx = fix(162.5);
        const dy = fix(605);
        const targetSize = fix(215); // 固定目标尺寸为 250 * 250

        // 计算二维码的缩放比例，保持正方形不变形
        const imageSize = Math.min(image.width, image.height);

        // 从源图像的中心裁剪正方形区域
        const sourceX = (image.width - imageSize) / 2;
        const sourceY = (image.height - imageSize) / 2;

        // 使用完整的 drawImage 参数，确保二维码严格保持 250 * 250 尺寸
        ctx.drawImage(
          image,
          sourceX, // 源图像 x 坐标
          sourceY, // 源图像 y 坐标
          imageSize, // 源图像宽度（正方形）
          imageSize, // 源图像高度（正方形）
          dx, // 目标 x 坐标
          dy, // 目标 y 坐标
          targetSize, // 目标宽度 250
          targetSize // 目标高度 250
        );
      });
    },
    clipRect({ radius, x, y, width, height }) {
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height));
      ctx.lineTo(fix(x), fix(y + height));
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.clip();
    },
    roundRect({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      ctx.fillStyle = backgroundColor;
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height - radius));
      ctx.arcTo(
        fix(x + width),
        fix(y + height),
        fix(x + width - 2 * radius),
        fix(y + height),
        fix(radius)
      );
      ctx.lineTo(fix(x + radius), fix(y + height));
      ctx.arcTo(
        fix(x),
        fix(y + height),
        fix(x),
        fix(y + height - radius),
        fix(radius)
      );
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.fill();
    },
    loadResource(url, allFailedReturnResolve = false) {
      const fetch = (url, tryTimes = 5) =>
        new Promise((resolve, reject) => {
          const image = canvas.createImage();
          image.onload = () => resolve(image);
          image.onerror = (error) => {
            console.log(`load image error :try:${tryTimes} !!!`);
            if (tryTimes-- > 0) {
              fetch(
                url.replace(/\?t=\d+/g, '') + '?t=' + new Date().getTime(),
                tryTimes
              );
            } else {
              allFailedReturnResolve ? resolve(null) : reject(error);
            }
          };
          image.src = url;
        });
      // return promise<image> or undefined
      if (catched.has(url)) {
        const target = catched.get(url);
        target.catch(() => catched.delete(url));
      } else {
        catched.set(url, fetch(url));
      }
      return catched.get(url);
    },
    queryNode(selector, times = 10) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(selector)
          .fields({ node: true })
          .exec((res) => {
            if (res?.length > 0) {
              resolve(res);
            } else {
              if (times--) {
                setTimeout(() => {
                  resolve(this.queryNode(selector, times));
                }, 50);
              } else {
                uni.$log(
                  '[poster]没有查询到节点！',
                  this.params.title,
                  'error'
                );
                resolve([]);
              }
            }
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.poster-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.6);
  transition: opacity 0.3s;
  opacity: 0;
}
.poster-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10001;
  transition: transform 0.3s;
  transform: translateY(100%);
}
.canvas-wrap {
  position: absolute;
  top: 45%;
  left: 50%;
  width: 270 * 2rpx;
  height: 529 * 2rpx;
  transform: translate(-50%, -50%);
  z-index: 10;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.close {
  position: absolute;
  top: 0;
  right: 0;
  height: 80rpx;
  width: 80rpx;
  background: url(https://static.soyoung.com/sy-design/1azvpt7asrrzj1728646295362.png)
    no-repeat center center transparent;
  background-size: 40rpx 40rpx;
  z-index: 2;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  /* border-radius: 30rpx 30rpx 0 0; */
  padding-bottom: calc(
    20rpx + constant(safe-area-inset-bottom)
  ); /* 兼容 IOS<11.2 */
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  padding-left: 50rpx;
  padding-right: 50rpx;
  padding-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;

  button {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 42 * 2rpx;
    background-color: transparent;
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    text-align: center;
    font-weight: 500;
    border: none;
    border-radius: 0;

    &:after {
      border: none;
    }
  }

  .save {
    background-color: #ffffff;
    border: 2rpx solid #333333;
    color: #333333;
  }

  .share {
    background-color: #333333;
    color: #ffffff;
  }
}
.poster-mask-show {
  opacity: 1;
}
.poster-wrap-show {
  transform: translateY(-0%);
}
</style>
<style lang="less" scoped>
.canvas {
  position: fixed;
  top: -10000rpx;
  left: -10000rpx;
}
</style>
