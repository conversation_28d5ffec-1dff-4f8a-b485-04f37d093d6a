<template>
  <page-meta :page-style="pageMetaStyle">
    <NavBar :hasFixedHome="true" />
    <block v-if="isA">
      <div class="width-fix">
        <div class="rules" @click="go2rule"></div>
        <img class="head-view-image" :src="headerImage" mode="widthFix" />
        <!-- 当前奖品信息 -->
        <div v-if="info.top_award_info" class="current-prize-information">
          <div class="prompt">{{ info.top_award_info.award_use_range }}</div>
          <div class="title">{{ info.top_award_info.award_title }}</div>
          <div class="subtitle">
            {{ info.top_award_info.award_selling_point }}
          </div>
        </div>
      </div>
      <div class="width-fix a-main">
        <div class="green-text">
          {{ info.progress_text || '' }}
          <button
            open-type="share"
            v-if="isSuccess"
            class="goon-share"
            @click="reportStat('share_card_click', 2)"
          ></button>
        </div>
        <div class="user-list" v-if="userLength">
          <template v-for="(user, index) in userList">
            <div class="user-item" :key="index">
              <div class="more" v-if="index === 3" @click="onMore" />
              <div
                :style="{
                  backgroundImage: `url(${user.avatar})`
                }"
                class="avatar"
              ></div>
              <p class="name">{{ index === 3 ? '查看更多' : user.name }}</p>
            </div>
          </template>
          <div
            v-for="(_, index) in inviteList"
            :key="index"
            class="user-item no-border"
          >
            <button
              :style="{
                backgroundImage: `url(https://static.soyoung.com/sy-pre/sharess-1699434600681.png)`
              }"
              open-type="share"
              class="avatar"
            />
            <p class="name">待分享</p>
          </div>
        </div>
        <div class="btn-group">
          <template v-if="isDoing">
            <button class="btn share" open-type="share" @click="onSubscribe(1)">
              分享漂亮
            </button>
            <button class="cover" type="button" @click="onSavePoster">
              生成海报
            </button>
          </template>
          <!--          <button v-else-if="isSuccess" class="btn-long" @click="go2exp">-->
          <!--            去兑换并体验-->
          <!--          </button>-->
          <button
            v-else-if="info.code !== undefined && !isSuccess"
            class="btn-long"
            @click="go2home"
          >
            去首页逛逛
          </button>
        </div>
        <div class="tip" v-if="isSuccess && info.coupon_text">
          {{ info.coupon_text }}
        </div>
      </div>
      <div v-if="info.award_list.length" class="prize-body">
        <PrizeList
          @viewPrizeDetails="viewPrizeDetails"
          @onBtnClick="onExchange"
          :list="info.award_list"
          :title="info.award_title"
          :user-length="userLength"
          :code="info.code"
          :activity-id="activity_id"
        ></PrizeList>
      </div>
    </block>
    <block v-else-if="isB">
      <div class="width-fix b-main" v-if="isDoing">
        <img :src="headerImage" mode="widthFix" />
        <div class="rules" @click="go2rule"></div>
        <div class="user-item from-user-sm">
          <div
            :style="{
              backgroundImage: 'url(' + fromUserAvatar + ')'
            }"
            class="avatar"
          />
          <p class="name">{{ fromUserName }}</p>
        </div>
        <div class="user-item from-user-big">
          <div
            :style="{
              backgroundImage: 'url(' + fromUserAvatar + ')'
            }"
            class="avatar"
          />
          <p class="name">{{ fromUserName }}</p>
        </div>
        <div class="btn-group">
          <button class="btn" @click="go2help">立刻领取</button>
        </div>
      </div>
      <div class="width-fix b-main" v-else-if="isSuccess">
        <img :src="headerImage" mode="widthFix" />
        <div class="rules" @click="go2rule"></div>
        <div class="user-item from-user-big">
          <div
            :style="{
              backgroundImage: 'url(' + fromUserAvatar + ')'
            }"
            class="avatar"
          />
          <p class="name">{{ fromUserName }}</p>
        </div>
        <div class="btn-group">
          <button class="btn" @click="go2exp">去兑换并体验</button>
        </div>
        <div class="tip" v-if="info.coupon_text">{{ info.coupon_text }}</div>
      </div>
      <div class="width-fix b-main" v-else>
        <div class="rules" @click="go2rule"></div>
        <img :src="headerImage" mode="widthFix" />
        <div class="user-item from-user-sm">
          <div
            :style="{
              backgroundImage: 'url(' + fromUserAvatar + ')'
            }"
            class="avatar"
          />
          <p class="name">{{ fromUserName }}</p>
        </div>
        <div class="btn-group">
          <button class="btn-225" @click="onBInvite">邀请好友一起领</button>
        </div>
      </div>
    </block>
    <div class="width-fix" v-if="processImage">
      <!-- 参与流程模块 -->
      <img :src="processImage" mode="widthFix" />
    </div>
    <button
      v-if="isSuccess && isB"
      class="width-fix"
      @click="onShareAgainClick"
    >
      <img :src="staticData.share_again_img" mode="widthFix" />
    </button>
    <div class="width-fix">
      <img
        :src="url"
        :key="index"
        lazy-load="true"
        mode="widthFix"
        v-for="(url, index) in staticData.intro_imgs"
      />
      <img
        :src="url"
        :key="index"
        lazy-load="true"
        mode="widthFix"
        v-for="(url, index) in staticData.items_imgs"
      />
    </div>
    <div class="custom-popup" v-if="moreUserListVisible">
      <div class="wrap" @click.stop>
        <div class="close" @click="moreUserListVisible = false"></div>
        <div class="header">
          {{ '已有' + userLength + '位好友领取成功' }}
        </div>
        <view class="body-scroll">
          <div class="user-popup-list">
            <template v-for="(user, index) in info.join_users">
              <div class="user-item" :key="index">
                <div
                  :style="{
                    backgroundImage: `url(${user.avatar})`
                  }"
                  class="avatar"
                ></div>
                <p class="name">{{ user.name }}</p>
              </div>
            </template>
          </div>
        </view>
      </div>
    </div>
    <div class="error-box" v-if="errorMsgVisible">
      <div class="box">
        <div class="close" @click="onRefreshB"></div>
        <div class="header">{{ errorMsg }}</div>
        <div class="btn-225 bottom" @click="onErrorClick">
          {{
            errorCode === 10000 || errorCode === 10005
              ? '我知道了'
              : '获得1次领取机会'
          }}
        </div>
      </div>
    </div>
    <div class="error-view" v-if="isOver">
      <div class="center">
        <img
          src="https://static.soyoung.com/sy-pre/211id1gak5l3l-1699769400691.jpg"
        />
        <p>活动已结束～</p>
        <div class="btn-225" style="margin: 0 auto" @click="go2home">
          去首页逛逛
        </div>
      </div>
    </div>
    <Poster
      :visible.sync="posterGenVisible"
      :params="posterParams"
      @afterPosterCreated="loading = false"
      @saveClick="onPosterSave"
    />
    <BottomModal :visible.sync="jiacVisible" :showTitle="false">
      <div
        class="jiac-main"
        :style="{
          'backgroundImage': 'url(' + jiacBgImage + ')'
        }"
      >
        <!-- <div class="header">券已到账</div>
        <div class="text">添加福利官小氧 免费领取</div> -->
        <div class="qrcode">
          <img :src="jiacQrcodeImage" show-menu-by-longpress @click.stop />
        </div>
      </div>
      <div class="jiac-sku">
        <img :src="jiacSku" mode="widthFix" />
      </div>
    </BottomModal>
    <PageLoading :visible="loading" />
    <PrizeDetails
      :visible.sync="prizeDetailsVisible"
      @onClose="prizeDetailsVisible = false"
      :title="
        prizeDetailsIndex !== -1
          ? info.award_list[prizeDetailsIndex].award_info.award_title
          : ''
      "
    >
      <block v-if="prizeDetailsIndex !== -1">
        <view class="width-fix prize-details-img">
          <img
            v-for="item in info.award_list[prizeDetailsIndex].award_info
              .award_intro_imgs"
            :key="item"
            :src="item"
            mode="widthFix"
          />
          <view
            v-if="
              (isDoing &&
                Number(info.award_list[prizeDetailsIndex].code_info.get_yn) ===
                  0) ||
              (Number(info.award_list[prizeDetailsIndex].code_info.get_yn) ===
                1 &&
                Number(info.award_list[prizeDetailsIndex].code_info.use_yn) ===
                  0)
            "
            class="prize-details-bottom"
          >
            <button
              v-if="
                isDoing &&
                Number(info.award_list[prizeDetailsIndex].code_info.get_yn) ===
                  0
              "
              class="prize-details-button prize-details-button-share"
              open-type="share"
              @click="
                reportStat('gift_info_click', 1, prizeDetailsIndex + 1);
                onSubscribe(3);
              "
            >
              <img
                src="https://static.soyoung.com/sy-pre/<EMAIL>"
                alt=""
              />
              分享漂亮
              <text>获取专属福利机会</text>
            </button>
            <button
              v-else
              class="prize-details-button prize-details-button-exchange"
              @click="
                reportStat('gift_info_click', 2, prizeDetailsIndex + 1);
                awardCollection();
              "
            >
              <text>去兑换并使用</text>
              <text
                >请在{{
                  info.award_list[prizeDetailsIndex].code_info.end_date
                }}前兑换</text
              >
            </button>
          </view>
        </view>
      </block>
    </PrizeDetails>
    <view
      v-if="promoteSharingVisible"
      class="modal-mask"
      :class="{ 'fade-in': promoteSharingTransform }"
    >
      <view class="mask"></view>
      <view class="pannel" @click.stop>
        <view class="pannel-header" :class="isB ? 'pannel-header-b' : ''">
          <block v-if="isA">
            <view
              v-for="(item, index) in userListThree"
              :key="index"
              class="pannel-header-user-avatar"
              :style="{ zIndex: userListThree.length - index }"
            >
              <view>
                <image :src="item.avatar" mode="widthFix"></image>
              </view>
            </view>
            <view v-if="info.join_users.length" class="pannel-header-text">
              {{ info.join_users.length }}位好友已领取
            </view>
          </block>
          <block v-else-if="isB">
            <view class="pannel-header-user-avatar">
              <view>
                <image
                  :src="info.from_user_avatar.avatar"
                  mode="widthFix"
                ></image>
              </view>
            </view>
            <view class="pannel-header-text-b">
              {{ bPromptContent }}
            </view>
          </block>
        </view>
        <view
          class="pannel-content"
          :style="{
            backgroundImage: `url(${
              isA
                ? info.promote_share_pop.background_img
                : info.infect_data.B.share_again_pop
            })`
          }"
        >
          <block v-if="isA">
            <view class="pannel-content-text-1">
              恭喜你获得{{
                info.promote_share_pop.get_award_num || ''
              }}次专属福利
            </view>
            <view class="pannel-content-text-2">
              还差{{
                info.promote_share_pop.short_user_num || ''
              }}人领取，可再得
            </view>
            <view class="pannel-content-text-3">
              {{ info.promote_share_pop.award_info.award_use_range || '' }}
            </view>
            <view class="pannel-content-text-4">
              {{ info.promote_share_pop.award_info.award_title || '' }}
            </view>
            <view class="pannel-content-text-5">
              {{ info.promote_share_pop.award_info.award_selling_point || '' }}
            </view>
          </block>
          <button
            v-if="isA"
            class="pannel-content-button"
            open-type="share"
            @click="onSubscribe(3)"
          ></button>
          <button
            v-else
            class="pannel-content-button"
            @click="
              reportStat('fail_pop_click');
              onBInvite();
            "
          ></button>
        </view>
        <image
          class="pannel-down"
          src="https://static.soyoung.com/sy-pre/1t4pyhik1dytx-1700017800669.png"
          @click="
            bRefresh();
            promoteSharing(false);
          "
        ></image>
      </view>
    </view>
  </page-meta>
</template>
<script>
import NavBar from '@/components/NavBar.vue';

import { mapGetters, mapState } from 'vuex';

import BottomModal from '@/components/bottomModal.vue';

import Poster from '@/packageActivity/components/infect-poster.vue';

import {
  apiGetInfectSuccessSkus,
  apiGetShareInfectImg,
  apiInfectActivityDoHelp,
  apiInfectActivityInit,
  apiReceiveCoupon,
  apiSubscribe
} from '@/api/activity';
import PageLoading from '@/components/pageLoading.vue';
import PrizeList from '@/packageActivity/components/prizeList.vue';
import PrizeDetails from '@/packageActivity/components/prizeDetails.vue';

// import json from './infect_data.json';

// import json from '/Users/<USER>/Library/Application Support/JetBrains/WebStorm2023.3/scratches/scratch_3.json';

export default {
  components: {
    PrizeList,
    NavBar,
    PageLoading,
    BottomModal,
    Poster,
    PrizeDetails
  },
  data() {
    return {
      loading: false,
      activity_id: 0,
      from_uid: 0,
      from_qw_entry: 0,
      info: {},
      moreUserListVisible: false,
      jiacVisible: false,
      errorMsgVisible: false,
      posterGenVisible: false,
      errorCode: 0,
      errorMsg: '',
      jiacQrcodeImage: '',
      posterParams: {},
      prizeDetailsVisible: false,
      prizeDetailsIndex: -1,
      promoteSharingTransform: false,
      promoteSharingVisible: false,
      bPromptContent: ''
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin']),
    isA() {
      return !this.isB;
    },
    isB() {
      return (
        (this.from_uid && this.from_uid !== +this.userInfo.uid) ||
        this.from_qw_entry
      );
    },
    staticData() {
      if (!('infect_data' in this.info)) return {};
      return this.isA ? this.info.infect_data.A : this.info.infect_data.B;
    },
    shareImage() {
      return this.info.infect_data?.share_img || '';
    },
    ruleUrl() {
      return this.info.infect_data?.rule_url || '';
    },
    headerImage() {
      if (this.isA) return this.staticData.header_img;
      if (this.isB && this.isSuccess) return this.staticData.header_img.success;
      if (this.isB && this.isFail) return this.staticData.header_img.fail;
      return this.staticData.header_img?.doing || '';
    },
    jiacSku() {
      return this.staticData.jiac_sku || '';
    },
    jiacBgImage() {
      return this.staticData.jiac_bg || '';
    },
    needUserLength() {
      return this.info.a_people_num;
    },
    userLength() {
      return this.info.join_users?.length || 0;
    },
    userList() {
      return this.info.join_users?.slice(0, 4) || [];
    },
    userListThree() {
      return this.info.promote_share_pop?.join_users?.slice(0, 3) || [];
    },
    // 获得奖品数量
    numberPrizesWon() {
      return (
        this.info.award_list?.filter(
          (item) => Number(item.code_info.get_yn) === 1
        ) || []
      );
    },
    inviteList() {
      // let a = [];
      // let need = this.needUserLength - this.userLength;
      // while (need-- > 0) {
      //   a.push(1);
      // }
      // return this.needUserLength <= this.userLength ? [] : [1];
      // 已完成，已结束不展示 “待邀请” 按钮
      return this.isSuccess || this.isFail || this.info.code === 4 ? [] : [1];
    },
    isDoing() {
      return this.info.code === 0;
    },
    isSuccess() {
      return this.info.code === 1;
    },
    isFail() {
      return this.info.code === 2;
    },
    isOver() {
      return this.info.code === 3;
    },
    // 页面禁止滚动样式
    pageMetaStyle() {
      return this.moreUserListVisible ||
        this.jiacVisible ||
        this.posterGenVisible ||
        this.errorMsgVisible ||
        this.prizeDetailsVisible
        ? 'overflow:hidden;height:100vh;'
        : '';
    },
    processText() {
      switch (this.info.code) {
        case 0:
          return this.userLength === 0
            ? `只要${this.needUserLength}位领取，你也获得1次专属福利哦`
            : `还差${
                this.needUserLength - this.userLength
              }位姐姐，你就可以获得专属福利了哦`;
        case 1:
          return `体验券已到账，可继续分享`;
        case 2:
          return `活动已结束，未送出${this.needUserLength}张，无法领取体验卡`;
        case 4:
          return `未在活动有效期内领取奖励，奖励失效`;
        default:
          return '活动数据请求中...';
      }
    },
    fromUserAvatar() {
      return this.info?.from_user_avatar?.avatar || '';
    },
    fromUserName() {
      return this.info?.from_user_avatar?.name || '';
    },
    processImage() {
      return this.isSuccess
        ? this.staticData.act_process?.after || ''
        : this.staticData.act_process?.before || '';
    },
    // 活动进行中，且未获得奖品
    notObtainedProgress() {
      return (
        this.isDoing &&
        !this.info.award_list.filter((item) => item.code_info.get_yn === 1)
          .length
      );
    }
  },
  methods: {
    onExchange(param) {
      console.log(111);
      this.reportStat('exchange_click', '', param + 1);
      console.log(222);
    },
    bRefresh() {
      if (this.isB) {
        this.onRefreshB();
      }
    },
    async awardCollection() {
      const data = this.info.award_list[this.prizeDetailsIndex];
      if (Number(data.code_info.get_yn) === 0) {
        const res = await apiReceiveCoupon(
          this.activityId,
          1,
          data.award_info.coupon_id
        );
        if (!res) {
          return;
        }
      }
      console.log(`/pages/product?id=${data.award_info.sku_id}`);
      uni.navigateTo({
        url: `/pages/product?id=${data.award_info.sku_id}`
      });
    },
    viewPrizeDetails(index) {
      this.prizeDetailsIndex = index;
      this.prizeDetailsVisible = true;
      this.reportStat('view_gift_click', '', index + 1);
      this.reportStat('gift_info_exposure', '', index + 1);
    },
    async onSavePoster() {
      this.reportStat('share_pic_click');
      this.loading = true;
      const responseData = await apiGetShareInfectImg(
        `packageActivity/infect`,
        this.activity_id,
        this.userInfo.uid
      );
      if (!responseData) {
        this.loading = false;
        return;
      }
      this.posterParams = {
        code: `data:image/png;base64,${responseData.applets_img}`,
        cover: this.info.share_poster
      };
      await this.$nextTick();
      this.posterGenVisible = true;
    },
    async fetchInitData() {
      this.loading = true;
      await this.loginPromise;
      this.info = await apiInfectActivityInit(
        this.activity_id,
        this.from_uid,
        this.from_qw_entry,
        this.userInfo.uid
      );
      this.loading = false;
      if (this.isA) {
        let infectCoupon = uni.getStorageSync('INFECT_COUPON');
        if (!infectCoupon) {
          infectCoupon = [];
        } else {
          infectCoupon = JSON.parse(infectCoupon);
        }
        if (
          this.info.promote_share_pop &&
          !infectCoupon.includes(
            this.info.promote_share_pop.award_info.coupon_id
          ) &&
          new Date(this.info.end_time) > new Date() &&
          new Date() > new Date(this.info.start_time) &&
          this.numberPrizesWon.length !== 0 &&
          this.numberPrizesWon.length < this.info.award_list.length &&
          this.info.promote_share_pop
        ) {
          infectCoupon.push(this.info.promote_share_pop.award_info.coupon_id);
          this.promoteSharing(true);
          uni.setStorageSync('INFECT_COUPON', JSON.stringify(infectCoupon));
          const sectionNum = this.info.award_list.findIndex(
            (item) =>
              Number(item.award_info.coupon_id) ===
              Number(this.info.promote_share_pop.award_info.coupon_id)
          );
          this.reportStat(
            'share_pop_exposure',
            '',
            sectionNum === -1 ? '' : sectionNum + 1
          );
        }
      }
      console.log('首屏头图', this.headerImage);
      console.log('首页数据', this.info);
      // this.info.join_users = [
      //   ...this.info.join_users,
      //   ...this.info.join_users,
      //   ...this.info.join_users,
      //   ...this.info.join_users
      // ];
      // this.info.infect_data = json;
      // this.info = json.responseData;
    },
    changeToA() {
      this.from_uid = 0;
      this.from_qw_entry = 0;
      this.fetchInitData();
    },
    async go2exp() {
      const sectionNum = this.info.award_list.findIndex(
        (item) =>
          Number(item.award_info.coupon_id) ===
          Number(this.info.top_award_info.coupon_id)
      );
      this.reportStat(
        'exchange_click',
        '',
        sectionNum === -1 ? '' : sectionNum + 1
      );
      this.loading = true;
      // 如果领券没有成功就再领一次
      if (
        !(await apiReceiveCoupon(
          this.activity_id,
          this.isA ? 1 : 2,
          this.info.b_coupon_id
        ))
      ) {
        this.loading = false;
        return;
      }
      const res = await apiGetInfectSuccessSkus(
        this.activity_id,
        this.isA ? 1 : 2,
        this.from_uid
      );
      if (!res) {
        this.loading = false;
        return;
      }
      const [{ product_id }] = res.award_obj;
      uni.navigateTo({
        url: `/pages/product?id=${product_id}`
      });
      this.loading = false;
    },
    async go2help() {
      this.reportStat('help_click');
      this.loading = true;
      if (!(await this.keepSession())) {
        this.loading = false;
        return;
      }
      // 重新登陆之后 刷新接口
      await this.fetchInitData();
      if (this.from_uid === +this.userInfo.uid) return;
      const { locationAuthorized, locationEnabled } = uni.getSystemInfoSync();
      console.log(locationAuthorized, locationEnabled);
      if (!locationAuthorized) {
        uni.showModal({
          title: '系统定位异常',
          content:
            '微信不能确定你的位置,你可以通过以下操作提高微信的定位精确度:在位置设置中打开GPS和无线网络',
          confirmText: '去设置',
          success(resp) {
            if (resp.confirm) {
              wx.openAppAuthorizeSetting();
            }
          }
        });
        this.loading = false;
        return;
      }
      // 获取地理位置
      const cityInfo = await this.$getCityId().catch((msg) => {
        const content =
          msg === 'getLocation:fail auth deny'
            ? '需先授权位置后才能参与活动哦~'
            : msg;
        uni.showModal({
          title: '获取地理位置异常',
          content,
          confirmText: '去授权',
          success(resp) {
            if (resp.confirm) {
              uni.openSetting();
            }
          }
        });
        return null;
      });
      if (!cityInfo) {
        this.loading = false;
        return;
      }
      // 关系绑定
      const { errorCode, errorMsg, responseData } =
        await apiInfectActivityDoHelp({
          activity_id: this.activity_id,
          from_uid: this.from_uid,
          city_id: cityInfo.cityId,
          from_qw_entry: this.from_qw_entry
        });
      switch (errorCode) {
        case 0:
        case 200:
          this.info.code = 1;
          // uni.showToast({
          //   title: '领取成功',
          //   icon: 'none'
          // });
          this.fetchInitData();
          break;
        case 10003:
          this.jiacVisible = true;
          // wx.setStorageSync('__infect_1106_jiac_flag__', 1);
          this.reportStat('add_c_exposure');
          this.jiacQrcodeImage = responseData;
          break;
        default:
          this.errorCode = errorCode;
          this.errorMsg = errorMsg;
          // this.errorMsgVisible = true;
          this.reportStat('fail_pop_exposure');
          this.bPromptContent = errorMsg;
          this.promoteSharing(true);
          break;
      }
      this.loading = false;
    },
    onShareAgainClick() {
      this.reportStat('new_share_click');
      this.changeToA();
    },
    onBInvite() {
      this.reportStat('old_share_click');
      this.changeToA();
      this.promoteSharing(false);
    },
    onErrorClick() {
      this.reportStat('fail_pop_click');
      // this.errorMsgVisible = false;
      this.promoteSharing(false);
      if (this.errorCode === 10000 || this.errorCode === 10005) {
        return;
      }
      this.changeToA();
    },
    onRefreshB() {
      // this.errorMsgVisible = false;
      this.promoteSharing(false);
      this.info.code = 2;
    },
    onPosterSave() {
      this.reportStat('save_pic_click');
    },
    onMore() {
      this.reportStat('more_click');
      this.moreUserListVisible = true;
    },
    go2home() {
      uni.switchTab({
        url: '/pages/index'
      });
    },
    go2rule() {
      this.reportStat('rule_click');
      this.$bridge({
        url: '/pages/h5?url=' + encodeURIComponent(this.ruleUrl)
      });
    },
    reportStat(action, type = '', sectionNum = '') {
      const ext = {};
      let info = 0;
      if (this.isA) {
        let status = 0;
        if (this.info.code === 0) {
          if (this.info.join_users.length === 0) {
            status = 1;
          } else {
            status = 2;
          }
        } else if (this.info.code === 1) {
          status = 3;
        } else if (this.info.code === 2 || this.info.code === 3) {
          status = 4;
        }
        info = 'sy_wxtuan_tuan_shop_share_activity_a';
        ext.status = status;
        ext.content = this.userLength;
      } else {
        info = 'sy_wxtuan_tuan_shop_share_activity_b';
        ext.exp_ext = this.from_qw_entry ? 2 : 1;
        ext.share_uid = this.from_uid;
      }
      ext.from = this.userInfo.marketActivityId || 0;
      if (type) {
        ext.type = type;
      }
      if (sectionNum) {
        ext.section_num = sectionNum;
      }
      this.$reportData({
        info: `${info}:${action}`,
        ext: {
          activity: this.activity_id,
          ...ext
        }
      });
    },
    onSubscribe(type) {
      console.log('模版消息订阅信息！！！！');
      this.reportStat('share_card_click', type);
      if (this.shareCnt !== 0) return;
      this.createSubscribe({
        'ZXFKEy2_SoQmEPtmB-AI0lJ8phFur9oUkVOJqGF4oGA': [29],
        'Q8WsLp8vtpsaR9IzGpqFxKHGfCO3WnibLr5o-kKy1c4': [30]
      }).then((cb) => cb());
      this.shareCnt++;
    },
    createSubscribe(map, type = 'need-accept') {
      const tmpIds = Object.keys(map);
      return this.$subscribeMsg(tmpIds)
        .then((res) => {
          const scene = [];
          tmpIds.forEach((key) => {
            if (type === 'need-accept') {
              // 这种情况下，需要用户同意订阅，才调接口
              if (
                res?.[key] === 'accept' &&
                map[key] &&
                map[key] instanceof Array &&
                map[key].length > 0
              ) {
                scene.push(...map[key]);
              }
            } else {
              // 这种情况下，不管用户是否同意都调接口
              if (
                map[key] &&
                map[key] instanceof Array &&
                map[key].length > 0
              ) {
                scene.push(...map[key]);
              }
            }
          });
          return (opts) => {
            scene.forEach((num) => apiSubscribe(num));
            // 接受订阅，执行回调
            scene.length > 0 && opts?.callback && opts?.callback();
          };
        })
        .catch((msg) => {
          console.log(msg);
        });
    },
    pageReport(method = '$reportPageShow') {
      const ext = {};
      let info = 0;
      if (this.isA) {
        let status = 0;
        if (this.info.code === 0) {
          if (this.info.join_users.length === 0) {
            status = 1;
          } else {
            status = 2;
          }
        } else if (this.info.code === 1) {
          status = 3;
        } else if (this.info.code === 2 || this.info.code === 3) {
          status = 4;
        }
        info = 'sy_wxtuan_tuan_shop_share_activity_a_page';
        ext.status = status;
        ext.content = this.userLength;
      } else {
        info = 'sy_wxtuan_tuan_shop_share_activity_b_page';
        ext.exp_ext = this.from_qw_entry ? 2 : 1;
        ext.from = this.from_qw_entry ? 2 : 1;
        ext.share_uid = this.from_uid;
      }
      ext.from = this.userInfo.marketActivityId || 0;
      this[method]({
        info,
        ext: {
          activity: this.activity_id,
          ...ext
        }
      });
    },
    promoteSharing(param) {
      this.promoteSharingVisible = param;
      if (this.promoteSharingVisible) {
        setTimeout(() => {
          this.promoteSharingTransform = true;
        }, 50);
      } else {
        this.promoteSharingTransform = false;
      }
    },
    // 登录
    async keepSession() {
      // 如果没有登录，先去登录，然后判断是否是 新用户
      if (!this.isLogin) {
        this.loading = true;
        // 校验登录
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        this.loading = false;
        if (!isAuth) return false;
      }
      return true;
    }
  },
  created() {
    this.shareCnt = 0;
  },
  async onLoad({ activity_id, from_uid = 0, from_qw_entry = 0, scene }) {
    if (scene) {
      const matches = /a=(\d+)&u=(\d+)/.exec(decodeURIComponent(scene));
      activity_id = +matches[1];
      from_uid = +matches[2];
      console.log(matches);
    }
    if (activity_id === undefined) {
      uni.showModal({
        title: '异常',
        content: '缺少活动ID',
        showCancel: false,
        success: (resp) => {
          if (resp.confirm) this.go2home();
        }
      });
      return;
    }
    this.activity_id = Number(activity_id);
    this.from_uid = Number(from_uid);
    this.from_qw_entry = Number(from_qw_entry);
    // 1202
    // {
    //   const { scene, chatType, query } = wx.getLaunchOptionsSync();
    //   console.log('场景值', scene, chatType, query);
    //   uni.$log('场景值', scene, chatType, query, 'info');
    // }
    // if (this.from_qw_entry === 1) {
    uni.updateShareMenu({
      withShareTicket: true,
      isUpdatableMessage: true
    });
    // }
    // if (wx.getStorageSync('__infect_1106_jiac_flag__')) {
    //   // TODO
    //   wx.removeStorageSync('__infect_1106_jiac_flag__');
    // }
    // A用户必须登陆
    if (this.isA) {
      this.loginPromise = this.keepSession();
      if (!(await this.loginPromise)) return;
    } else {
      this.loginPromise = Promise.resolve();
      if (this.isLogin && this.from_qw_entry) {
        this.go2help();
      }
    }
    // this.prizeDetailsVisible = true;
  },
  async onShow() {
    await this.fetchInitData();
    this.pageReport();
  },
  onHide() {
    this.jiacVisible = false;
    this.pageReport('$reportPageHide');
  },
  onUnload() {
    this.pageReport('$reportPageUnload');
  },
  onShareAppMessage() {
    const activity_id = this.activity_id;
    return {
      title: this.info.share_txts,
      imageUrl: this.shareImage,
      path: `/packageActivity/infect?activity_id=${activity_id}&from_uid=${
        this.userInfo.uid
      }&market_activity_id=${this.userInfo.marketActivityId || ''}`
    };
  }
};
</script>
<style lang="less" scoped>
@px: 2rpx;
.width-fix {
  position: relative;
  display: block;
  margin: 0 auto;
  width: 750rpx;
  height: auto;
  & > img {
    width: 100%;
    vertical-align: top;
  }
}
.rules {
  position: absolute;
  top: 184rpx;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 128rpx;
  height: 52rpx;
  // background-color: rgba(0, 0, 0, 0.5);
  background: url(https://static.soyoung.com/sy-pre/2ce8fbll5b558-1699949400674.png)
    no-repeat center center transparent;
  background-size: contain;
  z-index: 1;
}
.head-view-image {
  height: 395 * @px !important;
}
.current-prize-information {
  width: 100vw;
  position: absolute;
  left: 0;
  right: 0;
  top: 256 * @px;
  z-index: 10;
  box-sizing: border-box;
  padding-left: 36 * @px;
  view {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .prompt {
    max-width: 202 * @px;
    height: 18 * @px;
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #222222;
    font-weight: 400;
    padding-bottom: 21 * @px;
  }
  .title {
    max-width: 276 * @px;
    font-family: PingFangSC-Regular;
    font-size: 23 * @px;
    color: #222222;
    font-weight: 400;
    line-height: 23 * @px;
    padding-bottom: 10 * @px;
  }
  .subtitle {
    max-width: 276 * @px;
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    line-height: 13 * @px;
    color: #222222;
    font-weight: 400;
  }
}
button {
  padding: 0;
  margin: 0;
  &::after {
    display: none;
  }
}
.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 268 * 2rpx;
  height: 100rpx;
  font-size: 38rpx;
  color: #fff;
  text-shadow: 0 1px 1px rgba(0, 105, 66, 0.53);
  background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
    no-repeat center center transparent;
  background-size: contain;
  &::after {
    display: none;
  }
}
.btn-225 {
  .btn;
  width: 225 * 2rpx;
  background-image: url(https://static.soyoung.com/sy-pre/<EMAIL>);
}
.btn-long {
  .btn;
  width: 335 * 2rpx;
  background-image: url(https://static.soyoung.com/sy-pre/<EMAIL>);
}
.header {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  min-height: 60rpx;
  line-height: 60rpx;
  font-size: 32rpx;
  color: #333333;
  text-align: center;
  font-weight: 400;
  &::before,
  &::after {
    margin: 0 4rpx;
    content: '';
    height: 60rpx;
    width: 60rpx;
    background: url(https://static.soyoung.com/sy-pre/star-1699434600681.png)
      no-repeat center center transparent;
    background-size: contain;
  }
}
.user-item {
  position: relative;
  width: 104rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-right: 30rpx;
  .avatar {
    box-sizing: border-box;
    display: block;
    margin: 0 auto;
    height: 80rpx;
    width: 80rpx;
    border: 2px solid #fdc9c9;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: 80rpx 80rpx;
    background-position: center;
    border-radius: 50%;
    background-color: transparent;
    &::after {
      display: none;
    }
  }
  .name {
    background-image: linear-gradient(
      to left,
      rgba(255, 230, 230, 0) 0%,
      #ffe0e0 53%,
      rgba(254, 217, 217, 0) 100%,
      rgba(254, 217, 217, 0) 100%
    );
    height: 28rpx;
    line-height: 28rpx;
    width: 100%;
    box-sizing: border-box;
    padding: 0 5rpx;
    font-size: 20rpx;
    color: #404040;
    text-align: center;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: -5 * @px;
    position: relative;
    z-index: 10;
  }
  .more {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 80rpx;
    width: 80rpx;
    background: url(https://static.soyoung.com/sy-pre/20231114-102839-1699927800715.png)
      no-repeat center center transparent;
    background-size: contain;

    z-index: 1;
  }
}
.a-main {
  //padding-bottom: 50rpx;
  .green-text {
    position: relative;
    padding: 20rpx 0 0;
    text-align: center;
    font-weight: 500;
    font-family: PingFangSC-Medium;
    font-size: 16 * @px;
    color: #222222;
    letter-spacing: 0;
    max-width: 273 * @px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    .goon-share {
      //position: absolute;
      //right: 30rpx;
      //top: 0;
      width: 150rpx;
      height: 80rpx;
      margin-left: 10 * @px;
      background: url(https://static.soyoung.com/sy-pre/4jevkojz397i-1712049000681.png)
        no-repeat center center transparent;
      background-size: contain;
      &::before,
      &::after {
        display: none;
      }
    }
  }
  .user-list {
    padding-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    .user-item {
      &:last-child {
        margin-right: 0;
      }
      &.no-border {
        .avatar {
          border: none;
        }
      }
    }
  }
  .btn-group {
    box-sizing: border-box;
    padding: 30rpx 40rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .share::before {
      content: '';
      margin-right: 14rpx;
      height: 60rpx;
      width: 66rpx;
      background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
        no-repeat center center transparent;
      background-size: contain;
    }
    .cover {
      padding: 0;
      width: 104rpx;
      line-height: 36rpx;
      font-size: 26rpx;
      color: #00ac85;
      text-align: center;
      background: transparent;
      &::before {
        display: block;
        margin: 0 auto 10rpx;
        content: '';
        height: 54rpx;
        width: 54rpx;
        background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
          no-repeat center center transparent;
        background-size: contain;
      }
      &::after {
        display: none;
      }
    }
  }
  .tip {
    width: 100%;
    padding-top: 20rpx;
    font-size: 26rpx;
    color: #6f6f6f;
    text-align: center;
    font-weight: 400;
  }
}
.b-main {
  .a-main;
  padding-bottom: 0;
  .from-user-sm {
    position: absolute;
    left: 34rpx;
    top: 442rpx;
    .avatar {
      height: 74rpx;
      width: 74rpx;
      border: 1px solid #fff;
      background-size: 74rpx 74rpx;
      margin-bottom: -12rpx;
    }
  }
  .from-user-big {
    position: absolute;
    left: 50%;
    bottom: 444rpx;
    transform: translateX(-50%);
    width: auto;
    margin-right: 0;
    .avatar {
      height: 120rpx;
      width: 120rpx;
      border: 1px solid #fff;
      background-size: 120rpx 120rpx;
      margin-bottom: 8rpx;
    }
    .name {
      text-align: center;
      width: 300rpx;
      line-height: 44rpx;
      height: 44rpx;
      background-image: none;
      font-size: 32rpx;
      color: #ee6b6b;
    }
  }
  .btn-group {
    position: absolute;
    bottom: 106rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
  }
  .tip {
    position: absolute;
    bottom: 60rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    padding-top: 0;
  }
}
.custom-popup {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9999999;
  top: 0px;
  left: 0px;
  background-color: rgba(0, 0, 0, 0.6);
  transform: translateZ(999px);
  text-align: center;
  .wrap {
    box-sizing: border-box;
    width: 285 * 2rpx;
    padding: 40rpx 30rpx 30rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 30rpx;
    background: url(https://static.soyoung.com/sy-pre/111-1699434600681.jpg)
      no-repeat top center #fff;
    background-size: 285 * 2rpx 181 * 2rpx;
    .close {
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      left: 50%;
      transform: translateX(-50%);
      bottom: -100rpx;
      background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
        no-repeat center / 100%;
      z-index: 2;
    }
    .body-scroll {
      margin-top: 30rpx;
      box-sizing: border-box;
      transform: translateZ(1px);
      background-color: #fff;
      border-radius: 30rpx;
      max-height: 800rpx;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        display: block;
        width: 10rpx;
        height: 100%;
      }
      &::-webkit-scrollbar-thumb {
        width: 10rpx;
        background: #f5b7b7;
        border-radius: 3px;
      }
    }
    .user-popup-list {
      width: 255 * 2rpx;
      box-sizing: border-box;
      padding: 50rpx 50rpx 0;
      overflow: hidden;
      .user-item {
        float: left;
        margin-right: 46rpx;
        margin-bottom: 50rpx;
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }
}
.jiac-main {
  position: relative;
  box-sizing: border-box;
  padding-top: 50rpx;
  height: 616rpx;
  width: 100%;
  background-repeat: no-repeat;
  background-position: top center;
  background-color: transparent;
  background-size: 750rpx 616rpx;
  .header {
    font-size: 34rpx;
  }
  .text {
    text-align: center;
    font-size: 26rpx;
    color: #222222;
    font-weight: 400;
  }
  .qrcode {
    position: absolute;
    top: 210rpx;
    left: 50%;
    height: 280rpx;
    width: 280rpx;
    background: #ffeded;
    border-radius: 16rpx;
    overflow: hidden;
    transform: translateX(-50%);
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.jiac-sku {
  width: 750rpx;
  overflow: hidden;
  img {
    width: 100%;
    vertical-align: top;
  }
}
.error-box {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9999999;
  top: 0px;
  left: 0px;
  background-color: rgba(0, 0, 0, 0.6);
  transform: translateZ(999px);
  text-align: center;
  .box {
    box-sizing: border-box;
    padding: 60rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    height: 444rpx;
    width: 285 * 2rpx;
    transform: translate(-50%, -50%);
    background: url(https://static.soyoung.com/sy-pre/111111-1699521000713.png)
      no-repeat center center transparent;
    background-size: contain;
    border-radius: 30rpx;
    .close {
      position: absolute;
      width: 60rpx;
      height: 60rpx;
      left: 50%;
      transform: translateX(-50%);
      bottom: -100rpx;
      background: url(https://static.soyoung.com/sy-pre/<EMAIL>)
        no-repeat center / 100%;
      z-index: 2;
    }
    .header {
      margin: 0 auto;
      max-width: 100%;
    }
    .bottom {
      position: absolute;
      bottom: 60rpx;
    }
  }
}
.error-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  transform: translateZ(5px);
  z-index: 10;
  background-color: #fff;
  .center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    img {
      margin-bottom: 100rpx;
      width: 375 * 2rpx;
      height: 288rpx;
    }
    p {
      margin-bottom: 30rpx;
      font-size: 34rpx;
      color: #333333;
      text-align: center;
      font-weight: 400;
    }
  }
}

.prize-body {
  width: 100%;
  height: 203 * 2rpx;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background-image: url('https://static.soyoung.com/sy-pre/38lqw03a0xq62-1711703400683.jpg');
  background-size: 100% 100%;
}

.prize-details-bottom {
  width: 100%;
  height: 108 * @px;
  background-image: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    #ffffff 100%
  );
  position: fixed;
  left: 0;
  bottom: 0;
}

.prize-details-button {
  width: 285 * @px;
  height: 54 * @px;
  background-color: #00ab84;
  border-radius: 27 * @px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 20 * @px;
  z-index: 100;
}

.prize-details-button-exchange {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text:nth-child(1) {
    font-family: PingFangSC-Regular;
    font-size: 17 * @px;
    color: #ffffff;
    font-weight: 400;
    line-height: 24 * @px;
  }
  text:nth-child(2) {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #ffffff;
    font-weight: 400;
    line-height: 14 * @px;
  }
}

.prize-details-button-share {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Regular;
  font-size: 19 * @px;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
  image {
    width: 26 * @px;
    height: 22.4 * @px;
    margin-right: 5 * @px;
  }
  text {
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    color: #ffffff;
    text-align: center;
    font-weight: 400;
    padding-left: 14 * @px;
    position: relative;
    &:before {
      content: '';
      position: absolute;
      left: 7 * @px;
      height: 14 * @px;
      width: 1 * @px;
      background-color: rgba(255, 255, 255, 0.7);
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(900px);
  &.fade-in {
    .mask {
      opacity: 1;
    }
    .pannel {
      transform: translateY(-45%);
    }
  }
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.2s;
}

.pannel {
  position: absolute;
  top: 50%;
  left: 0;
  height: auto;
  //overflow: scroll;
  width: 100%;
  transform: translateY(-70%);
  transition: transform 0.2s;
  display: flex;
  flex-direction: column;
}

.pannel-header {
  width: 285 * @px;
  height: 46 * @px;
  margin: 0 auto 20 * @px;
  display: flex;
  .pannel-header-user-avatar {
    width: 32 * @px;
    height: 46 * @px;
    position: relative;
    view {
      width: 46 * @px;
      height: 46 * @px;
      min-width: 46 * @px;
      min-height: 46 * @px;
      border-radius: 50%;
      position: relative;
      z-index: 2;
      background-image: linear-gradient(180deg, #f5fff9 0%, #fdcaca 100%);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    image {
      width: 42 * @px;
      height: 42 * @px;
      min-width: 42 * @px;
      min-height: 42 * @px;
      max-width: 42 * @px;
      max-height: 42 * @px;
      border-radius: 50%;
      z-index: 2;
    }
  }
  .pannel-header-text {
    background-image: url('https://static.soyoung.com/sy-pre/b4e87h0l02ox-1712574600767.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-family: PingFangSC-Medium;
    font-size: 16 * @px;
    color: #222222;
    font-weight: 500;
    width: 164.48 * @px;
    height: 46 * @px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 6 * @px;
    margin-left: 24 * @px;
  }
}

.pannel-header-b {
  display: flex;
  align-items: center;
  margin-bottom: 30 * @px;
  .pannel-header-text-b {
    background-image: url('https://static.soyoung.com/sy-pre/31v15ii2ofa9e-1712581800684.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-family: PingFangSC-Medium;
    font-size: 14 * @px;
    color: #333333;
    line-height: 24 * @px;
    font-weight: 500;
    max-width: 228 * @px;
    max-height: 68 * @px;
    min-width: 228 * @px;
    min-height: 68 * @px;
    margin-left: 20 * @px;
    padding-left: 25 * @px;
    padding-right: 8 * @px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
}

.pannel-content {
  width: 285 * @px;
  height: 385 * @px;
  padding-top: 38 * @px;
  box-sizing: border-box;
  border-radius: 15 * @px;
  overflow: hidden;
  margin: 0 auto;
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  view {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .pannel-content-text-1 {
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    color: #333333;
    font-weight: 400;
    max-width: 245 * @px;
    margin: 0 auto 12 * @px;
    text-align: center;
  }
  .pannel-content-text-2 {
    font-family: PingFangSC-Medium;
    font-size: 16 * @px;
    color: #333333;
    font-weight: 500;
    max-width: 245 * @px;
    margin: 0 auto 71 * @px;
    text-align: center;
  }
  .pannel-content-text-3 {
    max-width: 155 * @px;
    font-family: PingFangSC-Regular;
    font-size: 10 * @px;
    color: #222222;
    font-weight: 400;
    margin-bottom: 28 * @px;
    justify-content: flex-start;
    margin-left: 27 * @px;
  }
  .pannel-content-text-4 {
    font-family: PingFangSC-Regular;
    font-size: 19 * @px;
    color: #222222;
    font-weight: 400;
    max-width: 245 * @px;
    margin-bottom: 6 * @px;
    margin-left: 27 * @px;
  }
  .pannel-content-text-5 {
    font-family: PingFangSC-Regular;
    font-size: 11 * @px;
    color: #222222;
    font-weight: 400;
    max-width: 245 * @px;
    margin-left: 27 * @px;
  }
  .pannel-content-button {
    width: 230 * @px;
    height: 45 * @px;
    position: absolute;
    bottom: 36 * @px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
  }
}

.pannel-down {
  width: 30 * @px;
  height: 30 * @px;
  margin: 20 * @px auto 0;
}

.prize-details-img {
  height: 75vh;
  overflow-y: scroll !important;
  image {
    display: block;
  }
}
</style>
