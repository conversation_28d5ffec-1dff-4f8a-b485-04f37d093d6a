<template>
  <div class="page" :style="{ paddingTop: menuRect.bottom + 8 + 'px' }">
    <div
      v-if="menuRect.top"
      class="pageTop"
      :style="{
        height: menuRect.bottom + 8 + 'px',
        background
      }"
    >
      <div
        class="nav"
        :style="{
          top: menuRect.top + 'px',
          width: menuRect.width + 'px',
          height: menuRect.height + 'px'
        }"
      >
        <div class="backBox" @click="handleBack">
          <img
            class="back"
            :src="
              thereIsNoMaskingLayerOnTheProduct
                ? 'https://static.soyoung.com/sy-design/3cj8rc3ipek931726026826755.png'
                : 'https://static.soyoung.com/sy-design/3cj8rc3ipek931727250525771.png'
            "
          />
        </div>
      </div>
      <!-- 分享按钮 -->
      <div
        v-if="hasData"
        class="share"
        @click="showShareBar"
        :style="{
          top: menuRect.top - 7 + 'px',
          right: menuRect.width + 30 + 'px'
        }"
      >
        <img
          class="shareIcon"
          src="https://static.soyoung.com/sy-pre/1wu38mq9u5lep-1716797400623.png"
          alt=""
        />
      </div>
    </div>
    <view class="pageTop-background"></view>
    <div class="skuCard">
      <SkuCard :skuInfo="mainSku" :is_hide_price="0" @click="onCardClick" />
      <view class="skuCard-title">
        <text>{{ mainSku.product_title }}</text>
        <text>x{{ mainSku.num || 1 }}</text>
      </view>
      <view class="skuCard-content">
        <text>{{ mainSku.title }}</text>
        <text>¥ {{ mainSku.price_online || '' }}</text>
      </view>
    </div>
    <div class="order-strip">
      <p class="title">拼团价</p>
      <div class="text">
        <text>￥</text>
        <text>{{ groupInfo.pin_tuan_price || 0 }}</text>
        <!-- <block v-if="mainSku.times">/{{ mainSku.times }}次</block> -->
      </div>
    </div>
    <div class="card">
      <Card
        :groupInfo="groupInfo"
        :reportInfo="{
          product_id: productInfo.product_id,
          group_id,
          order_id,
          activity_id
        }"
        @countdownEnd="onCountdownEnd"
      />
    </div>
    <div class="footer-fixed" v-if="loaded">
      <div class="bottom-bar">
        <button
          v-if="order_id"
          type="button"
          class="primary"
          @click="onOrderDetails"
        >
          查看订单详情
        </button>
        <button type="button" @click="onJoin()">
          {{ btnText }}
        </button>
      </div>
    </div>
    <Poster :params="posterParams" @afterPosterCreated="afterPosterCreated" />
    <PageLoading :visible="mixin_login_doing || checkJoining" />
    <DefaultFullScreen
      v-if="thereIsNoMaskingLayerOnTheProduct"
      page-key="sku"
    />
  </div>
</template>
<script>
import keepSessionMixins from '@/components/confirm-order/keepSession';
import { apiGroupCheckCanJoin, apiDecryptedData } from '@/api/order';
// import SkuCard from '@/components/confirm-order/sku-card';
import { apiJoinGroupDetail, getGroupShareInfo } from '@/api/activity';
import PageLoading from '@/components/pageLoading';
import Poster from '@/components/pintuan/poster';
import Card from '@/components/pintuan/card';
import { mapState, mapGetters } from 'vuex';
import { btoa } from '@/common/base64';
import DefaultFullScreen from '@/components/DefaultFullScreen.vue';

export default {
  components: { DefaultFullScreen, Poster, Card, PageLoading },
  mixins: [keepSessionMixins],
  data() {
    return {
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      background: '',
      group_id: '',
      productInfo: {},
      groupInfo: {
        failureTip: '', // 拼团失败提示
        group_id: 0,
        icon: '', // 拼团流程图
        joinNum: 0, // 参团总人数
        status: -100, // 拼团状态 0拼团中 1拼团成功 2拼团失败
        statusStr: '', // 状态信息
        timeCountDown: 0, // 拼团倒计时
        title: '', // 拼团标题
        userList: [], // 参团用户信息
        userNum: 0, // 拼团总人数
        share_info: {}
      },
      order_id: '',
      promise: {},
      loaded: false,
      checkJoining: false,
      from: 0,
      sku_id: '',
      activity_id: '',
      canvasImg: '',
      thereIsNoMaskingLayerOnTheProduct: false
    };
  },
  onLoad(options) {
    console.log('页面参数', options);
    this.menuRect = uni.getMenuButtonBoundingClientRect();
    this.group_id = options.group_id;
    this.sku_id = options.sku_id;
    this.from = options.from || 0;
    this.getGidPromise = this.getShareInfo();
  },
  created() {
    uni.updateShareMenu({
      withShareTicket: true
    });
    this.$options.pageTrackConfig = new Promise((r) => {
      this.fetchData().then((status) => {
        r({
          info: 'sy_wxtuan_tuan_pintuanorder_info_page',
          ext: {
            from: this.from,
            group_id: this.group_id,
            type: status
          }
        });
      });
    });
  },
  onShow() {
    if (this.loaded) {
      this.fetchData();
    }
  },
  onHide() {},
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin']),
    mainSku() {
      return this.productInfo || {};
    },
    posterParams() {
      console.log('分享数据', this.sku_id, this.activity_id, this.group_id);
      const base64Url = btoa(
        `/packageActivity/pintuan?sku_id=${this.sku_id}&activity_id=${this.activity_id}&group_id=${this.group_id}&from=1`
      );
      return {
        title: `好友邀你一起拼单，不同部位不同城市可混拼！`,
        path: `/pages/index?shining_link=` + base64Url.replace(/\//g, '_'),
        backgroundImage: this.groupInfo.share_info?.background_image,
        imgCover: this.groupInfo.share_info.img_cover,
        userNum: this.groupInfo.share_info.user_num,
        priceGroup: this.groupInfo.share_info.price_group,
        priceOnline: this.groupInfo.share_info.price_online
      };
    },
    // 是否在拼团中
    active() {
      return [0].includes(this.groupInfo.status);
    },
    btnText() {
      // 拼团状态 0拼团中 1拼团成功 2拼团失败
      if (!this.active || (this.isLogin && this.groupInfo.order_id)) {
        return '我要开团';
      } else {
        return '我要参团';
      }
    }
  },
  watch: {
    order_id(value) {
      if (value) {
        this.$reportData({
          info: 'sy_wxtuan_tuan_pintuan_info:chakan_btn_exposure',
          ext: {
            from: this.from,
            group_id: this.group_id,
            type: this.groupInfo.status,
            order_id: this.order_id
          }
        });
      }
    },
    'groupInfo.status'() {
      this.$watch(
        'btnText',
        function () {
          const info =
            this.btnText === '我要参团'
              ? 'sy_wxtuan_tuan_pintuanorder_info:cantuan_btn_exposure'
              : 'sy_wxtuan_tuan_pintuan_info:kaituan_btn_exposure';
          this.$reportData({
            info,
            ext: {
              from: this.from,
              group_id: this.group_id,
              type: this.groupInfo.status
            }
          });
        },
        {
          immediate: true
        }
      );
    }
  },
  methods: {
    handleBack() {
      if (getCurrentPages().length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack();
      }
    },
    /**
     * 获取当前群 GID
     * @returns Promise(string)
     */
    async getShareInfo() {
      const { scene, shareTicket, chatType } = uni.getEnterOptionsSync();
      console.log('拼团入参', shareTicket, scene, chatType);
      if (!shareTicket) return '';
      let encryptedData, iv;
      try {
        const res = await this.wxGetShareInfo(shareTicket);
        encryptedData = res.encryptedData;
        iv = res.iv;
      } catch (error) {
        console.log('wxGetShareInfo', error);
        uni.$log(error, 'error');
        return '';
      }
      // "{"openGId":"tGhb0V0S8H4wcHw9TkdI_9sA-4Qu0","watermark":{"timestamp":1684481409,"appid":"wx83a3c7bd850395ed"}}"
      console.log('拼团密钥和向量', encryptedData, iv);
      const responseData = await apiDecryptedData(
        this.userInfo.sessionKey,
        encryptedData,
        iv
      );
      console.log('拼团DecodeWxData解密', responseData);
      // 当前微信群的openGId
      return responseData?.openGId || '';
    },
    // 获取分享
    async wxGetShareInfo(shareTicket) {
      await this.$uniLogin();
      return new Promise((resolve, reject) => {
        uni.getShareInfo({
          shareTicket,
          success: (res) => {
            if (res.errMsg === 'getShareInfo:ok') {
              resolve(res);
            } else {
              reject(res.errMsg);
            }
          },
          fail: (error) => reject(error.errMsg)
        });
      });
    },
    onCountdownEnd() {
      this.fetchData();
    },
    afterPosterCreated(p) {
      p.then((res) => {
        this.canvasImg = res.imageUrl;
      });
    },

    all() {
      return Promise.all([
        apiJoinGroupDetail({
          sku_id: this.sku_id,
          activity_id: this.activity_id,
          group_id: this.group_id
        }),
        getGroupShareInfo({
          sku_id: this.sku_id,
          group_id: this.group_id
        })
      ]);
    },
    async fetchData() {
      await this.$uniLogin();
      const [joinGroupResult, shareInfoResult] = await this.all();
      const { errorMsg, responseData } = joinGroupResult;
      const { responseData: shareResponseData } = shareInfoResult;
      if (responseData) {
        if (
          !(
            responseData.group_info &&
            responseData.group_info.user_list &&
            responseData.group_info.user_list.length > 0
          )
        ) {
          responseData.group_info.user_list = [];
        }
        this.groupInfo = Object.assign({}, responseData.group_info, {
          share_info: shareResponseData
        });
        this.activity_id = responseData.group_info.activity_id;
        this.order_id = responseData.group_info.order_id;
        this.productInfo = Object.assign({}, responseData.sku_info, {
          group_id: this.group_id,
          activity_id: this.activity_id
        });
        if (responseData.toast_txt) {
          uni.showToast({
            title: responseData.toast_txt,
            icon: 'none',
            duration: 3000
          });
        }
      } else {
        // const toast = () => {
        //   uni.showToast({
        //     title: errorMsg,
        //     icon: 'none'
        //   });
        // };
        // if (this.loaded) {
        //   toast();
        // } else {
        //   setTimeout(toast, 1000);
        // }

        // 接口异常展示【商品已下架】蒙层
        this.thereIsNoMaskingLayerOnTheProduct = true;
        console.error(errorMsg);
      }
      this.loaded = true;
      return this.groupInfo.status || 0;
    },
    onCardClick() {
      const { activity_id, sku_id, group_id } = this.mainSku;
      console.log(
        '跳转参数',
        `/pages/product?id=${sku_id}&group_id=${group_id}&activity_id=${activity_id}`
      );
      uni.navigateTo({
        url: `/pages/product?id=${sku_id}&group_id=${group_id}&activity_id=${activity_id}`,
        success: function () {
          console.log('跳转成功');
        },
        fail: function (err) {
          console.error('跳转失败', err);
        }
      });
    },
    async onJoin() {
      const jump = () => {
        const { activity_id, sku_id, group_id } = this.mainSku;
        console.log(
          '跳转参数',
          `/pages/product?id=${sku_id}&group_id=${
            this.btnText === '我要开团' ? '' : group_id
          }&activity_id=${activity_id}`
        );
        uni.navigateTo({
          url: `/pages/product?id=${sku_id}&group_id=${
            this.btnText === '我要开团' ? '' : group_id
          }&activity_id=${activity_id}`,
          success: function () {
            console.log('跳转成功');
          },
          fail: function (err) {
            console.error('跳转失败', err);
          }
        });
      };
      // 是开团
      if (this.btnText === '我要开团') {
        this.$reportData({
          info: 'sy_wxtuan_tuan_pintuan_info:kaituan_btn_click',
          ext: {
            from: this.from,
            group_id: this.group_id,
            type: this.groupInfo.status
          }
        });
        jump();
        return;
      }
      this.$reportData({
        info: 'sy_wxtuan_tuan_pintuan_info:cantuan_btn_click',
        ext: {
          from: this.from,
          group_id: this.group_id,
          type: this.groupInfo.status
        }
      });
      if (!(await this.keepSession(this.fetchData.bind(this)))) return;
      this.checkJoining = true;
      const opengid = await this.getGidPromise;
      console.log('当前群opengid', opengid);
      // 如果是加入拼团
      const { errorCode, errorMsg } = await apiGroupCheckCanJoin({
        sku_id: this.sku_id,
        group_id: this.group_id,
        opengid
      });
      this.checkJoining = false;
      switch (errorCode) {
        case 0:
        case 200:
          jump();
          break;
        case 11:
          uni.showModal({
            title: '提示',
            content: errorMsg,
            showCancel: false
          });
          break;
        case 1:
        case 2:
        case 3:
        case 4:
        case 8:
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
          setTimeout(jump, 3000);
          break;
        default:
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
          this.fetchData();
      }
    },
    onOrderDetails() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_pintuan_info:chakan_btn_click',
        ext: {
          from: this.from,
          group_id: this.group_id,
          type: this.groupInfo.status,
          order_id: this.order_id
        }
      });
      console.log(
        '查看订单详情',
        `/packageOrder/order-detail?orderId=${this.order_id}&sku_id=${this.sku_id}&group_id=${this.group_id}&activity_id=${this.activity_id}`
      );
      uni.navigateTo({
        url: `/packageOrder/order-detail?orderId=${this.order_id}&sku_id=${this.sku_id}&group_id=${this.group_id}&activity_id=${this.activity_id}`,
        success: function () {
          console.log('跳转成功');
        },
        fail: function (err) {
          console.error('跳转失败', err);
        }
      });
    }
  },
  onShareAppMessage() {
    return {
      title: this.posterParams.title,
      path: this.posterParams.path,
      imageUrl: this.canvasImg || this.mainSku.img_url
    };
  },
  onPageScroll(e) {
    if (e.scrollTop < 300) {
      this.background = `rgba(255,255,255,${e.scrollTop / 300})`;
    } else {
      this.background = `#ffffff`;
    }
  }
};
</script>

<style lang="less" scoped>
@px: 2rpx;

.skuCard {
  //margin-top: 7px;
  box-sizing: border-box;
  padding: 0 30 * @px;
  position: relative;
  z-index: 1;

  .skuCard-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    text:nth-child(1) {
      font-family: PingFangSC-Semibold;
      font-size: 24 * @px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 600;
    }

    text:nth-child(2) {
      font-family: Outfit-SemiBold;
      font-size: 15 * @px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 600;
    }
  }

  .skuCard-content {
    padding-top: 3 * @px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    text {
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #bababa;
      letter-spacing: 0;
      font-weight: 500;
    }
  }
}
@bottom-bar-height: 56 * 2rpx;
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}

.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;
  .share {
    display: flex;
    position: fixed;
    align-items: center;
    height: 88rpx;
    right: -100%;
    .shareIcon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  .title {
    position: fixed;
    left: 50%;
    height: 88rpx;
    font-family: PingFangSC-Medium;
    font-size: 36rpx;
    color: #333333;
    display: flex;
    align-items: center;
    transform: translateX(-50%);
  }
  .nav {
    position: fixed;
    display: flex;
    align-items: center;
    .back {
      width: 44 * @px;
      height: 44 * @px;
      display: block;
    }
  }
}

.pageTop-background {
  width: 100%;
  height: 180 * @px;
  background-color: #030303;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.page {
  box-sizing: border-box;
  min-height: 100vh;
  background: #f8f8f8;
  color: #222222;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  padding-top: 1px;
  position: relative;
}
.order-strip {
  position: relative;
  box-sizing: border-box;
  padding: 0 30rpx;
  margin: 15 * @px 15 * 2rpx 10 * @px;
  height: 140rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  .title {
    font-family: PingFangSC-Medium;
    font-size: 14 * @px;
    color: #030303;
    font-weight: 500;
  }
  .text {
    display: flex;
    justify-content: flex-end;
    align-items: baseline;
    font-family: OutFit-Regular;
    color: #222222;
    text-align: right;
    font-weight: 500;

    text:nth-child(1) {
      font-size: 12 * @px;
    }

    text:nth-child(2) {
      font-size: 20 * @px;
    }
  }
}
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 7;
  .bottom-bar {
    box-sizing: border-box;
    padding: 0 30rpx;
    .flex-align-center;
    height: 62 * @px;
    justify-content: space-between;
    button {
      box-sizing: border-box;
      flex: 1;
      margin: 0 auto;
      height: 42 * 2rpx;
      background: @border-color;
      border-radius: 0;
      font-size: 26rpx;
      font-weight: 500;
      color: #fff;
      font-family: PingFangSC-Medium;
      .flex-center;
      &::after {
        border: none;
      }
    }
    .primary {
      font-family: PingFangSC-Medium;
      //margin-right: 30rpx;
      background: #fff;
      border: 1 * @px solid #333333;
      color: #030303;
      font-weight: 500;
    }
  }
}
.card {
  padding: 0 10rpx;
  margin: 10 * @px 15 * @px 0;
  background-color: #fff;
}
</style>
