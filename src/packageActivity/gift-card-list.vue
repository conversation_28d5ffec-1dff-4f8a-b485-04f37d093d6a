<template>
  <div class="gift-card-list-page">
    <NavBar
      title="卡片记录"
      @navbarMounted="navbarMounted"
      :hasFixedHome="true"
    >
    </NavBar>
    <div class="card-list" :style="style" v-if="list && list.length">
      <div
        class="gift-card"
        v-for="(item, index) in list"
        :key="index"
        @click="goDetail(item)"
      >
        <img mode="widthFix" class="card-img" :src="item.cover_img" />
        <div class="detail">
          <div class="number">卡号：{{ item.card_number }}</div>
          <div class="time">{{ item.change_time_str }}激活</div>
        </div>
      </div>
    </div>
    <div class="empty-list" v-else>
      <img
        class="empty-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-design/2k8b1fjb5gw6x1733738740168.png"
      />
      <div>暂无记录</div>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar.vue';
import { apiGiftCardDList } from '@/api/activity';
import navigateToGiftCardDetails from './gift-card-detail-prefetch.js';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_wxtuan_tuan_card_record_page'
    };
  },
  components: {
    NavBar
  },
  data() {
    return {
      navBarHeight: 58,
      page: 1,
      page_size: 50,
      total: 0,
      list: [],
      style: ''
    };
  },
  onLoad() {
    this.page = 1;
    this.getList();
  },
  onShow() {
    console.log('onShow');
  },
  onPullDownRefresh() {
    console.log('refresh');
    this.page = 1;
    this.getList();
  },
  onReachBottom() {
    console.log('onReachBottom');
    if (this.list.length < this.total) {
      this.page = this.page + 1;
      this.getList();
    } else {
      // uni.showToast({
      //   title: '已经到底了，没有更多了',
      //   icon: 'none'
      // });
    }
  },
  methods: {
    navbarMounted({ height }) {
      this.navBarHeight = height;
      this.style = `margin-top:${this.navBarHeight * 2 + 60}rpx`;
    },
    async getList() {
      const res = await apiGiftCardDList({
        page: this.page,
        page_size: this.page_size
      });
      uni.stopPullDownRefresh();
      if (+res.errorCode === 200 || +res.errorCode === 0) {
        if (this.page === 1) {
          this.list = res.responseData.list;
          this.total = res.responseData.total;
        } else {
          this.list.push(...res.responseData.list);
        }
      } else {
        uni.showToast({
          title: res.errorMsg,
          icon: 'none'
        });
      }
    },
    goDetail(item) {
      navigateToGiftCardDetails({ number: item.card_number });
    }
  }
};
</script>
<style lang="less" scoped>
.gift-card-list-page {
  .card-list {
    width: 100%;
    padding: 0 30rpx 30px;
    box-sizing: border-box;
    .gift-card {
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 40rpx;
      background: #f8f8f8;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .card-img {
        width: 100%;
      }
      .detail {
        height: 100rpx;
        width: 100%;
        padding: 0 20rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        background: #f8f8f8;
        .number {
          font-size: 28rpx;
          color: #222222;
        }
        .time {
          font-size: 26rpx;
          color: #777777;
        }
      }
    }
  }

  .empty-list {
    height: 86vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #030303;
    text-align: center;
    font-weight: 400;

    .empty-img {
      margin-bottom: 28rpx;
      width: 70rpx;
    }
  }
}
</style>
