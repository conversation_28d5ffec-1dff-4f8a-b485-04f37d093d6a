<template>
  <div class="gift-card-page">
    <NavBar
      title="卡片激活"
      @navbarMounted="navbarMounted"
      :hasFixedHome="true"
    >
    </NavBar>
    <div class="code-area" :style="style">
      <div
        class="activation-code-wrap"
        :class="{ 'active': activationCode.length, 'focus': inputFocus }"
      >
        <input
          v-model.trim="activationCode"
          class="activation-code-input"
          type="text"
          placeholder="请输入激活码"
          maxlength="19"
          @input="onCodeInput"
          @click="inputFocus = true"
          @focus="inputFocus = true"
          @blur="inputFocus = false"
        />
        <div class="icon-scan" @click="onScanClick">
          <img
            mode="widthFix"
            class="scan-img"
            src="https://static.soyoung.com/sy-design/2hca637nb8ad51733738740213.png"
          />
        </div>
      </div>
      <div
        class="confirm-btn"
        :class="{ 'active': activationCode.length }"
        @click="onActiveBtnClick"
      >
        去激活
      </div>
      <span class="activation-record" @click="goList">激活记录</span>
    </div>
    <div class="flows-area">
      <div class="header">
        <div class="title">使用流程</div>
        <div class="rules" @click="jumpToRule">
          规则说明<i class="arrow"></i>
        </div>
      </div>
      <img
        class="flows-img"
        mode="widthFix"
        src="https://static.soyoung.com/sy-pre/11-1734513000640.png"
      />
      <div class="text-wrap">
        <div class="text">
          1.卡片激活后，不能解绑，不能重新绑定，不能跨账户使用
        </div>
        <div class="text">
          2.请确认卡片激活后，使用同一手机号登录本小程序进行预约及后续体验
        </div>
        <div class="text">
          3.请确认卡内商品，卡片激活后，卡内商品无法退款及变更商品
        </div>
        <div class="text">
          4.如有其他问题请联系
          <span class="kefu-link" @click="jumpToKefu">新氧青春客服</span>
          协助处理
        </div>
      </div>
      <div class="tip-wrap">
        <img
          class="tip-img"
          src="https://static.soyoung.com/sy-design/238380hlp6pof1734079549863.png"
        />
        <div class="tip-text">详细使用规则请查阅</div>
        <div class="rule-entry" @click="jumpToRule">规则说明</div>
      </div>
    </div>

    <PageLoading :visible="loading" />
  </div>
</template>
<script>
import NavBar from '@/components/NavBar.vue';
import { apiGiftCardDetail } from '@/api/activity';
import navigateToGiftCardDetails from './gift-card-detail-prefetch.js';
import PageLoading from '@/components/pageLoading.vue';

export default {
  pageTrackConfig() {
    return {
      info: 'sy_wxtuan_tuan_card_active_page'
    };
  },
  components: {
    NavBar,
    PageLoading
  },
  data() {
    return {
      navBarHeight: 112,
      activationCode: '', // 激活码
      style: '',
      inputFocus: false,
      lock: false,
      timer: null,
      loading: false
    };
  },
  methods: {
    navbarMounted({ height }) {
      this.navBarHeight = height;
      this.style = `margin-top:${this.navBarHeight * 2 + 60}rpx`;
    },
    onScanClick() {
      const that = this;
      console.log('strat scan');
      wx.scanCode({
        success(res) {
          console.log(res);
          that.activationCode = res.result;
        },
        fail: (err) => {
          console.log(err);
        }
      });
    },
    onActiveBtnClick() {
      const that = this;
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.loading = true;
      this.timer = setTimeout(() => {
        that.getInfo();
      }, 300);
    },
    async getInfo() {
      if (!this.lock) {
        this.lock = true;
        this.$reportData({
          info: 'sy_wxtuan_tuan_card_active:go_active_click',
          ext: {}
        });
        const card_password = this.activationCode.replace(/\s/g, '');
        if (card_password === '' || card_password.length !== 16) {
          uni.showToast({
            title: '请填写16位激活码或扫描条形码获取激活码',
            icon: 'none',
            duration: 1500
          });
          this.lock = false;
          this.loading = false;
          return;
        }
        const promise = apiGiftCardDetail({
          card_password: card_password
        });
        const res = await promise;
        this.loading = false;
        this.lock = false;
        uni.hideLoading();
        if (+res.errorCode === 200 || +res.errorCode === 0) {
          navigateToGiftCardDetails({ psd: this.activationCode }, promise);
        } else {
          uni.showToast({
            title: res.errorMsg,
            icon: 'none',
            duration: 3000
          });
        }
      }
    },
    onCodeInput(e) {
      const that = this;
      const screeningStr = /[^a-zA-Z0-9\s]/g;
      setTimeout(() => {
        this.activationCode = that.splitString(
          e.target.value.replace(screeningStr, '')
        );
      }, 10);
    },
    splitString(str) {
      var result = [];
      str = str.replace(/\s/g, '');

      for (var i = 0; i < str.length; i += 4) {
        result.push(str.slice(i, i + 4));
      }
      return result.join(' ');
    },
    goList() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_card_active:record_click',
        ext: {}
      });
      this.$bridge({
        url: `/packageActivity/gift-card-list`
      });
    },
    // 客服
    jumpToKefu() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_card_active:contact_service_click',
        ext: {}
      });

      this.$toKefuDialog({
        source: 'sy_yx_mini_program_private_msg_customer_service_icon',
        direct: true
      });
    },
    // 规则
    jumpToRule() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_card_active:rule_click',
        ext: {}
      });

      const url = 'https://m.soyoung.com/tmwap26521#/';

      // http开头的跳h5，其余的跳小程序
      if (url.indexOf('http') === 0) {
        this.$toH5(url);
      } else {
        this.$bridge({
          url
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.gift-card-page {
  background: #ffffff;
  .code-area {
    text-align: center;
    margin-top: 224rpx;
    .activation-code-wrap {
      margin: 60rpx auto 0;
      position: relative;
      width: 321 * 2rpx;
      height: 80rpx;
      text-align: left;
      border: 4rpx solid #f2f2f2;
      background: #f2f2f2;
      overflow: hidden;
      &.focus {
        border-color: @border-color;
        background: #fff;
      }
      &.active {
        border-color: @border-color;
        background: #fff;
      }
      .activation-code-input {
        width: 600rpx;
        height: 100%;
        box-sizing: border-box;
        padding-left: 40rpx;
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #222;
        letter-spacing: 0.43px;
        font-weight: 400;
        cursor: pointer;
        border: none;
      }
      .icon-scan {
        position: absolute;
        right: 0rpx;
        top: 0rpx;
        cursor: pointer;
        width: 100rpx;
        height: 88rpx;
        z-index: 10;
        // background: red;
        display: flex;
        align-items: center;
        justify-content: center;
        .scan-img {
          margin-top: -4rpx;
          width: 44rpx;
          max-height: 44rpx;
        }
      }
    }

    .confirm-btn {
      margin: 50rpx auto 0;
      background: @border-color;
      width: 325rpx * 2;
      text-align: center;
      padding: 24rpx 0;
      background: #bababa;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
      &.active {
        background: @border-color;
      }
    }

    .activation-record {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      display: inline-block;
      text-align: center;
      margin: 40rpx auto 0;
      position: relative;
      cursor: pointer;
      &::after {
        content: '';
        width: 100rpx;
        height: 2rpx;
        background: #222222;
        position: absolute;
        left: 0;
        bottom: 2rpx;
      }
    }
  }

  .flows-area {
    background: #f8f8f8;
    margin: 60rpx 50rpx;
    box-sizing: border-box;
    padding: 40rpx 0;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        font-family: PingFangSC-Medium;
        font-size: 15px;
        color: #222222;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        margin-left: 30rpx;
      }

      .rules {
        display: inline-flex;
        align-items: center;
        padding-right: 30rpx;
        height: 50rpx;
        line-height: 50rpx;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #555555;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        .arrow {
          display: inline-block;
          margin-left: 2px;
          width: 16rpx; /* 设置容器宽度，即箭头大小 */
          height: 20rpx; /* 设置容器高度，即箭头大小 */
          position: relative;
          background: url(https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png)
            no-repeat;
          background-size: 100%;
        }
      }
    }
    .flows-img {
      width: 100%;
      margin-top: 40rpx;
    }

    .text-wrap {
      margin: 40rpx 30rpx;
      font-family: PingFangSC-Regular;
      font-size: 13px;
      color: #8c8c8c;
      letter-spacing: -0.4px;
      line-height: 24px;
      font-weight: 400;
      .kefu-link {
        display: inline-block;
        margin: 0 2px;
        position: relative;
        cursor: pointer;
        font-weight: 600;
        &::after {
          content: '';
          width: 150rpx;
          height: 2rpx;
          background: #999999;
          position: absolute;
          left: 0;
          bottom: 6rpx;
        }
      }
    }

    .tip-wrap {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      margin-left: 30rpx;
      .tip-img {
        display: inline-block;
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }

      .tip-text {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
      }

      .rule-entry {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 600;
        margin-left: 8rpx;
        position: relative;
        cursor: pointer;
        &::after {
          content: '';
          width: 96rpx;
          height: 2rpx;
          background: #999999;
          position: absolute;
          left: 0;
          bottom: 2rpx;
        }
      }
    }
  }
}
</style>
