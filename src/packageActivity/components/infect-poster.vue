<template>
  <div>
    <div
      class="poster-mask"
      :class="aniFadeIn ? 'poster-mask-show' : ''"
      :style="{
        display: aniVisible ? 'block' : 'none'
      }"
      @transitionend="ontransitionendMask"
    ></div>
    <div
      class="poster-wrap"
      :class="aniFadeIn ? 'poster-wrap-show' : ''"
      :style="{
        display: aniVisible ? 'block' : 'none'
      }"
    >
      <div class="canvas-wrap">
        <img :src="url" />
        <div class="close" @click="$emit('update:visible', false)"></div>
      </div>
      <div class="footer">
        <div class="button" @click="onSave">保存美图</div>
      </div>
    </div>
    <canvas
      type="2d"
      class="canvas"
      id="myCanvas"
      :style="{
        width: width * scale + 'px',
        height: height * scale + 'px'
      }"
    ></canvas>
  </div>
</template>
<script>
let ctx;
let canvas;
const dpr = 3;
const width = 283 * dpr;
const height = 475 * dpr;
const fix = (n) => n * dpr;
const scale = 1 / dpr;
// 全局缓存加载的图片资源，不load两次
const catched = new Map();
export default {
  props: {
    visible: Boolean,
    params: {
      type: Object,
      default: () => ({
        cover: '',
        code: ''
      })
    }
  },
  data() {
    return {
      aniVisible: false,
      aniFadeIn: false,
      url: '',
      width,
      height,
      scale,
      queue: [],
      resourceUrls: []
    };
  },
  watch: {
    visible(value) {
      if (value) {
        this.$nextTick().then(() => {
          this.aniVisible = true;
          setTimeout(() => {
            this.aniFadeIn = true;
          }, 50);
        });
      } else {
        this.aniFadeIn = false;
      }
    },
    params: {
      handler: function (params) {
        const { code, cover } = params;
        if (code && cover) {
          this.createPoster(params);
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    ontransitionendMask() {
      if (!this.aniFadeIn) {
        this.aniVisible = false;
      }
    },
    onSave() {
      uni.saveImageToPhotosAlbum({
        filePath: this.url,
        success: () => {
          uni.showToast({
            title: '保存成功,赶快去分享吧!',
            icon: 'none'
          });
          this.$emit('update:visible', false);
        },
        fail(err) {
          const msg = Number(err.errno) === 103 ? '没有权限' : '';
          uni.showToast({
            title: '保存失败!' + msg,
            icon: 'none'
          });
        }
      });
      this.$emit('saveClick');
    },
    createPoster({ code, cover }) {
      const promise = new Promise((resolve) => {
        /**
         * 在draw之前调用，向绘制队列添加操作，如果存在图片url,也会发起一个不阻塞的图片fetch promise
         * 下面的顺序决定了绘制的顺序: 数组累加器 + promise调用链实现
         */
        this.resetCanvas();
        this.drawBgImage(cover);
        this.drawCode(code);
        this.draw(resolve);
      });
      promise.then((url) => {
        this.url = url;
      });
      this.$emit('afterPosterCreated', promise);
    },
    async draw(resolve) {
      this.queue.push(function scaleCanvas() {
        ctx.scale(this.scale, this.scale);
        uni.canvasToTempFilePath({
          canvas,
          x: 0,
          y: 0,
          width,
          height,
          fileType: 'png',
          quality: 1,
          destWidth: width,
          destHeight: height,
          success: (res) => {
            console.log('图片生成：', res.tempFilePath);
            resolve(res.tempFilePath);
            // this.$uploadImg({
            //   url: '/xinyang/posts/addPic',
            //   filePath: res.tempFilePath,
            //   name: 'imgFile'
            // }).then((res) => {
            //   if (res.errorCode === 0) {
            //     console.log({
            //       title: this.params.title,
            //       path: this.params.path,
            //       imageUrl: res.responseData.u
            //     });
            //   }
            // });
          }
        });
      });
      // 所有的方法依赖ctx初始化
      await this.getCtxPromise();
      this.resourceUrls.forEach((url) => this.loadResource(url));
      console.log(
        '绘制操作队列：',
        this.queue.map((func) => func.name)
      );
      this.queue.reduce((prev, cur) => {
        return prev.then((...args) => {
          ctx.save();
          let isPromise = cur.apply(this, args);
          if (!isPromise) {
            isPromise = Promise.resolve(isPromise);
          }
          return isPromise.then((result) => {
            ctx.restore();
            return result;
          });
        });
      }, Promise.resolve());
    },
    drawTip() {
      this.queue.push(function drawTip() {
        // const width = fix(121);
        // const height = fix(16);
        const x = fix(81);
        const y = fix(397);
        const padding = fix(2);
        const fontStyle = `400 ${fix(11)}px PingFangSC-Regular`;
        // ctx.strokeRect(x, y, width, height);
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillStyle = '#666';
        ctx.font = fontStyle;
        ctx.fillText('长按识别二维码查看商品', x, y + padding);
      });
    },
    resetCanvas() {
      this.queue = [];
      this.queue.unshift(function resetCanvas() {
        ctx.clearRect(0, 0, width, height);
      });
    },
    getCtxPromise() {
      return this.queryNode('#myCanvas').then((res) => {
        if (!res) {
          return Promise.reject('没有查询到canvas');
        }
        const [{ node }] = res;
        canvas = node;
        canvas.width = width;
        canvas.height = height;
        ctx = canvas.getContext('2d');
        ctx.strokeStyle = '#000';
        ctx.strokeRect(0, 0, width, height);
      });
    },
    drawTitle(title) {
      this.queue.push(function drawTitle() {
        const width = fix(233);
        const height = fix(21);
        const x = fix(25);
        const y = fix(250);
        const padding = fix(3);
        const fontStyle = `500 ${fix(14)}px PingFangSC-Medium`;

        const wrapText = (text, maxWidth, limit, ellipsis = true) => {
          const fonts = text.split('');
          let subStr = '';
          const rows = [];
          ctx.font = fontStyle;
          while (fonts.length) {
            const cur = fonts.shift();
            if (ctx.measureText(subStr + cur).width > maxWidth) {
              rows.push(subStr);
              subStr = cur;
            } else {
              subStr += cur;
            }
          }
          if (rows.length < limit) {
            rows.push(subStr);
          } else if (ellipsis) {
            let str = rows.pop();
            str = str.replace(/\S{1}$/g, '...');
            rows.push(str);
          }
          return rows;
        };
        const rows = wrapText(title, width, 2);
        rows.forEach((text, index) => {
          // ctx.strokeRect(x, y, width, height * 2);
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          ctx.fillStyle = '#222';
          ctx.font = fontStyle;
          ctx.fillText(text, x, y + padding + height * index);
        });
      });
    },
    drawPanel({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      this.queue.push(function drawPanel() {
        this.roundRect({ radius, x, y, width, height, backgroundColor });
      });
    },
    drawBgImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawBgImage() {
        const image = await this.loadResource(url);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          0,
          0,
          width,
          height
        );
      });
    },
    loadCoverImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function loadCoverImage() {
        const image = await this.loadResource(url);
        const dx = fix(15);
        const dy = fix(50);
        const dWidth = fix(253);
        const dHeight = fix(190);
        this.clipRect({ radius: 8, width: 253, height: 190, x: 15, y: 50 });
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    drawCode(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawCode() {
        const image = await this.loadResource(url);
        const dx = fix(95);
        const dy = fix(299);
        const dWidth = fix(94);
        const dHeight = fix(94);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    clipRect({ radius, x, y, width, height }) {
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height));
      ctx.lineTo(fix(x), fix(y + height));
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.clip();
    },
    roundRect({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      ctx.fillStyle = backgroundColor;
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height - radius));
      ctx.arcTo(
        fix(x + width),
        fix(y + height),
        fix(x + width - 2 * radius),
        fix(y + height),
        fix(radius)
      );
      ctx.lineTo(fix(x + radius), fix(y + height));
      ctx.arcTo(
        fix(x),
        fix(y + height),
        fix(x),
        fix(y + height - radius),
        fix(radius)
      );
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.fill();
    },
    loadResource(url) {
      const fetch = (url, tryTimes = 5) =>
        new Promise((resolve, reject) => {
          const image = canvas.createImage();
          image.onload = () => resolve(image);
          image.onerror = (error) => {
            console.log(`load image error :try:${tryTimes} !!!`);
            if (tryTimes-- > 0) {
              fetch(
                url.replace(/\?t=\d+/g, '') + '?t=' + new Date().getTime(),
                tryTimes
              );
            } else {
              reject(error);
            }
          };
          image.src = url;
        });
      // return promise<image> or undefined
      if (catched.has(url)) {
        const target = catched.get(url);
        target.catch(() => catched.delete(url));
      } else {
        catched.set(url, fetch(url));
      }
      return catched.get(url);
    },
    queryNode(selector, times = 10) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(selector)
          .fields({ node: true })
          .exec((res) => {
            if (res?.length > 0) {
              resolve(res);
            } else {
              if (times--) {
                setTimeout(() => {
                  resolve(this.queryNode(selector, times));
                }, 50);
              } else {
                uni.$log(
                  '[poster]没有查询到节点！',
                  this.params.title,
                  'error'
                );
                resolve([]);
              }
            }
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.poster-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.6);
  transition: opacity 0.3s;
  opacity: 0;
}
.poster-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10001;
  transition: transform 0.3s;
  transform: translateY(100%);
}
.canvas-wrap {
  position: absolute;
  top: 45%;
  left: 50%;
  width: 283 * 2rpx;
  height: 475 * 2rpx;
  transform: translate(-50%, -50%);
  overflow: hidden;
  border-radius: 30rpx;
  z-index: 10;
  background-color: #ffffff;
  img {
    width: 100%;
    height: 100%;
  }
}
.close {
  position: absolute;
  top: 0;
  right: 0;
  height: 80rpx;
  width: 80rpx;
  background: url(https://static.soyoung.com/sy-pre/close-1678432200772.png)
    no-repeat center center transparent;
  background-size: 40rpx 40rpx;
  z-index: 2;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  border-radius: 30rpx 30rpx 0 0;
  padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  padding-bottom: env(safe-area-inset-bottom);
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 30rpx auto 15rpx;
    width: 345 * 2rpx;
    height: 44 * 2rpx;
    background: #34a27f;
    border-radius: 44px;
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
  }
}
.poster-mask-show {
  opacity: 1;
}
.poster-wrap-show {
  transform: translateY(-0%);
}
</style>
<style lang="less" scoped>
.canvas {
  position: fixed;
  top: -10000rpx;
  left: -10000rpx;
}
</style>
