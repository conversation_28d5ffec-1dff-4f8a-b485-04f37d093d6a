<template>
  <view class="prize">
    <view class="prize-title">
      <image
        src="https://static.soyoung.com/sy-pre/25m7nhje8y2wf-1711699800692.png"
      ></image>
      <text>{{ title }}</text>
      <image
        src="https://static.soyoung.com/sy-pre/25m7nhje8y2wf-1711699800692.png"
      ></image>
    </view>
    <scroll-view
      class="prize-list"
      scroll-x
      enable-flex
      :scroll-left="scrollLeft"
      :scroll-into-view="'prize-item-' + currentPrizeCoordinates"
      :class="{ 'prize-list-more': list.length > 3 }"
    >
      <view
        v-for="(item, index) in list"
        class="prize-item"
        :id="'prize-item-' + index"
        :class="{ 'prize-item-currently': Number(item.selected) === 1 }"
        :key="index"
      >
        <view class="prize-top" @click="viewPrizeDetails(index)">
          <image
            class="prize-top-image"
            :src="item.award_info.award_img"
          ></image>
          <view class="prize-top-name">
            <block v-if="Number(item.code_info.reach_yn) === 0">
              <!-- 当前奖品阶段未达成 -->
              <block v-if="code === 0">
                <!-- 活动进行中 -->
                {{ index === 0 ? '送' : '送满'
                }}<text>{{ item.award_info.invite_user_num }}人</text
                >{{ index !== 0 ? '再' : '' }}得
              </block>
              <block v-else-if="code === 2"> 未获得 </block>
            </block>
            <block v-else>
              <!-- 当前奖品阶段已达成 -->
              {{ achievedText(item) }}
            </block>
          </view>
          <view
            class="prize-top-price"
            :style="{
              backgroundImage:
                Number(item.selected) === 1
                  ? 'url(https://static.soyoung.com/sy-pre/369lau583o3hz-1713165000700.png)'
                  : 'url(https://static.soyoung.com/sy-pre/1q3k9uq2ropdx-1711937400665.png)'
            }"
            >￥{{ item.award_info.award_price }}</view
          >
        </view>
        <view class="prize-bottom">
          <image
            v-if="Number(item.selected) === 1"
            class="current"
            src="https://static.soyoung.com/sy-pre/3ud4qo9wx172l-1711937400665.png"
          ></image>
          <!-- prize-bottom-redeemed 已兑换 -->
          <!-- prize-bottom-redeemed-reach 待兑换 -->
          <view
            v-else-if="buttonHidden(item)"
            class="prize-bottom-button"
            :class="btnTxt(item).className"
            @click="btnClick(item, index)"
          >
            {{ btnTxt(item).text || '' }}
          </view>
        </view>
      </view>
      <view class="prize-list-more-shadow" v-if="list.length > 3"></view>
    </scroll-view>
  </view>
</template>

<script>
import { apiReceiveCoupon } from '@/api/activity';

export default {
  props: {
    list: {
      type: Array,
      detail() {
        return [];
      }
    },
    title: {
      type: String,
      detail() {
        return '';
      }
    },
    userLength: {
      type: Number,
      detail() {
        return 0;
      }
    },
    code: {
      type: Number,
      detail() {
        return -1;
      }
    },
    activityId: {
      type: String,
      detail() {
        return '';
      }
    }
  },
  computed: {
    currentPrizeCoordinates() {
      return this.list.findIndex((item) => Number(item.selected) === 1);
    }
  },
  data() {
    return {
      scrollLeft: 0
    };
  },
  methods: {
    buttonHidden(param) {
      if (
        new Date() > new Date(param.code_info.end_date) ||
        new Date() > new Date(param.code_info.stop_time)
      ) {
        return false;
      }
      return true;
    },
    viewPrizeDetails(index) {
      this.$emit('viewPrizeDetails', index);
    },
    btnTxt(param) {
      if (!param) return {};
      if (
        Number(param.code_info.get_yn) === 1 &&
        Number(param.code_info.use_yn) === 1
      ) {
        return {
          text: '已兑换',
          status: 2,
          className: 'prize-bottom-redeemed'
        };
      } else if (
        Number(param.code_info.reach_yn) === 1 &&
        (Number(param.code_info.get_yn) === 0 ||
          (Number(param.code_info.get_yn) === 1 &&
            Number(param.code_info.use_yn) === 0))
      ) {
        return {
          text: '立即兑换',
          status: 1,
          className: 'prize-bottom-redeemed-reach'
        };
      }
    },
    async btnClick(param, index) {
      this.$emit('onBtnClick', index);
      if (this.btnTxt(param).status === 2) {
        uni.showToast({
          title: '当前奖品已完成兑换，可前往订单页查看',
          icon: 'none'
        });
      } else if (this.btnTxt(param).status === 1) {
        const res = await apiReceiveCoupon(
          this.activityId,
          1,
          param.award_info.coupon_id
        );
        if (res) {
          uni.navigateTo({
            url: `/pages/product?id=${param.award_info.sku_id}`
          });
        }
      }
    },
    achievedText(param) {
      if (Number(param.code_info.get_yn) === 0) {
        if (new Date() > new Date(param.code_info.stop_time)) {
          return '已过期';
        }
        const time = `${
          new Date(param.code_info.stop_time).getMonth() + 1
        }-${new Date(param.code_info.stop_time).getDate()}`;
        return `${time}前有效`;
      } else {
        if (new Date() > new Date(param.code_info.end_date)) {
          return '已过期';
        }
        if (Number(param.code_info.use_yn) === 0) {
          const time = `${
            new Date(param.code_info.end_date).getMonth() + 1
          }-${new Date(param.code_info.end_date).getDate()}`;
          return `${time}前有效`;
        } else {
          return '已兑换';
        }
      }
    }
  },
  mounted() {
    setTimeout(() => {
      if (this.list.length > 3) {
        if (this.currentPrizeCoordinates === 0) {
          this.scrollLeft = -1000;
        } else if (this.currentPrizeCoordinates === this.list.length - 1) {
          this.scrollLeft = 100000;
        }
      }
    }, 50);
  }
};
</script>

<style scoped lang="less">
@px: 2rpx;

.prize {
  width: 345 * @px;
  height: 183 * @px;
  box-sizing: border-box;
  padding: 15 * @px 0 0;
  background-color: #ffffff;
  border-radius: 12 * @px;
  display: flex;
  flex-direction: column;
  .prize-title {
    font-family: PingFangSC-Regular;
    font-size: 16 * @px;
    color: #222222;
    letter-spacing: 0;
    font-weight: 400;
    position: relative;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30 * @px;
    image {
      width: 30 * @px;
      height: 30 * @px;
    }
  }
  .prize-list {
    &::-webkit-scrollbar {
      display: none;
    }
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
    flex: 1;
    height: 300 * @px;
    .prize-item {
      padding-top: 19 * @px;
      width: 91 * @px;
      min-width: 91 * @px;
      .prize-top {
        width: 91 * @px;
        height: 80.7 * @px;
        position: relative;
        border-radius: 4 * @px;
        .prize-top-image {
          object-fit: cover;
          width: 91 * @px;
          height: 80.7 * @px;
        }
        .prize-top-name {
          position: absolute;
          bottom: 0;
          z-index: 10;
          width: 100%;
          height: 20 * @px;
          font-family: PingFangSC-Regular;
          font-size: 10.64 * @px;
          color: #555555;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          text {
            color: #f05551;
          }
        }
        .prize-top-price {
          position: absolute;
          width: 40 * @px;
          height: 15 * @px;
          background-image: url(https://static.soyoung.com/sy-pre/1q3k9uq2ropdx-1711937400665.png);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          top: -3 * @px;
          left: 0;
          z-index: 10;
          font-family: PingFangSC-Medium;
          font-size: 8.87 * @px;
          color: #ffffff;
          font-weight: 500;
          box-sizing: border-box;
          padding-right: 3 * @px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .prize-bottom {
        width: 100%;
        display: flex;
        justify-content: center;
        padding-top: 8 * @px;
        .current {
          width: 14 * @px;
          height: 8 * @px;
        }
        .prize-bottom-button {
          width: 58 * @px;
          height: 18 * @px;
          border-radius: 15 * @px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-family: PingFangSC-Medium;
          font-size: 11 * @px;
          font-weight: 500;
        }
        .prize-bottom-redeemed {
          color: #ed404b;
          background-color: #ffe7e3;
        }
        .prize-bottom-redeemed-reach {
          background-color: #fe6364;
          color: #ffffff;
        }
      }
    }
    .prize-item-currently {
      .prize-top {
        animation-name: CurrentClaim;
        animation-timing-function: ease-in-out;
        animation-duration: 1.7s;
        animation-iteration-count: infinite;
      }
      .prize-bottom {
        transform: scale(1.1);
      }
    }
  }
  .prize-list-more {
    justify-content: flex-start;
    .prize-item {
      margin-right: 18 * @px;
    }
    .prize-item:nth-child(1) {
      margin-left: 18 * @px;
    }
  }
  .prize-list-more-shadow {
    min-width: 0.1 * @px;
    height: 100%;
  }
}

@keyframes CurrentClaim {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
