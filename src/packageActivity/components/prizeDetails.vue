<template>
  <view
    class="modal-mask"
    @click="close"
    @animationend="animationend"
    :class="{
      'fade-in': transform
    }"
    v-if="visible"
  >
    <view class="mask"></view>
    <view class="pannel" @click.stop>
      <view class="pannel-title">
        {{ title }}
        <image
          src="https://static.soyoung.com/sy-pre/56ro94fk2w7q-1710576600691.png"
          @click="close"
        ></image>
      </view>
      <view class="pannel-content">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    visible: {
      type: <PERSON><PERSON>an,
      default: false
    },
    title: {
      type: String,
      detail() {
        return '';
      }
    }
  },
  data() {
    return {
      transform: false
    };
  },
  watch: {
    visible(visible) {
      if (visible) {
        setTimeout(() => {
          this.transform = true;
        }, 50);
      } else {
        this.transform = false;
      }
    }
  },
  methods: {
    async animationend() {},
    close() {
      this.$emit('onClose');
    }
  }
};
</script>

<style lang="less" scoped>
@px: 2rpx;

.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(900px);
  &.fade-in {
    .mask {
      opacity: 1;
    }
    .pannel {
      transform: translateY(0);
    }
  }
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.2s;
}

.pannel {
  position: absolute;
  bottom: 0;
  left: 0;
  max-height: 86vh;
  height: auto;
  overflow: scroll;
  width: 100%;
  border-radius: 30rpx 30rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.2s;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  .pannel-title {
    height: 53 * @px;
    min-height: 53 * @px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: 16 * @px;
    color: #333333;
    font-weight: 600;
    position: relative;
    image {
      width: 20 * @px;
      height: 20 * @px;
      position: absolute;
      right: 10 * @px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .pannel-content {
    flex: 1;
    overflow-x: hidden;
    overflow-y: scroll;
    position: relative;
    & > image {
      width: 100%;
      vertical-align: top;
    }
  }
}
.title {
  .flex-align-center;
  justify-content: center;
  height: 52 * 2rpx;
  font-size: 32rpx;
  color: #222222;
  font-weight: 500;
}
.close {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 28rpx;
  background: url(https://static.soyoung.com/sy-pre/close-1661847000665.png)
    no-repeat center / 100%;
  z-index: 9;
}
</style>
