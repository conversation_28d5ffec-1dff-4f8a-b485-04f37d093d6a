<template>
  <div class="container">
    <div class="background-top">
      <div
        class="card"
        :style="{
          backgroundImage: 'url(' + imageUrl + ')'
        }"
      >
        <div class="label" v-if="status > 1">已激活</div>
      </div>
      <div class="tip">
        <div class="icon"></div>
        <div class="text" v-if="status === 1">
          请在{{ endTime }}前激活。详情可查阅
          <div class="underline" @click="go2rule">规则说明</div>
        </div>
        <div class="text" v-else-if="status === 2">
          <span class="important">有3个项目未激活成功，</span>请点击
          <span class="important">【继续激活】</span>激活全部项目。如有疑问，可
          <div class="underline" @click="go2kf">联系客服</div>
          或查看
          <div class="underline" @click="go2rule">规则说明</div>
          。
        </div>
        <div class="text" v-else-if="status === 3">
          已激活项目可至订单页查看并使用，<template v-if="endTime"
            >有效期至{{ endTime }}。</template
          >详情：
          <div class="underline" @click="go2rule">规则说明</div>
        </div>
      </div>
    </div>
    <div class="background-center-dash" :style="dashLineStyle"></div>
    <div class="background-bottom">
      <slot></slot>
    </div>
  </div>
</template>
<script>
const { windowWidth } = wx.getSystemInfoSync();
const base = windowWidth / 375;
function px(num) {
  return num * base;
}
export default {
  props: {
    imageUrl: {
      type: String,
      default: ''
    },
    // 1未激活， 2 部分激活  3 已激活
    status: {
      type: Number,
      default: 0
    },
    endTime: {
      type: String,
      default: ''
    },
    ruleUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      leftRediusStyle: '',
      rightRediusStyle: '',
      dashLineStyle: ''
    };
  },
  computed: {},
  watch: {
    status() {
      this.createPath();
    }
  },
  mounted() {},
  methods: {
    go2kf() {
      this.$toKefuDialog({
        source: 'sy_yx_mini_program_private_msg_customer_service_icon',
        unJoinFn: () => {
          // 未加C时执行
          this.$bridge({
            url: '/packageAccount/consult?qzychannel=3'
          });
        }
      });
    },
    go2rule() {
      this.$toH5(this.ruleUrl);
    },
    async createPath() {
      await this.$nextTick();
      const [{ height: top }] = await Promise.all([
        this.$queryFirstNode('.background-top'),
        this.$queryFirstNode('.container')
      ]);
      const R = px(20);
      const r = R / 2;
      const style = [
        `top:${top - 1}px`,
        `width:${R + 2}px`,
        `height:${R + 2}px`
      ].join(';');
      this.leftRediusStyle = style;
      this.rightRediusStyle = style;
      this.dashLineStyle = `top:${top + r - 1}px`;
    }
  }
};
</script>
<style lang="less" scoped>
.border(@redius: 32rpx,@position:relative) {
  position: @position;
  overflow: hidden;
  z-index: 3;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    border-radius: @redius;
    border: 1px solid #fff;
    z-index: 2;
  }
}
.container {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background-image: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0.45),
    rgba(255, 255, 255, 0.85)
  );
  font-size: 0;

  .background-top {
    box-sizing: border-box;
    position: relative;
    padding: 40rpx;
    padding-bottom: 0;
    .card {
      margin: 0 auto;
      width: 305 * 2rpx;
      height: 163 * 2rpx;
      background-position: 0 0;
      background-size: contain;
      background-repeat: no-repeat;
      .label {
        position: absolute;
        top: 1px;
        left: 1px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 114rpx;
        height: 52rpx;
        background-color: #333;
        font-size: 26rpx;
        color: #fff;
        z-index: 3;
      }
    }
    .tip {
      position: relative;
      box-sizing: border-box;
      padding: 24rpx 0 8rpx;
      font-size: 24rpx;
      color: #8c8c8c;
      line-height: 38rpx;
      min-height: 38rpx;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      z-index: 3;
      .icon {
        flex-shrink: 0;
        margin-right: 8rpx;
        height: 38rpx;
        width: 38rpx;
        background: url(https://static.soyoung.com/sy-design/238380hlp6pof1734079549888.png)
          no-repeat center center transparent;
        background-size: 32rpx 32rpx;
      }
      .text {
        flex: 1;
        line-height: 38rpx;
      }
      .underline {
        display: inline-block;
        margin: 0 4rpx;
        text-decoration: underline;
        font-weight: 500;
      }
    }
  }
  .background-center-dash {
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    border-top: 1.5px dashed #b9b9b9;
  }
  .background-bottom {
    padding: 40rpx;
    padding-top: 0;
  }
  .important {
    color: #333;
  }
}
</style>
