<template>
  <page-meta>
    <NavBar
      :background="background"
      :hasFixedHome="true"
      title="卡片详情"
      @navbarMounted="navbarMounted"
    >
    </NavBar>
    <div class="gift-card-detail-page">
      <div
        class="hell-gaos"
        :style="{
          backgroundImage: `url(${response.img})`
        }"
      ></div>
      <div
        class="gasket"
        :style="{ height: navBarHeight + navBarGap + 'px' }"
      ></div>
      <div class="gift-card-box-scroll" :style="giftCardScrollStyle">
        <div>
          <Background
            :imageUrl="response.img"
            :status="response.status"
            :endTime="response.end_time"
            :ruleUrl="response.rule_url"
          >
            <div class="title product-title">
              <div class="strong">{{ listTitle }}</div>
              <div class="grey">{{ listLengthText }}</div>
            </div>
            <div class="items">
              <block v-for="(sku, i) in skuList" :key="i">
                <div
                  class="item"
                  :key="i"
                  :data-id="sku.order_id"
                  @click="go2orderHandler"
                >
                  <div class="strong ellipsis">{{ sku.name }}</div>
                  <div class="flex-row" v-if="sku.status === 1">
                    <div class="blank">￥{{ sku.price }}</div>
                    <div class="grey">{{ sku.num }}次</div>
                  </div>
                  <div class="flex-row" v-else>
                    <div class="grey ellipsis">订单ID：{{ sku.order_id }}</div>
                    <div class="right">
                      <div class="grey" v-if="sku.status === 3">已失效</div>
                      <block v-else-if="sku.has_num === 0">
                        <div class="grey">已使用</div>
                      </block>
                      <block v-else>
                        <div class="grey" v-if="sku.use_num">
                          已使用{{ sku.use_num }}次，
                        </div>
                        <div class="blank">剩余{{ sku.has_num }}次</div>
                      </block>
                      <div class="arrow"></div>
                    </div>
                  </div>
                </div>
              </block>
            </div>
          </Background>
        </div>
      </div>
      <div class="footer">
        <button type="button" @click="confirmHandler">
          {{ footerBtnText }}
        </button>
      </div>
    </div>
    <div
      class="success-mask gift-card-detail-success-fadeIn"
      v-if="successVisible"
    >
      <div class="success gift-card-detail-success-zoomIn">
        <div
          class="cover"
          :style="{
            backgroundImage: 'url(' + toastUrl + ')'
          }"
        >
          <div class="label gift-card-detail-success-fadeIn">已激活</div>
          <div class="shining gift-card-detail-success-shining"></div>
        </div>
        <div class="text">
          <div class="ribbons gift-card-detail-success-bounceIn"></div>
          <div class="t t1 gift-card-detail-success-fadeIn">恭喜您</div>
          <div class="t gift-card-detail-success-fadeIn">
            已成功激活青春自由卡
          </div>
        </div>
        <button
          type="button"
          @click="go2orderListHandler"
          class="btn gift-card-detail-success-bounceIn"
        >
          立即使用
        </button>
        <div
          class="close gift-card-detail-success-fadeIn"
          @click="successCancelHandler"
        ></div>
      </div>
    </div>
    <!-- 销售数量说明 -->
    <Modal
      title="激活异常"
      :value="errorVisible"
      confirmBtnText="联系客服"
      @confirm="goonActiveHandler"
      @cancel="errorVisible = false"
    >
      <template #body>
        <div>{{ activeErrorMsg }}</div>
      </template>
    </Modal>
    <PageLoading :visible="loading" />
  </page-meta>
</template>
<script>
import NavBar from '@/components/NavBar.vue';
import Background from './gift-card-background.vue';
import Modal from '@/components/popup/index.vue';
import PageLoading from '@/components/pageLoading.vue';
import { fetchGiftCardData } from './gift-card-detail-prefetch';
import { apiActiveGiftCard } from '@/api/activity';
import { getUserPageInfoApi } from '@/api/my';

export default {
  components: {
    Modal,
    NavBar,
    Background,
    PageLoading
  },
  data() {
    return {
      psd: '',
      number: '',
      loading: true,
      successVisible: false,
      errorVisible: false,
      navBarHeight: 58,
      navBarGap: 15,
      footerHeight: 98,
      activeErrorMsg: '',
      response: {
        toast_img: '',
        card_name: '',
        sku_count: 0,
        list: [],
        status: 0
      },
      hasJoinC: false
    };
  },
  computed: {
    isAllUsed() {
      return this.skuList.every((sku) => sku.has_num === 0 || sku.status === 3);
    },
    footerBtnText() {
      switch (this.response.status) {
        case 1:
          return '立即激活';
        case 2:
          return '继续激活';
        case 3:
          return this.isAllUsed ? '立即查看' : '立即使用';
        default:
          return '立即激活';
      }
    },
    gaosBackgroundImage() {
      return `url(${'https://static.soyoung.com/sy-pre/screenshot-20240108-162232-1704701400673.png'})`;
    },
    background() {
      return 'transparent';
    },
    giftCardScrollStyle() {
      return [
        `height:calc(100vh - ${this.navBarHeight}px - ${this.navBarGap}px - ${this.footerHeight}px)`
      ].join(';');
    },
    giftCardStyle() {
      return `padding-bottom:${this.footerHeight}px`;
    },
    listTitle() {
      return this.response.card_name || '青春自由卡';
    },
    listLengthText() {
      return this.response.sku_count > 0
        ? `共${this.response.sku_count}个项目`
        : '';
    },
    skuList() {
      return this.response.list || [];
    },
    toastUrl() {
      return this.response.toast_img || '';
    }
  },
  onShow() {
    const unwatch = this.$watch(
      () => this.response.status,
      function (val) {
        if (val > 0) {
          this.pageReport();
          unwatch?.();
        }
      },
      { immediate: true }
    );
    this.getPageData();
  },
  onHide() {
    this.pageReport('$reportPageHide');
  },
  onUnload() {
    this.pageReport('$reportPageUnload');
  },
  onLoad(options) {
    const { prefetch, p: psd } = options;
    if (+prefetch === 1) {
      this.eventChannel = this.getOpenerEventChannel();
      this.eventChannel.once(
        'prefetchGiftCardDetails',
        ({ promise, psd, number }) => {
          this.psd = psd;
          this.number = number;
          this.setData(promise);
        }
      );
    } else {
      this.psd = psd;
      this.fetchData();
    }
  },
  async mounted() {
    const { height } = await this.$queryFirstNode('.footer');
    this.footerHeight = height;
  },
  methods: {
    async getPageData() {
      const responseData = await getUserPageInfoApi({});
      if (responseData) {
        this.hasJoinC = responseData.has_join_c === 1;
      }
    },
    // 联系客服
    handleGoToKefu() {
      if (this.hasJoinC) {
        // 跳转到客服会话页
        this.$toKefuDialog({
          direct: true, // 直接跳转
          source: 'sy_yx_mini_program_private_msg_customer_service_my_contact'
        });
      } else {
        this.$bridge({
          url: '/packageAccount/consult?qzychannel=4'
        });
      }
    },
    pageReport(method = '$reportPageShow') {
      this[method]({
        info: 'sy_wxtuan_tuan_card_info_page',
        ext: {
          status: this.response.status
        }
      });
    },
    reportStat(action) {
      this.$reportData({
        info: `sy_wxtuan_tuan_card_info:${action}`,
        ext: {
          status: this.response.status
        }
      });
    },
    async setData(data) {
      if (data instanceof Promise) {
        data = await data;
      }
      const { errorCode, errorMsg, responseData } = await data;
      this.loading = false;
      if ((errorCode !== 200 && errorCode !== 0) || !responseData) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      Object.keys(responseData).forEach((k) => {
        this.response[k] = responseData[k];
      });
    },
    navbarMounted({ height }) {
      this.navBarHeight = height;
    },
    async fetchData() {
      this.loading = true;
      const res = await fetchGiftCardData({ psd: this.psd });
      this.setData(res);
      this.loading = false;
    },
    successCancelHandler() {
      this.successVisible = false;
      this.loading = true;
      setTimeout(() => {
        this.fetchData();
      }, 2000);
    },
    confirmHandler() {
      switch (this.response.status) {
        case 1:
        case 2:
          this.activeCard();
          this.reportStat('confirm_active_click');
          break;
        case 3:
          this.go2orderListHandler();
          break;
      }
    },
    go2orderListHandler() {
      this.reportStat('go_use_click');
      uni.setStorageSync('order_list_page_tab_index', '0');
      uni.navigateTo({
        url: '/pages/order-list?status=0&refresh=1'
      });
    },
    go2orderHandler(event) {
      if (this.response.status <= 1) return;
      this.reportStat('project_card_click');
      const orderId = event.currentTarget.dataset.id;
      this.$bridge({
        url: `/packageOrder/order-detail?orderId=${orderId}`
      });
    },
    goonActiveHandler() {
      this.reportStat('continue_active_click');
      this.handleGoToKefu();
    },
    async activeCard() {
      this.loading = true;
      const card_password = this.psd.replace(/\s/g, '');
      const { errorCode, errorMsg } = await apiActiveGiftCard(card_password);
      this.loading = false;
      if (errorCode === 200 || errorCode === 0) {
        this.successVisible = true;
        this.errorVisible = false;
        this.activeErrorMsg = '';
        this.reportStat('active_success_exposure');
      } else {
        this.successVisible = false;
        this.errorVisible = true;
        this.activeErrorMsg = errorMsg;
        this.reportStat('active_unsuccess_exposure');
        uni.$log(this.psd, errorMsg, 'error');
      }
    }
  },
  onShareAppMessage() {
    return {
      title: '新氧青春—专业轻医美连锁',
      path: `/pages/index?market_activity_id=${
        this.userInfo.marketActivityId || ''
      }`,
      imageUrl:
        'https://static.soyoung.com/sy-pre/20230801-104619-1690855800774.jpeg'
    };
  }
};
</script>
<style lang="less" scoped>
@footerBtnHeight: 88rpx;
.important {
  color: #333;
}
.button(@width: 305 * 2rpx) {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: @width;
  height: @footerBtnHeight;
  background-color: #333;
  border-radius: 0rpx;
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 500;
  &::after {
    display: none;
  }
  &[disabled] {
    background-color: #d2cdea;
  }
}
.gift-card-detail-page {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  font-size: 0;
  .gift-card-box-scroll {
    position: relative;
    margin: 0 auto;
    width: 346 * 2rpx;
    height: 100vh;
    overflow-y: auto;
    background-color: transparent;
    .title {
      height: 48rpx;
      padding: 54rpx 0 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .strong {
        font-size: 34rpx;
        color: #222222;
        font-weight: 600;
      }
      .grey {
        font-size: 26rpx;
        color: #555555;
      }
    }
    .items {
      overflow-y: auto;
      .item {
        position: relative;
        margin-bottom: 30rpx;
        box-sizing: border-box;
        padding: 30rpx;
        width: 610rpx;
        height: 164rpx;
        color: #333333;
        background: #fff;
        z-index: 4;
        .flex-row {
          margin-top: 20rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .right {
          position: relative;
          flex-shrink: 0;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          &::after {
            content: '';
          }
          .arrow {
            margin-left: 4rpx;
            height: 20rpx;
            width: 16rpx;
            background: url(https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png)
              no-repeat;
            background-size: 100%;
          }
        }
        .strong {
          font-size: 32rpx;
          font-weight: 500;
        }
        .ellipsis {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        .grey {
          font-size: 26rpx;
          color: #777777;
        }
        .blank {
          font-size: 26rpx;
          font-weight: 500;
        }
      }
    }
  }
  .hell-gaos {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    filter: blur(100rpx);
    transform: translateZ(0);
    opacity: 0.6;
  }
  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding-top: 40rpx;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: ~'max(env(safe-area-inset-bottom), 20px)';
    background-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1)
    );
    transform: translateZ(2px);
    z-index: 3;
    button {
      .button();
    }
  }
}
.success-mask {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
  .success {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    position: absolute;
    box-sizing: border-box;
    width: 315 * 2rpx;
    max-height: 1220rpx;
    height: 80vh;
    padding: 30rpx 30rpx 60rpx;
    top: 50%;
    left: 50%;
    background: #ffffff;
    margin-left: -315rpx;
    transform: translateY(-50%);
    z-index: 1;
    .cover {
      flex-shrink: 1;
      position: relative;
      width: 285 * 2rpx;
      height: 430 * 2rpx;
      background-origin: 0 0;
      background-size: 285 * 2rpx 430 * 2rpx;
      background-repeat: no-repeat;
      background-position: center top;
      overflow: hidden;
      .label {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 114rpx;
        height: 52rpx;
        background-color: #333;
        font-size: 26rpx;
        color: #fff;
        z-index: 3;
      }
      .shining {
        position: relative;
        width: 240rpx;
        height: 150%;
        &::before {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          transform: rotate(30deg);
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.65)
          );
        }
      }
    }
    .text {
      flex-shrink: 0;
      position: relative;
      // padding: 20rpx 0;
      font-weight: 400;
      color: #333333;
      font-size: 30rpx;
      text-align: center;
      .t1 {
        margin-bottom: 16rpx;
        font-size: 36rpx;
      }
      .ribbons {
        position: absolute;
        top: -10rpx;
        left: 50%;
        height: 116rpx;
        width: 446rpx;
        margin-left: -223rpx;
        background: url(https://static.soyoung.com/sy-pre/20241216-150234-1734329400641.png)
          no-repeat center center transparent;
        background-size: contain;
      }
    }
    .btn {
      flex-shrink: 0;
      .button(265 * 2rpx);
      font-size: 26rpx;
    }
    .close {
      position: absolute;
      bottom: -100rpx;
      left: 50%;
      height: 60rpx;
      width: 60rpx;
      background: url(https://static.soyoung.com/sy-design/8v45bpmpui1f1734079549829.png)
        no-repeat center center transparent;
      background-size: contain;
      transform: translateX(-50%);
    }
  }
}
</style>

<style lang="less">
.animate__animated(@duration: 0.5s,@delay: 0s) {
  animation-duration: @duration;
  animation-fill-mode: both;
  animation-delay: @delay;
}
.success-mask {
  .animate__animated;
  .success {
    .animate__animated;
  }
  .label {
    .animate__animated(0.6s, 1s);
  }
  .ribbons {
    .animate__animated(0.6s, 0.5s);
  }
  .close {
    .animate__animated(0.6s, 0.5s);
  }
  .t {
    .animate__animated(1s, 0.5s);
  }
  .btn {
    .animate__animated(0.6s, 0.5s);
  }
  .shining {
    .animate__animated(3s, 0.5s);
  }
}
.gift-card-detail-success-zoomIn {
  animation-name: __gift_card_detail_card_zoomIn;
}
.gift-card-detail-success-fadeIn {
  animation-name: __gift_card_detail_mask_fadeIn;
}
.gift-card-detail-success-bounceIn {
  animation-name: __gift_card_detail_card_bounceIn;
}
.gift-card-detail-success-shining {
  animation-name: __gift_card_detail_mask_moveIn;
}
@keyframes __gift_card_detail_mask_moveIn {
  from {
    transform: translate(-100%, -100%);
  }
  to {
    transform: translate(100vw, 100vh);
  }
}

@keyframes __gift_card_detail_mask_fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes __gift_card_detail_card_zoomIn {
  0% {
    opacity: 0;
    transform: translateY(-50%) scale3d(0.6, 0.6, 0.6);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(-50%);
  }
}

@keyframes __gift_card_detail_card_bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    transform: scale3d(0.8, 0.8, 0.8);
  }

  60% {
    opacity: 1;
    transform: scale3d(1.01, 1.01, 1.01);
  }

  80% {
    transform: scale3d(0.99, 0.99, 0.99);
  }

  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}
</style>
