import { apiGiftCardDetail } from '@/api/activity';

export default function navigateToGiftCardDetails({ psd, number }, promise) {
  uni.navigateTo({
    url: `/packageActivity/gift-card-detail?prefetch=1`,
    events: {},
    success(res) {
      res.eventChannel.emit('prefetchGiftCardDetails', {
        promise: promise || fetchGiftCardData({ psd, number }),
        psd,
        number
      });
    }
  });
}

export function fetchGiftCardData({ psd, number }) {
  return apiGiftCardDetail({
    card_password: psd,
    card_number: number
  });
}
