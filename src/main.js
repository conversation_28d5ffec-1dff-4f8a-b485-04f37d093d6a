import '@/common/log.js';
import Vue from 'vue';
import App from './App';
import store from './store';
import './filter/index.js';
import '@/common/smartReport'; // 同步导入埋点模块，确保所有组件可用

// 非关键路径的依赖可以延迟加载
// 延迟加载prefetch功能，保持部分性能优化
const loadNonCriticalModules = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      import('@/common/prefetch');
      resolve();
    }, 2000); // 延迟2秒加载非关键模块
  });
};

// 按需加载Sentry
let Sentry = null;
const initSentry = async () => {
  if (!Sentry) {
    Sentry = await import('@/utils/sentry-uniapp.min');
    const { version } = await import('../package.json');

    // Sentry错误监控配置
    Sentry.init({
      dsn: 'https://<EMAIL>/62',
      release: version,
      environment: process.env.NODE_ENV,
      attachStacktrace: true,
      tracesSampleRate: 0.5,
      maxBreadcrumbs: 20,
      beforeSend: (event) => {
        try {
          if (event.exception && event.exception.values) {
            event.exception.values.forEach((exception) => {
              if (
                exception.stacktrace &&
                exception.stacktrace.frames &&
                exception.stacktrace.frames.length > 20
              ) {
                exception.stacktrace.frames = exception.stacktrace.frames.slice(
                  0,
                  20
                );
              }
            });
          }

          if (event.extra && Object.keys(event.extra).length > 5) {
            const keys = Object.keys(event.extra);
            const newExtra = {};
            keys.slice(0, 5).forEach((key) => {
              newExtra[key] = event.extra[key];
            });
            event.extra = newExtra;
          }

          return event;
        } catch (err) {
          console.error('Sentry beforeSend错误', err);
          return event;
        }
      }
    });

    // 监听小程序异步组件加载失败事件
    wx.onLazyLoadError = function (err) {
      uni.$logError(err, 'LazyLoadError');
      if (err && typeof err === 'object') {
        const simplifiedErr = {
          message: err.message || '异步组件加载失败',
          errMsg: err.errMsg
        };
        Sentry.captureException(simplifiedErr);
      } else {
        Sentry.captureMessage(
          '异步组件加载失败:' + String(err).substring(0, 100)
        );
      }
    };
  }
  return Sentry;
};

// 先异步加载非关键模块
loadNonCriticalModules();
// 页面稳定后初始化Sentry
setTimeout(() => {
  initSentry();
}, 3000);

Vue.config.productionTip = false;
if (!Vue.prototype.__HOLDER__) {
  Vue.prototype.__HOLDER__ = () => {};
}

// 插件按需导入
import plugins from '@/plugins/index';
Vue.use(plugins);

App.mpType = 'app';
const app = new Vue({
  store,
  ...App
});

const eventBus = new Vue();
app.$mount();

getApp().globalData.__MINI_PRIME_CHAIN_STORE__ = __MINI_PRIME_CHAIN_STORE__;
getApp().globalData.eventBus = eventBus;

uni.addInterceptor('navigateTo', {
  invoke(args) {
    const getParams = (url, name) => {
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
      const r = url.split('?')[1] && url.split('?')[1].match(reg);
      if (r != null) {
        return unescape(r[2]);
      } else {
        return null;
      }
    };
    let userInfo = uni.getStorageSync('user_info') || {};
    let url = '';
    if (args.url.indexOf('/pages/h5?') == -1) {
      url = args.url;
    } else {
      url = getParams(args.url, 'url');
    }
    if (getParams(url, 'market_activity_id')) {
      userInfo.marketActivityId = parseInt(
        getParams(url, 'market_activity_id')
      );
      Vue.prototype.$setUserInfoToStorage(userInfo);
    }
  }
});
