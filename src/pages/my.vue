<template>
  <div class="my-page" :class="{ 'page-skeleton-diagram': !hasData }">
    <div
      v-if="hasData"
      class="my-page-header-login-box"
      :style="{ backgroundImage: `url(${backgroundInfo.url || ''})` }"
    >
      <template v-if="!isLogin">
        <div class="my-page-login-bar" @click="handleLogin">
          <div class="my-page-login-bar-title-line">
            <div class="my-page-login-bar-title">登录/注册</div>
            <img
              class="my-page-login-bar-title-arrow"
              src="https://static.soyoung.com/sy-design/3dhi453czg4cw1725524061027.png"
              alt=""
            />
          </div>
          <div class="my-page-login-subtitle">
            {{ greetingInfo.title || '' }}
          </div>
          <div class="my-page-login-desc">
            {{ greetingInfo.subtitle || '' }}
          </div>
        </div>
        <div class="my-page-info-bar" v-if="fulfillmentInfo.length">
          <block v-for="(item, index) in fulfillmentInfo" :key="index">
            <login-button
              @after="handleJump(item.link)"
              v-if="item.need_login === 1"
            >
              <div class="my-page-info-item">
                <div class="my-page-info-number">{{ item.count || 0 }}</div>
                <div class="my-page-info-text">{{ item.title || '' }}</div>
              </div>
            </login-button>
            <div class="my-page-info-item" v-else>
              <div class="my-page-info-number">{{ item.count || 0 }}</div>
              <div class="my-page-info-text">{{ item.title || '' }}</div>
            </div>
          </block>
        </div>
      </template>
      <template v-else>
        <div class="my-page-account-bar">
          <div
            class="exposure my-page-account-main-message-button"
            @click="handleGoToKefu"
          >
            <img
              class="my-page-account-main-message-button-img"
              :src="consultantInfo.icon"
              alt=""
            />
            <div>{{ consultantInfo.btn_name || '' }}</div>
          </div>
          <div class="my-page-account-main-bar">
            <div class="my-page-account-main-title">
              {{ userInfo.user_name || '' }}
            </div>
            <div
              class="my-page-qrcode-entry"
              @click="handleGoToQrCode"
              v-if="vipInfo.show_member_qr_code"
            >
              <image
                class="my-page-qrcode-icon"
                :src="
                  vipInfo.member_qr_code_url ||
                  'https://static.soyoung.com/sy-design/2hca5b5fhk6c31746590518696.png'
                "
                mode="widthFix"
              />
              <span class="my-page-qrcode-text">{{
                vipInfo.member_qr_code_name || '会员码'
              }}</span>
            </div>
          </div>
          <div class="my-page-account-main-desc">welcome back</div>
          <div
            class="my-page-vip-wrapper vip-exposure"
            v-if="vipInfo.level_value_name"
            @click="handleGoToVip"
          >
            <div
              class="my-page-vip-info"
              :style="{
                background:
                  'url(' +
                  vipInfo.level_back_img.u +
                  ') no-repeat center center',
                backgroundSize: 'cover'
              }"
            >
              <div
                class="my-page-vip-main"
                :style="{ color: vipInfo.page_color || '#333333' }"
              >
                <div class="my-page-vip-level">
                  {{ vipInfo.level_value_name }}
                </div>
                <div class="my-page-vip-name">{{ vipInfo.level_name }}</div>
                <div class="my-page-vip-exp">{{ vipInfo.experience }}</div>
                <div class="my-page-vip-num" v-if="vipInfo.next_experience">
                  /
                </div>
                <div class="my-page-vip-num" v-if="vipInfo.next_experience">
                  {{ vipInfo.next_experience }}
                </div>
                <custom-svg
                  class="my-page-vip-arrow"
                  :width="'12rpx'"
                  :height="'18rpx'"
                  :color="vipInfo.page_color || '#333333'"
                  :originData="arrowData"
                />
              </div>
            </div>
            <div
              class="my-page-vip-progress-bar"
              v-if="vipInfo.next_experience"
            >
              <div
                class="active"
                :style="{
                  width: `${
                    (vipInfo.experience / vipInfo.next_experience) * 100
                  }%`
                }"
              ></div>
            </div>
          </div>
          <div class="my-page-info-bar" v-if="fulfillmentInfo.length">
            <block v-for="(item, index) in fulfillmentInfo" :key="index">
              <login-button
                @after="handleClickToFulfillment(item, index)"
                v-if="item.need_login === 1"
              >
                <div class="my-page-info-item">
                  <div class="my-page-info-number">{{ item.count || 0 }}</div>
                  <div class="my-page-info-text">{{ item.title || '' }}</div>
                </div>
              </login-button>
              <div
                class="my-page-info-item"
                v-else
                @click="handleClickToFulfillment(item, index)"
              >
                <div class="my-page-info-number">{{ item.count || 0 }}</div>
                <div class="my-page-info-text">{{ item.title || '' }}</div>
              </div>
            </block>
          </div>
        </div>
      </template>
    </div>
    <!-- <div class="my-page-order-box" v-if="hasData">
      <block v-for="(item, index) in orderListInfo" :key="index">
        <login-button @after="handleClickToOrder(item, index)">
          <div class="my-page-order-item">
            <div class="my-page-order-item-core">
              <div v-if="item.count" class="my-page-order-badge">
                {{ item.count }}
              </div>
              <img class="my-page-order-item-icon" :src="item.icon" alt="" />
            </div>
            <div class="my-page-order-item-text">{{ item.title }}</div>
          </div>
        </login-button>
      </block>
    </div> -->
    <!-- 主功能区 -->
    <div class="my-page-main-buttons" v-if="hasData && mainButtons.length">
      <div
        v-for="(item, index) in mainButtons"
        :key="index"
        class="main-button-item"
      >
        <login-button
          @after="handleMainButtonClick(item, index)"
          v-if="item.need_login === 1"
        >
          <div class="main-button-icon-wrapper">
            <img :src="item.icon" class="main-button-icon" alt="" />
            <span
              v-if="Number(item.style_type) === 1"
              class="main-button-badge-small"
            ></span>
            <span
              v-if="Number(item.style_type) === 2"
              class="main-button-badge"
            >
              {{ item.count }}</span
            >
            <span
              v-if="Number(item.style_type) === 3"
              class="main-button-badge-text"
            >
              {{ item.marker_text }}</span
            >
          </div>
          <div class="main-button-title" :style="{ color: item.color }">
            {{ item.title }}
          </div>
        </login-button>
        <div v-else @click="handleMainButtonClick(item, index)">
          <div class="main-button-icon-wrapper">
            <img :src="item.icon" class="main-button-icon" alt="" />
            <span
              v-if="Number(item.style_type) === 1"
              class="main-button-badge-small"
            ></span>
            <span
              v-if="Number(item.style_type) === 2"
              class="main-button-badge"
            >
              {{ item.count }}</span
            >
            <span
              v-if="Number(item.style_type) === 3"
              class="main-button-badge-text"
            >
              {{ item.marker_text }}</span
            >
          </div>
          <div class="main-button-title" :style="{ color: item.color }">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="line" v-if="hasData && mainButtons.length"></div>
    <!-- 邀请有礼 -->
    <div
      class="my-page-invite"
      v-if="hasData && oldBringNew && oldBringNew.banner_image"
      @click="handleInviteNowClick"
    >
      <div class="my-page-invite-header">
        <div class="my-page-invite-title">邀请有礼</div>
        <div class="my-page-invite-detail">
          <span>了解详情</span>
          <img
            src="https://static.soyoung.com/sy-design/6eboz32amcrz1728548577140.png"
            alt=""
          />
        </div>
      </div>
      <div class="my-page-invite-banner invite-exposure">
        <img
          class="my-page-invite-banner-img"
          :src="oldBringNew.banner_image.url"
          alt="邀请有礼"
        />
      </div>
      <div class="my-page-invite-footer">
        <div class="my-page-invite-amounts">
          <div class="my-page-invite-item">
            <div class="my-page-invite-amount">
              {{ isLogin ? `${oldBringNew.already_amount || '0'}` : '-' }}
            </div>
            <div class="my-page-invite-text">已到账</div>
          </div>
          <div class="my-page-invite-item">
            <div class="my-page-invite-amount">
              {{ isLogin ? `${oldBringNew.used_amount || '0'}` : '-' }}
            </div>
            <div class="my-page-invite-text">已使用</div>
          </div>
          <div class="my-page-invite-item">
            <div class="my-page-invite-amount">
              {{ isLogin ? `${oldBringNew.has_expired_amount || '0'}` : '-' }}
            </div>
            <div class="my-page-invite-text">已失效</div>
          </div>
        </div>
        <div class="my-page-invite-now-btn">立即邀请</div>
      </div>
    </div>
    <div
      class="line"
      v-if="hasData && oldBringNew && oldBringNew.banner_image"
    ></div>
    <div class="my-page-banner" v-if="bannerList.length && hasData">
      <new-banner
        :list="
          bannerList.map((item) => {
            return {
              img_url: item.image.url,
              jump_url: item.jump_url
            };
          })
        "
        @click="handleClick"
        @expose="handleExpose"
      />
    </div>
    <!-- <div class="my-page-wallet-box" v-if="hasData">
      <div class="my-page-wallet-item">
        <div v-if="isLogin" class="my-page-wallet-login-box">
          <div
            class="my-page-wallet-login-number"
            @click="handleClickToWallet(walletInfo)"
          >
            <span class="my-page-wallet-login-prefix">¥</span>
            <span class="my-page-wallet-login-suffix">{{
              walletInfo.amount || 0
            }}</span>
          </div>
          <div class="my-page-wallet-login-text">钱包</div>
        </div>
        <div v-else class="my-page-wallet-no-login-box">
          <login-button @after="handleClickToWallet(walletInfo)">
            <div class="my-page-wallet-no-login-title">
              <img
                class="my-page-wallet-no-login-icon"
                :src="walletInfo.icon"
                alt=""
              />
              <div class="my-page-wallet-no-login-text">
                {{ walletInfo.title }}
              </div>
            </div>
            <div class="my-page-wallet-no-login-desc">
              {{ walletInfo.subtitle }}
            </div>
          </login-button>
        </div>
      </div>
      <div
        class="my-page-wallet-item"
        v-if="installmentInfo.title || installmentInfo.amount"
      >
        <div
          v-if="isLogin"
          class="my-page-wallet-login-box"
          @click="handleClickToInstallment(installmentInfo)"
        >
          <div
            class="my-page-wallet-login-subtext"
            v-if="installmentInfo.credit_status === 0"
          >
            {{ installmentInfo.title }}
          </div>
          <div class="my-page-wallet-login-number" v-else>
            <span class="my-page-wallet-login-prefix">¥</span>
            <span class="my-page-wallet-login-suffix">{{
              installmentInfo.amount || 0
            }}</span>
          </div>
          <div class="my-page-wallet-login-text">分期额度</div>
        </div>
        <div v-else class="my-page-wallet-no-login-box">
          <login-button @after="handleClickToInstallment(installmentInfo)">
            <div class="my-page-wallet-no-login-title">
              <img
                class="my-page-wallet-no-login-icon"
                :src="installmentInfo.icon"
                alt=""
              />
              <div class="my-page-wallet-no-login-text">
                {{ installmentInfo.title }}
              </div>
            </div>
            <div class="my-page-wallet-no-login-desc">
              {{ installmentInfo.subtitle }}
            </div>
          </login-button>
        </div>
      </div>
    </div> -->
    <div class="my-page-list-box" v-if="hasData">
      <div class="my-page-grid-box">
        <!-- <template v-if="userSignInfo.is_show === 1">
          <div class="my-page-grid-item-wrapper">
            <login-button @after="handleJump(userSignInfo.jump_url)">
              <div class="my-page-grid-item">
                <img
                  class="my-page-grid-item-icon"
                  src="https://static.soyoung.com/sy-design/v26te9lvu1ky1736302413441.png"
                  alt=""
                />
                <div class="my-page-grid-item-text">病历签署</div>
              </div>
            </login-button>
          </div>
        </template> -->
        <block v-for="(item, index) in toolsInfo" :key="index">
          <template>
            <div class="my-page-grid-item-wrapper">
              <login-button
                v-if="item.need_login === 1"
                @after="handleToolsJump(item)"
              >
                <div class="my-page-grid-item">
                  <div class="my-page-grid-item-icon-wrapper">
                    <img
                      :class="[
                        'my-page-grid-item-icon',
                        item.style_type == 1
                          ? 'my-page-grid-item-icon-red-dot'
                          : ''
                      ]"
                      :src="item.icon"
                      alt=""
                    />
                    <div
                      class="my-page-grid-item-badge"
                      v-if="item.style_type == 2"
                    >
                      {{ item.count > 99 ? '99+' : item.count }}
                    </div>
                  </div>
                  <div class="my-page-grid-item-text">
                    {{ item.title }}
                  </div>
                </div>
              </login-button>
              <div
                class="my-page-grid-item"
                v-else
                @click="handleToolsJump(item)"
              >
                <div class="my-page-grid-item-icon-wrapper">
                  <img
                    :class="[
                      'my-page-grid-item-icon',
                      item.style_type == 1
                        ? 'my-page-grid-item-icon-red-dot'
                        : ''
                    ]"
                    :src="item.icon"
                    alt=""
                  />
                  <div
                    class="my-page-grid-item-badge"
                    v-if="item.style_type == 2"
                  >
                    {{ item.count > 99 ? '99+' : item.count }}
                  </div>
                </div>
                <div class="my-page-grid-item-text">
                  {{ item.title }}
                </div>
              </div>
            </div>
          </template>
        </block>

        <div v-if="userInfo.uid === 33708370" class="my-page-grid-item-wrapper">
          <div class="my-page-grid-item" @click="handleGoSocket">
            <div class="my-page-grid-item-icon">🔌</div>
            <div class="my-page-grid-item-text">去socket页面</div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="my-page-logout-box"
      v-if="isLogin && hasData"
      @click="handleLogout"
    >
      退出登录
    </div>
    <weak-network
      @refresh="initData"
      :path="['/syGroupBuy/chain/user/getUserHomepage']"
      :visible.sync="weakNetwork"
    />
  </div>
</template>
<script>
import { getUserPageInfoApi } from '@/api/my';
import { checkXyToken } from '@/api/user';
import LoginButton from '@/components/LoginButton';
import NewBanner from '@/components/NewBanner.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import CustomSvg from '@/components/customSvg/index.vue';
import SubscribeMixins from '@/mixins/subscribe';
import { throttle } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';

export default {
  components: {
    NewBanner,
    LoginButton,
    WeakNetwork,
    CustomSvg
  },
  mixins: [SubscribeMixins],
  data() {
    return {
      backgroundInfo: {}, // 背景信息
      greetingInfo: {}, // 问候语信息
      consultantInfo: {}, // 顾问信息
      fulfillmentInfo: [], // 履约信息
      orderListInfo: [], // 订单列表信息
      walletInfo: {}, // 钱包信息
      installmentInfo: {}, // 分期信息
      vipInfo: {}, // 会员信息
      toolsInfo: [], // 工具信息
      bannerList: [],
      userSignInfo: {},
      mainButtons: [], // 主功能区按钮
      hasJoinC: false,
      hasData: false,
      weakNetwork: false,
      arrowData: `<?xml version="1.0" encoding="UTF-8"?>
<svg width="6px" height="9px" viewBox="0 0 6 9" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>小灰箭头6*9</title>
    <g id="我的-各种入口添加" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="细节" transform="translate(-324, -938)" fill="">
            <g id="编组备份-9" transform="translate(35, 904)">
                <g id="编组-20" transform="translate(172, 33)">
                    <g id="小灰箭头6*9" transform="translate(117, 1)">
                        <path d="M1.16065983,0 L5.40330052,4.24264069 L5.292,4.353 L5.30330086,4.36396103 L1.06066017,8.60660172 L6.99440506e-15,7.54594155 L3.292,4.253 L0.0999996564,1.06066017 L1.16065983,0 Z" id="箭头2备份-2"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
      oldBringNew: {} // 邀请有礼信息
    };
  },
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  mounted() {},
  onLoad() {
    console.log('123456', this.userInfo);
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_my_page',
      ext: {}
    });
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_my_page',
      ext: {}
    });
    this.initData();
    checkXyToken().then((res) => {
      if (res.isValid !== 1) {
        this.$setUserInfoToStorage({
          uid: '',
          xyToken: ''
        });
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_my_page',
      ext: {}
    });
  },
  methods: {
    async handleLogin() {
      if (!this.isLogin) {
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        console.log('handleLogin', this.isLogin, this.userInfo);
        if (isAuth) {
          this.initData();
        }
      }
    },
    async getPageData() {
      const responseData = await getUserPageInfoApi();
      if (responseData) {
        this.backgroundInfo = (responseData.background || {}).image || {};
        this.bannerList = (responseData.banner || {}).list || [];
        this.greetingInfo = responseData.greeting_msg || {};
        this.consultantInfo = responseData.consultant || {};
        this.mainButtons = (responseData.main_buttons || {}).list || [];
        this.fulfillmentInfo = (responseData.fulfillment || {}).list || [];
        this.orderListInfo = (responseData.order || {}).list || [];
        this.installmentInfo = responseData.installment || {};
        // responseData.user_sign_info.title = '您有13份电子病例待签署';
        this.userSignInfo = responseData.user_sign_info || {};
        this.walletInfo = responseData.wallet || {};
        this.toolsInfo = (responseData.tools || {}).list || [];
        this.vipInfo = responseData.level_card || {};
        this.hasJoinC = responseData.has_join_c === 1;
        this.hasData = true;
        this.weakNetwork = false;
        this.oldBringNew = responseData.old_bring_new || {};
        this.$forceUpdate();
        setTimeout(() => {
          this.initExposure();
        }, 1000);
      }
    },
    initData() {
      this.getPageData();
    },
    initExposure() {
      this.$nextTick(() => {
        // 豆腐块埋点
        this.$registerExposure(
          '.exposure',
          () => {
            // 在回调中发送埋点，可以通过 result 的 id和 dataset 获取信息，dataset就是DOM上的 data-xx=xx
            this.$reportData({
              info: 'sy_wxtuan_tuan_my:consultant_exposure',
              ext: {}
            });
          },
          this
        );

        this.$registerExposure(
          '.vip-exposure',
          () => {
            this.$reportData({
              info: 'sy_chain_store_other_my:member_banner_exposure',
              ext: {}
            });
          },
          this
        );

        this.$registerExposure(
          '.invite-exposure',
          () => {
            this.$reportData({
              info: 'sy_wxtuan_tuan_my:old_brings_new_exposure',
              ext: {}
            });
          },
          this
        );
      });
    },
    handleJump(link) {
      if (link.indexOf('.com') > -1 || link.indexOf('http') > -1) {
        this.$toH5(link);
      } else {
        this.$bridge({
          url: link
        });
      }
    },
    async handleGoToVip() {
      // TODO @风尘
      await this.createGroupBySub(
        ['2EIe7K95g8vMiuvNk_cbxsl8AgSm4J_Xe1m02b2MHnU'],
        []
      );
      this.$reportData({
        info: ' sy_chain_store_other_my:member_banner_click',
        ext: {}
      });
      this.$bridge({
        url: '/packageVip/index'
      });
    },
    handleClickToFulfillment(item, index) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_my:list_click',
        ext: {
          title: item.title,
          content: item.title,
          url: item.link,
          serial_num: index + 1
        }
      });
      this.handleJump(item.link);
    },
    async handleClickToOrder(item, index) {
      this.$reportData({
        info: 'sy_chain_store_my_order:tools_click',
        ext: {
          content: item.title,
          serial_num: index + 1
        }
      });
      if (item.key === 'all_order') {
        // 订阅消息

        await this.createGroupBySub(
          [
            'cWqU-ViIiMqghwa1oNYJbK6qrArgr0iEY5aCGAfIXlA',
            'xHGxtKz_okVCgkvY6CEV8HH9HtiW1NPoSAKeE-8VKnE',
            'VgIwA_nZy-Fh6VZ_X1YNg89VZr2IHNIhqeOGGayj1yM'
          ],
          []
        );
      }
      this.handleJump(item.link);
    },
    handleGoToQrCode() {
      this.$bridge({
        url: '/packageVip/qrCode'
      });
    },
    async handleClickToWallet(item) {
      // TODO @风尘
      await this.createGroupBySub(
        ['oOKsRs0YI3ciiC42YX2JxEEZvzAzJXmVBzE6JqR04zE'],
        []
      );
      this.$reportData({
        info: 'sy_chain_store_my_wallet:money_click',
        ext: {
          serial_num: 1,
          content: item.title
        }
      });
      this.handleJump(item.link);
    },
    handleClickToInstallment(item) {
      this.$reportData({
        info: 'sy_chain_store_my_wallet:money_click',
        ext: {
          serial_num: 2,
          content: item.title
        }
      });
      this.handleJump(item.link);
    },
    handleTest() {
      this.$toH5('http://**************:3000/chain/case/batchSign?from=my');
    },
    handleToolsJump(item) {
      switch (+item.type) {
        case 1:
          if (item.title === '关于我们' || item.link === '/packageMy/about') {
            this.$reportData({
              info: 'sy_chain_store_tuan_about:us_click',
              ext: {
                url: item.link
              }
            });
          }
          this.handleJump(item.link);
          break;
        case 2:
          console.log(item.service_tel);
          uni.makePhoneCall({
            phoneNumber: item.service_tel,
            fail: (err) => {
              if (err.errMsg !== 'makePhoneCall:fail cancel') {
                uni.showToast({
                  title: err.errMsg,
                  icon: 'none'
                });
              }
            }
          });
          break;
      }
    },
    handleGoSocket() {
      this.$bridge({
        url: '/packageSocket/index'
      });
    },
    // 登录
    async keepSession() {
      // 如果没有登录，先去登录，然后判断是否是 新用户
      if (!this.isLogin) {
        // 校验登录
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        if (!isAuth) return false;
      }
      return true;
    },
    handleGoToKefu: throttle(
      async function () {
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:contact_click',
          ext: {
            url: this.userSignInfo.jump_url
          }
        });
        if (!(await this.keepSession())) return;
        // this.$toKefuDialog({
        //   source: 'sy_yx_mini_program_private_msg_customer_service_my_contact',
        //   unJoinFn: () => {
        //     console.log('unJoinFn', this.userSignInfo.jump_url);
        //     // 未加C时执行
        //     this.$bridge({
        //       url: '/packageAccount/consult?qzychannel=3'
        //     });
        //   }
        // });
        this.$bridge({
          url: '/packageAccount/consult?qzychannel=3'
        });
      },
      1000,
      { trailing: false }
    ),
    handleLogout() {
      this.$signout().then(() => {
        // uni.setStorageSync('clicked_my_page_signout_btn', 1);
        this.initData();
        this.hideOrderTip = uni.getStorageSync('my_page_hide_order_tip');
      });
    },
    handleClick(data) {
      const curData = this.bannerList[data.index];
      if (curData.need_login === 1 && !this.isLogin) {
        this.handleLogin();
      } else {
        this.$reportData({
          info: 'sy_chain_store_tuan_my:old_brings_new_click',
          ext: {}
        });
        console.log(this);
        this.handleJump(curData.jump_url);
      }
    },
    handleExpose(data) {
      console.log(data);
    },
    handleMainButtonClick(item, index) {
      // 需要登录且未登录时，先触发登录
      if (item.need_login === 1 && !this.isLogin) {
        this.handleLogin();
        return;
      }
      this.$reportData({
        info: 'sy_chain_store_my_order:tools_click',
        ext: {
          content: item.title,
          serial_num: index + 1
        }
      });
      // 根据跳转类型进行不同处理
      if (item.jump_type === 1) {
        // H5跳转
        this.$toH5(item.link);
      } else if (item.jump_type === 2) {
        // 小程序内部跳转
        // 如果有mini_app_id，则根据mini_app_id跳转小程序，否则根据link跳转
        if (item.mini_app_id) {
          if (item.mini_app_id === 'wx4c984b5d0eb25e91') {
            const path = [
              '/pages/index',
              '/pages/item',
              '/pages/coupon-center',
              '/pages/my'
            ];
            if (path.includes(item.link)) {
              uni.switchTab({
                url: item.link
              });
            } else {
              uni.navigateTo({
                url: item.link
              });
            }
          } else {
            uni.navigateToMiniProgram({
              appId: item.mini_app_id,
              path: item.link,
              success(res) {
                console.log('跳转小程序成功', res);
              },
              fail(err) {
                console.log('跳转小程序失败', err);
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                });
              }
            });
          }
        } else {
          this.$bridge({
            url: item.link
          });
        }
      } else {
        // 默认按普通链接处理
        this.handleJump(item.link);
      }
    },
    handleInviteNowClick() {
      if (this.oldBringNew.banner_need_login === 1 && !this.isLogin) {
        this.handleLogin();
        return;
      }
      this.$reportData({
        info: 'sy_wxtuan_tuan_my:old_brings_new_click',
        ext: {}
      });

      this.$bridge({
        url: '/packageActivity/invitation/index'
      });
    }
  }
};
</script>
<style lang="less" scoped>
.my-page {
  padding-bottom: 40rpx;
  background-image: none;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
  .my-page-header-login-box {
    width: 100vw;
    background-size: cover;
    position: relative;
    padding: 30rpx;
    box-sizing: border-box;
    .my-page-login-bar {
      margin-top: 278rpx;
      .my-page-login-bar-title-line {
        display: flex;
        align-items: center;
        .my-page-login-bar-title {
          font-family: PingFangSC-Medium;
          font-size: 40rpx;
          color: #030303;
        }
        .my-page-login-bar-title-arrow {
          width: 20rpx;
          height: 30rpx;
          margin-left: 10rpx;
        }
      }
      .my-page-login-subtitle {
        margin-top: 36rpx;
        font-family: Outfit-Regular;
        font-size: 30rpx;
        color: #030303;
        letter-spacing: 3.34rpx;
      }
      .my-page-login-desc {
        margin-top: 14rpx;
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #030303;
        letter-spacing: 0;
        font-weight: 400;
        margin-bottom: 30rpx;
      }
    }
    .my-page-vip-wrapper {
      padding: 0 40rpx;
      box-sizing: border-box;
      height: 112rpx;
      .my-page-vip-info {
        height: 112rpx;
        padding: 64rpx 30rpx 0;
        box-sizing: border-box;
        .my-page-vip-main {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .my-page-vip-level {
            font-family: DfKing-Regular;
            font-size: 22rpx;
            line-height: 22rpx;
          }
          .my-page-vip-name {
            font-family: DfKing-Regular;
            font-size: 22rpx;
            line-height: 22rpx;
            font-weight: 500;
            margin-left: 4rpx;
          }
          .my-page-vip-exp {
            font-family: DfKing-Regular;
            font-size: 22rpx;
            line-height: 22rpx;
            font-weight: 500;
          }
          .my-page-vip-num {
            font-family: DfKing-Regular;
            font-size: 22rpx;
            line-height: 22rpx;
            font-weight: 500;
          }
          .my-page-vip-arrow {
            margin-left: 10rpx;
            display: flex;
          }
        }
      }
      .my-page-vip-progress-bar {
        height: 4rpx;
        background-color: #bababa;
        .active {
          height: 100%;
          background-color: #a9ea6a;
        }
      }
    }
    .my-page-info-bar {
      background-color: rgba(255, 255, 255, 0.5);
      padding: 30rpx;
      box-sizing: border-box;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      .my-page-info-item {
        text-align: center;
        .my-page-info-number {
          font-family: OutFit-Regular;
          line-height: 44rpx;
          font-size: 44rpx;
          color: #030303;
          font-weight: 500;
        }
        .my-page-info-text {
          margin-top: 6rpx;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #030303;
        }
      }
    }
    .my-page-account-bar {
      margin-top: 278rpx;
      background-color: #ffffff;
      position: relative;
      .my-page-account-main-message-button {
        background: rgba(255, 255, 255, 0.8);
        padding: 16rpx;
        position: absolute;
        top: -184rpx;
        left: 0;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        .my-page-account-main-message-button-img {
          width: 40rpx;
          height: 40rpx;
          margin-top: 6rpx;
        }
        div {
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #030303;
          font-weight: 500;
          margin-left: 12rpx;
        }
      }
      .my-page-account-main-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 40rpx;
        padding-inline: 40rpx;
        box-sizing: border-box;
        .my-page-account-main-title {
          flex: 1;
          font-family: PingFangSC-Medium;
          font-size: 44rpx;
          color: #030303;
          line-height: 60rpx;
          max-width: 372rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          word-break: break-all;
        }
        .my-page-qrcode-entry {
          display: flex;
          flex-direction: column;
          align-items: center;
          align-items: center;
          .my-page-qrcode-icon {
            width: 36rpx;
            height: 36rpx;
          }
          .my-page-qrcode-text {
            font-family: PingFangSC-Regular;
            font-size: 16rpx;
            color: #030303;
            text-align: center;
            font-weight: 400;
          }
        }
      }
      .my-page-account-main-desc {
        margin-top: 12rpx;
        padding-inline: 40rpx;
        padding-block: 0;
        font-family: OutFit-Regular;
        font-size: 24rpx;
        color: #030303;
        padding-bottom: 30rpx;
        font-weight: 500;
      }
    }
  }
  .my-page-order-box {
    height: 216rpx;
    padding-inline: 30rpx;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    align-items: center;
    .my-page-order-item {
      text-align: center;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .my-page-order-item-core {
        position: relative;
        .my-page-order-badge {
          position: absolute;
          width: 30rpx;
          height: 30rpx;
          border-radius: 100%;
          background-color: #fe6631;
          color: #ffffff;
          font-size: 18rpx;
          line-height: 30rpx;
          text-align: center;
          top: -15rpx;
          right: -15rpx;
        }
        .my-page-order-item-icon {
          width: 60rpx;
          height: 60rpx;
          margin-bottom: 20rpx;
        }
      }

      .my-page-order-item-text {
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #030303;
        letter-spacing: 0;
        text-align: center;
      }
    }
  }
  .my-page-banner {
    margin: 0 30rpx;
    margin-top: 30rpx;
  }
  .my-page-wallet-box {
    margin: 0 30rpx 10rpx;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 22rpx;
    .my-page-wallet-item {
      height: 156rpx;
      background-color: #f8f8f8;
      padding: 30rpx 40rpx;
      box-sizing: border-box;
      .my-page-wallet-login-box {
        .my-page-wallet-login-number {
          display: flex;
          align-items: baseline;
          .my-page-wallet-login-prefix {
            font-family: PingFangSC-Regular;
            font-size: 36rpx;
            color: #030303;
            letter-spacing: 0;
            font-weight: 400;
            line-height: 50rpx;
            margin-right: 10rpx;
          }
          .my-page-wallet-login-suffix {
            font-family: OutFit-Regular;
            font-size: 44rpx;
            line-height: 56rpx;
            color: #030303;
            letter-spacing: 0;
            font-weight: 500;
          }
        }
        .my-page-wallet-login-text {
          margin-top: 4rpx;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #030303;
          letter-spacing: 0;
          font-weight: 400;
        }
        .my-page-wallet-login-subtext {
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          line-height: 56rpx;
          color: #8c8c8c;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
      .my-page-wallet-no-login-box {
        .my-page-wallet-no-login-title {
          display: flex;
          align-items: center;
          margin-bottom: 4rpx;
          .my-page-wallet-no-login-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 12rpx;
          }
          .my-page-wallet-no-login-text {
            margin-top: 4rpx;
            font-family: PingFangSC-Medium;
            font-size: 30rpx;
            color: #030303;
            letter-spacing: 0;
            font-weight: 400;
            font-weight: 500;
          }
          .my-page-wallet-no-login-subtext {
            margin-top: 4rpx;
            font-family: PingFangSC-Regular;
            font-size: 26px;
            color: #8c8c8c;
            letter-spacing: 0;
            font-weight: 400;
          }
        }
        .my-page-wallet-no-login-desc {
          margin-top: 4rpx;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #333333;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
    }
  }
  .my-page-list-box {
    // margin: 50rpx 0;
    background: #ffffff;
    .my-page-grid-box {
      display: flex;
      flex-wrap: wrap;
      padding: 30rpx 0;
      width: 100%;

      .my-page-grid-item-wrapper {
        width: 25%;
        box-sizing: border-box;
        margin-bottom: 40rpx;
      }

      .my-page-grid-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 10rpx;
        width: 100%;
        .my-page-grid-item-icon-wrapper {
          position: relative;
          width: 46rpx;
          height: 46rpx;
          margin-bottom: 20rpx;
        }
        .my-page-grid-item-icon {
          width: 46rpx;
          height: 46rpx;
        }
        .my-page-grid-item-badge {
          position: absolute;
          top: -15rpx;
          right: -15rpx;
          background-color: #ff6600;
          border-radius: 16rpx;
          font-size: 18rpx;
          line-height: 30rpx;
          padding: 0 4rpx;
          text-align: center;
          color: #ffffff;
          font-weight: 500;
          min-width: 30rpx;
        }
        .my-page-grid-item-icon-red-dot {
          position: relative;
          &::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 10rpx;
            height: 10rpx;
            background-color: #ff6600;
            border-radius: 50%;
          }
        }

        .my-page-grid-item-text {
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #030303;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .my-page-logout-box {
    width: 176rpx;
    height: 74rpx;
    line-height: 74rpx;
    background: #ffffff;
    border: 1px solid #333333;
    box-sizing: border-box;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #030303;
    letter-spacing: 0;
    font-weight: 400;
    text-align: center;
    margin: auto;
  }

  // 主功能区样式
  .my-page-main-buttons {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #ffffff;
    padding: 30rpx;

    .main-button-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .main-button-icon-wrapper {
        position: relative;
        margin-bottom: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .main-button-icon {
          width: 60rpx;
          height: 60rpx;
          display: block;
        }
        .main-button-badge-small {
          position: absolute;
          top: -8rpx;
          right: 18rpx;
          background: #fe6631;
          height: 12rpx;
          width: 12rpx;
          border-radius: 100%;
        }
        .main-button-badge {
          position: absolute;
          top: -8rpx;
          right: 18rpx;
          background: #fe6631;
          width: 38rpx;
          height: 26rpx;
          border-radius: 100%;
          font-family: PingFangSC-Medium;
          font-size: 18rpx;
          color: #ffffff;
          text-align: center;
          font-weight: 500;
        }
        .main-button-badge-text {
          position: absolute;
          top: -8rpx;
          right: -40rpx;
          padding: 0 4rpx;
          background: #fff2e9;
          width: 90rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: center;
          font-family: PingFangSC-Medium;
          font-size: 20rpx;
          color: #fe6631;
          font-weight: 500;
        }
      }

      .main-button-title {
        width: 130rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 26rpx;
        color: #030303;
        text-align: center;
      }
    }
  }

  .line {
    width: 100%;
    height: 20rpx;
    background: #f2f2f2;
  }

  // 邀请有礼样式
  .my-page-invite {
    padding: 40rpx 30rpx;
    background: #ffffff;
    overflow: hidden;

    .my-page-invite-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .my-page-invite-title {
        font-family: PingFangSC-Medium;
        font-size: 30rpx;
        color: #030303;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
      }

      .my-page-invite-detail {
        display: flex;
        align-items: center;
        cursor: pointer;

        span {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #030303;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          margin-right: 10rpx;
        }

        img {
          width: 12rpx;
          height: 18rpx;
        }
      }
    }

    .my-page-invite-banner {
      width: 690rpx;
      height: 160rpx;
      margin: 40rpx 0;

      .my-page-invite-banner-img {
        width: 100%;
        height: 100%;
      }
    }

    .my-page-invite-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .my-page-invite-amounts {
        width: calc(100% - 136rpx);
        display: flex;

        .my-page-invite-item {
          width: 33%;
          text-align: center;
          margin-right: 40rpx;
          .my-page-invite-amount {
            font-family: Outfit-SemiBold;
            font-size: 34rpx;
            color: #030303;
            font-weight: 600;
          }

          .my-page-invite-text {
            font-family: PingFangSC-Regular;
            font-size: 26rpx;
            color: #333333;
            font-weight: 400;
          }
        }
      }

      .my-page-invite-now-btn {
        width: 136rpx;
        height: 54rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #333333;
        color: #ffffff;
        font-size: 24rpx;
        font-family: PingFangSC-Regular;
        font-weight: 400;
      }
    }
  }
}
.page-skeleton-diagram {
  background-image: url('https://static.soyoung.com/sy-pre/30k2j7muav1a6-1729667400683.png');
  background-size: 100%;
  background-position: top;
  background-repeat: no-repeat;
}
</style>
