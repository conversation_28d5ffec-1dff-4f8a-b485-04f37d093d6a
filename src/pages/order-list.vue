<template>
  <view class="container-out" :class="[noScroll ? 'no-scroll' : '']">
    <div :style="{ 'margin-top': marginTop + 'px' }" class="container">
      <!-- 订单内容tab -->
      <OrderTab
        :top="4"
        :scrollTop="scrollTop"
        :defaultValue="cur_tab"
        padding="20rpx 30rpx 0rpx 30rpx"
        :tabList="tabList"
        :ceiling="true"
        :noContCeil="true"
        :fontSize="'28rpx'"
        @tabChange="changeTab"
      ></OrderTab>
      <!-- 轮播图区域 -->
      <div
        v-if="
          carouselBannerList &&
          carouselBannerList.length > 0 &&
          list.length !== 0
        "
        class="carousel-banner-container"
      >
        <NewBanner
          :list="carouselBannerList"
          height="80px"
          width="360px"
          class="carousel-banner"
          @click="handleBannerClick"
          @expose="handleBannerExpose"
        ></NewBanner>
      </div>
      <RefreshScrollView
        ref="orderListScrollView"
        @onPullDown="onPullDown"
        @loadNextPage="loadNextPage"
        :height="-60"
        :refreshing="refreshing"
        :loading="loading"
        :hasMore="hasMore"
        :isInTop="isInTop"
        :isInBottom="isInBottom"
        :pageScrollTop="scrollTop"
        :isLogin="isLogin"
        :fixedTopHeight="0"
        :showHasMore="list.length > 0"
      >
        <div
          class="empty"
          v-if="!refreshing && !loading && list && list.length === 0"
        >
          <div class="empty-text" v-if="isLogin">
            <div class="cover">
              <img
                src="https://static.soyoung.com/sy-design/2hca7nc2ykif91729482715257.png"
                alt=""
              />
            </div>
            当前暂无订单记录
          </div>
        </div>
        <div class="order-list" v-if="list && list.length > 0">
          <LongOrderList
            :list="list"
            :loading="refreshing || loading"
            @noscroll="noscroll"
            @getData="getData"
            @setShareInfo="setShareInfo"
            @clickButton="handleClickButton"
            @toDetail="goToOrderDetail"
          ></LongOrderList>
        </div>
      </RefreshScrollView>
      <div class="logo" v-if="list.length"></div>
      <Poster ref="poster" title="好友邀你一起拼单，不同部位不同城市可混拼！" />
      <DefaultFullScreen pageKey="order" v-if="!isLogin"></DefaultFullScreen>
    </div>
    <GlobalCancelPopup />
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import RefreshScrollView from '@/components/refresh-scroll-view/index.vue'; // 带刷新样式的滚动容器
import OrderTab from '@/components/order/tab.vue';
import LongOrderList from '@/components/order/long-order-list.vue';
import SubscribeMixins from '@/mixins/subscribe';
import Poster from '@/components/pintuan/poster';
import DefaultFullScreen from '@/components/DefaultFullScreen.vue';
import GlobalCancelPopup from '@/components/appointNew/global-popup-cancel-appt.vue';
import { cloneDeep } from 'lodash-es';
import { getOrderListDataApi } from '@/api/order';
import NewBanner from '@/components/NewBanner.vue';

export default {
  mixins: [SubscribeMixins],
  pageTrackConfig: 'sy_wxtuan_tuan_order_list_page',
  components: {
    RefreshScrollView,
    OrderTab,
    // OrderCard,
    LongOrderList,
    Poster,
    DefaultFullScreen,
    GlobalCancelPopup,
    NewBanner
  },
  data() {
    return {
      marginTop: '0',
      testId: '2451488567957815297',
      defaultTab: 0,
      cur_tab: 0,
      cur_tab_index: 0,
      tabList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '待付款',
          value: 1
        },
        {
          label: '可使用',
          value: 2
        },
        {
          label: '已使用',
          value: 8
        },
        {
          label: '退款/售后',
          value: 4
        }
      ],
      /**
        订单状态
        0等待付款未付款，未过期
        1是过期未付款，已失效。
        2未使用已付款，未核销，
        3已使用，已付款，已核销。
        4已付款，过期未核销。
        5已退款
      */
      // 订单状态和tab对应表
      status_map: {
        0: 1, // 待付款
        2: 2, // 可使用
        5: 4, // 退款/售后
        '': 0 // 全部
      },
      list: null,
      hasMore: 1,
      page: 1,
      limit: 10,
      scrollTop: 0,
      refreshing: false, // 列表刷新，或请求第一页数据
      loading: false, // 加载下一页数据中
      noScroll: false,
      yuyueOrderInfo: {},
      scrollToTop: true,
      hasLogined: false,
      isInTop: true, // 是否在顶部
      isInBottom: false, // 是否在底部
      share_info: {},
      posterParams: {}, // 拼团邀请好友的分享信息
      posterPromise: null,
      cache_uid: 0, // 当前用户的uid，为了处理切换账号后的列表状态重置
      firstGetData: true, // 第一次拉取订单列表数据，用于页面显示优化，第一次拉取时，不显示未登录按钮
      userThreeDaysReserve: null, // 用户3天内需要到店的订单信息（到店提醒）
      showThreeDayReserveModal: false, // 显示到店提醒弹窗
      shareInfo: {},
      carouselBannerList: []
    };
  },
  async onLoad(options) {
    uni.updateShareMenu({
      withShareTicket: true
    });
    // 根据携带参数status映射对应的tab
    this.cur_tab = this.defaultTab = options.status;
    this.judgeLogin();
    this.refreshFromQuery = Number(options.refresh) === 1;
    /**
     * 从客服会话进来，到待使用tab时，触发的埋点
        1、1v1；
        2、群聊；
        3、企微介绍；
        4、到店前预约推送；
    */
    if (+options.status === 2 && options.from_kefu) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_wechat:order_exposure',
        ext: {
          serial_num: options.from_kefu
        }
      });
    }
    this.getData();
  },
  async onShow() {
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_order_list_page',
      ext: {}
    });
    this.$setTabBar({
      selected: 2
    });

    this.refreshing = false;
    this.loading = false;
    // 缓存的uid 和 当前登录的uid不同时，重置订单列表tab
    if (
      !this.cache_uid ||
      +this.cache_uid === +this.userInfo.uid ||
      this.refreshFromQuery
    ) {
      if (uni.getStorageSync('cur_order_id')) {
        this.updateSingleData();
      }
    } else {
      const index = 0;
      const item = this.tabList[index];
      this.changeTab({ item, index, stopGoTop: true });
    }
    this.cache_uid = this.userInfo.uid;

    this.reportTabClick();
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_order_list_page',
      ext: {}
    });
    // uni.setStorageSync('order_list_page_num', this.page);
    // this.list = null;
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_order_list_page',
      ext: {}
    });
    uni.removeStorageSync('order_list_page_num');
  },
  onPageScroll(res) {
    this.scrollTop = res.scrollTop;
    if (this.scrollTop < 50) {
      this.isInBottom = false;
      this.isInTop = true;
    }
  },
  watch: {},
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  methods: {
    reportTabClick() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_homepage:nov_order_click',
        ext: {}
      });
    },
    async judgeLogin() {
      if (!this.isLogin) {
        const isAuth = await this.$login()
          .then(() => {
            this.cache_uid = this.userInfo.uid;
            this.page = 1;
            this.getData();
          })
          .catch(() => null);
        if (!isAuth) return;
      }
    },
    onPullDown(callback) {
      console.log('orderList: onPullDown');
      this.list = null;
      this.page = 1;
      // uni.setStorageSync('order_list_page_num', 1);
      this.getData('', callback);
    },
    // 订单切换tab
    async changeTab({ item, index, stopGoTop }) {
      this.$reportData({
        info: 'sy_chain_store_tuan_order_list:tab_click',
        ext: {
          tab_content: item.label
        }
      });
      console.log(item);
      this.cur_tab = item.value;
      this.defaultTab = item.value;
      this.cur_tab_index = index;
      this.list = null;
      this.page = 1;
      this.scrollToTop = !stopGoTop;
      // uni.setStorageSync('order_list_page_num', 1);
      this.getData();
    },
    async getData(type, callback) {
      if (this.page === 1) {
        this.refreshing = true;
      } else {
        this.loading = true;
      }
      const that = this;
      const res = await getOrderListDataApi({
        page: this.page,
        limit: this.limit,
        type: this.cur_tab,
        catchFn: () => {
          this.list = this.list || [];
          that.refreshing = false;
          that.loading = false;
          if (this.page > 1) {
            this.page = this.page - 1;
          }
        }
      });
      const { errorCode, errorMsg, responseData } = res;
      if (+errorCode === 0 && responseData) {
        const { order_list, hasMore, other_info } = responseData;

        // 处理轮播图数据
        if (
          other_info &&
          other_info.carousel_banner_list &&
          other_info.carousel_banner_list.length
        ) {
          this.carouselBannerList = other_info.carousel_banner_list.map(
            (item, index) => ({
              ...item,
              img_url: item.img, // 转换为NewBanner组件需要的字段
              index // 添加索引用于埋点
            })
          );
        } else {
          this.carouselBannerList = [];
        }

        this.hasMore = +hasMore;
        if (type === 'append') {
          if (!this.list) {
            this.list = [];
          }
          this.list = this.list.concat(order_list);
        } else {
          this.list = [];
          this.list = order_list || [];
          if (callback) {
            callback();
          }
        }

        this.refreshing = false;
        this.loading = false;
      } else if (+errorCode === 77777 || +errorCode === 789) {
        this.list = [];
        if (!this.firstGetData) {
          this.refreshing = false;
          this.loading = false;
        }
      } else {
        this.list = [];
        this.refreshing = false;
        this.loading = false;
        if (errorMsg !== '用户未登录') {
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      }
      this.firstGetData = false;
      this.scrollToTop && this.$refs.orderListScrollView.goTop();
      setTimeout(() => {
        this.scrollToTop = false;
      }, 300);
      this.$forceUpdate();
    },
    changeButton(orderData1, orderData2) {
      const buttonList1 = orderData1.button_module.show_button_list;
      const buttonList2 = orderData2.button_module.show_button_list;
      if ((buttonList1 || []).length !== (buttonList2 || []).length) {
        return true;
      }
      for (let i = 0; i < buttonList1.length; i++) {
        if (buttonList1[i].type !== buttonList2[i].type) {
          return true;
        }
      }
      return false;
    },
    async updateSingleData() {
      const orderId = uni.getStorageSync('cur_order_id');
      const res = await getOrderListDataApi({
        order_id: orderId
      });

      const { errorCode, errorMsg, responseData } = res;
      if (+errorCode === 0 && responseData) {
        let index = this.list.findIndex(
          (item) => item.basic_order_module.order_id === orderId
        );

        const list = cloneDeep(this.list);
        if (
          list[index].status_module.orderStatus !==
            responseData.order_list[0].status_module.orderStatus ||
          this.changeButton(list[index], responseData.order_list[0])
        ) {
          this.list = [];
          list[index] = responseData.order_list[0];
          this.list = cloneDeep(list);
        }

        uni.removeStorageSync('cur_order_id');
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    async init() {
      this.page = 1;
      const isAuth = await this.$login().catch(() => null);
      if (!isAuth) return;
      this.getData();
    },
    noscroll(v) {
      this.noScroll = v;
      // fixed定位滚动穿透问题
      if (v) {
        this.marginTop = -this.scrollTop;
      } else {
        const numS = this.marginTop;
        this.marginTop = 0;
        this.$nextTick(() => {
          uni.pageScrollTo({
            scrollTop: -numS,
            duration: 0
          });
        });
      }
    },
    async onSuccessSubscribe() {
      if (this.yuyueOrderInfo.package_id) {
        // 套餐
        const subscribe = await this.createSubscribe({
          'SpF-TaBRBQkRFWWWDBn33mHlTZ3U2UmwpFVYkDU65_4': [4, 18],
          '6lbjS-X0Q6pdPafDas7sw7_rrnXgpdvacJnK0UZrYc4': [5],
          'S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q': [17]
        });
        subscribe?.({
          order_id: this.yuyueOrderInfo.button.reserve_btn.order_id
        });
      } else if (
        this.yuyueOrderInfo.order_list &&
        +this.yuyueOrderInfo.order_list[0].product_info.card_num > 1
      ) {
        // 次卡
        const subscribe = await this.createSubscribe({
          'SpF-TaBRBQkRFWWWDBn33mHlTZ3U2UmwpFVYkDU65_4': [4, 16],
          '6lbjS-X0Q6pdPafDas7sw7_rrnXgpdvacJnK0UZrYc4': [5],
          'S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q': [10]
        });
        subscribe?.({
          order_id: this.yuyueOrderInfo.button.reserve_btn.order_id
        });
      } else {
        // 普通品
        const subscribe = await this.createSubscribe({
          'SpF-TaBRBQkRFWWWDBn33mHlTZ3U2UmwpFVYkDU65_4': [4],
          '6lbjS-X0Q6pdPafDas7sw7_rrnXgpdvacJnK0UZrYc4': [5]
        });
        subscribe?.({
          order_id: this.yuyueOrderInfo.button.reserve_btn.order_id
        });
      }
    },
    loadNextPage() {
      if (this.loading || this.refreshing) {
        return;
      }
      this.loading = true;
      this.page++;
      // uni.setStorageSync('order_list_page_num', this.page);
      console.log(this.page);
      this.getData('append');
    },
    // 打开评价列表
    setShareInfo(share_info) {
      console.log(share_info);
      this.shareInfo = {
        sku_id: share_info.basic_order_module.product_id,
        group_id: share_info.basic_order_module.group_id
      };
    },
    afterPosterCreated(p) {
      this.posterPromise = p;
    },
    handleClickButton(data) {
      console.log('handleClickButton');
      uni.setStorageSync('cur_order_id', data);
      console.log(data);
    },
    goToOrderDetail(data) {
      console.log('handleClickButton', data);
      uni.setStorageSync('cur_order_id', data);
    },
    handleBannerClick(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_list:banner_click',
        ext: {
          // 位置序号:serial_num-数据在list或者feed中的位置序号；从1开始递增，与sever保持一致 链接:url-图片、视频或h5的链接
          serial_num: item.index + 1,
          url: item.jump_url
        }
      });
      // 根据jump_type处理不同的跳转方式
      if (+item.jump_type === 1) {
        // H5跳转
        uni.navigateTo({
          url: `/pages/h5?url=${encodeURIComponent(item.jump_url)}`
        });
      } else if (+item.jump_type === 2) {
        // 小程序跳转
        if (item.need_auth && !this.isLogin) {
          this.judgeLogin();
          return;
        }
        if (item.mini_app_id === 'wx4c984b5d0eb25e91') {
          const path = [
            '/pages/index',
            '/pages/item',
            '/pages/coupon-center',
            '/pages/my'
          ];
          if (path.includes(item.jump_url)) {
            uni.switchTab({
              url: item.jump_url
            });
          } else {
            uni.navigateTo({
              url: item.jump_url
            });
          }
        } else {
          uni.navigateToMiniProgram({
            appId: item.mini_app_id,
            path: item.jump_url,
            success(res) {
              console.log('跳转小程序成功', res);
            },
            fail(err) {
              console.log('跳转小程序失败', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        }
      }
    },
    // 轮播图曝光埋点
    handleBannerExpose(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_list:banner_exposure',
        ext: {
          // 位置序号:serial_num-数据在list或者feed中的位置序号；从1开始递增，与sever保持一致 链接:url-图片、视频或h5的链接
          serial_num: item.index + 1,
          url: item.jump_url
        }
      });
    }
  },
  onReachBottom() {
    console.log('onReachBottom');
    this.isInBottom = true;
    this.isInTop = false;
    if (this.hasMore === 1 && !this.loading) {
      this.loadNextPage();
    }
  },
  onShareAppMessage() {
    console.log('---->', this.shareInfo);
    return {
      promise: this.$refs.poster.shareInfo({
        sku_id: this.shareInfo.sku_id,
        group_id: this.shareInfo.group_id,
        // TODO 缺少 activity_id
        activity_id: ''
      })
    };
  }
};
</script>
<style lang="less">
page {
  background: #f8f8f8;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
</style>
<style scoped lang="less">
.container-out {
  height: 100vh;
  width: 100%;
  background: #f8f8f8;
}
.no-scroll {
  overflow: hidden;
}
.carousel-banner-container {
  margin-top: -8rpx;
  padding: 30rpx;
  background-color: #ffffff;
  .carousel-banner {
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    width: 100%;
    // height: auto;
    overflow: hidden;
    // border-radius: 8rpx;
  }
}
.page-tip {
  height: 70rpx;
  background: #e3f5f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: -3px 0 14rpx;
  padding: 0 30rpx;
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  top: 106rpx;
  z-index: 10;
  .page-tip-main {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .page-tip-icon {
    width: 24rpx;
    height: 26rpx;
    margin-right: 8rpx;
  }
  .page-tip-text {
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    color: #00ab84;
    letter-spacing: 0;
    font-weight: 500;
  }
  .page-tip-arrow {
    width: 12rpx;
    height: 20rpx;
  }
}
// 可到店提示
.can-to-store-tip {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 30rpx;
  height: 38 * 2rpx;
  background: #dfefeb;
  box-sizing: border-box;
  margin-top: -10rpx;
  z-index: 10;
  position: fixed;
  top: 108rpx;
  width: 100%;
  .left {
    margin-right: 12rpx;
    display: inline-block;
    width: 28rpx;
    height: 30rpx;
  }
  .center {
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: @text-color;
    letter-spacing: 0;
    line-height: 36rpx;
    font-weight: 400;
    flex: 1;
  }
  .right {
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: @text-color;
    letter-spacing: 0;
    font-weight: 400;
    width: 160rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    .icon {
      display: inline-block;
      width: 12rpx;
      height: 22rpx;
      margin-left: 8rpx;
    }
  }
}
.container {
  height: 100vh;
  position: relative;
  background: #f8f8f8;

  .empty {
    box-sizing: border-box;
    height: 700rpx;
    .empty-text {
      margin: 450rpx 0 30rpx;
      font-size: 28rpx;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      font-family: PingFangSC-Medium;
      color: #030303;
      font-weight: 500;
      .cover {
        display: block;
        height: 64rpx;
        width: 70rpx;
        margin: 0 auto 40rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .order-list {
    width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
    background: #f8f8f8;
    min-height: calc(70vh - 154rpx);
    margin-top: 12rpx;
  }
}
// 底部logo
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 108 * 2rpx;
  background: #f8f8f8;
  margin-bottom: 0;
  img {
    width: 117 * 2rpx;
    height: 66 * 2rpx;
  }
}
.hasOrderPush {
  .container {
    .empty,
    .order-list {
      margin-top: 70rpx;
    }
  }
}
</style>
