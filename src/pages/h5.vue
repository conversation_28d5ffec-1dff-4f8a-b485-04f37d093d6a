<template>
  <view>
    <web-view :src="url" @message="somethings"></web-view>
  </view>
</template>

<script>
import config from '@/config.js';
import { mapState, mapMutations, mapGetters } from 'vuex';
import { getParamsSign } from '@/utils/common';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_wxtuan_other_webview_page',
      ext: {
        url: this.originUrl,
        // 23年4月19日,from 张玉龙,添加一个拓展参数,七巧板的活动ID,非七巧板页面为空
        tmwap_id: this.tmwap_id
      },
      is_h5: 1
    };
  },
  data() {
    return {
      url: '',
      originUrl: '', // 原始地址，onload 传递进来，不带多余参数
      lastUid: 0,
      lastToken: 0,
      shareInfo: {},
      share_page: '', // 加载url上所携带的指向分享页面的标识
      tmwap_id: '',
      title: '',
      shareImg: ''
    };
  },
  onShow() {
    const currentUid = this.userInfo.uid || 0;
    const currentXYToken = this.userInfo.xyToken || 0;
    console.log(this.userInfo, 'userinfo--h5');
    // 如果UID发生改变，则替换URL中的uid和xy_token
    if (
      (currentUid !== this.lastUid || currentXYToken !== this.lastToken) &&
      currentUid > 0
    ) {
      var newURL = this.url
        .replace(/([?&])uid=\d+/, '$1uid=' + currentUid)
        .replace(/([?&])xy_token=\d+/, '$1xy_token=' + currentXYToken);
      console.log('新的 url 为：' + newURL);
      // 替换UID
      this.lastUid = currentUid;
      this.lastToken = currentXYToken;
      // 使用redirectTo而不是this.url赋值，是为了回退时调到上一个页面。
      console.log(newURL);
      uni.redirectTo({
        url: '/pages/h5?url=' + encodeURIComponent(newURL)
      });
    }
  },
  async onLoad(options) {
    // const userInfo = uni.getStorageSync('user_info')
    var prams = {
      lver: config.version,
      sys: config.sys,
      app_id: config.appId,
      uid: this.userInfo.uid || 0,
      soyoung_uid: this.userInfo.uid || 0,
      xy_token: this.userInfo.xyToken || 0,
      lat: this.userInfo.lat || 0,
      lng: this.userInfo.lng || 0,
      market_activity_id: this.userInfo.marketActivityId || 0,
      cityId: this.userInfo.cityId || 0,
      union_id: this.userInfo.unionId || 0,
      open_id: this.userInfo.openId || 0,
      selectCityId: this.userInfo.selectCityId || 0,
      _time: new Date().getTime()
    };

    this.lastUid = this.userInfo.uid || 0;
    this.lastToken = this.userInfo.xyToken || 0;
    if (options.hideShare) uni.hideShareMenu({});
    let url = decodeURIComponent(options.url || options.u);

    const matches = /\/tmwap(\d+)/g.exec(url);
    if (matches?.[1]) {
      this.tmwap_id = +matches[1];
    }
    this.originUrl = url;
    if (!/^http/.test(url.trim()) && !url.includes('soyoung.com')) {
      url = config.webviewHost + url;
    }
    if (
      url.indexOf('//test-finapi.sy.soyoung.com') > -1 ||
      url.indexOf('//f.soyoung.com') > -1
    ) {
      prams.platform = 1;
    }
    let urlHash;
    const hashPos = url.indexOf('#');
    if (hashPos > -1) {
      urlHash = url.substr(hashPos);
      url = url.substr(0, hashPos);
    }
    // 汇总URL上的所有参数，计算 _sign
    prams = Object.assign(prams, this.getUrlAllParams(url));
    prams._sign = getParamsSign(prams);
    for (const key in prams) {
      const value = prams[key];
      if (!url.includes(`?${key}=`) && !url.includes(`&${key}=`)) {
        const link = url.includes('?') ? '&' : '?';
        url += link + key + '=' + value;
      }
    }
    if (hashPos > -1) url += urlHash;
    this.title = options.title || '';
    this.shareImg = options.shareImg
      ? decodeURIComponent(options.shareImg)
      : '';
    console.log('H5 url');
    console.log(url);
    // 如果URL上携带 微信跳转客服会话页的参数，则校验是否登录，客服会话页需要登录下访问
    if (url.indexOf('source=') > -1) {
      if (!this.userInfo.uid || +this.userInfo.uid === 0) {
        const isAuth = await this.$login().catch(() => null);
        if (isAuth) {
          uni.redirectTo({
            url: '/pages/h5?url=' + url
          });
        } else {
          uni.switchTab({
            url: '/pages/index'
          });
        }
      }
      // 以下场景需进行埋点
      const sourceValue = this.getParams(url, 'source');
      if (sourceValue) {
        // 客服会话页访问埋点
        const kefuMDMap = {
          y_yx_WeChat_1v1_default: 1, // 客服1v1私聊
          y_yx_WeChat_group_default: 2, // 群聊
          y_yx_WeChat_intro: 3 // 企微-介绍入口
        };
        if (kefuMDMap[sourceValue]) {
          this.$reportData({
            info: 'sy_wxtuan_tuan_wechat:service_exposure',
            ext: {
              serial_num: kefuMDMap[sourceValue]
            }
          });
        }
      }
    }
    this.url = url;
    // 小程序路径携带分享定位参数（/pages/h5）
    this.share_page = options.share_page;
    // H5链接携带分享定位参数
    if (!options.share_page && url.indexOf('share_page=home') > -1) {
      this.share_page = 'home';
    }
    // 问诊页分享都到首页
    if (url.indexOf('/inquiry') > -1) {
      this.share_page = 'inquiry';
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  methods: {
    somethings(info) {
      try {
        const data = info.detail.data[0];
        if (data.type === 'shareInfo') {
          // 会员主页h5分享
          this.shareInfo = data;
        } else if (data.type === 'transferParams') {
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          if (prevPage.route === 'packageOrder/order-detail') {
            uni.$emit('transfer_params_from_other_page', data.params);
          }
        }
      } catch (e) {}
    },
    getParams(url, name) {
      const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
      const r = url.split('?')[1].match(reg);
      if (r != null) {
        return unescape(r[2]);
      } else {
        return null;
      }
    },
    getUrlAllParams(url) {
      if (url === '') return {};
      let options = {};
      let name;
      let value;
      let paramsStart = url.indexOf('?') + 1;
      let paramsEnd = url.indexOf('#') === -1 ? url.length : url.indexOf('#');
      let str = url.slice(paramsStart, paramsEnd);
      let arrtmp = str.split('&');
      for (let i = 0, len = arrtmp.length; i < len; i++) {
        let paramCount = arrtmp[i].indexOf('=');
        if (paramCount > 0) {
          name = arrtmp[i].substring(0, paramCount);
          value = arrtmp[i].substr(paramCount + 1);
          try {
            if (value.indexOf('+') > -1) value = value.replace(/\+/g, ' ');
            options[name] = decodeURIComponent(value);
          } catch (exp) {}
        }
      }
      return options;
    }
  },
  // 分享
  onShareAppMessage(res) {
    var title = this.shareInfo?.title || this.title || '新氧青春诊所';
    var path = '/pages/h5?url=' + encodeURIComponent(res.webViewUrl);
    var url = decodeURIComponent(res.webViewUrl);
    console.log(this.shareImg, 'shareimg');
    var imageUrl = this.shareInfo?.shareImg || this.shareImg || '';
    console.log(imageUrl, 'imageUrl');

    // 分享路径中去掉uid token 经纬度 cityid
    url = url
      .replace(/&uid=\d+/, '')
      .replace(/&xy_token=.*&/, '&')
      .replace(/&cityId=\d+/, '')
      .replace(/&lat=[\d\.]+/, '')
      .replace(/&lng=[\d\.]+/, '');

    // 分享路径中去掉title
    if (/.*#title=/g.test(url)) {
      title = url.replace(/.*#title=(.*)END/g, '$1');
      var newUrl = url.replace(/#title=.*/g, '');
      path = '/pages/h5?url=' + encodeURIComponent(newUrl);
    } else {
      path = '/pages/h5?url=' + encodeURIComponent(url);
    }
    path = path + `&market_activity_id=${this.userInfo.marketActivityId || ''}`;

    if (this.shareInfo.type === 'shareInfo') {
      path = this.shareInfo.path;
      title = this.shareInfo.title;
      imageUrl = this.shareInfo.shareImg;
    }

    console.log({
      title,
      path,
      imageUrl,
      success(res) {
        console.log(res);
      },
      fail(res) {
        console.log(res);
      }
    });

    return {
      title,
      path,
      imageUrl,
      success(res) {
        console.log(res);
      },
      fail(res) {
        console.log(res);
      }
    };
  }
};
</script>

<style></style>
