<template>
  <page-meta>
    <div class="container" :style="pageStyle">
      <!-- 联系我们 -->
      <Contact :inf="contact" @jump="handleJump" :skeleton="skeleton" />
      <!-- 轮播图 -->
      <Banner :list="bannerList" @jump="handleJump" :skeleton="skeleton" />
      <!-- 服务卡片 -->
      <Service
        :inf="serviceInf"
        :skuClassifyCount="skuClassifyCount.list"
        :cityStoreConfig="cityStoreConfig"
        :skuImg="skuImage"
        :changeDataObj="changeDataObj"
        @login="handleLogin"
        @jump="handleJump"
        @close="close"
        :skeleton="skeleton"
      />
      <!-- 预约卡片 -->
      <div class="appt-wrap" v-if="appointment">
        <Appt :cardData="appointment" />
      </div>
      <!-- 豆腐块 -->
      <Tofu
        v-if="hasTofu"
        :list="tofu"
        :skeleton="skeleton"
        @jump="handleJump"
      />
      <!-- 推荐机构 -->
      <Hospital
        :inf="hospital"
        :has-location="hasLocation"
        :skeleton="skeleton"
      />
      <!-- 品宣海报 -->
      <div class="poster" v-if="!skeleton">
        <img
          :src="brand.material_url"
          v-for="(brand, index) in brandList"
          :key="index"
          :data-index="index"
          class="poster-brand"
          mode="widthFix"
          @click="handleBrandClick"
        />
      </div>
      <!-- 悬浮球 -->
      <div
        class="ball"
        v-if="!skeleton && ball"
        :style="ballStyle"
        @click="handleBallClick"
      ></div>
    </div>
    <Popup :visible.sync="popupVisible" @jump="handleJump" />
    <PageLoading :visible="loading" />
    <!-- weak -->
    <WeakNetwork
      @refresh="refresh"
      :path="['/syGroupBuy/chain/index/home']"
      :visible.sync="weakNetwork"
    />
  </page-meta>
</template>
<script>
import { getConfig } from '@/api/home';
import { atob } from '@/common/base64';
import Appt from '@/components/homecard/appt.vue';
import Banner from '@/components/homecard/banner.vue';
import Contact from '@/components/homecard/contact.vue';
import Hospital from '@/components/homecard/hospital.vue';
import Popup from '@/components/homecard/popup.vue';
import Service from '@/components/homecard/service.vue';
import Tofu from '@/components/homecard/tofu.vue';
import PageLoading from '@/components/pageLoading.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
// import PublicWS from '@/utils/chat/index';
import store from '@/store';
import { isEmpty, throttle } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_wxtuan_tuan_home_new_page',
      ext: {}
    };
  },
  components: {
    PageLoading,
    WeakNetwork,
    Hospital,
    Service,
    Contact,
    Banner,
    Popup,
    Tofu,
    Appt
  },
  data() {
    return {
      skeleton: true,
      loading: false,
      hasLocation: true,
      bannerList: [],
      serviceInf: {},
      tofu: [],
      ball: null,
      hospital: null,
      contact: null,
      brandList: [],
      appointment: null,
      skuClassifyCount: {},
      cityStoreConfig: {},
      skuImage: {
        url: '',
        width: '',
        height: ''
      },
      changeDataObj: {
        classify_id: 0,
        count: 0
      },
      popupVisible: false,
      disableScroll: true,
      weakNetwork: false,
      newWs: null,
      ws: null
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin']),
    hasTofu() {
      return this.skeleton || !isEmpty(this.tofu);
    },
    ballStyle() {
      if (!this.ball) return '';
      return `background-image: url(${this.ball.icon})`;
    },
    pageStyle() {
      return `overflow-y: ${
        this.popupVisible || this.disableScroll || this.weakNetwork
          ? 'hidden'
          : 'scroll'
      }`;
    }
  },
  methods: {
    async fetchHomeConfig() {
      const res = await this.$request({
        url: '/syGroupBuy/chain/index/home'
      })
        .then((res) => res.data)
        .catch((error) => {
          return {
            errorCode: -100,
            errorMsg: error || '网络错误,稍后再试',
            responseData: {}
          };
        });
      const { errorCode, responseData } = res;
      if (+errorCode === 0 && responseData) {
        const {
          contact_us,
          banner_list,
          service_module,
          level_card,
          tofu,
          suspended_ball,
          hospital_card,
          brand_promotion,
          guide_card_list,
          sku_classify_count,
          city_store_config
        } = responseData;
        this.contact = contact_us;
        this.bannerList = banner_list || [];
        this.serviceInf = {
          ...service_module,
          ...(level_card || {})
        };
        this.tofu = tofu?.slice(0, 10) || []; // 只取10个
        this.ball = suspended_ball;
        this.hospital = hospital_card;
        this.brandList = brand_promotion || [];
        this.appointment = guide_card_list;
        this.skuClassifyCount = sku_classify_count;
        this.cityStoreConfig = city_store_config;
        this.skuImage = sku_classify_count.img;
        this.weakNetwork = false;
      }
      // 第一次进来
      this.skeleton = false;
      this.disableScroll = false;
      if (this.ball) {
        this.$reportData({
          info: 'sy_chain_store_tuan_home:old_brings_new_exposure',
          ext: {
            url: this.ball.jump_url
          }
        });
      }
      setTimeout(() => this.createReportObserver(), 100);
    },
    async handleLogin() {
      this.loading = true;
      if (!(await this.keepSession())) {
        this.loading = false;
        this.onCreateSocket();
        return;
      }
      this.refresh();
    },
    async handleJump({ type, url, appId, auth }) {
      if (!url) return;
      /**
        img_url: ""
        jump_type: 1，  //跳转类型：1 app ；2 小程序 ； 3 h5
        jump_url: "app://"
        material_height: 700 图片高
        material_type: 媒体类型 1 图片 2 视频
        material_url: "https://img2.soyoung.com/origin/20240910/2/2a0ecbabf1327794e469c563d2b8f490.png" 图片地址
        material_width: 700 图片宽
        sub_title: "测试标题"
        title: "测试"
        title_color: "#000000"
        program_id 小程序的原始ID
      */
      if (auth && !(await this.keepSession())) return;
      const path = url.startsWith('/') ? url : `/${url}`;
      console.log('jump params', type, url, appId);
      switch (type) {
        case 2: // 1 小程序
          if (appId) {
            uni.navigateToMiniProgram({
              appId,
              path,
              extraData: {},
              fail(err) {
                if (err.errMsg === 'navigateToMiniProgram:fail cancel') return;
                uni.showToast({
                  title: err.errMsg,
                  icon: 'none'
                });
              }
            });
          } else {
            this.$bridge({
              url: path,
              callbackFail(error) {
                if (error.errMsg === 'navigateToMiniProgram:fail cancel')
                  return;
                uni.showToast({
                  title: error.errMsg,
                  icon: 'none'
                });
              }
            });
          }
          break;
        case 3: // 3 h5
          this.$toH5(url);
          break;
        default:
          uni.showToast({
            title: `小程序暂不支持此类跳转`,
            icon: 'none'
          });
          break;
      }
    },
    handleBallClick: throttle(
      async function () {
        if (this.skeleton) return;
        const { jump_url, jump_type, need_login, program_id } = this.ball;
        this.handleJump({
          type: jump_type,
          url: jump_url,
          appId: program_id,
          auth: need_login
        });
        this.$reportData({
          info: 'sy_chain_store_tuan_home:old_brings_new_click',
          ext: {
            url: jump_url
          }
        });
      },
      1000,
      { trailing: false }
    ),
    handleBrandClick: throttle(
      async function (evt) {
        if (this.skeleton) return;
        const { index } = evt.currentTarget.dataset;
        const { jump_url, jump_type, program_id } = this.brandList[index];
        this.handleJump({
          type: jump_type,
          url: jump_url,
          appId: program_id
        });
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:img_click',
          ext: {
            serial_num: index + 1,
            url: jump_url
          }
        });
      },
      1000,
      { trailing: false }
    ),
    // 获取地理位置定位
    async getLocation() {
      const res = await this.$getCityId('gcj02').catch(() => {
        // 解决下不然会抛出错误
        return {};
      });
      this.hasLocation = Boolean(res?.lat && res?.lng);
    },
    // 登录
    async keepSession() {
      // 如果没有登录，先去登录，然后判断是否是 新用户
      if (!this.isLogin) {
        this.loading = true;
        // 校验登录
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        this.loading = false;
        if (!isAuth) return false;
      }
      return true;
    },
    async refresh() {
      this.loading = true;
      await this.getLocation();
      await this.fetchHomeConfig();
      await this.onCreateSocket();
      this.loading = false;
    },
    createReportObserver() {
      if (this.ob) {
        this.ob.disconnect();
      }
      this.ob = this.createIntersectionObserver({
        thresholds: [0, 0.2, 0.4, 0.6, 0.8, 1],
        observeAll: true
      });
      this.ob.relativeToViewport({
        left: 0,
        top: 0,
        bottom: 0,
        right: 0
      });
      this.ob.observe('.poster-brand', (res) => {
        if (res.intersectionRatio > 0) {
          const index = res.dataset.index;
          const b = this.brandList[index];
          if (this.posterRp.get(b.jump_url)) return;
          this.posterRp.set(b.jump_url, 1);
          this.$reportData({
            info: 'sy_wxtuan_tuan_home_new:img_exposure',
            ext: {
              serial_num: index + 1,
              url: b.jump_url
            }
          });
        }
      });
      this.$once('hook:beforeDestroy', () => {
        this.ob.disconnect();
      });
    },
    async onCreateSocket() {
      if (
        (this.ws &&
          this.ws?.socketTask &&
          this.ws?.socketTask.readyState === 1) ||
        (this.ws?.socketTask && this.ws?.socketTask.readyState === 1)
      ) {
        return false;
      }
      try {
        const res = await getConfig();
        const conf = res.responseData;
        if (conf && conf.newWs) {
          this.newWs = 1;
          if (this.ws) {
            return false;
          }
          let _uid = store.state.global.userInfo.uid; //获取公共登录uid
          // 动态加载 PublicWS
          const { default: PublicWS } = await import(
            '@/packageHospital/components/chat/index'
          );
          this.ws = new PublicWS({
            config: {
              roomID: `soyoungLive://${conf.sku_count_room_id}`, // 'SY_F0506512D5535254://4245884627', // soyoungLive://1315
              senderUid: _uid,
              receiverUid: _uid,
              token: conf.chat_oauth_token,
              accepts: [86245575],
              debug: 1,
              currentPages: getCurrentPages()
            },
            socketTask: {
              onClose: (res) => {
                console.log('ws:关闭', res);
              },
              onError: (res) => {
                console.log('ws:error', res);
              },
              onMessage: (res) => {
                console.log(res, '接受消息啦');
                this.sendMessage(res);
              },
              onOpen: (res) => {
                console.log('ws:打开', res);
              }
            }
          });
        }
      } catch (error) {
        console.log('error', error);
      }
    },
    close() {
      this.changeDataObj = {
        classify_id: 0,
        count: 1
      };
    },
    sendMessage(res) {
      console.log(res, '*** send socket message ***');
      const body = res.body;
      try {
        const bd = JSON.parse(body || '{}');
        if (!bd) {
          return;
        }
        if (bd.msg_type === 'classify_stat') {
          console.log('匹配成功', bd.data.count);
          this.changeDataObj = bd.data;
        }
      } catch (error) {
        console.log(error, '*** socket error ***');
      }
    }
  },
  onLoad(options) {
    // 闪跳逻辑
    if (options.shining_link) {
      setTimeout(() => {
        let base64_shining_link = options.shining_link.replace(/_/g, '/');
        const shining_link = atob(base64_shining_link);
        const prevFix = shining_link.startsWith('/');
        uni.navigateTo({
          url: (prevFix ? '' : '/') + shining_link
        });
      }, 500);
    }
  },
  async onShow() {
    await this.getLocation();
    this.fetchHomeConfig();
    if (this.newWs) {
      this.ws.socketTask.readyState && this.ws.socketTask.readyState === 3
        ? this.ws.socketEvent.reConnect()
        : void 0;
    }
    if (this.ws?.socketTask) {
      console.log('*** socketTask已建立链接 ***');
    }
    this.onCreateSocket();
  },
  onHide() {
    console.log('*** onHide ***');
    if (this.newWs) {
      this.ws.socketEvent.close();
    }
    if (this.ws?.socketTask) {
      this.ws.socketTask.close();
    }
  },
  onUnload() {
    console.log('*** onUnload ***');
    if (this.newWs) {
      this.ws.socketEvent.close();
    }
    if (this.ws?.socketTask) {
      this.ws.socketTask.close();
    }
  },
  async onPullDownRefresh() {
    if (this.weakNetwork) return;
    await this.refresh();
    wx.stopPullDownRefresh();
  },
  created() {
    this.posterRp = new Map();
    // 每间隔5s更新一次changeDataObj TODO: 测试使用
    // setInterval(() => {
    //   this.changeDataObj = {
    //     classify_id: 65,
    //     classify_name: '我是测试的类目',
    //     uint: '次',
    //     count: 20
    //   };
    // }, 5000);
  },
  onShareAppMessage() {
    return {
      title: '新氧青春—专业轻医美连锁',
      path: '/pages/index',
      imageUrl:
        'https://static.soyoung.com/sy-pre/20240223-185625-1708683000724.jpeg'
    };
  }
};
</script>
<style lang="less" scoped>
.container {
  background-color: #f8f8f8;
  height: 100vh;
  overflow-y: auto;
  ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none !important;
    background: transparent !important;
    color: transparent !important;
  }
  .appt-wrap {
    margin-bottom: 30rpx;
    box-sizing: border-box;
    padding: 0 30rpx;
  }
}
.ball {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  height: 112rpx;
  width: 112rpx;
  z-index: 2;
  transform: translateY(2px);
  background-origin: 0 0;
  background-size: contain;
  background-repeat: no-repeat;
}
.poster {
  vertical-align: top;
  img {
    width: 100vw;
  }
}
</style>
