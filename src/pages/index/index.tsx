import React, { useState } from 'react'
import { View, Text } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import LoadingButton from '../../components/LoadingButton'
import { showToast } from '../../utils/common'
import './index.scss'

export default function Index() {
  const [count, setCount] = useState(0)

  useLoad(() => {
    console.log('Page loaded.')
  })

  const handleIncrement = async () => {
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    setCount(prev => prev + 1)
    showToast('计数增加成功！', 'success')
  }

  const handleReset = () => {
    setCount(0)
    showToast('计数已重置', 'success')
  }

  return (
    <View className="index">
      <View className="header">
        <Text className="title">Second Life</Text>
        <Text className="subtitle">基于 Taro + React + TypeScript</Text>
      </View>

      <View className="content">
        <View className="counter-section">
          <Text className="counter-label">当前计数：</Text>
          <Text className="counter-value">{count}</Text>
        </View>

        <View className="button-group">
          <LoadingButton type="primary" onClick={handleIncrement} className="action-button">
            增加计数
          </LoadingButton>

          <LoadingButton type="default" onClick={handleReset} className="action-button">
            重置计数
          </LoadingButton>
        </View>
      </View>

      <View className="footer">
        <Text className="footer-text">项目架构包含：API层、组件库、工具函数、样式系统</Text>
      </View>
    </View>
  )
}
