@import '../../styles/variables.scss';

.index {
  min-height: 100vh;
  padding: $spacing-base;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .header {
    text-align: center;
    margin-bottom: $spacing-xl;

    .title {
      display: block;
      font-size: $font-size-xl;
      font-weight: bold;
      color: $background-color-white;
      margin-bottom: $spacing-sm;
    }

    .subtitle {
      display: block;
      font-size: $font-size-sm;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .content {
    background: $background-color-white;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    box-shadow: $box-shadow-base;
    margin-bottom: $spacing-xl;

    .counter-section {
      text-align: center;
      margin-bottom: $spacing-lg;

      .counter-label {
        display: block;
        font-size: $font-size-base;
        color: $text-color-secondary;
        margin-bottom: $spacing-sm;
      }

      .counter-value {
        display: block;
        font-size: 72px;
        font-weight: bold;
        color: $primary-color;
      }
    }

    .button-group {
      display: flex;
      gap: $spacing-base;
      justify-content: center;

      .action-button {
        flex: 1;
        max-width: 200px;
      }
    }
  }

  .footer {
    text-align: center;

    .footer-text {
      font-size: $font-size-sm;
      color: rgba(255, 255, 255, 0.7);
      line-height: 1.6;
    }
  }
}
