<template>
  <div class="item-page-wrap">
    <template v-if="hasLocation === true">
      <div class="top">
        <div class="hospital-base-info-line">
          <!--<div class="hospital-name" @click="onHospitalNameClick">-->
          <!--  <span>{{ hospitalInfo.hospital_name }}</span>-->
          <!--  <img-->
          <!--    src="https://static.soyoung.com/sy-design/9idhpmii49m41716895628332.png"-->
          <!--    alt=""-->
          <!--    class="switch-hospital"-->
          <!--  />-->
          <!--</div>-->
          <location-for-item
            v-if="hospitalInfo"
            @select-click="selectClick"
            :hospital-name="hospitalInfo.name"
            :tip-text="hospitalInfo.hospital_addr"
          />
        </div>
        <div
          class="pos-line"
          @click="onHospitalDetailClick"
          v-if="hospitalInfo && hospitalInfo.dis"
        >
          <img
            class="pos-icon"
            src="https://static.soyoung.com/sy-design/atbnjwk6sovd1716895628372.png"
            alt=""
          />
          <div class="text">{{ hospitalInfo.dis }}</div>
        </div>
        <div class="notice-bar" v-if="notice">
          <div class="notice-bar-text">{{ notice }}</div>
          <img
            class="notice-bar-close"
            src="https://static.soyoung.com/sy-design/bzsokyai5osd1725518950255.png"
            alt="关闭"
            @click="onNoticeBarCloseClick"
          />
        </div>

        <div
          v-if="!rightLoading"
          class="jump-search-btn"
          @click="onJumpSearchClick"
        >
          <img
            class="jump-search-btn-icon"
            src="https://static.soyoung.com/sy-design/1zuveb3ngazob1725867174330.png"
            alt=""
          />
          <div class="jump-search-btn-text">搜项目</div>
        </div>
      </div>
      <view class="out-wrap" scroll-y>
        <scroll-view
          :scroll-x="true"
          class="banner"
          v-if="banner && banner.length"
          :style="{
            height: `${bannerHeight}`,
            paddingBottom: bannerHeight == '0rpx' ? 0 : undefined
          }"
        >
          <div class="banner-wrap">
            <img
              v-for="(item, index) in banner"
              :key="index"
              @click="topBannerClick(item)"
              class="banner-image"
              mode="heightFix"
              :src="item.img.img_u"
              alt=""
            />
          </div>
        </scroll-view>
        <div class="main">
          <scroll-view
            id="left-scroll-view"
            class="left"
            :scroll-y="true"
            :scroll-with-animation="true"
            :scroll-top="leftScrollTop"
            @scroll="onRightScroll"
          >
            <div v-if="!leftLoading" class="left-wrap">
              <div
                class="level-1-item"
                :id="`level-1-item-${level1Index}`"
                v-for="(item, level1Index) in class1List"
                :key="item.id"
                :data-id="item.id"
                :data-index="level1Index"
                :data-title="item.title"
                :data-style="
                  item.op_label_info && item.op_label_info.img ? 1 : 0
                "
                :class="[currentLevel1Index === level1Index ? 'active' : '']"
                @click="onLeftLevel1Click(level1Index)"
              >
                <div class="title-text">{{ item.title }}</div>
                <img
                  mode="heightFix"
                  v-if="item.op_label_info && item.op_label_info.img"
                  :src="item.op_label_info.img.img_url"
                  alt=""
                  class="op-label"
                />
              </div>
              <div class="left-scroll-place-holder"></div>
            </div>
            <img
              class="left-empty"
              mode="widthFix"
              v-else
              src="https://static.soyoung.com/sy-pre/20240605-192741-1717585800623.png"
            />
            <!--<scroll-view class="left" :scroll-y="true"  :scroll-with-animation="true">-->
            <!--<div>{{currentLevel1Index}}</div>-->
          </scroll-view>
          <div class="right">
            <template v-if="!rightLoading">
              <scroll-view
                class="right-scroll-view"
                id="right-scroll-view"
                :scroll-y="true"
                :scroll-top="rightScrollTop"
                @scroll="onRightScroll"
              >
                <div
                  class="right-scroll-view-inner"
                  style="padding-bottom: 300rpx"
                >
                  <div
                    class="level-2-box-wrap"
                    :id="`level-2-box-wrap-${class2Index}`"
                    :key="class2Item.id"
                    v-for="(class2Item, class2Index) in class2List"
                  >
                    <!--banner 图片-->
                    <img
                      class="level-banner"
                      v-if="
                        class1List[class2Index].banner &&
                        class1List[class2Index].banner.img_url
                      "
                      :class="{
                        'has-margin-top': class2Index !== 0
                      }"
                      :src="class1List[class2Index].banner.img_url"
                      alt=""
                      :key="class2Item.id"
                      :data-index="class2Index"
                      :data-title="class1List[class2Index].title"
                      :data-url="class1List[class2Index].banner.jump_url"
                      @click="
                        bannerClick(
                          class1List[class2Index].banner,
                          class2Index + 1,
                          class1List[class2Index].title
                        )
                      "
                    />
                    <!--吸顶头部-->
                    <div
                      class="level-2-header"
                      :key="class2Item.id"
                      :class="[
                        class2Item.list && class2Item.list.length
                          ? 'high'
                          : 'low'
                      ]"
                    >
                      <div
                        class="level-2-header-body"
                        :class="[
                          `level-2-header-body-${class2Index}`,
                          isLevel2HeaderFixed(class2Index) ? 'fixed' : '',
                          class2Item.list && class2Item.list.length
                            ? 'high'
                            : 'low'
                        ]"
                      >
                        <!--标题-->
                        <div
                          class="level-2-list-title"
                          :class="{
                            'has-op-label': hasOpLabel(class2Item)
                          }"
                        >
                          {{ class1List[class2Index].title }}
                        </div>
                        <!-- 有子分类 有一个二级横划菜单锚定 -->
                        <scroll-view
                          :scroll-x="true"
                          :scroll-y="false"
                          class="level-2-menu"
                          v-if="class2Item.list && class2Item.list.length"
                          :scroll-into-view="
                            currentLevel1Index === class2Index
                              ? `level-2-menu-item-${currentLevel2Index}`
                              : ``
                          "
                          :scroll-with-animation="true"
                        >
                          <div
                            class="level-2-menu-item"
                            v-for="(
                              class2MenuItem, level3Index
                            ) in class2Item.list"
                            :key="class2MenuItem.id"
                            :id="`level-2-menu-item-${level3Index}`"
                            :data-level-1-id="class1List[class2Index].id"
                            :data-active-index="
                              class1SelectMap[class1List[class2Index].id]
                            "
                            :data-index="level3Index"
                            :data-parent_index="class2Index"
                            :data-title="class2MenuItem.title"
                            :data-parent_title="class1List[class2Index].title"
                            :class="[
                              currentLevel1Index === class2Index &&
                              currentLevel2Index === level3Index
                                ? 'active'
                                : '',
                              hasOpLabel(class2Item) ? 'has-op-label' : ''
                            ]"
                            @click="
                              onLevel2MenuItemClick(class2Index, level3Index)
                            "
                          >
                            {{ class2MenuItem.title }}
                            <template v-if="class2MenuItem.products">
                              {{ ' ' }}({{ class2MenuItem.products.length }})
                            </template>
                            <img
                              class="op-label"
                              mode="heightFix"
                              v-if="
                                class2MenuItem.op_label_info &&
                                class2MenuItem.op_label_info.img
                              "
                              :src="class2MenuItem.op_label_info.img.img_url"
                            />
                          </div>
                          <div
                            style="
                              width: 40rpx;
                              height: 40rpx;
                              display: inline-flex;
                            "
                          ></div>
                        </scroll-view>
                      </div>
                    </div>
                    <!--多个列表拼接而成-->
                    <div
                      class="level-2-products"
                      v-if="class2Item.list && class2Item.list.length"
                      :key="class2Item.id"
                    >
                      <div
                        class="level-2-product-wrap"
                        :id="`level-2-product-wrap-${class2Index}-${class2ListIndex}`"
                        v-for="(
                          class2ListItem, class2ListIndex
                        ) in class2Item.list"
                        :key="class2ListItem.id"
                      >
                        <div
                          v-if="class2ListIndex !== 0"
                          class="level-2-small-title-text"
                          :class="[
                            `level-2-small-title-text-${class2Index}-${class2ListIndex}`
                          ]"
                        >
                          {{ class2ListItem.title }}
                          {{
                            class2ListItem.products
                              ? `(${class2ListItem.products.length})`
                              : ''
                          }}
                        </div>
                        <div
                          v-else
                          class="level-2-small-title-text empty"
                          :class="[
                            `level-2-small-title-text-${class2Index}-${class2ListIndex}`
                          ]"
                        ></div>
                        <div
                          v-for="(
                            product, productIndex
                          ) in class2ListItem.products"
                          class="product-card-outer"
                          :key="product.id"
                          :data-id="product.id"
                          :data-product_id="product.pid"
                          :data-serial_num="productIndex + 1"
                          :data-first_tab_num="class2Index + 1"
                          :data-first_tab_content="
                            class1List[class2Index] &&
                            class1List[class2Index].title
                          "
                          :data-second_tab_num="class2ListIndex + 1"
                          :data-second_tab_content="class2ListItem.title"
                          :data-item_content="
                            (product.pain_tag_info &&
                              product.pain_tag_info.name) ||
                            ''
                          "
                        >
                          <ItemCard
                            :data="product"
                            :report-ext="{
                              serial_num: productIndex + 1,
                              first_tab_num: class2Index + 1,
                              first_tab_content:
                                class1List[class2Index] &&
                                class1List[class2Index].title,
                              second_tab_num: class2ListIndex + 1,
                              second_tab_content: class2ListItem.title,
                              item_content:
                                (product.pain_tag_info &&
                                  product.pain_tag_info.name) ||
                                ''
                            }"
                          />
                        </div>
                      </div>
                    </div>
                    <!-- 没有子分类 直接是商品列表 -->
                    <div
                      class="level-2-product-wrap level-2-product-wrap-single"
                      :id="`level-2-product-wrap-${class2Index}-0`"
                      v-else-if="
                        class2Item.product_list &&
                        class2Item.product_list.length
                      "
                      :key="class2Item.id"
                    >
                      <div
                        class="product-card-outer"
                        v-for="(
                          product, productIndex
                        ) in class2Item.product_list"
                        :key="product.id"
                        :data-id="product.id"
                        :data-product_id="product.pid"
                        :data-serial_num="productIndex + 1"
                        :data-first_tab_num="class2Index + 1"
                        :data-first_tab_content="
                          class1List[class2Index] &&
                          class1List[class2Index].title
                        "
                        :data-second_tab_num="1"
                        :data-second_tab_content="1"
                        :data-item_content="
                          (product.pain_tag_info &&
                            product.pain_tag_info.name) ||
                          ''
                        "
                      >
                        <ItemCard
                          :data="product"
                          :report-ext="{
                            serial_num: productIndex + 1,
                            first_tab_num: class2Index + 1,
                            first_tab_content:
                              class1List[class2Index] &&
                              class1List[class2Index].title,
                            second_tab_num: 1,
                            second_tab_content: 1,
                            item_content:
                              (product.pain_tag_info &&
                                product.pain_tag_info.name) ||
                              ''
                          }"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </scroll-view>
              <!-- <img
                src="https://static.soyoung.com/sy-pre/1x0pnowgejxa0-1717492200643.png"
                alt=""
                class="left-corner"
              />
              <img
                src="https://static.soyoung.com/sy-pre/2qjd1ggngp58z-1717492200643.png"
                alt=""
                class="right-corner"
              /> -->
            </template>
            <img
              v-else
              class="right-empty"
              mode="widthFix"
              src="https://static.soyoung.com/sy-pre/20240605-192753-1717585800623.png"
            />
          </div>
        </div>
      </view>
      <!--<div class="top">-->
      <!--  <div>-->
      <!--    -->
      <!--  </div>-->
      <!--</div>-->
      <!-- <img
        src="https://static.soyoung.com/sy-pre/20240604-165507-1717488600621.png"
        alt=""
        class="float-mask"
      /> -->
      <PageLoading :visible="initLoading && !rightLoading" />
    </template>
    <div v-else-if="hasLocation === false" class="location-mask">
      <img
        class="location-mask-img"
        src="https://static.soyoung.com/sy-design/3km85ltqdn1fx1725359158396.png"
        alt=""
      />
      <div class="location-mask-text">暂未开启定位，无法获取您的位置</div>
      <div
        class="location-mask-btn recommend"
        @click="onLocationMaskBtnClickOpen"
      >
        开启定位
      </div>
      <div class="location-mask-btn" @click="onLocationMaskBtnClickManual">
        手动选择
      </div>
    </div>
    <WeakNetwork
      @refresh="init"
      :path="['/syGroupBuy/chainProduct/getGroupBuyChainProjectData']"
      :visible.sync="weakNetwork"
    />
  </div>
  <!--<div class="test-refresh" @click="init()">测试刷新用</div>-->
</template>
<script>
import ItemCard from '@/components/items/item-card.vue';
// import Location from '@/components/home/<USER>';
import LocationForItem from '@/components/items/location-for-item.vue';
import PageLoading from '@/components/pageLoading.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import { mapState } from 'vuex';
// import { getDistrictInfoByIp } from '@/api/home.js';
import { debounce } from 'lodash';
let exp1, exp2, exp3, exp4, exp5;
export default {
  components: { LocationForItem, ItemCard, PageLoading, WeakNetwork },
  data() {
    return {
      leftScrollDirection: 'down',
      currentScrollingDomId: null,
      debounceUnlockScroll: debounce(() => {
        this.currentScrollingDomId = null;
        this.leftScrollDirection = null;
      }, 500),
      debounceUpdateRightScrollTop: debounce((v) => {
        this.rightScrollTop = v;
      }, 500),
      debounceUpdateLeftScrollTop: debounce((v) => {
        this.leftScrollTop = v;
      }, 500),
      hasLocation: null, // null 未初始化，true 有权限，false 无权限
      pageInfo: {
        banner: {}
      },
      banner: [],
      leftLoading: true,
      rightLoading: true,
      initLoading: false,
      observerLock: false,
      class1List: [],
      class2List: [],
      currentLevel1Index: 0, // 左侧当前菜单
      currentLevel2Index: 0, // 右侧二级菜单
      fixedHeaderIndex: -1,
      hospitalInfo: null,
      rightScrollIntoView: '',
      rightScrollTop: 0,
      leftScrollTop: 0,
      class1SelectMap: {},
      notice: '',
      bannerHeight: '400rpx',
      weakNetwork: false,
      initPageStatus: false
    };
  },
  async mounted() {
    this.initExposure();
  },
  async onShow() {
    this.$setTabBar({
      selected: 1
    });
    this.$reportPageShow({
      info: 'sy_chain_store_tuan_product_list_page',
      ext: {}
    });
    this.$reportData({
      info: 'sy_chain_store_tuan_change:hospital_exposure',
      ext: {}
    });
    // dom结构已经存在，在切换前后台时，需要重新初始化
    if (this.initPageStatus) {
      this.updateData();
    }
    // 如果其他页面锚定到当前页面，则处理锚定
    if (this.class1List.length === 0) {
      return;
    }
    const anchoredItemId = uni.getStorageSync('anchoredItemId');
    const index = this.class1List.findIndex((i) => +i.id === +anchoredItemId);
    if (anchoredItemId && index !== -1 && this.class1List.length > 0) {
      this.onLeftLevel1Click(index);
      uni.setStorageSync('anchoredItemId', null);
    }
  },
  watch: {
    userInfo: {
      async handler({ hospital, cityId }, old) {
        const checkScopeOfUserLocationRes =
          await this.checkScopeOfUserLocation();
        if (
          !checkScopeOfUserLocationRes &&
          !this.userInfo.hospital &&
          !this.userInfo.cityId
        ) {
          this.hasLocation = false;
          return;
        } else {
          this.hasLocation = true;
        }
        if (!hospital && old && old.hospital) {
          // 之前有机构，现在没有
          this.init();
        } else if (hospital && (!old || !old.hospital)) {
          // 之前没有，现在有机构
          setTimeout(() => {
            this.init();
          }, 200);
        } else if (
          hospital &&
          old.hospital &&
          hospital.hospital_id !== old.hospital.hospital_id
        ) {
          // 之前和现在都有机构，机构变化
          this.init();
        } else if (cityId && (!old || !old.cityId)) {
          // 之前没有城市，现在有
          this.init();
        }
      },
      immediate: true,
      deep: true
    },
    productId: {
      async handler(newValue, oldValue) {
        if (this.initPageStatus && newValue) {
          this.updateData();
        }
        console.log('productId', newValue, oldValue);
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo,
      productId: (state) => state.global.productId
    })
  },
  methods: {
    async updateData() {
      const hospitalId =
        this.userInfo &&
        this.userInfo.hospital &&
        this.userInfo.hospital.hospital_id
          ? this.userInfo.hospital.hospital_id
          : '';
      const res = await this.$request({
        method: 'post',
        url: '/syGroupBuy/chainProduct/getGroupBuyChainProjectData',
        data: {
          location_city_id:
            this.userInfo.hospital?.city_id || this.userInfo.cityId,
          hospital_id: hospitalId,
          city_id: this.userInfo.hospital?.city_id || this.userInfo.cityId,
          cityId: this.userInfo.hospital?.city_id || this.userInfo.cityId
        }
      }).catch((err) => {
        console.log(err);
        return {};
      });
      if (res?.data?.errorCode === 0) {
        // 一级类目
        this.class1List = res.data.responseData.list;
        // 二级类目
        this.class2List = res.data.responseData.list;
        // dom数据更新后，重新清空
        this.$store.dispatch('global/setProductId', '');
      }
    },
    onNoticeBarCloseClick() {
      uni.setStorageSync('noticeCloseCityId', this.userInfo.cityId);
      this.notice = '';
    },
    onJumpSearchClick() {
      uni.navigateTo({
        url: `/packageSearch/homePage?cityId=${
          this.hospitalInfo.city_id || this.userInfo.cityId
        }`
      });
    },
    f(title) {
      const max = 8;
      if (title) {
        if (title.length > max) {
          return title.slice(0, max) + '...';
        } else {
          return title;
        }
      } else {
        return title;
      }
    },
    hasOpLabel(class2Item) {
      return class2Item.list && class2Item.list.some((i) => i.op_label_info);
    },
    onRightScroll(e) {
      // 并发锁定，左右两个 scroll-view 不能同时触发回调
      if (
        this.currentScrollingDomId &&
        this.currentScrollingDomId !== e.target.id
      ) {
        return;
      }
      console.log('onRightScroll', e.target);
      this.currentScrollingDomId = e.target.id;
      // 滚动方向和位置
      const direction = e.detail.deltaY > 0 ? 'down' : 'up';
      if (this.currentScrollingDomId === 'left-scroll-view') {
        if (direction === 'down' && this.leftScrollDirection === 'up') {
          return;
        }
        this.leftScrollDirection = direction;
      }
      const scrollTop = uni.upx2px(e.detail.scrollTop);
      if (direction === 'down') {
        // 向上滚动 快回到顶部时，展示 banner
        if (scrollTop <= 30) {
          this.bannerHeight = `400rpx`;
          // 如果当前滚动元素是左侧，且scrollTop=0，给改成1（防止左侧无法向下滚动，banner 无法展示）
          if (
            scrollTop < 2 &&
            this.currentScrollingDomId === 'left-scroll-view'
          ) {
            setTimeout(() => {
              this.leftScrollTop = Math.random() + 0.5;
            }, 200);
          }
        }
      } else {
        // 向下滚动 超过一定距离时，隐藏 banner
        if (scrollTop > 30) {
          this.bannerHeight = `0rpx`;
          // 如果当前滚动元素是左侧，且右侧scrollTop=0，给改成1（防止右侧无法向下滚动，banner 无法展示）
          if (this.currentScrollingDomId === 'left-scroll-view') {
            setTimeout(() => {
              const query = uni.createSelectorQuery();
              query.select('.right-scroll-view').scrollOffset();
              query.exec((data) => {
                console.log('onRightScroll query', data);
                if (data && data[0] && data[0].scrollTop < 2) {
                  this.rightScrollTop = Math.random() + 0.5;
                  console.log(
                    'onRightScroll this.rightScrollTop',
                    this.rightScrollTop
                  );
                }
              });
            }, 200);
          }
          // 如果当前滚动元素是右侧，且左侧scrollTop=0，给改成1（防止右侧无法向上滚动，banner 无法隐藏）
          if (this.currentScrollingDomId === 'right-scroll-view') {
            setTimeout(() => {
              const query = uni.createSelectorQuery();
              query.select('#left-scroll-view').scrollOffset();
              query.exec((data) => {
                console.log('onLeftScroll query', data);
                if (data && data[0] && data[0].scrollTop < 2) {
                  this.leftScrollTop = Math.random() + 0.5;
                  console.log(
                    'onRightScroll this.leftScrollTop',
                    this.leftScrollTop
                  );
                }
              });
            }, 200);
          }
        }
      }
      // 解锁
      this.debounceUnlockScroll();
    },
    async init() {
      const checkScopeOfUserLocationRes = await this.checkScopeOfUserLocation();
      if (
        !checkScopeOfUserLocationRes &&
        !this.userInfo.hospital &&
        !this.userInfo.cityId
      ) {
        this.hasLocation = false;
        return;
      } else {
        this.hasLocation = true;
      }
      const hospitalId =
        this.userInfo &&
        this.userInfo.hospital &&
        this.userInfo.hospital.hospital_id
          ? this.userInfo.hospital.hospital_id
          : '';
      if (this.initLoading) {
        return;
      }

      this.initLoading = true;
      this.leftLoading = true;
      this.rightLoading = true;
      this.currentLevel1Index = 0;
      this.currentLevel2Index = 0;
      const loadingTimer = setTimeout(() => {
        // uni.showLoading({});
      }, 300);
      // 一级列表
      const res = await this.$request({
        method: 'post',
        url: '/syGroupBuy/chainProduct/getGroupBuyChainProjectData',
        // url: '/syGroupBuy/chainProduct/chainClass1',
        data: {
          location_city_id:
            this.userInfo.hospital?.city_id || this.userInfo.cityId,
          hospital_id: hospitalId,
          city_id: this.userInfo.hospital?.city_id || this.userInfo.cityId,
          cityId: this.userInfo.hospital?.city_id || this.userInfo.cityId
        }
      }).catch((err) => {
        console.log(err);
        return {};
      });
      if (res?.data?.errorCode === 0) {
        console.log(res.data);
        clearTimeout(loadingTimer);
        // 机构信息
        this.hospitalInfo = res.data.responseData.hospital_data;
        // 顶部消息提示
        if (
          +uni.getStorageSync('noticeCloseCityId') !== +this.userInfo.cityId
        ) {
          this.notice = res.data.responseData.notice;
        }

        // banner
        // this.banner = [
        //   ...res.data.responseData.banner,
        //   ...res.data.responseData.banner
        // ];
        this.banner = res.data.responseData.banner;
        if (
          !res.data.responseData.list ||
          res.data.responseData.list.length === 0
        ) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
          return;
        }
        // 一级类目
        this.class1List = res.data.responseData.list;
        // this.class1List = res.data.responseData.class1_list;
        // 二级类目
        this.class2List = res.data.responseData.list;
        this.initPageStatus = true;
        this.leftLoading = false;
        this.rightLoading = false;
        // uni.hideLoading();
        this.weakNetwork = false;
        // const level2ListIds = res.data.responseData.class1_list.map(
        //   (i) => i.id
        // );
        // await this.mapGetLevel2List(level2ListIds);
        const anchoredItemId = uni.getStorageSync('anchoredItemId');
        const index = this.class1List.findIndex(
          (i) => +i.id === +anchoredItemId
        );
        if (index !== -1) {
          uni.setStorageSync('anchoredItemId', null);
          this.$nextTick(() => {
            this.onLeftLevel1Click(index);
          });
        }
        uni.setStorageSync('anchoredItemId', null);
        // 初始化滚动元素，露出banner
        this.currentScrollingDomId = 'left-scroll-view';
        this.bannerHeight = '400rpx';
        this.rightScrollTop = 0;
        this.debounceUnlockScroll();
        this.$nextTick(() => {
          this.initScrollObserver();
          this.setLeftScrollTop();
          this.initExposureAfterData();
        });
      }
      this.initLoading = false;
    },
    topBannerClick(bannerItem) {
      if (bannerItem && bannerItem.wx_app_link) {
        if (bannerItem.wx_app_link.includes('http')) {
          uni.navigateTo({
            url: `/pages/h5?url=${bannerItem.wx_app_link}`
          });
        } else if (bannerItem.app_id) {
          //
          uni.navigateToMiniProgram({
            appId: bannerItem.app_id, // 公证签小程序APPID
            path: bannerItem.wx_app_link, // 刷脸页面地址
            extraData: {},
            success(res) {
              console.info(res);
              // 打开成功
            }
          });
        } else {
          uni.navigateTo({
            url: bannerItem.wx_app_link,
            fail: (err) => {
              console.log('topBannerClick 错误', err);
            }
          });
        }
      }
    },
    bannerClick(bannerItem, first_tab_num, first_tab_content) {
      if (bannerItem && bannerItem.jump_url) {
        if (bannerItem.jump_url.includes('http')) {
          uni.navigateTo({
            url: `/pages/h5?url=${bannerItem.jump_url}`
          });
        } else {
          uni.navigateTo({
            url: bannerItem.jump_url
          });
        }
        this.$reportData({
          info: 'sy_chain_store_tuan_banner:click_click',
          ext: {
            serial_num: first_tab_num,
            url: bannerItem.jump_url,
            first_tab_num,
            first_tab_content
          }
        });
      }
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    },
    async selectClick() {
      console.log('selectClick');
      const locationPermission = await this.checkScopeOfUserLocation();
      // const city1 = this.userInfo.cityId || this.hospitalInfo.city_id;
      // const city2 = this.userInfo.cityId;
      // 优先取定位城市，如果没有定位取推荐的城市
      uni.navigateToHospitalChoosePage(
        this.userInfo.cityId,
        this.userInfo.hospital?.city_id || ''
      );
      this.$reportData({
        info: 'sy_chain_store_tuan_change:hospital_click',
        ext: {
          content:
            this.userInfo &&
            this.userInfo.hospital &&
            this.userInfo.hospital.hospital_name,
          type: locationPermission ? 1 : 0
        }
      });
    },
    initExposure() {},
    initExposureAfterData() {
      if (!exp1) {
        exp1 = this.$registerExposure('.level-1-item', (info) => {
          // console.log('sy_chain_store_tuan_first:tab_exposure', res);
          const data = info?.dataset || {};
          // console.log(data);
          this.$reportData({
            info: 'sy_chain_store_tuan_first:tab_exposure',
            ext: {
              first_tab_num: data.index + 1,
              first_tab_content: data.title,
              style: data.style
            }
          });
        });
      } else {
        exp1.reInit();
      }
      if (!exp2) {
        exp2 = this.$registerExposure('.level-2-menu-item', (info) => {
          // console.log('sy_chain_store_tuan_first:tab_exposure', res);
          const data = info?.dataset || {};
          // console.log(data);
          this.$reportData({
            info: 'sy_chain_store_tuan_second_tab:exp_exposure',
            ext: {
              second_tab_num: data.index + 1,
              second_tab_content: data.title,
              first_tab_num: data.parent_index + 1,
              first_tab_content: data.parent_title
            }
          });
        });
      } else {
        exp2.reInit();
      }
      if (!exp3) {
        exp3 = this.$registerExposure('.level-banner', (info) => {
          // console.log('sy_chain_store_tuan_first:tab_exposure', res);
          const data = info?.dataset || {};
          // console.log(data);
          this.$reportData({
            info: 'sy_chain_store_tuan_banner:exp_exposure',
            ext: {
              serial_num: data.index + 1,
              url: data.url,
              first_tab_num: data.index + 1,
              first_tab_content: data.title
            }
          });
        });
      } else {
        exp3.reInit();
      }
      if (!exp4) {
        exp4 = this.$registerExposure('.product-card-outer', (info) => {
          // console.log('sy_chain_store_tuan_first:tab_exposure', res);
          const data = info?.dataset || {};
          const url = `/pages/product?material_id=${data.pid}&material_type=1`;
          // console.log(data);
          this.$reportData({
            info: 'sy_chain_store_tuan_product_feed:list_exposure',
            ext: {
              url,
              id: data.id,
              product_id: data.product_id,
              serial_num: data.serial_num,
              first_tab_num: data.first_tab_num,
              first_tab_content: data.first_tab_content,
              second_tab_num: data.second_tab_num,
              second_tab_content: data.second_tab_content,
              item_content: data.item_content
            }
          });
        });
      } else {
        exp4.reInit();
      }
      if (!exp5) {
        exp5 = this.$registerExposure('.pos-line', async () => {
          const locationPermission = await this.checkScopeOfUserLocation();
          this.$reportData({
            info: 'sy_chain_store_tuan_navigation:exp_exposure',
            ext: {
              content:
                this.userInfo &&
                this.userInfo.hospital &&
                this.userInfo.hospital.hospital_addr,
              type: locationPermission ? 1 : 0
            }
          });
        });
      } else {
        exp5.reInit();
      }
    },
    async onLeftLevel1Click(index) {
      this.observerLock = true;
      setTimeout(() => {
        this.observerLock = false;
      }, 200);
      const scrollViewInner = await this.getRect('.right-scroll-view-inner');
      const targetBoxItem = await this.getRect(`#level-2-box-wrap-${index}`);
      // const selfParentHeadRect = await this.getRect(
      //   `.level-2-header-body-${index}`
      // );
      // console.log(targetBoxItem.top);
      // this.rightScrollIntoView = `level-2-box-wrap-${index}`;
      const t = targetBoxItem?.top - scrollViewInner?.top + 2; // -scrollViewRect.top
      // selfParentHeadRect.height +

      // targetBoxItem.height -
      // 50
      // debugger
      this.rightScrollTop = t + Math.random() / 100;
      // 安卓手机（正常流程）
      this.currentLevel1Index = index;
      this.currentLevel2Index = 0;
      this.setLeftScrollTop();
      this.$reportData({
        info: 'sy_chain_store_tuan_first:tab_click',
        ext: {
          first_tab_num: index + 1,
          first_tab_content: this.class1List[index].title,
          style:
            this.class1List[index].op_label_info &&
            this.class1List[index].op_label_info.img
              ? 1
              : 0
        }
      });
    },
    async onLevel2MenuItemClick(level2Index, level3Index) {
      this.observerLock = true;
      setTimeout(() => {
        this.observerLock = false;
      }, 200);
      console.log({ level2Index, level3Index });
      this.currentLevel1Index = level2Index;
      this.currentLevel2Index = level3Index;
      this.$nextTick(() => {
        this.setLeftScrollTop();
      });
      // const scrollViewRect = await this.getRect('#scrollWrap');
      const scrollViewInner = await this.getRect('.right-scroll-view-inner');
      const targetSmallTitleRect = await this.getRect(
        `.level-2-small-title-text-${level2Index}-${level3Index}`
      );
      const selfParentHeadRect = await this.getRect(
        `.level-2-header-body-${level2Index}`
      );
      console.log(targetSmallTitleRect, selfParentHeadRect);
      const scrollTop =
        targetSmallTitleRect.top -
        scrollViewInner.top -
        (selfParentHeadRect ? selfParentHeadRect.height : 0);
      this.rightScrollTop = scrollTop + Math.random() / 100;
      this.$reportData({
        info: 'sy_chain_store_tuan_second:tab_click',
        ext: {
          second_tab_num: level3Index + 1,
          second_tab_content:
            this.class2List[level2Index].list[level3Index].title,
          first_tab_num: level2Index + 1,
          first_tab_content: this.class1List[level2Index].title
        }
      });
    },
    async setLeftScrollTop() {
      const selector = `#level-1-item-${this.currentLevel1Index}`;
      const level1Rect = await this.getRect(selector);
      const rightRect = await this.getRect('.right');
      const level1WrapRect = await this.getRect('.left-wrap');
      const level1ScrollViewRect = await this.getRect('.left');
      let scrollTop = 0;
      if (level1Rect && level1WrapRect && level1ScrollViewRect && rightRect) {
        scrollTop =
          level1Rect.top -
          level1WrapRect.top +
          (level1ScrollViewRect.top - rightRect.top);
      }
      console.log('setLeftScrollTop', scrollTop);
      this.leftScrollTop = scrollTop;
    },
    initScrollObserver() {
      const level2IdList = this.class2List.map((i) => i.id);
      // this.observerLock = true;
      // setTimeout(() => {
      //   this.observerLock = false;
      // }, 500);
      level2IdList.forEach(async (item, index) => {
        // const prevHeaderHeight = await this.getPrevHeaderHeight(index);
        // 一级定位
        const selector = `#level-2-box-wrap-${index}`;
        const ob = uni.createIntersectionObserver(this);
        ob.relativeTo('.right', {}).observe(selector, async (res) => {
          // console.log(res.intersectionRatio, selector);
          if (this.observerLock) {
            return;
          }
          if (res.intersectionRatio === 0) {
            const currentRect = await this.getRect(selector);
            const rightRect = await this.getRect('.right');
            // console.log({ c: currentRect.bottom, t: rightRect.top });
            console.log(res.intersectionRatio, index);
            if (
              currentRect &&
              rightRect &&
              currentRect.bottom <= rightRect.bottom
            ) {
              this.currentLevel1Index = index + 1;
              this.currentLevel2Index = 0;
              this.setLeftScrollTop();
            }
          } else {
            const currentRect = await this.getRect(selector);
            const rightRect = await this.getRect('.right');
            if (
              currentRect &&
              rightRect &&
              currentRect.bottom >= rightRect.top &&
              currentRect.bottom <= rightRect.bottom &&
              currentRect.top < rightRect.top
            ) {
              this.currentLevel1Index = index;
              console.log(res.intersectionRatio, index);
              this.setLeftScrollTop();
              const prevLevel1 = this.class2List[index];
              if (prevLevel1 && prevLevel1.list && prevLevel1.list.length) {
                this.currentLevel2Index = prevLevel1.list.length - 1;
              }
            }
          }
        });
        // 二级定位
        const level2Item = this.class2List[index];
        if (level2Item.list && level2Item.list.length) {
          // 有二级菜单的
          level2Item.list.forEach(async (item, childIndex) => {
            const selector = `#level-2-product-wrap-${index}-${childIndex}`;
            const selfHeaderHeight = await this.getRect(
              `.level-2-header-body-${index}`
            );
            // console.log({ selector, selfHeaderHeight, index });
            const ob = uni.createIntersectionObserver(this);
            ob.relativeTo('.right', {
              top: selfHeaderHeight.height * -1 || 0
            }).observe(selector, async (res) => {
              // console.log(res.intersectionRatio, selector);
              if (this.observerLock) {
                return;
              }
              const currentRect = await this.getRect(selector);
              const rightRect = await this.getRect('.right');
              if (res.intersectionRatio === 0) {
                if (
                  currentRect &&
                  rightRect &&
                  currentRect.bottom <= rightRect.bottom
                ) {
                  this.currentLevel1Index = index;
                  this.currentLevel2Index = childIndex + 1;
                }
              } else {
                if (
                  currentRect &&
                  rightRect &&
                  currentRect.bottom >= rightRect.top &&
                  currentRect.bottom <= rightRect.bottom &&
                  currentRect.top < rightRect.top
                ) {
                  console.log(
                    res.intersectionRatio,
                    'level2',
                    index,
                    childIndex
                  );
                  this.currentLevel1Index = index;
                  this.currentLevel2Index = childIndex;
                }
              }
            });
          });
        } else if (level2Item.product_list && level2Item.product_list.length) {
          // 没有二级菜单
          const selector = `#level-2-product-wrap-${index}-0`;
          const ob = uni.createIntersectionObserver(this);
          ob.relativeTo('.right').observe(selector, async () => {
            // console.log(res.intersectionRatio, selector);
            if (this.observerLock) {
              return;
            }
          });
        }
      });
    },
    getRect(selector) {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select(selector)
          .boundingClientRect((res) => {
            // console.log(res);
            resolve(res);
          })
          .exec();
      });
    },
    async getPrevHeaderHeight(index) {
      if (index === 0) return 0;
      else {
        const prevSelector = `.level-2-header-body-${index - 1}`;
        const prevHeaderRect = await this.getRect(prevSelector);
        return prevHeaderRect.height;
      }
    },
    isLevel2HeaderFixed(index) {
      return index === this.fixedHeaderIndex;
    },
    async mapGetLevel2List(level2ListIds) {
      const hospitalId =
        this.userInfo &&
        this.userInfo.hospital &&
        this.userInfo.hospital.hospital_id
          ? this.userInfo.hospital.hospital_id
          : '';
      this.$set(this, 'class2List', []);
      const errorList = [];
      console.log(level2ListIds, level2ListIds.length);
      for (let i = 0; i < level2ListIds.length; i++) {
        const id = level2ListIds[i];
        const res = await this.$request({
          method: 'post',
          url: `/syGroupBuy/chainProduct/getGroupBuyChainProductClass2?class1_id=${id}&hospital_id=${hospitalId}`
        }).catch((err) => {
          console.log(err);
          return {};
        });
        if (res?.data?.errorCode === 200 || res?.data?.errorCode === 0) {
          this.class2List.push(res.data.responseData);
          this.rightLoading = false;
          if (res.data.responseData.list && res.data.responseData.list.length) {
            this.$set(this.class1SelectMap, id, 0);
          }
        } else {
          errorList.push(res.data?.errorMsg || '未知错误');
        }
      }
      if (errorList.length) {
        uni.showToast({
          icon: 'none',
          title: errorList.join('/')
        });
      }
      // 如果其他页面锚定到当前页面，则处理锚定
      const anchoredItemId = uni.getStorageSync('anchoredItemId');
      const index = this.class1List.findIndex((i) => +i.id === +anchoredItemId);
      if (index !== -1) {
        uni.setStorageSync('anchoredItemId', null);
        this.$nextTick(() => {
          this.onLeftLevel1Click(index);
        });
      }
      uni.setStorageSync('anchoredItemId', null);
      this.$nextTick(() => {
        this.initScrollObserver();
      });

      // console.log('保存好数据~', JSON.stringify(this.class1List));
      // console.log('保存好数据~', JSON.stringify(this.class2List));
    },
    // onHospitalNameClick() {
    // },
    async onHospitalDetailClick() {
      const locationPermission = await this.checkScopeOfUserLocation();
      this.$reportData({
        info: 'sy_chain_store_tuan_navigation:click_click',
        ext: {
          content:
            this.userInfo &&
            this.userInfo.hospital &&
            this.userInfo.hospital.hospital_addr,
          type: locationPermission ? 1 : 0
        }
      });
      uni.navigateTo({
        url: `/packageHospital/hospital-home?hospital_id=${this.userInfo.hospital.hospital_id}`
      });
    },
    async onLocationMaskBtnClickOpen() {
      const locationPermission = await this.checkScopeOfUserLocation();
      if (locationPermission === false) {
        wx.openSetting({
          success(res) {
            console.log(res);
          },
          fail(error) {
            console.log(error);
          }
        });
      } else if (locationPermission === null) {
        this.$getCityId('gcj02').catch(() => ({}));
      }
    },
    // 没有定位时，手动选择
    async onLocationMaskBtnClickManual() {
      uni.navigateToHospitalChoosePage(
        this.userInfo.cityId,
        this.userInfo.hospital?.city_id || ''
      );
    }
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_chain_store_tuan_product_list_page',
      ext: {}
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_chain_store_tuan_product_list_page',
      ext: {}
    });
  },
  onPullDownRefresh() {
    this.init();
  },
  onShareAppMessage() {
    return {
      title: '新氧青春—专业轻医美连锁',
      path: '/pages/item',
      imageUrl:
        'https://static.soyoung.com/sy-pre/20240223-185625-1708683000724.jpeg'
    };
  }
};
</script>
<style lang="scss" scoped>
$topHeight: 160rpx;
.item-page-wrap {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: #f5f6f7;
  /* padding-bottom: 20rpx; */
  //background: #f00;
  .top {
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx 20rpx 20rpx;
    background: #ffffff;
    font-family: PingFangSC-Regular;
    .hospital-base-info-line {
      display: flex;
      justify-content: space-between;
      .hospital-name {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Medium;
        font-size: 28rpx;
        color: #212121;
        .switch-hospital {
          margin-left: 10rpx;
          width: 32rpx;
          height: 32rpx;
        }
      }
      .distance {
        font-size: 24rpx;
        color: #222;
        line-height: 40rpx;
      }
    }
    .pos-line {
      margin-top: 20rpx;
      display: flex;
      font-size: 12px;
      color: #999999;
      align-items: center;
      justify-content: flex-start;
      .text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .pos-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 4rpx;
        flex-shrink: 0;
      }
      .pos-arrow {
        margin-left: 4rpx;
        width: 12rpx;
        height: 20rpx;
        flex-shrink: 0;
      }
    }

    .jump-search-btn {
      position: absolute;
      box-sizing: border-box;
      right: 30rpx;
      top: 4rpx;
      width: 180rpx;
      height: 64rpx;
      padding: 0 20rpx;
      background: #f2f2f2;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 10rpx;
      .jump-search-btn-icon {
        width: 28rpx;
        height: 28rpx;
      }
      .jump-search-btn-text {
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #bababa;
      }
    }
  }
  .out-wrap {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .banner {
    /* display: flex; */
    /* align-items: center; */
    /* justify-content: center;       */
    /* position: sticky; */
    flex-shrink: 0;
    background: #fff;
    overflow: hidden;
    height: 400rpx;
    /* top: 0; */
    /* z-index: 500; */
    transition: all 0.2s linear;
    padding-bottom: 20rpx;
    padding-left: 30rpx;
    .banner-wrap {
      /* display: flex; */
      /* align-items: center; */
      /* justify-content: center; */
      white-space: nowrap;
      overflow: visible;
      .banner-image {
        display: inline-block;
        vertical-align: top;
        height: 400rpx;
        width: 0;
        margin-right: 30rpx;
        flex-shrink: 0;
        &:nth-child(1) {
        }
      }
    }
  }
  .main {
    flex: 1;
    display: flex;
    overflow: hidden;
    min-height: calc(100vh - 400rpx);
    .left {
      width: 176rpx;
      overflow: scroll;
      .level-1-item {
        height: 90rpx;
        position: relative;
        margin-top: 10rpx;
        text-align: left;
        color: #222222;
        padding-right: 20rpx;
        &:nth-child(1) {
          //padding-top: 10rpx;
          margin-top: 20rpx;
          //height: 80rpx;
        }
        .title-text {
          height: 90rpx;
          line-height: 90rpx;
          font-size: 24rpx;
          padding-left: 26rpx;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .op-label {
          position: absolute;
          height: 28rpx;
          right: 16rpx;
          top: -4rpx;
          width: 0;
        }
        &.active {
          font-family: PingFangSC-Semibold;
          .title-text {
            background: #ffffff;
          }
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 10rpx;
            background-color: #61b43e;
          }
        }
      }
    }
    .left-wrap {
      padding-bottom: 200rpx;
      .left-scroll-place-holder {
        height: 50vh;
        width: 100%;
        /* background: #f00; */
      }
    }
    .right {
      flex: 1;
      position: relative;
      overflow: hidden;
      margin-top: 20rpx;
      margin-right: 20rpx;
      background: #fff;
      .level-banner {
        width: 100%;
        height: 90rpx;
        object-fit: cover;
        border-radius: 8rpx 8rpx 0 0;
        &.has-margin-top {
          margin-top: 30rpx;
        }
      }
      .left-corner,
      .right-corner {
        position: absolute;
        top: 20rpx;
        width: 12rpx;
        height: 12rpx;
        z-index: 10;
      }
      .left-corner {
        left: 0;
      }
      .right-corner {
        right: 20rpx;
      }
    }
  }
}
.level-2-menu {
  overflow-x: scroll;
  overflow-y: visible;
  white-space: nowrap;
  margin-left: 20rpx;
  //padding-right: 20rpx;
  .wrap {
    padding-right: 20rpx;
  }
  .level-2-menu-item {
    height: 54rpx;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background: #f5f6f7;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #7e8ba0;
    padding: 0 20rpx;
    position: relative;
    margin-top: 30rpx;
    &:nth-last-child(1) {
      //margin-right: 20rpx;
    }
    &:not(:nth-child(1)) {
      margin-left: 20rpx;
    }
    &.has-op-label {
      /* margin-top: 30rpx; */
    }
    .op-label {
      position: absolute;
      height: 28rpx;
      right: -10rpx;
      top: -18rpx;
    }
    &:nth-last-child(1) {
      margin-right: 20rpx;
    }
    &.active {
      background: #333;
      color: #fff;
    }
  }
}

.level-2-box-wrap {
  padding-bottom: 30rpx;
  background: #fff;
  border-radius: 8rpx;
  /* margin-bottom: 20rpx; */
  /* margin-right: 20rpx; */
}

.level-2-header {
  background: #ffffff;
  z-index: 10;
  position: relative;
  overflow: hidden;
  &.high {
    margin-bottom: 10rpx;
  }
}
.level-2-header-body {
  background: #ffffff;
  z-index: 10;
  position: relative;
  overflow: hidden;
  &.high {
    //height: 174rpx;
    padding-bottom: 20rpx;
    .level-2-list-title {
      font-family: PingFangSC-Medium;
      padding: 30rpx 20rpx 0;
      line-height: 36rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 30rpx;
    }
  }
  &.low {
    //height: 96rpx;
    padding-bottom: 20rpx;
    .level-2-list-title {
      font-family: PingFangSC-Medium;
      padding: 30rpx 20rpx 0;
      line-height: 36rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 30rpx;
    }
  }

  &.level-2-header-body-1 {
    z-index: 11;
  }
  &.level-2-header-body-2 {
    z-index: 12;
  }
  &.level-2-header-body-3 {
    z-index: 13;
  }
  &.level-2-header-body-4 {
    z-index: 14;
  }
  &.level-2-header-body-5 {
    z-index: 15;
  }
  &.level-2-header-body-6 {
    z-index: 16;
  }
  &.level-2-header-body-7 {
    z-index: 17;
  }
  &.level-2-header-body-8 {
    z-index: 18;
  }
  &.level-2-header-body-9 {
    z-index: 19;
  }
  &.level-2-header-body-10 {
    z-index: 20;
  }
  &.level-2-header-body-11 {
    z-index: 21;
  }
  &.level-2-header-body-12 {
    z-index: 22;
  }
}
.level-2-header {
  background: #fff;
  position: sticky;
  top: -1rpx; // 修复黑边
  .level-2-header-body {
    //width: 100%;
    //&.fixed {
    //  position: fixed;
    //  //position: absolute;
    //  top: $topHeight;
    //  right: 20rpx;
    //  left: 176rpx;
    //}
  }
  .header-fixed-trigger {
    height: 1px;
    //background: #f00;
    position: relative;
    //top: 200rpx;
  }
}
.level-2-product-wrap {
  padding: 0rpx 20rpx 0rpx;
}
.level-2-product-wrap-single {
  padding-top: 20rpx;
}
.right-scroll-view {
  //height: 400rpx;
  //position: absolute;
  flex: 1;
  height: 100%;
  overflow-y: scroll;
}
.level-2-small-title-text {
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #222222;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
  &.empty {
    height: 0;
    margin: 0;
  }
}
.product-card-outer {
  &:not(:nth-last-child(1)) {
    margin-bottom: 40rpx;
  }
}
.level-2-products {
  padding-top: 10rpx;
}
.float-mask {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 750rpx;
  height: 214rpx;
  pointer-events: none;
  z-index: 6;
}
.test-refresh {
  position: fixed;
  right: 10rpx;
  bottom: 400rpx;
  z-index: 10;
  width: 100rpx;
  height: 100rpx;
  background: #00ab8433;
}
.left-empty {
  width: 156rpx;
  height: 0;
  margin-right: 20rpx;
  margin-top: 20rpx;
}
.right-empty {
  display: block;
  width: calc(100% - 20rpx);
  margin-right: 20rpx;
}

.notice-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 52rpx;
  padding: 0 20rpx;
  background: rgba(235, 251, 220, 0.5);
  overflow: hidden;
  margin: 0 10rpx;
  margin-top: 20rpx;
  .notice-bar-text {
    font-size: 22rpx;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .notice-bar-close {
    width: 30rpx;
    height: 30rpx;
    flex-shrink: 0;
  }
}
.location-mask {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .location-mask-img {
    width: 70rpx;
    height: 70rpx;
    margin-bottom: 40rpx;
  }
  .location-mask-text {
    font-size: 28rpx;
    color: #030303;
    margin-bottom: 60rpx;
  }
  .location-mask-btn {
    width: 340rpx;
    height: 74rpx;
    box-sizing: border-box;
    border: 2rpx solid #333333;
    font-size: 28rpx;
    text-align: center;
    line-height: 60rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    &.recommend {
      background: #333333;
      color: #fff;
    }
  }
}
</style>
