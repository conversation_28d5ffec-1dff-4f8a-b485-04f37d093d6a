<template>
  <page-meta :page-style="pageScrollStyle" class="productDetail">
    <div class="page" :class="{ 'page-skeleton-diagram': !hasData }">
      <!-- 顶部栏 -->
      <div
        v-if="menuRect.top"
        class="pageTop"
        :style="{
          height: menuRect.bottom + 8 + 'px',
          background
        }"
      >
        <div
          class="nav"
          :style="{
            top: menuRect.top + 'px',
            width: menuRect.width + 'px',
            height: menuRect.height + 'px'
          }"
        >
          <div class="backBox" @click="handleBack">
            <img
              class="back"
              src="https://static.soyoung.com/sy-design/3cj8rc3ipek931726026826755.png"
            />
          </div>
        </div>
        <!-- 分享按钮 -->
        <div
          v-if="hasData"
          class="share"
          @click="showShareBar"
          :style="{
            top: menuRect.top - 7 + 'px',
            right: menuRect.width + 30 + 'px'
          }"
        >
          <img
            class="shareIcon"
            src="https://static.soyoung.com/sy-pre/1wu38mq9u5lep-1716797400623.png"
            alt=""
          />
        </div>
      </div>
      <view
        v-if="Number(opacitySuction) > 0"
        class="page-ceiling-suction"
        :style="{
          top: menuRect.bottom + 7 + 'px',
          opacity: opacitySuction
        }"
      >
        <view
          class="page-ceiling-suction-detail"
          :class="{
            'page-ceiling-suction-select': tabAnchoring === 0
          }"
          @click="pageSlide('.productIntroduce')"
        >
          项目详情
        </view>
        <view
          class="page-ceiling-suction-store"
          :class="{
            'page-ceiling-suction-select': tabAnchoring === 1
          }"
          @click="pageSlide('.applicable-stores')"
        >
          适用门店
        </view>
      </view>
      <block v-if="hasData">
        <view style="background-color: #ffffff">
          <!-- 轮播图 -->
          <view
            v-if="pageInfo.sku_media && pageInfo.sku_media.image_list"
            class="uni-margin-wrap"
          >
            <new-banner
              :list="pageInfo.sku_media.image_list"
              width="100%"
              height="416rpx"
              @expose="imgChange"
            ></new-banner>
          </view>
          <view v-if="beltInfo" class="page-belt" @click="beltTap">
            <view
              class="page-belt-top"
              :style="{
                'background-image': `url(${beltInfo.backgroundImage})`
              }"
            >
              <view class="page-belt-title">
                <image
                  class="page-belt-title__icon"
                  :src="beltInfo.titleIcon"
                  mode="heightFix"
                ></image>
              </view>
              <view class="page-belt-right">
                <view
                  v-if="beltInfo.countDown || beltInfo.rightTips"
                  class="page-belt-right__count-down"
                  :style="{ color: beltInfo.rightColor }"
                >
                  <text v-if="beltInfo.rightTips.before">
                    {{ beltInfo.rightTips.before }}
                  </text>
                  <GroupCountDown
                    v-if="beltInfo.countDown"
                    :totalSeconds="beltInfo.countDown"
                    :color="beltInfo.rightColor"
                    :hideMilliseconds="beltInfo.type === 1"
                  />
                  <text v-if="beltInfo.rightTips.after">
                    {{ beltInfo.rightTips.after }}
                  </text>
                </view>
                <image
                  v-if="
                    !(
                      pageInfo.chain_extend.chain_add_c &&
                      pageInfo.sku_price_info.writeoff_cashback
                    )
                  "
                  class="page-belt-right__more-icon"
                  :src="
                    beltInfo.type === 1
                      ? 'https://static.soyoung.com/sy-design/6eboz32amcrz1726026826568.png'
                      : 'https://static.soyoung.com/sy-design/35p8mqzgrxfp01726715721920.png'
                  "
                ></image>
              </view>
            </view>
            <view
              class="page-belt-bottom"
              :style="{
                backgroundColor: beltInfo.beltColor
              }"
            >
              <div class="belt-bottom-price-bar">
                <text class="belt-bottom-price-bar__price-text"> 到手价 </text>
                <image
                  class="belt-bottom-price-bar__price-icon"
                  src="https://static.soyoung.com/sy-design/3la6j2ydurtvx1747194665690.png"
                ></image>
                <text class="belt-bottom-price-bar__price-price">
                  {{ pageTitlePriceInfo.price }}
                </text>
                <view
                  v-if="beltInfo.subtitle"
                  class="belt-bottom-price-bar__price-label"
                >
                  <text v-if="beltInfo.subtitle">
                    {{ beltInfo.subtitle }}
                  </text>
                </view>
                <view
                  v-if="pageTitlePriceInfo.consultPrice"
                  class="belt-bottom-price-bar__price-consult"
                  @click="onAddCPriceClick"
                >
                  <span>咨询好价</span>
                  <image
                    :src="'https://static.soyoung.com/sy-design/35p8mqzgrxfp01726715721920.png'"
                    mode="widthFix"
                  ></image>
                </view>
              </div>
              <div
                class="belt-bottom-summary"
                v-if="!pageInfo.chain_extend.chain_add_c"
              >
                <text>{{
                  pageInfo.sku_price_info.price_discount_summary
                }}</text>
                <image
                  src="https://static.soyoung.com/sy-pre/20250523-105942-1747966200624.png"
                  mode="widthFix"
                ></image>
              </div>
            </view>
          </view>
          <view class="page-title-price">
            <view class="page-title-price__left">
              <view class="page-title-price__left-title">
                {{ pageTitlePriceInfo.product_title }}
              </view>
              <view
                v-if="pageTitlePriceInfo.chain_promote_info"
                class="page-title-price__left-subtitle"
              >
                {{ pageTitlePriceInfo.chain_promote_info }}
              </view>
              <view class="page-title-price__left-other">
                <view
                  v-if="pageTitlePriceInfo.description"
                  class="page-title-price__left-other-item"
                >
                  <text>
                    {{ pageTitlePriceInfo.description }}
                  </text>
                  <image
                    src="https://static.soyoung.com/sy-design/3vg2r23aqk16d1725959022557.png"
                    @click="showFQmodel"
                  ></image>
                </view>
                <view
                  v-if="pageTitlePriceInfo.sold_cnt"
                  class="page-title-price__left-other-item"
                >
                  <text>{{ pageTitlePriceInfo.sold_cnt }}</text>
                  <image
                    src="https://static.soyoung.com/sy-design/3vg2r23aqk16d1725959022557.png"
                    @click="showXLmodel"
                  ></image>
                </view>
                <!-- 疼痛标签 -->
                <view
                  v-if="pageTitlePriceInfo.pain_tag_info"
                  :data-item_content="pageTitlePriceInfo.pain_tag_info.name"
                  class="page-title-price__left-other-item painTag"
                  @click="showPainModel(pageTitlePriceInfo.pain_tag_info)"
                >
                  <div
                    class="painInfoBox"
                    :style="{
                      color: pageTitlePriceInfo.pain_tag_info.text_color,
                      backgroundColor:
                        pageTitlePriceInfo.pain_tag_info.background_color
                    }"
                  >
                    <image
                      class="painIcon"
                      :src="pageTitlePriceInfo.pain_tag_info.icon"
                    ></image>
                    <div class="text">
                      {{ pageTitlePriceInfo.pain_tag_info.name }}
                    </div>
                  </div>
                  <image
                    :style="{
                      marginTop: '1rpx'
                    }"
                    src="https://static.soyoung.com/sy-design/3vg2r23aqk16d1725959022557.png"
                  ></image>
                </view>
              </view>
            </view>
            <view class="page-title-price__right" v-if="!beltInfo">
              <view class="page-title-price__right-amount">
                <view class="page-title-price__right-amount-name">
                  <text>
                    <block v-if="pageTitlePriceInfo.type === 1"> 拼团 </block>
                    <block v-else-if="pageTitlePriceInfo.type === 2">
                      到手价
                    </block>
                    <block v-else> 售价 </block>
                  </text>
                  <image
                    src="https://static.soyoung.com/sy-design/3la6j2ydurtvx1725959022556.png"
                  ></image>
                </view>
                <view class="page-title-price__right-amount-number">
                  {{ pageTitlePriceInfo.price }}
                </view>
              </view>
              <view
                v-if="pageTitlePriceInfo.consultPrice"
                class="page-title-price__right-consult-price"
                @click="onAddCPriceClick"
              >
                <span>咨询好价</span>
                <image
                  :src="'https://static.soyoung.com/sy-design/35p8mqzgrxfp01726715721920.png'"
                  mode="widthFix"
                ></image>
              </view>
              <view
                v-else-if="pageTitlePriceInfo.underlinedPrice"
                class="page-title-price__right-underlined-price"
              >
                ￥{{ pageTitlePriceInfo.underlinedPrice }}
              </view>
            </view>
          </view>
          <view
            v-if="
              pageInfo.product_brand_service &&
              pageInfo.product_brand_service.product_brand_publicity_data
            "
            class="page-guarantee"
            @click="
              to1(
                pageInfo.product_brand_service.product_brand_publicity_data
                  .jump_url
              )
            "
          >
            <view class="page-guarantee__header">
              <image
                class="page-guarantee__header-title"
                src="https://static.soyoung.com/sy-design/32q76aat6dbg71729076376123.png"
                mode="heightFix"
              ></image>
              <image
                class="page-guarantee__header-more"
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726026826568.png"
              ></image>
            </view>
            <view class="page-guarantee__body">
              <view class="page-guarantee__body-left">
                <view
                  v-for="(item, index) in productServiceGuaranteeData"
                  :key="index"
                  class="page-guarantee__body-content"
                >
                  <!--                  <image-->
                  <!--                    src="https://static.soyoung.com/sy-design/kgmpik5wigpe1725959023160.png"-->
                  <!--                  ></image>-->
                  <text :style="{ 'font-family': item.fontFamily }">
                    {{ item.text }}
                  </text>
                </view>
              </view>
              <!--            <image-->
              <!--              class="page-guarantee__body-more"-->
              <!--              src="https://static.soyoung.com/sy-design/6eboz32amcrz1726026826568.png"-->
              <!--            ></image>-->
            </view>
          </view>
          <!-- 拼团楼层模块 -->
          <div v-if="groupFloorInfo && !isJoinGroupProduct" class="groupFloor">
            <div class="title">
              <div class="left">
                {{ groupFloorInfo.total_user }}人在拼，参与可立即拼成
              </div>
              <div
                v-if="groupFloorInfo.list.length > 2"
                class="right"
                @click="showAllFloor"
              >
                <text>查看全部</text>
                <img
                  src="https://static.soyoung.com/sy-design/6eboz32amcrz1726026826568.png"
                  alt=""
                  class="arrow"
                />
              </div>
            </div>
            <div class="floorCardBox">
              <div
                v-for="item in groupFloorInfo.list.slice(0, 2)"
                :key="item.group_id"
                class="cardBorder"
              >
                <FloorCard
                  :skuId="sku_id"
                  :cardData="item"
                  :activityId="groupFloorInfo.activity_id"
                />
              </div>
            </div>
          </div>
          <!-- 项目规格模块 -->
          <div class="productSelect">
            <div class="selectItem" @click="openSkuSelection">
              <div class="title">项目</div>
              <div class="text">{{ skuItem.title }}</div>
              <img
                v-if="
                  pageInfo.base_sku_list && pageInfo.base_sku_list.length > 1
                "
                src="https://static.soyoung.com/sy-design/3dhi453czg4cw1726026826755.png"
                alt=""
                class="arrow"
              />
            </div>
            <!-- 商详主页项目规格@王爽 -->
            <div class="productSpecification">
              <view class="productSpecificationTitle"> 包含项目 </view>
              <Specification
                v-if="pageInfo.chain_item"
                :specification="pageInfo.chain_item"
                :isPackage="
                  Number(
                    pageInfo.sku_detail_core_info.sku_all_dto.is_package
                  ) === 1
                    ? true
                    : false
                "
                @reportStat="reportStat"
              ></Specification>
            </div>
          </div>
        </view>
        <!-- 图文详情 -->
        <div v-if="pageInfo.img_text_info" class="productIntroduce">
          <div class="title">项目介绍</div>
          <div class="contentBox">
            <div class="rich-text">
              <rich-text
                class="list"
                type="text"
                :nodes="pageInfo.img_text_info"
              />
            </div>
            <div
              class="productCompanyImageWrapper"
              v-if="pageInfo.chain_ext_data.brand_img_info"
            >
              <div
                class="productCompanyBox"
                v-if="
                  pageInfo.chain_ext_data.brand_img_info &&
                  pageInfo.chain_ext_data.brand_img_info.top_image
                "
              >
                <image
                  class="productCompanyImage"
                  :src="pageInfo.chain_ext_data.brand_img_info.top_image"
                  mode="widthFix"
                />
              </div>
              <div style="margin-top: -1px">
                <new-banner
                  v-if="
                    pageInfo.chain_ext_data.brand_img_info &&
                    pageInfo.chain_ext_data.brand_img_info.list &&
                    pageInfo.chain_ext_data.brand_img_info.list.length
                  "
                  :list="
                    pageInfo.chain_ext_data.brand_img_info.list.map((item) => {
                      return {
                        img_url: item.img_url,
                        jump_url: ''
                      };
                    })
                  "
                  :width="'690rpx'"
                  :height="
                    (690 *
                      pageInfo.chain_ext_data.brand_img_info.list[0].height) /
                      pageInfo.chain_ext_data.brand_img_info.list[0].width +
                    'rpx'
                  "
                />
              </div>

              <div
                class="productCompanyBox"
                v-if="
                  pageInfo.chain_ext_data.brand_img_info &&
                  pageInfo.chain_ext_data.brand_img_info.bottom_image
                "
              >
                <image
                  class="productCompanyImage"
                  :src="pageInfo.chain_ext_data.brand_img_info.bottom_image"
                  mode="widthFix"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 服务流程 -->
        <view class="serviceProcessModel">
          <view class="serviceProcessModelTitle"> 服务流程 </view>
          <Service
            :service_process="pageInfo.chain_ext_data.service_process"
          ></Service>
        </view>
        <!-- 底部按钮 -->
        <view class="bottomBtn">
          <view class="leftBtn">
            <view @click="onZx">
              <img
                src="https://static.soyoung.com/sy-design/2oz4gvql823qq1725959023150.png"
                alt=""
                class="icon"
              />
              <text class="text">咨询/预约</text>
              <img
                mode="heightFix"
                class="addCPopoverImage"
                v-if="
                  !bottomPopoverClicked &&
                  showAddCPopoverImage &&
                  pageInfo.chain_extend &&
                  pageInfo.chain_extend.chain_add_c &&
                  pageInfo.chain_extend.chain_add_c.bottom_add_c_tag
                "
                :src="pageInfo.chain_extend.chain_add_c.bottom_add_c_tag"
                alt="咨询有惊喜"
              />
            </view>
          </view>
          <!-- 参加拼团按钮 -->
          <view class="rightButtonRegion">
            <button
              v-if="isJoinGroupProduct"
              class="join-group"
              @click="buy('joinGroup')"
            >
              <view class="join-group-btn-title">参加拼团</view>
              <view class="join-group-btn-tips">
                <GroupCountDown
                  v-if="beltInfo && beltInfo.countDown"
                  :totalSeconds="beltInfo && beltInfo.countDown"
                  color="#a9ea6a"
                  :hideMilliseconds="beltInfo && beltInfo.type === 1"
                />
                <text>结束</text>
              </view>
            </button>
            <div v-else-if="isGroup" class="combination-btn">
              <button class="leftBtn" @click="buy('alone')">
                <text class="line1">
                  ￥{{
                    pageInfo.chain_extend &&
                    pageInfo.chain_extend.chain_add_c &&
                    pageInfo.chain_extend.chain_add_c.hidden_price_text
                      ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                      : pageInfo.sku_price_info.price_online
                  }}
                </text>
                <!-- 单次核销礼计算后最终价格 @文鑫 -->
                <text class="line2">单独购买到手价</text>
              </button>
              <button
                :class="[
                  'rightBtn',
                  groupStatus !== 'begining' ? 'gray' : '',
                  beltInfo && beltInfo.buyStatus === 1 ? 'new-gray' : ''
                ]"
                @click="groupBuy"
              >
                <!-- <text class="line1">
                  ￥{{
                    pageInfo.chain_extend &&
                    pageInfo.chain_extend.chain_add_c &&
                    pageInfo.chain_extend.chain_add_c.hidden_price_text
                      ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                      : pageInfo.chain_discount.sku_group_activity_data
                          .group_data.pin_tuan_price
                  }}
                </text> -->
                <text class="line1">
                  ￥{{
                    pageInfo.chain_extend &&
                    pageInfo.chain_extend.chain_add_c &&
                    pageInfo.chain_extend.chain_add_c.hidden_price_text
                      ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                      : pageInfo.sku_price_info.price_final
                  }}
                </text>
                <!-- 发起拼团到手价 单次核销礼计算后最终价格 @文鑫 -->
                <text class="line2">发起拼团到手价</text>
              </button>
            </div>
            <!-- 非拼团按钮 -->
            <button v-else class="join-group" @click="buy()">
              <text class="join-group-btn-title">立即购买</text>
              <text class="join-group-btn-tips">
                <block
                  v-if="
                    pageInfo.sku_price_info.has_discount ||
                    pageInfo.sku_price_info.writeoff_cashback
                  "
                >
                  到手价 </block
                >￥{{
                  pageInfo.chain_extend &&
                  pageInfo.chain_extend.chain_add_c &&
                  pageInfo.chain_extend.chain_add_c.hidden_price_text
                    ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                    : pageInfo.sku_price_info.price_final
                }}
              </text>
              <!-- 单次核销礼计算后最终价格 @文鑫 -->
            </button>
          </view>
          <!-- 拼团单独购买+开团按钮 -->
        </view>
        <!-- 分享弹窗 -->
        <SpuShare
          :visible.sync="wakeShareVisible"
          :opts="wakeShareOpts"
          @reportStat="reportStat"
          @gaussianBlur="(value) => (gaussianBlur = value)"
        />
        <!-- 优惠券弹窗 -->
        <BottomModal
          title="优惠"
          :bottom="0"
          background="#fff"
          :visible.sync="discountVisible"
          @onClose="closeFn"
        >
          <div class="coupon-bottom-popup">
            <div
              class="cashback-text-area"
              v-if="
                (beltInfo &&
                  beltInfo.type === 1 &&
                  pageInfo.chain_discount &&
                  pageInfo.chain_discount.sku_group_activity_data &&
                  pageInfo.chain_discount.sku_group_activity_data
                    .pintuan_cut_text) ||
                (beltInfo &&
                  beltInfo.type === 2 &&
                  pageInfo.sku_price_info &&
                  pageInfo.sku_price_info.writeoff_cashback &&
                  pageInfo.sku_price_info.writeoff_cashback
                    .max_writeoff_cashback_text)
              "
            >
              <div
                class="cashback-text-area-title"
                v-if="
                  pageInfo.sku_price_info &&
                  pageInfo.sku_price_info.writeoff_cashback &&
                  pageInfo.sku_price_info.writeoff_cashback
                    .max_writeoff_cashback_text
                "
              >
                <image
                  src="https://static.soyoung.com/sy-pre/20250523-124858-1747973400676.png"
                  mode="widthFix"
                ></image>
                <div class="cashback-text-area-title-text">
                  <span class="cashback-text-area-title-title">核销：</span>
                  {{
                    pageInfo.sku_price_info.writeoff_cashback
                      .max_writeoff_cashback_text
                  }}
                  <span
                    class="cashback-text-area-title-link"
                    @click="handleClickToCashBack"
                  >
                    查看活动详情>
                  </span>
                </div>
              </div>
              <div
                class="cashback-text-area-title"
                v-if="
                  pageInfo.chain_discount &&
                  pageInfo.chain_discount.sku_group_activity_data &&
                  pageInfo.chain_discount.sku_group_activity_data
                    .pintuan_cut_text
                "
              >
                <image
                  src="https://static.soyoung.com/sy-pre/20250523-125007-1747973400676.png"
                  mode="widthFix"
                ></image>
                <div class="cashback-text-area-title-text">
                  <span class="cashback-text-area-title-title">拼团：</span>
                  {{
                    pageInfo.chain_discount.sku_group_activity_data
                      .pintuan_cut_text
                  }}
                  <span
                    class="cashback-text-area-title-link"
                    @click="handleClickToPinTuan"
                  >
                    查看活动详情>
                  </span>
                </div>
              </div>
            </div>
            <div class="scroll" v-if="!(beltInfo && beltInfo.type === 1)">
              <!-- 自营App改版，不展示津贴，产品另提需求 -->
              <div
                v-if="
                  couponData.allowance_list &&
                  couponData.allowance_list.list &&
                  couponData.allowance_list.list.length
                "
                class="bottomPopCard"
              >
                <div class="title">可领津贴</div>
                <div class="perksBox">
                  <block
                    v-for="item in couponData.allowance_list.list"
                    :key="item.id"
                  >
                    <div v-if="item.type === '1'" class="item">
                      <div class="perksBoxItemTop">
                        <div class="line1">
                          <div class="left">
                            <div class="line1Title">{{ item.name }}</div>
                            <div class="tips">可与红包叠加</div>
                          </div>
                          <div class="right">
                            <span class="smore">￥</span>
                            {{ item.discount_value }}
                          </div>
                        </div>
                        <div class="line2">
                          <div class="left">{{ item.use_time_notice }}</div>
                          <div class="right">
                            {{ item.threshold_notice }}
                          </div>
                        </div>
                      </div>
                      <div class="perksBoxItemBottom">
                        <div class="left">{{ item.desc }}</div>
                        <div
                          v-if="!item.get_yn"
                          class="right getJt"
                          @click="onReceiveJT(item)"
                        >
                          领取
                        </div>
                      </div>
                      <div v-if="item.get_yn" class="received"></div>
                    </div>
                  </block>
                </div>
              </div>
              <div
                v-if="
                  couponData.coupon_list &&
                  couponData.coupon_list.list &&
                  couponData.coupon_list.list.length
                "
                class="bottomPopCard"
              >
                <div class="title">可领取优惠券</div>
                <div class="couponsBox">
                  <view
                    v-for="item in couponData.coupon_list.list"
                    :key="item.id"
                    style="margin-bottom: 30rpx"
                  >
                    <new-card
                      :source="'product'"
                      :card-data="couponDataCombination(item)"
                      :right-button-text="item.get_yn ? '已领取' : '领取'"
                      :right-button-status="item.get_yn"
                      @card-tap="couponTap('single', item)"
                    ></new-card>
                    <!--                    <CouponCard-->
                    <!--                      :cardData="item"-->
                    <!--                      @reportStat="reportStat"-->
                    <!--                      @refreshPage="pageInit"-->
                    <!--                      @changePageLoadingStatus="changePageLoadingStatus"-->
                    <!--                    />-->
                  </view>
                </div>
              </div>
            </div>
            <view
              class="btnBox collect-all"
              v-if="
                !(beltInfo && beltInfo.type === 1) &&
                couponData.coupon_list.list.length
              "
            >
              <button
                class="collect-all-btn"
                :class="{ 'disable': CouponAllCollectionStatus }"
                :disabled="CouponAllCollectionStatus"
                @click="couponTap('all')"
              >
                {{ CouponAllCollectionStatus ? '已领取' : '全部领取' }}
              </button>
            </view>
            <!-- <div class="btnBox">
              <div
                class="bottompop-btn coupon-btn"
                :class="{
                  disabled: hasAllReceived
                }"
                @click="receiveAll"
              >
                {{ hasAllReceived ? '已全部领取' : '全部领取' }}
              </div>
            </div> -->
          </div>
        </BottomModal>
        <!-- 选择规格弹窗 -->
        <BottomModal
          :bottom="0"
          :showTitle="false"
          background="#f8f8f8"
          :visible="selectionVisible"
          @close="onSelectionClose"
        >
          <div class="spu-bottom-popup">
            <div class="spupopTopInfo">
              <div class="row1">
                <view class="money">
                  <text>
                    {{ popupPriceInfo.priceTypeName }}
                  </text>
                  <image
                    src="https://static.soyoung.com/sy-design/3la6j2ydurtvx1725959022556.png"
                  ></image>
                </view>
                <span class="price">
                  {{ popupPriceInfo.price }}
                </span>
              </div>
              <view
                v-if="byStagesSpecific && byStagesSpecific.description"
                class="row3"
              >
                ({{ byStagesSpecific.description }})
              </view>
            </div>
            <div class="scroll">
              <div class="bottomPopCard">
                <div class="spu-bottom-popup-title">
                  <text class="title">项目规格</text>
                </div>
                <div class="spupopSkuBox">
                  <div
                    v-for="item in pageInfo.base_sku_list"
                    :key="item.sku_id"
                    :class="[
                      'skuItem',
                      skuItem.sku_id === item.sku_id ? 'select' : ''
                    ]"
                    @click="selectSku(item)"
                  >
                    {{ item.title }}
                  </div>
                </div>
              </div>
              <!-- 分期 -->
              <view
                v-if="
                  byStagesSpecific &&
                  byStagesSpecific.support_loan &&
                  byStagesSpecific.loan_check_data
                "
                class="by-stages"
              >
                <view class="spu-bottom-popup-title">
                  <text class="title">
                    {{ byStagesSpecific.loan_check_data.main_title }}
                  </text>
                  <text class="subtitle">
                    {{ byStagesSpecific.loan_check_data.sub_title }}
                  </text>
                </view>
                <scroll-view
                  v-if="
                    byStagesSpecific.loan_check_data.periods &&
                    byStagesSpecific.loan_check_data.periods.length > 0
                  "
                  class="by-stages-list"
                  scroll-x
                  enable-flex
                >
                  <view
                    class="by-stages-item"
                    :class="{
                      'by-stages-item-select': Number(byStagesID) === 0,
                      'by-stages-item-self-adaption':
                        byStagesSpecific.loan_check_data.periods.length < 3
                    }"
                    @click="selectByStages(0)"
                  >
                    <text class="by-stages-item-name"> 不分期 </text>
                  </view>
                  <view
                    v-for="(item, index) in byStagesSpecific.loan_check_data
                      .periods"
                    class="by-stages-item"
                    :class="{
                      'by-stages-item-select':
                        Number(item.period) === Number(byStagesID),
                      'by-stages-item-self-adaption':
                        byStagesSpecific.loan_check_data.periods.length < 3
                    }"
                    :key="index"
                    @click="selectByStages(Number(item.period))"
                  >
                    <text class="by-stages-item-name">
                      {{ item.repay_description }}
                    </text>
                    <text class="by-stages-item-tips">
                      {{ item.interest_description }}
                    </text>
                  </view>
                  <view style="min-width: 30rpx"></view>
                </scroll-view>
              </view>
              <!-- 弹窗 项目信息 @王爽 -->
              <view class="specification-dialog">
                <view
                  class="spu-bottom-popup-title"
                  style="padding-bottom: 30rpx"
                >
                  <text class="title">项目信息</text>
                </view>
                <Specification
                  v-if="pageInfo.chain_item"
                  :specification="pageInfo.chain_item"
                  :isPackage="
                    Number(
                      pageInfo.sku_detail_core_info.sku_all_dto.is_package
                    ) === 1
                      ? true
                      : false
                  "
                  @reportStat="reportStat"
                ></Specification>
              </view>
            </div>
            <div class="btnBox">
              <button
                v-if="isJoinGroupProduct"
                class="bottompop-btn spu-btn"
                @click="popBuy('joinGroup')"
              >
                <text>参加拼团</text>
                <text>￥{{ popupPriceInfo.price }}</text>
              </button>
              <div v-else-if="isGroup" class="groupDoubleBtn">
                <button
                  v-if="
                    popBuyFrom === 'chooseSpecification' ||
                    popBuyFrom === 'createGroup' ||
                    popBuyFrom === 'alone'
                  "
                  class="leftBtn"
                  @click="popBuy('alone')"
                >
                  <text v-if="popBuyFrom === 'alone'"
                    >单独购买<block v-if="pageInfo.sku_price_info.has_discount"
                      >到手价</block
                    ></text
                  >
                  <!-- <text>
                    <block v-if="pageInfo.sku_price_info.has_discount"
                      >优惠后</block
                    >￥{{
                      pageInfo.chain_extend &&
                      pageInfo.chain_extend.chain_add_c &&
                      pageInfo.chain_extend.chain_add_c.hidden_price_text
                        ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                        : pageInfo.sku_price_info.price_online
                    }}
                  </text> -->
                  <text>
                    ￥{{
                      pageInfo.chain_extend &&
                      pageInfo.chain_extend.chain_add_c &&
                      pageInfo.chain_extend.chain_add_c.hidden_price_text
                        ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                        : pageInfo.sku_price_info.price_online
                    }}
                  </text>
                  <!-- 单次核销礼计算后最终价格 @文鑫 -->
                  <text v-if="popBuyFrom !== 'alone'"
                    >单独购买<block
                      v-if="
                        pageInfo.sku_price_info.has_discount ||
                        pageInfo.sku_price_info.writeoff_cashback
                      "
                      >到手价</block
                    ></text
                  >
                </button>
                <button
                  v-if="
                    popBuyFrom === 'chooseSpecification' ||
                    popBuyFrom === 'createGroup'
                  "
                  :class="[
                    'rightBtn',
                    groupStatus !== 'begining' ? 'gray' : ''
                  ]"
                  @click="popBuy('createGroup')"
                >
                  <!-- <text v-if="popBuyFrom === 'createGroup'">
                    发起拼团<block
                      v-if="
                        pageInfo.sku_price_info.has_discount ||
                        pageInfo.sku_price_info.writeoff_cashback
                      "
                    >
                      到手价
                    </block>
                  </text> -->
                  <!-- <text
                    ><block v-if="pageInfo.sku_price_info.has_discount"
                      >优惠后</block
                    >￥{{
                      pageInfo.chain_extend &&
                      pageInfo.chain_extend.chain_add_c &&
                      pageInfo.chain_extend.chain_add_c.hidden_price_text
                        ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                        : pageInfo.chain_discount.sku_group_activity_data.group_data.pin_tuan_price
                    }}</text
                  > -->
                  <!-- <text v-if="popBuyFrom === 'createGroup'"> 发起拼团 </text> -->
                  <text
                    >￥{{
                      pageInfo.chain_extend &&
                      pageInfo.chain_extend.chain_add_c &&
                      pageInfo.chain_extend.chain_add_c.hidden_price_text
                        ? pageInfo.chain_extend.chain_add_c.hidden_price_text
                        : pageInfo.sku_price_info.price_final
                    }}</text
                  >
                  <!-- 单次核销礼计算后最终价格 @文鑫 -->
                  <text
                    v-if="
                      popBuyFrom === 'chooseSpecification' ||
                      popBuyFrom === 'createGroup'
                    "
                  >
                    发起拼团<block
                      v-if="
                        pageInfo.sku_price_info.has_discount ||
                        pageInfo.sku_price_info.writeoff_cashback
                      "
                      >到手价</block
                    >
                  </text>
                </button>
              </div>
              <button v-else class="bottompop-btn spu-btn" @click="popBuy">
                <text>立即购买</text>
                <text
                  ><block
                    v-if="
                      pageInfo.sku_price_info.has_discount ||
                      pageInfo.sku_price_info.writeoff_cashback
                    "
                    >到手价</block
                  >￥{{ popupPriceInfo.price }}
                </text>
              </button>
            </div>
          </div>
        </BottomModal>
        <!--加C弹窗-->
        <BottomModal
          :bottom="0"
          :showTitle="false"
          background="#f8f8f8"
          :visible="addCPopupVisible"
          @close="closeAddCPopup"
        >
          <img
            v-if="
              pageInfo.chain_extend &&
              pageInfo.chain_extend.chain_add_c &&
              pageInfo.chain_extend.chain_add_c.add_c_banner
            "
            :src="pageInfo.chain_extend.chain_add_c.add_c_banner"
            alt="加C弹窗内带二维码"
            :show-menu-by-longpress="true"
            mode="widthFix"
            class="addCPopupImage"
          />
        </BottomModal>
        <!--  -->
        <!-- 适用门店 -->
        <ApplicableStores
          v-if="isLocationFetched"
          class="applicable-stores"
          :userInfo="userInfo"
          :skuId="pageInfo.sku_id"
          :parentIngredientsId="ingredientsId"
          :isPackage="productTypeNumber === 9"
          @reportStat="reportStat"
          @changeCity="changeCity"
          @loadCity="loadCity"
        ></ApplicableStores>
        <!-- 购买须知 -->
        <PurchaseNotes
          v-if="pageInfo.chain_ext_data.purchase_notes"
          class="purchase-notes"
          :purchase_notes="pageInfo.chain_ext_data.purchase_notes"
          @reportStat="reportStat"
        ></PurchaseNotes>
        <!-- 说明弹窗 -->
        <!--        <Modal-->
        <!--          :title="tipsModal.title"-->
        <!--          :value="tipsModal.visible"-->
        <!--          :showCancel="false"-->
        <!--          @confirm="-->
        <!--            () => {-->
        <!--              tipsModal.visible = false;-->
        <!--            }-->
        <!--          "-->
        <!--          @cancel="-->
        <!--            () => {-->
        <!--              tipsModal.visible = false;-->
        <!--            }-->
        <!--          "-->
        <!--        >-->
        <!--          <template #body>-->
        <!--            <div class="rule-wrap">-->
        <!--              <rich-text :nodes="tipsModal.content"></rich-text>-->
        <!--            </div>-->
        <!--          </template>-->
        <!--        </Modal>-->
        <!-- 全部楼层弹窗 -->
        <div
          v-if="floorPopVisible"
          class="mask"
          @click="floorPopVisible = false"
        >
          <div class="floorPopMain" @click.stop>
            <div class="closebox" @click="floorPopVisible = false">
              <img
                src="https://static.soyoung.com/sy-design/56ro94fk2w7q1718159431202.png"
                alt=""
                class="close"
              />
            </div>
            <div class="title">可参与的拼团</div>
            <div class="popFloorCardBox">
              <block v-for="item in groupFloorInfo.list" :key="item.group_id">
                <PopFloorCard
                  :skuId="sku_id"
                  :cardData="item"
                  :activityId="groupFloorInfo.activity_id"
                />
              </block>
            </div>
            <div class="whiteMask"></div>
          </div>
        </div>
      </block>

      <Popup ref="rule-explanation-left">
        <view class="rule-explanation">
          <view class="rule-explanation-title">
            {{ tipsModal.title }}
            <image
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1726735613529.png"
              @click.stop="popupClose"
            ></image>
          </view>
          <view class="rule-explanation-body alignLeft">
            <text>{{ tipsModal.content }}</text>
          </view>
          <view class="rule-explanation-button">
            <button @click.stop="popupClose">{{ tipsModal.btnText }}</button>
          </view>
        </view>
      </Popup>

      <Popup ref="rule-explanation">
        <view class="rule-explanation">
          <view class="rule-explanation-title">
            {{ tipsModal.title }}
            <image
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1726735613529.png"
              @click.stop="popupClose"
            ></image>
          </view>
          <view class="rule-explanation-body">
            {{ tipsModal.content }}
          </view>
          <view class="rule-explanation-button">
            <button @click.stop="popupClose">确定</button>
          </view>
        </view>
      </Popup>

      <Popup ref="group-listing">
        <view class="group-listing">
          <view class="group-listing-header">
            <text>可参与的拼团</text>
            <image
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1726715721923.png"
              @click="closeGroupListing"
            ></image>
          </view>
          <scroll-view class="group-listing-body" :scroll-y="true">
            <view
              v-for="item in groupFloorInfo.list"
              class="group-listing-body-item"
              :key="item.group_id"
            >
              <PopFloorCard
                :skuId="sku_id"
                :cardData="item"
                :activityId="groupFloorInfo.activity_id"
              />
            </view>
          </scroll-view>
        </view>
      </Popup>
    </div>
  </page-meta>
</template>

<script>
// import { ref, getCurrentInstance, nextTick } from 'vue';
// import { onShow, onLoad } from '@dcloudio/uni-app';
import Vue from 'vue';
import { mapGetters, mapState } from 'vuex';
import SpuShare from '@/components/product/spu-share.vue';
// import CountDown from '@/components/product/countDown.vue';
import FloorCard from '@/components/product/floorCard.vue';
import PopFloorCard from '@/components/product/popFloorCard.vue';
import NewBanner from '@/components/NewBanner.vue';
import GroupCountDown from '@/components/product/groupCountDown.vue';
import BottomModal from '@/components/bottomModal.vue';
import Service from '@/components/product/service.vue';
import PurchaseNotes from '@/components/product/purchase-notes.vue';
import ApplicableStores from '@/components/product/applicable-stores.vue';
import Specification from '@/components/product/specification.vue';
import Popup from '@/components/uni/popup.vue';
import SubscribeMixins from '@/mixins/subscribe';
import mock from './mock.js';
// import CouponCard from '@/components/product/couponCard.vue';
import {
  apiGetSpuInfo,
  apiGetCouponList,
  apiReceiveAllowance,
  GetDistrictInfoByIp,
  apiGetGroupFloor,
  apiGroupCheckCanJoin,
  apiDecryptedData,
  couponReceiveCoupon,
  loanInfo
} from '@/api/productDetail';
// import Modal from '@/components/popup';
import NewCard from '@/components/coupon/new-card.vue';

export default {
  components: {
    NewCard,
    NewBanner,
    SpuShare,
    BottomModal,
    Service,
    PurchaseNotes,
    ApplicableStores,
    Specification,
    // Modal,
    // CouponCard,
    // CountDown,
    GroupCountDown,
    FloorCard,
    PopFloorCard,
    Popup
  },
  mixins: [SubscribeMixins],
  data() {
    return {
      isFirst: false,
      background: '',
      opacitySuction: 0,
      sku_id: 0,
      exp_ext: '',
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      productType: '1',
      // currentIndex: 1,
      gaussianBlur: false,
      wakeShareVisible: false, // 分享弹窗
      discountVisible: false, // 优惠券弹窗
      floorPopVisible: false, // 全部楼层弹窗
      selectionVisible: false, // 选择规格弹窗
      addCPopupVisible: false, // 选择规格弹窗
      hasAllReceived: false, // 优惠券全部领取
      blur: false,
      skuItem: {},
      pageInfo: {},
      swiperMap: {},
      tipsModal: {
        visible: false,
        title: '',
        content: ''
      },
      hasData: false,
      autoplay: true, // banner视频播放
      couponData: {}, // 红包弹窗列表
      pageLoadingStatus: false,
      pageScrollStyle: '',
      city_id: '',
      city_name: '',
      ingredientsId: '', // 适用城市组件中获取机构和城市需要的原料id
      groupFloorInfo: null,
      popBuyFrom: '', // 从哪唤起的选择规格弹窗 chooseSpecification 选择规格条 alone单独购买 createGroup 发起拼团
      group_id: '', // 参团团id
      joinGroupActivityId: '', // 参团活动id
      isLocationFetched: false, // 获取定位城市函数加载完成后再加载城市组件

      activity_id: '',
      getGidPromise: '',
      jumpComfirmFlag: false,
      product_id: '',
      product_type: '',
      showAddCPopoverImage: false, // 是否显示咨询有惊喜图片（用户滚动控制
      bottomPopoverClicked: false,
      projectIntroductionElementInformation: null, // 项目介绍元素位置信息
      tabAnchoring: 0, // 0 项目介绍 1 适用门店
      byStagesID: 0,
      byStages: {} // 分期数据
    };
  },
  onPageScroll(e) {
    if (e.scrollTop < 300) {
      this.background = `rgba(255,255,255,${e.scrollTop / 300})`;
    } else if (this.background !== '#ffffff') {
      this.background = `#ffffff`;
    }

    if (this.projectIntroductionElementInformation === null) {
      this.projectIntroductionElementInformation = {
        top: 0
      };
    }

    const startCallingOut =
      this.projectIntroductionElementInformation.top - 250;
    if (startCallingOut < e.scrollTop) {
      if ((e.scrollTop - startCallingOut) / 100 < 1) {
        this.opacitySuction = ((e.scrollTop - startCallingOut) / 300).toFixed(
          2
        );
      } else {
        this.opacitySuction = 1;
      }
    } else {
      this.opacitySuction = 0;
    }

    if (
      e.scrollTop >
      this.projectIntroductionElementInformation.top +
        this.projectIntroductionElementInformation.height
    ) {
      this.tabAnchoring = 1;
    } else {
      this.tabAnchoring = 0;
    }
  },
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState({
      userInfo: (state) => state.global.userInfo,
      productId: (state) => state.global.productId
    }),

    /**
     * @typedef {Object} productServiceGuaranteeDataObject
     * @property {string} text - 文本内容
     * @property {string} fontFamily - 字体
     * */

    /**
     * 产品服务保障内容
     * @returns {productServiceGuaranteeDataObject[]}
     * */
    productServiceGuaranteeData() {
      const data =
        this.pageInfo?.product_brand_service?.product_service_guarantee_data?.content?.split(
          ''
        );
      if (data?.length) {
        return data.map((item) => {
          return {
            text: item,
            fontFamily: /^[A-Za-z]+$/.test(item)
              ? 'Outfit-SemiBold'
              : 'PingFangSC-Regular'
          };
        });
      } else {
        return [];
      }
    },

    /**
     * 全部优惠券领取状态
     * @returns {boolean} - true
     * */
    CouponAllCollectionStatus() {
      return this.couponData?.coupon_list?.list?.length > 0
        ? !this.couponData.coupon_list.list.find((item) => !item.get_yn)
        : true;
    },

    /**
     * 根据商品类型返回不同分期数据
     * 破价类型商品不展示分期数据
     * */
    byStagesSpecific() {
      if (this.pageTitlePriceInfo.isHiddenPrice) {
        return null;
      } else if (this.isGroup && this.byStages?.group_chain_loan_info) {
        return this.byStages.group_chain_loan_info;
      } else if (this.byStages?.chain_loan_info) {
        return this.byStages.chain_loan_info;
      } else {
        return null;
      }
    },

    /**
     * 商品类型
     * @returns {number}
     * */
    productTypeNumber() {
      return Number(
        this.pageInfo?.sku_detail_core_info?.sku_all_dto?.product_type
      );
    },

    /**
     * @typedef {Object||null} beltInfoObject
     * @property {number} type - 1 拼团类型 2 特惠类型
     * @property {string} backgroundImage - 腰带背景图像链接
     * @property {string} titleIcon - 标题图像链接
     * @property {string} subtitle - 副标题内容
     * @property {string} subtitleSecondLevel - 副标题内容（立省...）
     * @property {string} rightColor - 右侧内容主题色
     * @property {number} countDown - 倒计时
     * @property {Object} rightTips - 右侧文本内容
     * @property {string} rightTips.before - 倒计时前方文本内容
     * @property {string} rightTips.after - 倒计时后方文本内容
     * @property {number} [buyStatus] - 拼团商品返回，拼团状态 1 未开始 2 进行中 3 已结束
     * */

    /**
     * 腰带条兼容拼团与特惠
     * @returns {beltInfoObject}
     * */
    beltInfo() {
      if (
        this.pageInfo?.chain_discount?.sku_group_activity_data &&
        this.isGroup
      ) {
        const data = {
          type: 1,
          backgroundImage:
            'https://static.soyoung.com/sy-pre/20250527-190415-1748340600633.png',
          beltColor: '#FDDCFF',
          titleIcon:
            'https://static.soyoung.com/sy-design/kgmpik5wigpe1726735613550.png',
          subtitle:
            this.pageInfo?.chain_discount?.sku_group_activity_data
              ?.promotion_data?.promotion_text,
          subtitleSecondLevel: `立省￥${this.pageInfo?.chain_discount?.sku_group_activity_data?.cut_price}`,
          rightColor: '#030303',
          countDown: 0,
          rightTips: {
            after: '',
            before: ''
          },
          buyStatus: Number(
            this.pageInfo?.chain_discount?.sku_group_activity_data
              ?.promotion_data?.buy_status
          )
        };

        // 判断拼团是否未开始 或 进行中
        if (
          Number(
            this.pageInfo?.chain_discount?.sku_group_activity_data
              ?.promotion_data?.buy_status
          ) === 1
        ) {
          // 未开始
          // 判断距离开始是否仅剩两小时
          if (
            Number(
              this.pageInfo?.chain_discount?.sku_group_activity_data
                ?.promotion_data?.countdown_time
            ) > 0
          ) {
            data.rightTips.before = '距离开始仅剩';
            data.countDown = Number(
              this.pageInfo?.chain_discount?.sku_group_activity_data
                ?.promotion_data?.countdown_time
            );
          } else {
            data.rightTips.after = `${this.pageInfo?.chain_discount?.sku_group_activity_data?.promotion_data?.start_date}${this.pageInfo?.chain_discount?.sku_group_activity_data?.promotion_data?.start_time}开抢`;
          }
        } else if (
          Number(
            this.pageInfo?.chain_discount?.sku_group_activity_data
              ?.promotion_data?.buy_status
          ) === 2
        ) {
          // 进行中
          data.countDown = Number(
            this.pageInfo?.chain_discount?.sku_group_activity_data
              ?.promotion_data?.countdown_time
          );
          data.rightTips.after = '后结束';
        }

        return data;
      } else if (
        this.pageInfo?.sku_price_info?.has_discount ||
        this.pageInfo?.sku_price_info?.writeoff_cashback
      ) {
        // !(this.pageInfo?.chain_discount?.max_discount?.discount_type <= 0)
        const data = {
          type: 2,
          backgroundImage:
            'https://static.soyoung.com/sy-pre/20250527-190202-1748340600633.png',
          beltColor: '#FFE1D4',
          titleIcon:
            this.pageInfo?.chain_discount?.max_discount?.day <= 7 &&
            this.pageInfo?.chain_discount?.max_discount?.day > 0
              ? 'https://static.soyoung.com/sy-design/uykswugl9hci1725507935748.png'
              : 'https://static.soyoung.com/sy-pre/20241009-164351-1728461400633.png',
          subtitle: '',
          subtitleSecondLevel: '',
          rightColor: '#ffffff',
          countDown: 0,
          rightTips: {
            before: '',
            after: ''
          }
        };

        if (
          this.pageInfo?.chain_discount?.max_discount?.day <= 7 &&
          this.pageInfo?.chain_discount?.max_discount?.day > 0
        ) {
          if (
            !this.isLogin ||
            this.pageInfo?.chain_discount?.max_discount?.get_yn === '0'
          ) {
            data.rightTips.after = `优惠仅剩${this.pageInfo?.chain_discount?.max_discount?.day}天`;
          } else {
            data.countDown = Number(
              this.pageInfo?.chain_discount?.max_discount?.deadline
            );
            data.rightTips.after = '后结束';
          }
        }

        return data;
      }

      return null;
    },

    /**
     * @typedef {Object} pageTitlePriceInfoObject
     * @property {number} type - 0 售价 1 拼团 2 特惠
     * @property {string} sold_cnt - 已售信息
     * @property {string} product_title - 商品标题
     * @property {string} chain_promote_info - 商品副标题，连锁推广信息
     * @property {string} description - 商品可分期文案
     * @property {string||number} price - 商品价格，type = 0 售价，type = 1 拼团价，type = 2 券后价
     * @property {string||number} underlinedPrice - 划线价，type = 0 无划线价，type = 1 || type = 2 券前价
     * @property {string} consultPrice - 咨询好价icon
     * @property {boolean} isHiddenPrice - 是否为破价商品
     * */

    /**
     * 商品名称价格区域数据整合
     * */
    pageTitlePriceInfo() {
      const data = {
        product_title: this.pageInfo?.sku_base_info?.product_title || '',
        chain_promote_info:
          this.pageInfo?.sku_base_info?.chain_promote_info || '',
        sold_cnt: this.pageInfo?.sku_base_info?.sold_info?.sold_cnt_str || '',
        description: '',
        type: 0,
        price: '',
        underlinedPrice: '',
        consultPrice: this.pageInfo?.chain_extend?.chain_add_c?.price_add_c_tag,
        isHiddenPrice: false,
        pain_tag_info: this.pageInfo?.sku_base_info?.pain_tag_info,
        price_discount_summary: ''
      };

      // 判断商品是否可分期
      if (
        this.pageInfo?.chain_loan_info?.support_loan &&
        this.pageInfo?.chain_loan_info?.description
      ) {
        data.description = this.pageInfo.chain_loan_info.description;
      }

      // 判断商品类型 及 根据商品类型赋值商品价格
      if (
        this.pageInfo?.chain_discount?.sku_group_activity_data &&
        this.isGroup
      ) {
        data.type = 1;
        // data.price =
        //   this.pageInfo?.chain_discount?.sku_group_activity_data?.group_data?.pin_tuan_price;
        data.price = this.pageInfo?.sku_price_info?.price_final; // 单次核销礼计算后最终价格 @文鑫
        data.underlinedPrice =
          this.pageInfo?.sku_price_info?.price_online_before;
      } else if (
        this.pageInfo?.sku_price_info?.has_discount ||
        this.pageInfo?.sku_price_info?.writeoff_cashback
      ) {
        data.type = 2;
        data.price = this.pageInfo?.sku_price_info?.price_online;
        data.price = this.pageInfo?.sku_price_info?.price_final; // 单次核销礼计算后最终价格 @文鑫
        data.underlinedPrice =
          this.pageInfo?.sku_price_info?.price_online_before;
      } else {
        data.price = this.pageInfo?.sku_price_info?.price_online_before;
      }

      // 判断是否为破价商品，破价类型优先级最高
      if (this.pageInfo?.chain_extend?.chain_add_c?.hidden_price_text) {
        data.isHiddenPrice = true;
        data.price = this.pageInfo.chain_extend.chain_add_c.hidden_price_text;
      }

      return data;
    },
    writeCashbackInfo() {
      if (
        this.pageInfo?.sku_price_info?.writeoff_cashback
          ?.max_writeoff_cashback_amount > 0
      ) {
        return {};
      } else {
        return this.pageInfo?.sku_price_info?.writeoff_cashback || {};
      }
    },
    /**
     * @typedef {Object} popupPriceInfoObject
     * @property {string} priceTypeName - 价格类型名称 售价、拼团、券后
     * @property {string||number} price - 商品价格
     * */

    /**
     * 项目底弹窗商品信息
     * 根据用户点击触发点区分价格，目前共 3 处触发点，具体详见 popBuyFrom 数据
     * @returns {popupPriceInfoObject}
     * */
    popupPriceInfo() {
      const data = {
        price: '',
        priceTypeName: ''
      };

      switch (this.popBuyFrom) {
        case 'alone':
          if (this.pageInfo?.sku_price_info?.has_discount) {
            data.priceTypeName = '到手价';
            data.price = this.pageInfo?.sku_price_info?.price_online;
          } else {
            data.priceTypeName = '售价';
            data.price = this.pageInfo?.sku_price_info?.price_online_before;
          }
          break;
        case 'createGroup':
          data.priceTypeName = '拼团';
          data.price =
            this.pageInfo?.chain_discount?.sku_group_activity_data?.group_data?.pin_tuan_price;
          break;
        default:
          switch (this.pageTitlePriceInfo.type) {
            case 0:
              data.priceTypeName = '售价';
              break;
            case 1:
              data.priceTypeName = '拼团';
              break;
            case 2:
              data.priceTypeName = '到手价';
              break;
          }
          data.price = this.pageTitlePriceInfo.price;
          break;
      }

      // 判断是否为破价商品，破价类型优先级最高
      if (this.pageInfo?.chain_extend?.chain_add_c?.hidden_price_text) {
        data.price = this.pageInfo.chain_extend.chain_add_c.hidden_price_text;
      }

      return data;
    },

    // 活动状态
    groupStatus() {
      if (
        !this.pageInfo ||
        !this.pageInfo.chain_discount ||
        !this.pageInfo.chain_discount.sku_group_activity_data ||
        !this.pageInfo.chain_discount.sku_group_activity_data.promotion_data
      ) {
        return '';
      }
      let status = '';
      if (
        this.pageInfo.chain_discount.sku_group_activity_data.promotion_data
          .buy_status === '2'
      ) {
        status = 'begining'; // 活动已开始
      } else if (
        this.pageInfo.chain_discount.sku_group_activity_data.promotion_data
          .buy_status === '1'
      ) {
        if (
          this.pageInfo.chain_discount.sku_group_activity_data.promotion_data
            .countdown_time === '0'
        ) {
          status = 'longTimeBegin'; // 2小时以后小于24小时才开始
        } else {
          status = 'fastBegin'; // 2小时内开始
        }
      } else {
        status = 'ending'; // 活动结束
      }
      return status;
    },
    // 商品是否可拼团
    isGroup() {
      return !!this.groupStatus && this.groupStatus !== 'ending';
    },
    // 参团商详中这个品是否可参团
    isJoinGroupProduct() {
      if (this.isGroup) {
        const groupData =
          this.pageInfo.chain_discount.sku_group_activity_data.group_data;
        const promotionData =
          this.pageInfo.chain_discount.sku_group_activity_data.promotion_data;
        return (
          +this.joinGroupActivityId === +groupData.activity_id &&
          groupData.join_visible == 1 &&
          promotionData.buy_status == 2
        );
      } else {
        return false;
      }
    },
    // 分享参数
    wakeShareOpts() {
      return {
        title: this.pageTitlePriceInfo?.product_title || '',
        cover: this.pageInfo.share_info
          ? this.pageInfo?.share_info?.share_image
          : '',
        subtitle: this.pageInfo?.share_info?.share_desc || '',
        sku_id: +this.sku_id,
        pusher_id: this.pusher_id
      };
    }
  },
  methods: {
    // newBannerExpose(data) {
    //   console.log(data);
    //   console.log(this);
    // },
    selectByStages(params) {
      this.byStagesID = params;
      if (params !== 0) {
        if (this.isJoinGroupProduct) {
          this.popBuy('joinGroup');
        } else if (
          this.isGroup &&
          (this.popBuyFrom === 'chooseSpecification' ||
            this.popBuyFrom === 'createGroup')
        ) {
          this.popBuy('createGroup');
        } else {
          this.popBuy('alone');
        }
      }
    },
    /**
     * 滑动页面至某元素
     * @param {string} elementIdentification - 元素标识
     * */
    async pageSlide(elementIdentification) {
      const [{ top: pageTop }, { top }] = await Promise.all([
        this.$queryFirstNode('.page'),
        this.$queryFirstNode(elementIdentification)
      ]);
      uni.pageScrollTo({
        scrollTop: top - pageTop - this.menuRect.bottom - 50
      });
    },
    popupClose() {
      this.$refs['rule-explanation'].close('bottom');
      this.$refs['rule-explanation-left'].close('bottom');
    },
    /**
     * 领取红包
     * */
    async couponTap(type, params = {}) {
      if (!this.isLogin) {
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        if (!isAuth) return;
        await this.getCouponList();
      }
      let coupon_ids = [];
      if (type === 'single') {
        coupon_ids.push(params.id);
      } else if (type === 'all') {
        coupon_ids = this.couponData.coupon_list.list.map((item) => item.id);
      }

      try {
        const subList = [];
        const response = await couponReceiveCoupon({
          coupon_ids: coupon_ids.join(',')
        });
        if (response.errorCode !== 0) {
          uni.showToast({
            title: response.errorMsg,
            icon: 'none'
          });
          return;
        }
        const keyList = Object.keys(response.responseData);
        let toastCoupon = true;
        for (let item of keyList) {
          const valueData = response.responseData[item];
          if (Number(valueData.code) !== 0) {
            if (toastCoupon) {
              toastCoupon = false;
              uni.showToast({
                title: valueData.msg,
                icon: 'none'
              });
            }
          } else {
            subList.push(Number(item));
            for (let i of this.couponData.coupon_list.list) {
              console.log(i);
              if (Number(i?.id) === Number(item)) {
                i.get_yn = true;
              }
            }
          }
        }
        if (subList.length) {
          // 订阅消息
          await this.createGroupBySub(
            [
              'F9tgwtgH228AvSbaXVGVAQ2dIrOR6LrapCWApcvv1Ts',
              'du6O-P5ekgS7sgunEZ7jZRxeXnc47hhYC0IKWcz_P_w',
              '9Z6qT2uSf1JM5UiiSY4KZjh_a8ozTolN_sS9jc-pr8w'
            ],
            subList
          );
        }

        // await this.getCouponList();
        console.log('【优惠券底弹窗】', response);
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 红包 card 数据组合
     * */
    couponDataCombination(params) {
      return {
        coupon_info: {
          type: params.type,
          discount_value: params.discount_value,
          discount_rate: params.discount_rate,
          min_amount: params.min_amount,
          name: params.name
        },
        basic_desc: {
          coupon_tag_name: params.coupon_tag_name,
          coupon_status_name: params.coupon_status_name
        },
        get_info: {
          use_time_notice: params.use_time_notice
        },
        extend_info: {
          desc: params.desc
        }
      };
    },
    beltTap() {
      // TODO @风尘
      this.createGroupBySub(
        [
          'cWqU-ViIiMqghwa1oNYJbK6qrArgr0iEY5aCGAfIXlA',
          'xHGxtKz_okVCgkvY6CEV8HH9HtiW1NPoSAKeE-8VKnE',
          'VgIwA_nZy-Fh6VZ_X1YNg89VZr2IHNIhqeOGGayj1yM'
        ],
        []
      );
      if (
        this.pageInfo.chain_extend &&
        this.pageInfo.chain_extend.chain_add_c &&
        this.pageInfo.sku_price_info.writeoff_cashback
      ) {
        return;
      }
      this.showCoupon();
      // if (this.beltInfo.type === 1) {
      //   this.goRule();
      // } else if (this.beltInfo.type === 2) {

      // }
    },
    // 格式化入参
    formatEnterOptions(options) {
      // 正常情况的入参
      if (!options.scene) {
        return options;
      }

      // 驻场码进入
      const sign = query(options.scene, 'id');
      if (sign) {
        const splitArray = sign.split('_');
        const id = splitArray?.[0] || '';
        if (!id) {
          this.catchError('驻场码解析错误');
          return null;
        }
        return {
          id
        };
      }
      return null;
    },
    /**
     * 获取当前群 GID
     * @returns Promise(string)
     */
    async getShareInfo() {
      const { scene, shareTicket, chatType } = uni.getEnterOptionsSync();
      console.log('拼团入参', shareTicket, scene, chatType);
      if (!shareTicket) return '';
      let encryptedData, iv;
      try {
        const res = await this.wxGetShareInfo(shareTicket);
        encryptedData = res.encryptedData;
        iv = res.iv;
      } catch (error) {
        console.log('wxGetShareInfo', error);
        uni.$log(error, 'error');
        return '';
      }
      // "{"openGId":"tGhb0V0S8H4wcHw9TkdI_9sA-4Qu0","watermark":{"timestamp":1684481409,"appid":"wx83a3c7bd850395ed"}}"
      console.log('拼团密钥和向量', encryptedData, iv);
      const responseData = await apiDecryptedData(
        this.userInfo.sessionKey,
        encryptedData,
        iv
      );
      console.log('拼团DecodeWxData解密', responseData);
      // 当前微信群的openGId
      return responseData?.openGId || '';
    },

    groupBuy() {
      if (this.groupStatus !== 'begining') return;
      this.buy('createGroup');
    },
    goRule() {
      this.reportStat('sy_wxtuan_tuan_product_new:pintuan_rule_click');
      this.$toH5('https://m.soyoung.com/tmwap21727');
    },
    handleHome() {
      uni.switchTab({
        url: '/pages/index'
      });
    },
    // 检查是否可拼团
    async checkCanJoinGroup() {
      // const opengid = await this.getGidPromise;
      // 如果是加入拼团
      const { errorCode, errorMsg } = await apiGroupCheckCanJoin({
        sku_id: this.sku_id_remove_redundant_character(this.sku_id),
        group_id: this.group_id,
        opengid: ''
      });
      return { errorCode, errorMsg };
    },
    handleBack() {
      if (this.isFirst) {
        this.handleHome();
      } else {
        uni.navigateBack();
      }
    },
    showSelection(from) {
      this.popBuyFrom = from;
      this.selectionVisible = true;
      this.reportStat('sy_wxtuan_tuan_product_new:pintuan_rule_click', {
        style: 0
      });
      this.disablePageScroll();
    },
    hideSelection() {
      this.selectionVisible = false;
      this.enablePageScroll();
    },
    showAllFloor() {
      // this.floorPopVisible = true;
      this.$refs['group-listing'].open('bottom');
    },
    closeGroupListing() {
      this.$refs['group-listing'].close('bottom');
    },
    async jumpOrder(from) {
      const product_module = {
        [this.sku_id]: {
          pid: Number(this.sku_id),
          amount: 1,
          times_card: 1,
          action: 'deposit_coupon',
          deposit_coupon_code_id: -1,
          new_deposit_coupon_code_id: -1,
          pirce_online:
            this.pageInfo.sku_detail_core_info.price_info.price_online,
          reduce_list: this.pageInfo.sku_detail_core_info.price_info.reduce_list
        }
      };
      const user_module = {
        is_selected: 1
      };
      const order_module = {
        allowance_id: -1,
        payment_channel: this.byStagesID !== 0 ? 27 : -1,
        installment_type: this.byStagesID !== 0 ? this.byStagesID : -1
      };
      if (from === 'createGroup') {
        product_module[this.sku_id].is_group_mode = 1;
        order_module.group_id = 0;
        // 订阅消息
        // await this.createGroupBySub(
        //   ['13Atn_516_McSwwnsra6zuV_X8-FTWkH2r102RP8CME'],
        //   [this.sku_id]
        // );
      } else if (from === 'joinGroup') {
        product_module[this.sku_id].is_group_mode = 1;
        order_module.group_id = this.group_id;
      } else {
        product_module[this.sku_id].is_group_mode = 0;
        order_module.group_id = 0;
      }
      let url = `/packageOrder/confirmOrder?product_module=${JSON.stringify(
        product_module
      )}&user_module=${JSON.stringify(
        user_module
      )}&order_module=${JSON.stringify(order_module)}&city_id=${
        this.city_id
      }&activity_id=${this.activity_id}`;
      this.$bridge({
        url
      });
    },
    async goComfirmOrder(from) {
      if (this.isGroup && from !== 'alone') {
        // 拼团商品且非单独购买要先确认是否能拼
        if (this.jumpComfirmFlag) return;
        this.jumpComfirmFlag = true;
        const checkJoinRes = await this.checkCanJoinGroup();
        const { errorCode, errorMsg } = checkJoinRes;
        switch (errorCode) {
          case 0:
          case 200:
            this.jumpOrder(from);
            break;
          case 11:
            uni.showModal({
              title: '提示',
              content: errorMsg,
              showCancel: false
            });
            this.jumpComfirmFlag = false;
            break;
          case 1:
          case 2:
          case 3:
          case 4:
          case 8:
            uni.showToast({
              title: errorMsg,
              icon: 'none'
            });
            setTimeout(() => this.jumpOrder(from), 3000);
            break;
          default:
            uni.showToast({
              title: errorMsg,
              icon: 'none'
            });
            this.jumpComfirmFlag = false;
            this.pageInit();
        }
      } else {
        this.jumpOrder(from);
      }
    },
    // 弹窗按钮购买点击
    async popBuy(from) {
      if (this.groupStatus !== 'begining' && from === 'createGroup') return;
      const login = await this.checkSession();
      if (!login) return;
      this.goComfirmOrder(from);
    },
    // 底部按钮购买点击
    async buy(from) {
      // 就一个类可以选
      const isSingleProduct =
        this.pageInfo.base_sku_list && this.pageInfo.base_sku_list.length <= 1;
      let trackName = '';
      const trackInfo = {
        id: this.sku_id,
        status: isSingleProduct ? 1 : 0
      };
      switch (from) {
        case 'alone':
          trackName = 'sy_wxtuan_tuan_product_info:dandugoumai_click';
          trackInfo.activity_id = this.activity_id;
          trackInfo.activity_type =
            this.groupStatus === 'begining'
              ? 2
              : this.groupStatus === 'ending'
              ? 3
              : 1; // 1.未开始2.进行中3.活动结束
          break;
        case 'createGroup':
          trackName = 'sy_wxtuan_tuan_product_info:faqipintuan_click';
          trackInfo.activity_id = this.activity_id;
          trackInfo.activity_type =
            this.groupStatus === 'begining'
              ? 2
              : this.groupStatus === 'ending'
              ? 3
              : 1; // 1.未开始2.进行中3.活动结束
          break;
        case 'joinGroup':
          trackName = 'sy_wxtuan_tuan_product_info:cantuan_click';
          trackInfo.group_id = this.group_id;
          trackInfo.activity_type =
            this.groupStatus === 'begining'
              ? 2
              : this.groupStatus === 'ending'
              ? 3
              : 1; // 1.未开始2.进行中3.活动结束
          break;
        default:
          trackName = 'sy_wxtuan_tuan_product_new:selected_click';
          break;
      }
      this.reportStat(trackName, trackInfo);
      const login = await this.checkSession();
      if (!login) return;
      if (isSingleProduct) {
        // 去订单详情页
        this.goComfirmOrder(from);
      } else {
        this.showSelection(from);
      }
    },
    // 领取津贴
    async onReceiveJT(item) {
      this.reportStat('sy_wxtuan_tuan_product:red_pop_get_click');

      const login = await this.checkSession();
      if (!login) return;
      const { errorCode, errorMsg, responseData } = await apiReceiveAllowance({
        id: item.id
      });
      if ((+errorCode === 200 || +errorCode === 0) && responseData) {
        this.pageInit();
      } else {
        uni.showModal({
          content: errorMsg,
          showCancel: false
        });
      }
      // if ((+errorCode === 200 || +errorCode === 0) && responseData) {
      //   return responseData;
      // } else {
      //   uni.$log('优惠券列表接口', params, errorMsg, 'error');
      //   return null;
      // }
    },
    // 改变loading
    changePageLoadingStatus(status) {
      this.pageLoadingStatus = status;
    },
    wxProfile() {
      return new Promise((resole, reject) => {
        wx.getUserProfile({
          lang: 'zn_CN',
          desc: '用于完善信息',
          success: (res) => {
            console.log(res, 'profile');
            resole(res);
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },
    async checkSession() {
      if (!this.isLogin && !this.pageLoadingStatus) {
        // 没有微信头像调起授权
        this.pageLoadingStatus = true;
        const res = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return false;
        });
        this.pageLoadingStatus = false;
        if (res) {
          return true;
        } else {
          return false;
        }
      }
      return true;
    },
    to1(url) {
      this.reportStat('sy_wxtuan_tuan_product_info:pinxuan_top_click');
      this.$bridge({
        url: '/pages/h5?url=' + encodeURIComponent(url)
      });
    },
    // 图片预览
    onPreviewBanner(url, index) {
      const imgList =
        this.pageInfo.sku_media && this.pageInfo.sku_media.image_list
          ? this.pageInfo.sku_media.image_list.filter((item) => item.img_url)
          : [];
      uni.previewImage({
        current: url.img_url,
        urls: imgList.map(({ img_url }) => img_url)
      });
      this.reportStat('sy_wxtuan_tuan_product:head_pic_click', {
        serial_num: index + 1
      });
    },
    onPreview(url) {
      uni.previewImage({
        current: url,
        urls: [url]
      });
      this.reportStat('sy_wxtuan_tuan_product_new:guige_pop_sku_img_click');
    },
    reportStat(info, ext) {
      this.$reportData({
        info,
        ext: {
          product_id: this.product_id,
          type: this.product_type,
          ...ext
        }
      });
    },
    load() {
      this.reportStat('sy_wxtuan_tuan_product:head_pic_exposure', {
        serial_num: 1
      });
    },
    onPlay() {
      this.autoplay = false;
    },
    onPause() {
      this.autoplay = true;
    },
    showFQmodel() {
      this.$refs['rule-explanation'].open('bottom');
      this.tipsModal = {
        visible: true,
        title: '规则说明',
        content: this.pageInfo.chain_loan_info.tips
      };
      this.reportStat('sy_wxtuan_tuan_product_info:fenqi_click');
    },
    showPainModel(info) {
      this.reportStat('sy_chain_store_other_product_info:pain_tag_click', {
        item_content: info.name,
        product_id: this.sku_id,
        spu_id: this.pageInfo.sku_base_info.product_id
      });
      if (info.jump_url) {
        if (info.jump_url.indexOf('http') === 0) {
          this.$toH5(info.jump_url);
        } else {
          this.$bridge({
            url: info.jump_url
          });
        }
      } else {
        this.$refs['rule-explanation-left'].open('bottom');
        this.tipsModal = {
          visible: true,
          title: info.name,
          btnText: '确认',
          content: info.desc
        };
      }
    },
    showXLmodel() {
      this.tipsModal = {
        visible: true,
        title: '规则说明',
        content:
          this.pageInfo.sku_base_info.sold_info.yuyue_standard_explain_str
      };
      this.$refs['rule-explanation'].open('bottom');
    },
    pageInit(sku_id) {
      this.getSpuData(sku_id);
      this.getCouponList(sku_id);
      this.getGroupFloor(sku_id);
      this.getLoanInfo(sku_id);
    },
    async initObserver() {
      // 根据价格区域，显隐加 C 气泡
      const intersectionObserver = uni.createIntersectionObserver();
      intersectionObserver.relativeToViewport({
        bottom: 10
      });
      intersectionObserver.observe(`.productInfo`, (res) => {
        console.log(res);
        this.showAddCPopoverImage = res.intersectionRatio === 0;
      });
      console.log('intersectionObserver', intersectionObserver);
    },
    productIntroduceObserve() {
      // 查看【项目介绍Tab】距页面顶部距离
      if (this.projectIntroductionElementInformation) {
        return;
      }

      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('.productIntroduce')
          .boundingClientRect((rect) => {
            console.log('createSelectorQuery -->', rect);
            this.projectIntroductionElementInformation = rect;
          })
          .exec();
      }, 300);
    },
    async getGroupFloor(sku_id) {
      const { errorCode, errorMsg, responseData } = await apiGetGroupFloor({
        sku_id: this.sku_id_remove_redundant_character(sku_id || this.sku_id),
        activity_id: this.joinGroupActivityId
      });
      if (+errorCode === 200 || +errorCode === 0) {
        this.groupFloorInfo = responseData;
      } else {
        console.log(errorMsg, 'errorMsg');
        // uni.showModal({
        //   content: errorMsg,
        //   showCancel: false
        // });
      }
    },
    /**
     * @since v10.0.10 去除冗杂信息，换行，空格
     * @param {string} sku_id
     * @returns {string}
     * */
    sku_id_remove_redundant_character(sku_id) {
      try {
        if (typeof sku_id === 'string') {
          return sku_id
            .replace(/<\/?.+?>/g, '')
            .replace(/[\r\n]/g, '')
            .replace(/(^\s*)|(\s*$)/g, '');
        } else {
          return sku_id;
        }
      } catch (error) {
        console.error(
          '【sku_id_remove_redundant_character 去除sku_id冗杂数据失败】',
          error
        );
        return sku_id;
      }
    },
    async getLoanInfo(sku_id) {
      const { errorCode, errorMsg, responseData } = await loanInfo({
        sku_id: this.sku_id_remove_redundant_character(sku_id || this.sku_id),
        group_id: this.group_id
      });
      if (errorCode === 0 && responseData) {
        console.log('分期数据', responseData);
        this.byStages = responseData;
      } else {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    async getSpuData(sku_id) {
      const { errorCode, errorMsg, responseData } = await apiGetSpuInfo({
        sku_id: this.sku_id_remove_redundant_character(sku_id || this.sku_id),
        group_id: this.group_id,
        city_id: this.city_id,
        cityId: this.city_id
      });
      console.log('responseData', mock);
      // const { errorCode, errorMsg, responseData } = mock;
      if ((+errorCode === 200 || +errorCode === 0) && responseData) {
        this.pageInfo = responseData;
        // this.pageInfo.chain_discount.sku_group_activity_data.promotion_data.buy_status = '1'
        // this.pageInfo.chain_discount.sku_group_activity_data.promotion_data.countdown_time = '0'
        this.sku_id = responseData.sku_id;
        this.product_type =
          responseData.sku_detail_core_info?.sku_all_dto?.product_type;
        this.product_id =
          responseData.sku_base_info && responseData.sku_base_info.product_id;
        const sku_group = responseData.chain_discount.sku_group_activity_data;
        if (sku_group) {
          this.activity_id = sku_group.group_data.activity_id;
          if (!this.popBuyFrom) this.popBuyFrom = 'chooseSpecification';
        } else {
          this.popBuyFrom = '';
        }
        if (this.pageInfo.base_sku_list) {
          this.skuItem = this.pageInfo.base_sku_list.find(
            (item) => item.is_selected === '1'
          );
        }
        // 获取原料id
        if (+responseData.sku_detail_core_info.sku_all_dto.product_type === 9) {
          this.ingredientsId =
            responseData.sku_detail_core_info.sku_all_dto.materials[0].sku_id;
        } else if (
          +responseData.sku_detail_core_info.sku_all_dto.product_type === 8
        ) {
          this.ingredientsId =
            responseData.sku_detail_core_info.sku_all_dto.materials[0].sku_id;
        } else if (
          +responseData.sku_detail_core_info.sku_all_dto.product_type === 7
        ) {
          this.ingredientsId = responseData.sku_id;
        }
        if (+responseData.has_new_discount == 1) {
          this.$store.dispatch('global/setProductId', this.sku_id);
        }
        this.$nextTick(() => {
          this.productIntroduceObserve();
          console.log('isJoinGroupProduct -->', this.isJoinGroupProduct);
        });
        if (!this.hasData) {
          this.$nextTick(() => {
            this.initExposure();
          });
        }
        this.hasData = true;
        this.$nextTick(() => {
          this.initObserver();
        });
      } else {
        uni.showModal({
          content: errorMsg,
          showCancel: false
        });
      }
    },
    async getCouponList(sku_id) {
      const res = await apiGetCouponList({
        sku_id: this.sku_id_remove_redundant_character(sku_id || this.sku_id)
      });
      if (res) {
        this.couponData = res;
      }
    },
    async onAddCPriceClick() {
      if (!this.isLogin) {
        const hasLogin = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        if (hasLogin) {
          this.openAddCPopup({ position: 1 });
        }
      } else {
        // +C 的
        this.openAddCPopup({ position: 1 });
      }
    },
    // 点击咨询
    async onZx() {
      if (
        this.pageInfo.chain_extend &&
        this.pageInfo.chain_extend.chain_add_c
      ) {
        // +C 的
        let hasLogin = false;
        if (!this.isLogin) {
          hasLogin = await this.$login().catch((msg) => {
            if (msg !== '页面退回') {
              uni.showModal({
                title: '登录异常',
                content: msg,
                showCancel: false
              });
            }
            return false;
          });
        }
        if (!this.isLogin) {
          if (hasLogin) {
            this.openAddCPopup({ position: 2 });
            await Vue.$request({
              url: `/syGroupBuy/chain/product/setProductPriceBreakingFreq?sku_id=${this.sku_id}`
            });
            this.bottomPopoverClicked = true;
          }
        } else {
          this.openAddCPopup({ position: 2 });
          await Vue.$request({
            url: `/syGroupBuy/chain/product/setProductPriceBreakingFreq?sku_id=${this.sku_id}`
          });
          this.bottomPopoverClicked = true;
        }
      } else {
        // 正常跳咨询
        // if (this.skeleton) return;
        this.reportStat('sy_wxtuan_tuan_product_info:zixun_click');
        // 判断是否已加C，加C后跳转到客服会话页
        // this.$toKefuDialog({
        //   source:
        //     'sy_yx_mini_program_private_msg_customer_service_detail_consult',
        //   pid: this.sku_id,
        //   unJoinFn: () => {
        //     // 未加C时执行
        //     this.$bridge({
        //       url: `/packageAccount/consult?type=2&spuid=${this.pageInfo.sku_base_info.product_id}&skuid=${this.sku_id}&scene=1`
        //     });
        //   }
        // });
        this.$reportData({
          info: 'sy_yx_mini_program_private_msg_customer_service_detail_consult',
          ext: {}
        });
        this.$bridge({
          url: `/packageAccount/consult?type=2&spuid=${this.pageInfo.sku_base_info.product_id}&skuid=${this.sku_id}&scene=1`
        });
      }
    },
    // 弹窗选sku
    selectSku(item) {
      this.reportStat('sy_wxtuan_tuan_product_new:guige_switch_click', {
        content: item.title,
        id: item.sku_id
      });
      if (item.is_selected === '1') return;
      this.skuItem = item;
      this.pageInit(item.sku_id);
      this.getLoanInfo(item.sku_id);
    },
    onSelectionClose() {
      this.hideSelection();
      this.reportStat('sy_wxtuan_tuan_product_info:guige_pop_close_click');
    },
    openSkuSelection() {
      this.reportStat('sy_wxtuan_tuan_product_info:guige_click');
      if (
        this.pageInfo.base_sku_list &&
        this.pageInfo.base_sku_list.length <= 1
      )
        return;
      this.showSelection('chooseSpecification');
    },
    closeAddCPopup() {
      this.addCPopupVisible = false;
    },
    // position 1-商详价格区域，2-底部咨询按钮
    openAddCPopup({ position }) {
      this.addCPopupVisible = true;
      this.$reportData({
        info:
          position === 1
            ? 'sy_chain_store_other_product_info:price_tag_click'
            : 'sy_chain_store_other_product_info:consult_tag_click',
        ext: {
          product_id: this.sku_id
        }
      });
    },
    hosScroll(e) {
      const top = e?.detail?.scrollTop;
      // 再往下走
      if (top - this.lastTop > 0) {
        if (top > 5) {
          this.$emit('setTTop', true);
        }
      }
      this.lastTop = top;
    },
    imgChange(currentItem, currentIndex) {
      console.log('this.swiperMap', this);
      console.log('currentIndex', currentIndex);
      if (!(currentIndex in this.swiperMap)) {
        this.reportStat('sy_wxtuan_tuan_product:head_pic_exposure', {
          serial_num: currentIndex + 1
        });
        this.swiperMap[currentIndex] = 1;
      }
      // this.currentIndex = e.detail.current + 1;
    },
    showShareBar() {
      this.wakeShareVisible = true;
      this.reportStat('sy_wxtuan_op_product_info:share_button_click');
    },
    showShareBarAuto() {
      this.$nextTick(() => {
        this.wakeShareVisible = true;
      });
      this.hasAutoShareOpened = true;
    },
    async showCoupon() {
      this.discountVisible = true;
      this.reportStat('sy_wxtuan_tuan_product_new:lingquan_click');
      this.disablePageScroll();
    },
    closeFn() {
      this.enablePageScroll();
    },
    disablePageScroll() {
      this.pageScrollStyle = 'overflow:hidden;height:100vh;';
    },
    enablePageScroll() {
      this.pageScrollStyle = '';
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    },
    handleClickToPinTuan() {
      this.discountVisible = false;
      this.$toH5(
        this.pageInfo.chain_discount.sku_group_activity_data
          .pintuan_cut_rule_url
      );
    },
    handleClickToCashBack() {
      this.discountVisible = false;
      this.$toH5(
        this.pageInfo.sku_price_info.writeoff_cashback.writeoff_gift_rule_url
      );
    },
    // 获取当前城市
    async getLocationCity() {
      const auth = await this.checkScopeOfUserLocation();
      // 如果开启了定位权限，使用坐标定位，如果没有使用ip定位
      if (auth) {
        await this.$getCityId();
      } else {
        await this.getIpLocation();
      }
      this.isLocationFetched = true;
      if (this.isLocationFetched) {
        this.$nextTick(() => {
          this.$registerExposure(
            '.applicable-stores',
            () => {
              this.reportStat('sy_wxtuan_tuan_product_new:store_exposure');
            },
            this
          );
        });
      }
      const cityName = this.userInfo.cityName || '全部城市';
      const cityId = Number(this.userInfo.cityId || 0);
      const selectCityId =
        this.userInfo.hospital && this.userInfo.hospital.city_id
          ? Number(this.userInfo.hospital.city_id)
          : 0;
      const selectCityName =
        this.userInfo.hospital && this.userInfo.hospital.city_name
          ? this.userInfo.hospital.city_name
          : '全部城市';
      if (selectCityId) {
        return {
          cityId: selectCityId,
          cityName: selectCityName
        };
      } else {
        return {
          cityId,
          cityName
        };
      }
    },
    // 获取ip定位
    async getIpLocation() {
      const {
        errorCode,
        errorMsg,
        responseData: data
      } = await GetDistrictInfoByIp();
      if (errorCode !== 200 && errorCode !== 0) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      const cityId = Number(data.city_id) || 0;
      const cityName = data.city || '全部城市';
      this.$setUserInfoToStorage({
        cityId,
        cityName
      });
    },
    changeCity(cityId) {
      this.city_id = cityId;
      this.pageInit();
    },
    loadCity(cityId) {
      this.city_id = cityId;
    },
    initExposure() {
      this.$registerExposure(
        '.specialBox',
        () => {
          this.reportStat('sy_wxtuan_tuan_product_new:lingquan_exposure');
        },
        this
      );
      this.$registerExposure(
        '.fenqi',
        () => {
          this.reportStat('sy_wxtuan_tuan_product_info:fenqi_exposure', {
            style: 0
          });
        },
        this
      );
      this.$registerExposure(
        '.alone',
        () => {
          this.reportStat('sy_wxtuan_tuan_product_info:dandugoumai_exposure');
        },
        this
      );
      this.$registerExposure(
        '.createGroup',
        () => {
          this.reportStat('sy_wxtuan_tuan_product_info:faqipintuan_exposure');
        },
        this
      );
      this.$registerExposure(
        '.joinGroup',
        () => {
          this.reportStat('sy_wxtuan_tuan_product_info:cantuan_exposure');
        },
        this
      );
      this.$registerExposure(
        '.getJt',
        () => {
          this.reportStat('sy_wxtuan_tuan_product:red_pop_get_exposure');
        },
        this
      );
      // 购买须知曝光埋点
      this.$registerExposure(
        '.purchase-notes',
        () => {
          this.reportStat('sy_wxtuan_tuan_product_new:notice_exposure');
        },
        this
      );
      this.$registerExposure(
        '.painTag',
        (res) => {
          const { item_content } = res?.dataset || {};
          this.reportStat(
            'sy_chain_store_other_product_info:pain_tag_exposure',
            {
              item_content,
              product_id: this.sku_id,
              spu_id: this.pageInfo.sku_base_info.product_id
            }
          );
        },
        this
      );
    }
  },
  onShareAppMessage() {
    const shareInfo = this.pageInfo.share_info || {};
    const { share_desc, share_image } = shareInfo;
    const sharePath = `/pages/product?id=${
      this.sku_id || ''
    }&psid=${encodeURIComponent(this.pusher_id)}`;
    console.log('onShareAppMessage 分享地址：', sharePath);
    return {
      title: share_desc || '',
      path: sharePath,
      imageUrl: share_image || ''
    };
  },
  onShareTimeline() {
    const shareInfo = this.pageInfo.share_info || {};
    const { share_desc, share_image } = shareInfo;
    return {
      title: share_desc || '',
      query: `id=${this.sku_id || ''}`,
      imageUrl: share_image || ''
    };
  },
  async created() {
    const { cityId, cityName } = await this.getLocationCity();
    this.city_id = cityId;
    this.city_name = cityName;
    this.isFirst = getCurrentPages().length === 1;
  },
  onLoad(options) {
    console.log('连锁商详入参：', options);
    const menu = uni.getMenuButtonBoundingClientRect();
    this.menuRect = menu;
    const _options = this.formatEnterOptions(options);
    console.log('连锁商详入参：解析后', _options);
    const {
      id,
      group_id,
      activity_id,
      pusher_id,
      auto_share_pull,
      share_type
    } = _options;
    this.group_id = group_id || '';
    this.joinGroupActivityId = activity_id || '';
    this.sku_id = id;
    this.pusher_id = decodeURIComponent(pusher_id);
    this.auto_share_pull = auto_share_pull;
    this.share_type = share_type;
    // this.getGidPromise = this.getShareInfo();

    if (options.market_activity_id) {
      const userInfo = uni.getStorageSync('user_info') || {};
      userInfo.marketActivityId = options.market_activity_id;
      this.$setUserInfoToStorage(userInfo);
    }
  },
  async onShow() {
    if (!this.sku_id) {
      uni.showModal({
        content: '商品不存在',
        showCancel: false
      });
      return;
    }
    this.exp_ext = Date.now();
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_product_new_page',
      ext: {
        product_id: this.sku_id
      }
    });
    this.pageLoadingStatus = false;
    this.jumpComfirmFlag = false;
    if (!this.city_id) {
      this.city_id = this.userInfo.hospital?.city_id || this.userInfo.cityId;
    }
    await this.pageInit();
    if (this.auto_share_pull && !this.hasAutoShareOpened) {
      await this.showShareBarAuto();
    }
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_product_new_page',
      ext: {
        product_id: this.sku_id,
        type: this.product_type,
        exp_ext: (Date.now() - this.exp_ext) / 1000
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_product_new_page',
      ext: {
        product_id: this.sku_id,
        type: this.product_type,
        exp_ext: (Date.now() - this.exp_ext) / 1000
      }
    });
  }
};
function query(search, name) {
  const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
  const res = decodeURIComponent(search).match(reg);
  if (res === null) {
    return null;
  }
  return res[2];
}
</script>

<style lang="less" scoped>
@bottomModalHeight: 104rpx;
@theme-color: #ff4556;
@px: 2rpx;

.group-listing {
  background-color: #fff;
  .group-listing-header {
    width: 100%;
    height: 52 * @px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    text {
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }

    image {
      width: 20 * @px;
      height: 20 * @px;
      position: absolute;
      right: 15 * @px;
      top: 15 * @px;
    }
  }

  .group-listing-body {
    overflow-x: hidden;
    overflow-y: scroll;
    box-sizing: border-box;
    padding: 5 * @px 15 * @px;
    max-height: 70vh;
    height: 70vh;

    .group-listing-body-item {
      background: #f8f8f8;
      box-sizing: border-box;
      padding: 15 * @px 10 * @px;
      margin-bottom: 10 * @px;
    }
  }
}

.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page {
  background-color: #f2f2f2;
  background-image: none;
  padding-bottom: 186rpx;
  min-height: 100vh;
  width: 100%;
}

.page-skeleton-diagram {
  background-image: url('https://static.soyoung.com/sy-pre/20240911-142225-1726035000662.jpeg');
  background-size: 100%;
  background-position: top;
  background-repeat: no-repeat;
}

.page-belt {
  width: 100%;

  .page-belt-top {
    height: 40 * @px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 15.5 * @px;
    background-repeat: no-repeat;
    background-size: 100%;
    .page-belt-title {
      display: flex;
      align-items: center;

      &__icon {
        height: 15 * @px;
      }

      &__subtitle {
        height: 18 * @px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0 5 * @px;
        background-color: #030303;
        margin-left: 8.61 * @px;
        position: relative;

        text {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 400;
        }
      }

      &__subtitle-pin {
        margin-left: 15.72 * @px;

        text:first-child {
          font-family: PingFangSC-Semibold;
          font-size: 13 * @px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 600;
        }

        &:before {
          content: '';
          width: 1 * @px;
          height: 13 * @px;
          position: absolute;
          left: -8 * @px;
          top: 50%;
          transform: translateY(-50%);
          background-color: #030303;
        }
      }
    }

    .page-belt-right {
      display: flex;
      align-items: center;

      &__count-down {
        display: flex;
        align-items: center;
        text {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          letter-spacing: 0;
          font-weight: 400;
        }
      }

      &__more-icon {
        width: 8 * @px;
        height: 10 * @px;
        margin-left: 8 * @px;
      }
    }
  }
  .page-belt-bottom {
    width: 100%;
    height: 120rpx;
    padding: 12rpx 30rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .belt-bottom-price-bar {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .belt-bottom-price-bar__price-text {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #030303;
        letter-spacing: 0;
        font-weight: 400;
      }
      .belt-bottom-price-bar__price-icon {
        width: 11 * @px;
        height: 10 * @px;
        margin-left: 3 * @px;
      }
      .belt-bottom-price-bar__price-price {
        font-family: OutFit-Medium;
        font-size: 24px;
        color: #030303;
        letter-spacing: 0;
        line-height: 30px;
        font-weight: 500;
      }
      .belt-bottom-price-bar__price-label {
        height: 18 * @px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 0 5 * @px;
        background-color: #030303;
        margin-left: 8.61 * @px;
        position: relative;

        text {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
      .belt-bottom-price-bar__price-consult {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 69 * @px;
        height: 18 * @px;
        margin-left: 8.61 * @px;
        background: #030303;
        span {
          display: block;
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          line-height: 18 * @px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 400;
        }
        image {
          width: 8 * @px;
          height: 10 * @px;
          margin-left: 5 * @px;
          flex-shrink: 0;
        }
      }
    }
    .belt-bottom-summary {
      font-family: Outfit-Regular;
      font-size: 24rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
      display: flex;
      align-items: center;
      margin-left: -2rpx;
      image {
        width: 22rpx;
        height: 22rpx;
        margin-left: 10rpx;
      }
    }
  }
}

.page-title-price {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 17 * @px 15 * @px 12 * @px 15 * @px;

  &__left {
    overflow: hidden;

    &-title {
      font-family: PingFangSC-Semibold;
      font-size: 22 * @px;
      color: #030303;
      letter-spacing: 0;
      font-weight: 600;
      line-height: 30 * @px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &-subtitle {
      font-family: PingFangSC-Medium;
      font-size: 12 * @px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
      padding-top: 3 * @px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &-other {
      display: flex;
      align-items: center;
      padding-top: 5 * @px;
      flex-wrap: wrap;
      margin-left: -40rpx;
      &-item {
        display: flex;
        align-items: center;
        margin-left: 21 * @px;
        position: relative;
        flex-shrink: 0;
        margin-bottom: 10rpx;
        &:before {
          content: '';
          width: 1 * @px;
          height: 9 * @px;
          background-color: #646464;
          position: absolute;
          left: -10.5 * @px;
          top: 50%;
          transform: translateY(-50%);
        }

        // &:nth-child(1) {
        //   margin-left: 0;

        //   &:before {
        //     display: none;
        //   }
        // }

        text {
          font-family: Outfit-Regular;
          font-size: 12 * @px;
          color: #646464;
          letter-spacing: 0;
          font-weight: 400;
        }

        image {
          width: 11 * @px;
          height: 11 * @px;
          margin-left: 3 * @px;
        }
      }
    }
  }

  &__right {
    padding-left: 30 * @px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    &-amount {
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;

      &-name {
        transform: translateY(2 * @px);
        padding-right: 4 * @px;
        display: flex;
        align-items: center;

        text {
          font-family: PingFangSC-Semibold;
          font-size: 11 * @px;
          color: #8c8c8c;
          letter-spacing: 0;
          font-weight: 600;
          white-space: nowrap;
        }

        image {
          width: 11 * @px;
          height: 10 * @px;
          margin-left: 1.5 * @px;
        }
      }

      &-number {
        font-family: Outfit-Regular;
        font-size: 20 * @px;
        color: #61b43e;
        letter-spacing: 0;
        font-weight: 600;
        line-height: 20 * @px;
        white-space: nowrap;
      }
    }

    &-consult-price {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 2 * @px;
      width: 69 * @px;
      height: 18 * @px;
      background: #030303;
      margin-top: 10 * @px;
      span {
        display: block;
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        line-height: 18 * @px;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 400;
      }
      image {
        width: 9 * @px;
        height: 12 * @px;
        margin-left: 5 * @px;
      }
    }

    &-underlined-price {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #bababa;
      letter-spacing: 0;
      font-weight: 400;
      text-decoration: line-through;
      text-align: right;
    }
  }
}

.page-guarantee {
  border: 1 * @px solid #f2f2f2;
  width: 345 * @px;
  margin: 13 * @px auto 15 * @px;

  &__header {
    box-sizing: border-box;
    padding: 0 10 * @px;
    height: 38 * @px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f8f8;

    &-title {
      height: 18 * @px;
    }

    &-more {
      width: 8.5 * @px;
      height: 10 * @px;
    }
  }

  &__body {
    box-sizing: border-box;
    padding: 0 10 * @px;
    border-top: 1 * @px solid #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 37 * @px;
    background-color: #ffffff;

    &-left {
      display: flex;
      align-items: center;
    }

    &-content {
      display: flex;
      align-items: center;
      //margin-right: 15 * @px;

      image {
        width: 10.6 * @px;
        height: 10.6 * @px;
        margin-right: 5 * @px;
      }

      text {
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
      }
    }

    &-more {
      width: 6.5 * @px;
      height: 8 * @px;
    }
  }
}

.productDetail {
  height: calc(100vh - 200rpx);
  overflow-y: auto;
  padding-bottom: 200rpx;
}
.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;
  .share {
    display: flex;
    position: fixed;
    align-items: center;
    height: 88rpx;
    right: -100%;
    .shareIcon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  .title {
    position: fixed;
    left: 50%;
    height: 88rpx;
    font-family: PingFangSC-Medium;
    font-size: 36rpx;
    color: #333333;
    display: flex;
    align-items: center;
    transform: translateX(-50%);
  }
  .nav {
    //border: 1rpx solid rgba(151, 151, 151, 0.2);
    //border-radius: 80rpx;
    position: fixed;
    //left: 16rpx;
    display: flex;
    align-items: center;
    //justify-content: space-evenly;
    //background: rgba(255, 255, 255, 0.6);
    //.indexBox {
    //}
    //.backBox {
    //}
    .back {
      width: 44 * @px;
      height: 44 * @px;
      display: block;
    }
    //.index {
    //  width: 38rpx;
    //  height: 38rpx;
    //  display: block;
    //}
    //.line {
    //  width: 1rpx;
    //  height: 36rpx;
    //  background: rgba(0, 0, 0, 0.2);
    //}
  }
}
.uni-margin-wrap {
  position: relative;
  width: 100%;
  height: 208 * @px;
  //.whiteMask {
  //  position: absolute;
  //  bottom: 0;
  //}
  //.imgCounter {
  //  position: absolute;
  //  bottom: 156.5rpx;
  //  right: 30rpx;
  //  height: 34rpx;
  //  display: flex;
  //  align-items: center;
  //  padding: 0 16rpx;
  //  opacity: 0.5;
  //  background: #1d1e2c;
  //  border-radius: 18rpx;
  //  font-family: PingFangSC-Medium;
  //  font-size: 24rpx;
  //  color: #ffffff;
  //  font-weight: 500;
  //}
  //.swiper {
  //  height: 562.5rpx;
  //}
  //.swiperImg,
  //.bannerVideo {
  //  width: 100%;
  //  height: 562.5rpx;
  //  display: block;
  //}
}

.productInfo {
  //margin-top: -146.5rpx;
  display: flex;
  justify-content: center;
  .normalOutBox {
    position: relative;
    width: 100%;
    .whiteMask {
      position: absolute;
      bottom: 26rpx;
    }
  }
  .normalBox {
    position: relative;
    box-sizing: border-box;
    padding: 26rpx 0 30rpx 22rpx;
    z-index: 2;
    width: 732rpx;
    background: #fff;
    border-radius: 16rpx;
    margin: 0 auto;
    .smore {
      font-size: 28rpx;
    }
  }
  .specialBox {
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 16rpx;
    z-index: 2;
    width: 750rpx;
    background: #ff4556;
    border-radius: 20rpx 20rpx 0 0;
    .whiteMask {
      margin-top: 56rpx;
    }
    .productLine2 {
      padding-right: 0;
    }
  }
  .groupBox {
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    box-sizing: border-box;
    z-index: 2;
    width: 750rpx;
    background: url(https://static.soyoung.com/sy-design/1451prul7kj481718252534987.png)
      no-repeat top center / 100% 100%;
    border-radius: 20rpx 20rpx 0 0;
    color: #ffffff;
    .groupTitle {
      display: flex;
      box-sizing: border-box;
      width: 100%;
      align-items: center;
      justify-content: space-between;
      height: 146rpx;
      padding: 0 24rpx 0 30rpx;
    }
    .whiteMask {
      height: 94rpx;
    }
    .groupMain {
      top: 146rpx;
      height: 94rpx;
    }
  }
  .spacialMain,
  .groupMain {
    box-sizing: border-box;
    padding: 26rpx 20rpx 30rpx;
    position: absolute;
    top: 68rpx;
    width: 730rpx;
    height: 174rpx;
    background: #ffffff;
    border-radius: 16rpx;
  }
  .specialTitle {
    width: 100%;
    box-sizing: border-box;
    padding: 0 30rpx;
    font-family: PingFangSC-Semibold;
    font-size: 26rpx;
    color: #ffffff;
    font-weight: 600;
    height: 36rpx;
    line-height: 36rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .right {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
    }
    .day,
    .date {
      margin-right: 10rpx;
    }
  }
  .productPrice {
    font-family: PingFangSC-Medium;
    font-size: 56rpx;
    color: #ff4040;
    line-height: 66rpx;
    font-weight: 500;
    .leftSmore {
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      font-weight: 600;
      display: inline-block;
    }
    .price {
      display: inline-block;
    }
    .gray {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #777777;
      margin-left: 10rpx;
      display: inline-block;
      vertical-align: bottom;
      .smore {
        font-size: 18rpx;
      }
    }
    .addCPriceRightIcon {
      width: 0;
      height: 42rpx;
      margin-left: 12rpx;
    }
  }
  .productLine2 {
    margin-top: 8rpx;
    padding-right: 48rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #777777;
    .item {
      display: flex;
      align-items: center;
    }
    .text {
      line-height: 36rpx;
    }
    .icon {
      width: 22rpx;
      height: 22rpx;
      margin-left: 6rpx;
    }
  }
}
.groupTitleLeft {
  display: flex;
  flex-direction: column;
  font-family: PingFangSC-Medium;
  .top {
    display: flex;
    height: 60rpx;
    .text {
      font-size: 24rpx;
      font-weight: 500;
      margin-top: 24rpx;
    }
    .price {
      font-size: 56rpx;
      line-height: 60rpx;
      font-weight: 500;
      &::before {
        content: '￥';
        font-family: PingFangSC-Semibold;
        font-size: 26rpx;
        font-weight: 600;
        margin: 0 6rpx 0 4rpx;
      }
    }
    .groupNum {
      height: 44rpx;
      line-height: 44rpx;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx 24rpx 24rpx 0;
      padding: 0 16rpx;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #fc4139;
      font-weight: 500;
      margin: 10rpx 0 0 10rpx;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        left: -6rpx;
        bottom: 0;
        display: block;
        width: 6rpx;
        height: 18rpx;
        background: url(https://static.soyoung.com/sy-pre/cjad8tdielx-1719303000639.png)
          no-repeat center / 100% 100%;
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    margin-top: 4rpx;
    .icon {
      height: 24rpx;
      width: 24rpx;
    }
    .text {
      margin-left: 6rpx;
      opacity: 0.95;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
    }
    .price {
      opacity: 0.95;
      font-family: PingFangSC-Semibold;
      font-size: 26rpx;
      font-weight: 600;
      line-height: 36rpx;
      &::before {
        content: '￥';
        opacity: 0.95;
        font-size: 20rpx;
        font-weight: 600;
        margin: 0 0 0 2rpx;
      }
    }
    .addCPriceRightIconInGroup {
      width: 0;
      height: 38rpx;
    }
  }
}
.groupTitleRight {
  .top {
    display: flex;
    align-items: center;
    height: 48rpx;
    justify-content: flex-end;
    padding-top: 10rpx;
    .dateBox {
      display: flex;
      align-items: center;
      .text {
        font-family: PingFangSC-Medium;
        font-size: 34rpx;
        font-weight: 500;
        margin: 0 6rpx;
      }
    }
    .icon {
      width: 172rpx;
      height: 36rpx;
      margin-right: 10rpx;
    }
    .smoreIcon {
      width: 30rpx;
      height: 30rpx;
    }
    .arrow {
      width: 14rpx;
      height: 26rpx;
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    margin-top: 6rpx;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
  }
}
.whiteMask {
  width: 100%;
  height: 133rpx;
  background-image: linear-gradient(rgba(248, 248, 248, 0) 0%, #f8f8f8 100%);
}
.labelBox {
  display: flex;
  flex-direction: column;
  margin: 14rpx auto 0;
  width: 730rpx;
  &.groupMargin {
    margin-top: 24rpx;
  }
  .labelLine1 {
    display: flex;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    box-sizing: border-box;
    background-image: linear-gradient(
      90deg,
      #d5efe9 4%,
      rgba(207, 241, 232, 0.57) 100%
    );
    border-radius: 16rpx 16rpx 0 0;
    padding: 0 20rpx 20rpx;
    .content {
      display: flex;
      align-items: center;
    }
    .leftIcon {
      width: 26rpx;
      height: 28rpx;
      display: block;
      margin-right: 8rpx;
    }
    .textLeft {
      font-family: PingFangSC-Semibold;
      font-size: 26rpx;
      color: #00a077;
      font-weight: 600;
    }
    .line {
      color: #00a077;
      font-size: 20rpx;
      margin: 0 12rpx;
    }
    .textRight {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #00a077;
    }
  }
}
.labelLine2 {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 74rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-top: -20rpx;
  .item {
    display: flex;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #222222;
    .img {
      width: 22rpx;
      height: 22rpx;
      margin-right: 8rpx;
    }
  }
}
.card {
  box-sizing: border-box;
  width: 730rpx;
  margin: 14rpx auto 0;
  padding: 30rpx 20rpx;
  background: #fff;
  border-radius: 16rpx;
  .title {
    font-family: PingFangSC-Semibold;
    font-size: 32rpx;
    color: #222222;
    font-weight: 600;
    line-height: 44rpx;
  }
}

.productSelect {
  padding: 15 * @px;
  box-sizing: border-box;

  .selectItem {
    //width: 345 * @px;
    min-height: 42 * @px;
    display: flex;
    align-items: flex-start;
    box-sizing: border-box;
    border: 2 * @px solid #333333;
    padding: 10 * @px;
    background-color: #ffffff;
    //justify-content: space-between;

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 16 * @px;
      color: #030303;
      letter-spacing: 0;
      font-weight: 600;
      white-space: nowrap;
    }

    .text {
      padding-left: 10 * @px;
      padding-top: 1.5 * @px;
      flex: 1;
      font-family: PingFangSC-Regular;
      font-size: 14 * @px;
      color: #333333;
      letter-spacing: 0.47px;
      font-weight: 400;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      overflow: hidden;
      white-space: normal;
      -webkit-box-orient: vertical;
    }

    .arrow {
      width: 10 * @px;
      height: 15 * @px;
      min-width: 10 * @px;
      transform: translateY(3.5 * @px);
      margin-left: 15 * @px;
    }
  }
  .productSpecification {
    padding-top: 30 * @px;

    .productSpecificationTitle {
      font-family: PingFangSC-Semibold;
      font-size: 16 * @px;
      color: #bababa;
      letter-spacing: 0;
      font-weight: 600;
      padding-bottom: 20 * @px;
    }
  }
}
.productIntroduce {
  box-sizing: border-box;
  background-color: #ffffff;
  padding: 15 * @px;
  margin-top: 10 * @px;

  .title {
    font-family: PingFangSC-Semibold;
    font-size: 16 * @px;
    color: #030303;
    letter-spacing: 0;
    font-weight: 600;
  }

  .contentBox {
    margin-top: 15 * @px;
    .productCompanyImageWrapper {
      width: 690rpx;
      overflow: hidden;
    }
    .productCompanyBox {
      width: 690rpx;
      margin-top: -2rpx;
      margin-bottom: -30rpx;
    }
    .productCompanyImage {
      width: 690rpx;
    }
    .rich-text {
      box-sizing: border-box;
      // padding: 0 30rpx 30rpx 30rpx;
      /deep/ .lazy {
        width: 100%;
        height: auto;
        display: block;
        vertical-align: top;
        &:first-child {
          border-top-left-radius: 16rpx;
          border-top-right-radius: 16rpx;
        }
        &:last-child {
          border-bottom-left-radius: 16rpx;
          border-bottom-right-radius: 16rpx;
        }
      }
    }
  }
}
.arrow {
  width: 12rpx;
  height: 20rpx;
  display: block;
}

.bottomBtn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  width: 100vw;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  z-index: 10;
  border-top: 1 * @px solid #efefef;
  padding: 10 * @px 0 * @px calc(10 * @px + env(safe-area-inset-bottom));
  //padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  //padding-bottom: ~'max(env(safe-area-inset-bottom), 12rpx)';

  .rightButtonRegion {
    flex: 1;
    box-sizing: border-box;
    padding-right: 15 * @px;
    height: 50 * @px;
    min-height: 50 * @px;

    button {
      padding: 0;
      margin: 0;
      height: 100%;
      border: none;
      border-radius: 0;
      background-color: transparent;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &:after {
        border: none;
      }
    }

    .join-group {
      background-color: #030303;

      .join-group-btn-title {
        font-family: PingFangSC-Medium;
        font-size: 16 * @px;
        color: #a9ea6a;
        letter-spacing: 0;
        line-height: 14 * @px;
        font-weight: 500;
      }

      .join-group-btn-tips {
        font-family: Outfit-Regular;
        font-size: 10 * @px;
        color: #a9ea6a;
        letter-spacing: 0;
        line-height: 14 * @px;
        font-weight: 400;
        padding-top: 5 * @px;
        display: flex;
        align-items: center;
      }
    }

    .combination-btn {
      display: flex;
      flex: 1;
      height: 100%;

      button {
        text:nth-child(1) {
          font-family: OutFit-Regular;
          font-size: 16 * @px;
          letter-spacing: 0;
          line-height: 14 * @px;
          font-weight: 500;
        }

        text:nth-child(2) {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          letter-spacing: 0;
          line-height: 14 * @px;
          font-weight: 400;
          padding-top: 5 * @px;
        }
      }

      .leftBtn {
        flex: 1;
        background-color: #89dc65;
        color: #030303;
      }

      .rightBtn {
        flex: 1;
        background-color: #030303;
        color: #a9ea6a;
      }

      .new-gray {
        background-color: #bababa;
        color: #fff;
      }
    }
  }

  .groupRightBtn {
    display: flex;
    margin-left: 8rpx;
    .line1 {
      line-height: 32rpx;
      font-size: 32rpx;
      &::before {
        content: '￥';
        font-family: PingFangSC-Medium;
        font-size: 20rpx;
        font-weight: 500;
        margin-right: 1rpx;
      }
    }
    .line2 {
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      line-height: 24rpx;
    }
    .leftBtn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 276rpx;
      height: 84rpx;
      border: 4rpx solid #00ab84;
      border-radius: 44rpx 0 0 44rpx;
      font-family: PingFangSC-Medium;
      color: #00ab84;
      font-weight: 500;
    }
    .rightBtn {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      justify-content: center;
      width: 280rpx;
      height: 88rpx;
      background: #00ab84;
      border: 2rpx solid #00ab84;
      border-radius: 0 44rpx 44rpx 0;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #ffffff;
      margin-left: -2rpx;
      &.gray {
        background: #aaabb3;
        border: 2rpx solid #aaabb3;
      }
    }
  }
  > .leftBtn {
    min-width: 96 * @px;
    max-width: 96 * @px;
    flex: 1;
    box-sizing: border-box;
    //padding-top: 2 * @px;
    > view {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      font-family: PingFangSC-Regular;
      font-size: 10 * @px;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
    }
    .icon {
      width: 28 * @px;
      height: 28 * @px;
    }
    .text {
      margin-top: 4rpx;
      white-space: nowrap;
    }
    .addCPopoverImage {
      position: absolute;
      top: -40rpx;
      width: 0;
      height: 64rpx;
      left: 16rpx;
      z-index: 10;
    }
  }
  > .rightBtn {
    background: #030303;
    width: 264 * @px;
    min-width: 264 * @px;
    margin-right: 15 * @px;
    height: 50 * @px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .line1 {
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #a9ea6a;
      letter-spacing: 0;
      text-align: center;
      line-height: 14 * @px;
      font-weight: 500;
    }
    .line2 {
      padding-top: 4 * @px;
      font-family: Outfit-Regular;
      font-size: 10px;
      color: #a9ea6a;
      letter-spacing: 0;
      text-align: center;
      //line-height: 14px;
      font-weight: 400;
    }
    .smore {
      font-size: 16rpx;
    }
  }
}
.coupon-bottom-popup,
.spu-bottom-popup {
  box-sizing: border-box;
  max-height: calc(80vh - 52 * @px);
  background-color: #ffffff;
  // padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
  // padding-bottom: env(safe-area-inset-bottom);
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .spu-bottom-popup-title {
    display: flex;
    flex-direction: column;

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 16 * @px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 600;
      white-space: nowrap;
    }

    .subtitle {
      font-family: PingFangSC-Regular;
      font-size: 13 * @px;
      color: #8c8c8c;
      letter-spacing: 0;
      font-weight: 400;
      padding-top: 5 * @px;
    }
  }

  .scroll {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    padding-bottom: 14rpx;
  }

  .btnBox {
    width: 100%;
    background: #fff;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
    border-top: 1 * @px solid #f2f2f2;

    button {
      padding: 0;
      margin: 0;
      border: none;
      border-radius: 0;

      &:after {
        border: none;
      }
    }

    .groupDoubleBtn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 30rpx;
      font-family: PingFangSC-Regular;
      height: 50 * @px;

      button {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        flex: 1;

        text:nth-child(1) {
          font-family: OutFit-Regular;
          font-size: 16 * @px;
          letter-spacing: 0;
          line-height: 14 * @px;
          font-weight: 500;
        }

        text:nth-child(2) {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          letter-spacing: 0;
          line-height: 14 * @px;
          font-weight: 400;
          padding-top: 5 * @px;
        }
      }

      .leftBtn {
        //height: 88rpx;
        //color: #222222;
        //background: #ffffff;
        //border: 3rpx solid #dedede;
        //border-radius: 44rpx;
        color: #030303;
        background-color: #89dc65;
      }

      .rightBtn {
        //height: 88rpx;
        //background: #00a077;
        //border-radius: 44rpx;
        //color: #fff;
        //border: 3rpx solid #00a077;
        //margin-left: 30rpx;
        color: #a9ea6a;
        background-color: #030303;
        //&.gray {
        //background: #aaabb3;
        //border: 3rpx solid #aaabb3;
        //}
      }

      button:last-child {
        color: #a9ea6a;
        background-color: #030303;
      }
    }
    .bottompop-btn {
      flex-shrink: 0;
      margin: 10rpx auto 20rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: calc(100vw - 30 * @px);
      height: 50 * @px;
      background-color: #030303;

      text:nth-child(1) {
        font-family: PingFangSC-Medium;
        font-size: 16 * @px;
        color: #a9ea6a;
        letter-spacing: 0;
        line-height: 14 * @px;
        font-weight: 500;
      }

      text:nth-child(2) {
        font-family: Outfit-Regular;
        font-size: 10 * @px;
        color: #a9ea6a;
        letter-spacing: 0;
        line-height: 14 * @px;
        font-weight: 400;
        padding-top: 5 * @px;
      }

      .line2 {
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        color: red;
        margin-top: 2rpx;
      }
      .smore {
        font-size: 16rpx;
      }
      &.disabled {
        background: #dedede;
      }
      &.coupon-btn {
        background: #ff4556;
      }
      &.spu-btn {
        background: #030303;
      }
    }
  }

  .bottomPopCard {
    margin: 10rpx 10rpx 0;
    background: #fff;
    padding: 30rpx 20rpx 40rpx;
    border-radius: 16rpx;
  }

  .by-stages {
    box-sizing: border-box;
    padding: 20 * @px 0;
    position: relative;

    &:before {
      content: '';
      width: calc(100vw - 30 * @px);
      height: 1 * @px;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      background-color: #f2f2f2;
      position: absolute;
    }

    .spu-bottom-popup-title {
      box-sizing: border-box;
      padding: 0 15 * @px;
    }

    .by-stages-list {
      margin-top: 17 * @px;
      height: 50 * @px;
      display: flex;

      .by-stages-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-width: 105 * @px;
        background-color: #f2f2f2;
        margin-left: 10 * @px;

        &:nth-child(1) {
          margin-left: 15 * @px;
        }

        &-name {
          font-family: PingFangSC-Regular;
          font-size: 13 * @px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 400;
        }

        &-tips {
          font-family: PingFangSC-Regular;
          font-size: 11 * @px;
          color: #8c8c8c;
          letter-spacing: 0;
          font-weight: 400;
          padding-top: 1 * @px;
        }
      }

      .by-stages-item-select {
        position: relative;
        background-color: #ffffff;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: calc(100% - 4 * @px);
          height: calc(100% - 4 * @px);
          border: 2 * @px solid #333333;
          background-color: transparent;
          background-image: url('https://static.soyoung.com/sy-design/3natgg5v59z4r1726221853473.png');
          background-repeat: no-repeat;
          background-size: 18 * @px 12 * @px;
          background-position: 100% 100%;
        }
      }

      .by-stages-item-self-adaption {
        flex: 1;
        margin-left: 15 * @px;
      }
    }
  }

  .specification-dialog {
    width: calc(100vw - 30 * @px);
    //padding: 40rpx 20rpx 30rpx;
    padding-top: 20 * @px;
    padding-bottom: 30 * @px;
    box-sizing: border-box;
    margin: 0 auto;
    border-top: 1 * @px solid #f2f2f2;
  }

  .collect-all {
    width: 100%;
    box-sizing: border-box;
    padding-top: 10 * @px;
    padding-left: 25 * @px;
    padding-right: 25 * @px;
    padding-bottom: calc(10 * @px + constant(safe-area-inset-bottom));
    padding-bottom: calc(10 * @px + env(safe-area-inset-bottom));
    display: flex;
    justify-content: center;

    .collect-all-btn {
      width: 100%;
      height: 42 * @px;
      background-color: #333;
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #ffffff;
      text-align: center;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .disable {
      background-color: #bababa;
      color: #ffffff;
    }
  }
}
.cashback-text-area {
  max-height: 416rpx;
  margin: 0rpx 30rpx 60rpx;
  box-sizing: border-box;
  background: #f8f8f8;
  border: 1px solid #f2f2f2;
  padding: 36rpx 20rpx;
  .cashback-text-area-title {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }
    image {
      width: 30rpx;
      height: 30rpx;
      margin-right: 10rpx;
    }
    .cashback-text-area-title-text {
      flex: 1;
      color: #8c8c8c;
      font-size: 26rpx;
      line-height: 36rpx;
    }
    .cashback-text-area-title-title {
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 500;
    }
    .cashback-text-area-title-link {
      font-size: 24rpx;
      color: #6dba4c;
      margin-left: 10rpx;
      text-decoration: underline;
    }
  }
}
.spu-bottom-popup {
  height: 80vh;
}
.perksBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
  .item {
    position: relative;
    background-image: linear-gradient(90deg, #fff2e2 0%, #ffeae2 100%);
    border-radius: 16rpx;
    box-sizing: border-box;
    width: 345rpx;
    margin-bottom: 20rpx;
    width: 100%;
    padding: 18rpx 30rpx 20rpx;
    .received {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 108rpx;
      height: 84rpx;
      background: url(https://static.soyoung.com/sy-pre/3g153e0r5h4s4-1717495800650.png)
        no-repeat center / 100% 100%;
    }
  }
}
.perksBoxItemTop {
  border-bottom: 2rpx dashed #e7b280;
  padding-bottom: 10rpx;
  .line1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .line1Title {
        font-family: PingFangSC-Medium;
        font-size: 30rpx;
        color: #222222;
        font-weight: 500;
      }
      .tips {
        line-height: 34rpx;
        margin-left: 12rpx;
        padding: 0 10rpx;
        background: rgba(#d0842e, 0.15);
        border-radius: 4rpx;
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        color: #b68155;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
    }
    .right {
      font-family: PingFangSC-Medium;
      font-size: 48rpx;
      color: #b68155;
      font-weight: 500;
      line-height: 66rpx;
      .smore {
        font-size: 26rpx;
      }
    }
  }
  .line2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    .left {
      color: #222222;
    }
    .right {
      color: #777777;
    }
  }
}
.perksBoxItemBottom {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #777777;
    height: 56rpx;
    line-height: 56rpx;
  }
  .right {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 42rpx;
    height: 56rpx;
    background: #ff4556;
    border-radius: 14px;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: #ffffff;
  }
}
.couponsBox {
  margin-top: 20rpx;
}

.installment {
  width: 728rpx;
  box-sizing: border-box;
  margin: 24rpx auto 0;
  background: #fff;
  background-image: linear-gradient(
    rgba(255, 243, 221, 0.59) 0%,
    rgba(251, 247, 241, 0) 63%
  );
  display: flex;
  flex-direction: column;
  padding: 30rpx 0 30rpx 18rpx;
  height: 228rpx;
  font-family: PingFangSC-Regular;
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .line {
    width: 2rpx;
    height: 18rpx;
    background: #bfbfbf;
    margin: 0 16rpx;
  }
  .row1 {
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    color: #222222;
    font-weight: 500;
    .left {
      letter-spacing: 0.86rpx;
      display: flex;
      align-items: center;
    }
    .right {
      height: 34rpx;
      display: flex;
      padding: 0 8rpx 0 6rpx;
      font-family: PingFangSC-Medium;
      font-size: 24rpx;
      color: #be8e4e;
      align-items: center;
      background: #ffffff;
      border: 2rpx solid rgba(235, 176, 97, 0.75);
      border-radius: 200rpx 0 0 200rpx;
      .rightIcon {
        width: 22rpx;
        height: 22rpx;
        margin-right: 4rpx;
      }
    }
  }
  .row2 {
    margin-top: 28rpx;
    height: 48rpx;
    justify-content: flex-start;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333333;
    letter-spacing: 0.8rpx;
    .price {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #be8e4e;
      letter-spacing: 1.14rpx;
      font-weight: 600;
    }
  }
  .row3 {
    height: 32rpx;
    margin-top: 24rpx;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #9997a4;
    padding-right: 20rpx;
    .left {
      display: flex;
      align-items: center;
      &::before {
        content: '';
        background: #b2b0be;
        width: 8rpx;
        height: 8rpx;
        margin-right: 6rpx;
        border-radius: 50%;
      }
    }
  }
}
.spupopTopInfo {
  padding: 20 * @px 0 18 * @px 18 * @px;
  display: flex;
  align-items: center;
  //align-items: flex-start;
  //justify-content: center;
  //flex-direction: column;
  position: relative;

  &:before {
    content: '';
    width: calc(100vw - 30 * @px);
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 1 * @px;
    background-color: #f2f2f2;
    position: absolute;
  }

  .row1 {
    display: flex;
    align-items: center;
    font-family: PingFangSC-Medium;
    font-size: 24rpx;
    color: #ff4040;
    font-weight: 500;
    .money {
      display: flex;
      align-items: center;

      text {
        font-family: PingFangSC-Semibold;
        font-size: 11 * @px;
        color: #8c8c8c;
        letter-spacing: 0;
        text-align: right;
        line-height: 30 * @px;
        font-weight: 600;
      }

      image {
        width: 11 * @px;
        min-width: 11 * @px;
        height: 10 * @px;
        margin-left: 1 * @px;
      }
    }
    .price {
      margin-left: 4 * @px;
      font-family: Outfit-SemiBold;
      font-size: 20 * @px;
      color: #61b43e;
      letter-spacing: 0;
      line-height: 20 * @px;
      font-weight: 600;
    }
    .priceBefore {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #777777;
      margin-left: 12rpx;
      .smore {
        font-size: 18rpx;
      }
    }
    .groupCutPrice {
      line-height: 42rpx;
      padding: 0 10rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #ffffff;
      background: #ff4040;
      border-radius: 22rpx;
      margin-left: 10rpx;
      .smore {
        font-size: 20rpx;
      }
    }
  }
  .row2 {
    margin-top: 12rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #777777;
  }

  .row3 {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #646464;
    letter-spacing: 0;
    font-weight: 400;
    padding-left: 5 * @px;
    //padding-top: 5 * @px;
  }
}
.spupopSkuBox {
  width: 100%;
  //display: flex;
  //flex-wrap: wrap;
  //margin: 10rpx 0 0 -20rpx;
  .skuItem {
    //border: 3rpx solid #dedede;
    //border-radius: 16rpx;
    //padding: 20rpx 30rpx;
    //line-height: 36rpx;
    //font-family: PingFangSC-Medium;
    //font-size: 26rpx;
    //color: #222222;
    //margin: 20rpx 0 0 20rpx;
    margin-top: 15 * @px;
    background-color: #f2f2f2;
    padding: 10 * @px;
    box-sizing: border-box;
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    &.select {
      //padding: 19rpx 28rpx;
      //border: 4rpx solid #00a077;
      //color: #00a077;
      //border: 2 * @px solid #333333;
      background-color: #ffffff;
      box-sizing: border-box;
      position: relative;

      &:before {
        content: '';
        width: calc(100% - 4 * @px);
        height: calc(100% - 4 * @px);
        position: absolute;
        left: 0;
        top: 0;
        background-color: transparent;
        border: 2 * @px solid #333333;
        background-image: url('https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1726221853496.png');
        background-size: 18 * @px 12 * @px;
        background-repeat: no-repeat;
        background-position: 100% 100%;
      }
    }
  }
}
.purchase-notes {
  margin-bottom: 260rpx;
}
.timeBox {
  display: flex;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #ffffff;
  height: 36rpx;
  margin-right: 6rpx;
  padding-top: 2rpx;
  .timeItem {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 38rpx;
    height: 30rpx;
    background: rgba(222, 24, 23, 0.71);
    border-radius: 6rpx;
    margin: 0 6rpx;
    font-weight: bold;
  }
}
.groupFloor {
  background: #f8f8f8;
  padding: 20 * @px 20rpx 13rpx;
  margin: 60rpx 30rpx 30rpx;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      font-size: 28rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 500;
      font-family: OutFit-Regular;
    }
    .right {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #333333;
      display: flex;
      align-items: center;
    }
    .arrow {
      width: 13rpx;
      height: 16rpx;
      margin-left: 10rpx;
    }
  }
}
.floorCardBox {
  .cardBorder {
    border-bottom: rgba(#f0f0f0, 0.6) solid 2rpx;
    &:last-child {
      border: none;
    }
  }
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 99;
}
.floorPopMain {
  display: flex;
  flex-direction: column;
  position: absolute;
  box-sizing: border-box;
  width: 600rpx;
  height: 830rpx;
  background: #fff;
  border-radius: 20rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding-top: 52rpx;
  overflow: hidden;
  .whiteMask {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
    height: 64rpx;
  }
  .closebox {
    position: absolute;
    right: 18rpx;
    top: 22rpx;
    padding: 10rpx;
    .close {
      width: 30rpx;
      height: 30rpx;
    }
  }
  .title {
    font-family: PingFangSC-Medium;
    font-size: 34rpx;
    color: #333333;
    font-weight: 500;
    line-height: 48rpx;
    text-align: center;
  }
}
.popFloorCardBox {
  padding: 0 32rpx;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
}
.addCPopupImage {
  width: 100vw;
  height: 0;
}

.rule-explanation {
  width: 100%;
  background-color: #ffffff;
  border-radius: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

.rule-explanation-title {
  font-family: PingFangSC-Medium;
  font-size: 16 * @px;
  color: #030303;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  height: 52 * @px;
  line-height: 52 * @px;
  position: relative;

  image {
    position: absolute;
    right: 15 * @px;
    top: 50%;
    transform: translateY(-50%);
    width: 20 * @px;
    height: 20 * @px;
  }
}

.rule-explanation-body {
  font-family: PingFangSC-Regular;
  font-size: 12 * @px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 20 * @px;
  font-weight: 400;
  padding: 15 * @px 25 * @px;
  box-sizing: border-box;
  &.alignLeft {
    text-align: left;
  }
}

.rule-explanation-button {
  padding: 10 * @px 25 * @px;
  box-sizing: border-box;

  button {
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 0;
    background-color: #333333;
    width: 100%;
    height: 42 * @px;
    text-align: center;
    line-height: 42 * @px;
    font-family: PingFangSC-Medium;
    font-size: 13 * @px;
    color: #ffffff;
    font-weight: 500;

    &:after {
      border: none;
    }
  }
}

.page-ceiling-suction {
  width: 100vw;
  height: 42 * @px;
  display: flex;
  position: fixed;
  z-index: 1000;
  background-color: #ffffff;
  opacity: 0;

  & > view {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    color: #646464;
    letter-spacing: 0;
    font-weight: 400;
    position: relative;
  }

  .page-ceiling-suction-detail {
  }

  .page-ceiling-suction-select {
    font-family: PingFangSC-Semibold;
    color: #030303;
    font-weight: 600;

    &:before {
      content: '';
      position: absolute;
      width: 20 * @px;
      height: 2 * @px;
      background-color: #030303;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.serviceProcessModel {
  margin-top: 10 * @px;
  background-color: #ffffff;

  .serviceProcessModelTitle {
    padding: 15 * @px 15 * @px 0;
    font-family: PingFangSC-Semibold;
    font-size: 16 * @px;
    color: #030303;
    letter-spacing: 0;
    font-weight: 600;
    //padding-bottom: 15 * @px;
  }
}
.painInfoBox {
  height: 28rpx;
  display: flex;
  align-items: center;
  padding: 0 6rpx;
  font-family: PingFangSC-Regular;
  font-size: 20rpx;
  color: #373a36;
  .painIcon {
    width: 20rpx;
    height: 20rpx;
    margin-right: 4rpx;
  }
}
.painTag {
  height: 34rpx;
}
</style>
