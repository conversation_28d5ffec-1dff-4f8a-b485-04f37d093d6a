<!--
    <ConfirmationWindow
      ref="confirmation"
      button-left="取消"
      button-right="确认"
      title="title内容-优先级低"
      content="content内容-优先级低"
      :automatic-closing="点击双按钮自动关闭弹窗 Boolean 默认关闭"
      @onInteractive="onInteractive"
    />
    打开弹窗
    this.$refs.confirmation.open({
      title: 'title内容-优先级高',
      content: 'content内容-优先级高',
    });
-->
<script>
import Popup from '@/components/uni/popup.vue';

export default {
  name: 'confirmationWindow',
  components: {
    Popup
  },
  props: {
    position: {
      type: String,
      default: 'center'
    },
    buttonLeft: {
      type: String,
      default: () => ''
    },
    buttonRight: {
      type: String,
      default: () => '确认'
    },
    // 自动关闭
    automaticClosing: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: () => ''
    },
    content: {
      type: String,
      default: () => ''
    }
  },
  computed: {
    titleTxt() {
      return this.titleApi || this.title;
    },
    contentTxt() {
      return this.contentApi || this.content || '';
    }
  },
  data() {
    return {
      titleApi: '',
      contentApi: ''
    };
  },
  methods: {
    down() {
      this.$refs.popup.close(this.position);
      this.$emit('onInteractive', { type: 'maskClick' });
    },
    open(param = {}) {
      this.titleApi = param.title;
      this.contentApi = param.content;
      this.$refs.popup.open(this.position);
    },
    interactive(param) {
      if (this.automaticClosing) {
        this.$refs.popup.close(this.position);
      }
      this.$emit('onInteractive', { type: param });
    }
  }
};
</script>

<template>
  <Popup ref="popup" @mask-click="down">
    <view
      class="confirmation-window"
      :class="{ 'confirmation-window-bottom': position === 'bottom' }"
    >
      <view v-if="titleTxt" class="confirmation-window-title">
        {{ titleTxt }}
      </view>
      <image
        v-if="position === 'bottom'"
        class="confirmation-window-down"
        src="https://static.soyoung.com/sy-design/bzsokyai5osd1726715721923.png"
        @click="down"
      ></image>
      <view
        class="confirmation-window-content"
        v-if="!$slots['custom-content']"
      >
        {{ contentTxt }}
      </view>
      <slot name="custom-content"></slot>
      <view class="confirmation-window-button">
        <button
          v-if="buttonLeft"
          class="white"
          @click="interactive('buttonLeft')"
        >
          {{ buttonLeft }}
        </button>
        <button class="green" @click="interactive('buttonRight')">
          {{ buttonRight }}
        </button>
      </view>
    </view>
  </Popup>
</template>

<style scoped lang="less">
@px: 2rpx;

.confirmation-window {
  width: 300 * @px;
  box-sizing: border-box;
  padding: 20 * @px;
  background-color: #ffffff;
  border-radius: 10 * @px;
  position: relative;

  .confirmation-window-down {
    width: 20 * @px;
    height: 20 * @px;
    right: 15 * @px;
    top: 22 * @px;
    z-index: 10;
    position: absolute;
  }

  .confirmation-window-title {
    font-family: PingFangSC-Medium;
    font-size: 17 * @px;
    color: #222222;
    text-align: center;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 10 * @px;
  }
  .confirmation-window-content {
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    line-height: 18 * @px;
    color: #333333;
    font-weight: 400;
    margin-bottom: 20 * @px;
    text-align: center;
  }
  .confirmation-window-button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 34 * @px;
    button {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      flex: 1;
      &:after {
        border: none;
      }
      &:nth-child(2) {
        margin-left: 20 * @px;
      }
    }
    .green {
      background-color: #333333;
      border-radius: 34 * @px;
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #ffffff;
      font-weight: 500;
    }
    .white {
      border: 1 * @px solid #dedede;
      border-radius: 17 * @px;
      font-family: PingFangSC-Regular;
      font-size: 13 * @px;
      color: #222222;
      font-weight: 500;
      background-color: #ffffff;
    }
  }
}

.confirmation-window-bottom {
  width: 100vw;
  border-radius: 0;

  .confirmation-window-button {
    height: 42 * @px;

    button {
      border-radius: 0 !important;
      height: 100%;
    }
  }
}
</style>
