<template>
  <div
    class="modal-mask"
    @click="close"
    :style="{ bottom: '-1px' }"
    @animationend="animationend"
    :class="{
      'fade-in': transform
    }"
    v-if="visible"
  >
    <div class="mask"></div>
    <div
      class="pannel"
      :class="{
        safe: false
      }"
      @click.stop
    >
      <div class="title">选择预约项目</div>
      <div class="close" @click="close"></div>
      <div class="project-bottom-modal-content">
        <scroll-view scroll-y :scroll-x="false" class="project-list">
          <div class="js-wraper">
            <div
              class="project-item"
              :key="project.sku_id"
              v-for="project in dataList"
              :data-id="project.sku_id"
              :class="{
                active: Number(curProject) === Number(project.sku_id)
              }"
              @click="handleProjectSelect"
            >
              <div class="name">{{ project.title }}</div>
              <div
                class="cashback"
                v-if="
                  project.cashback_amount && project.cashback_amount !== '0'
                "
              >
                最高返¥{{ project.cashback_amount }}
              </div>
              <div class="duihao"></div>
            </div>
          </div>
          <div class="project-empty"></div>
        </scroll-view>
        <div class="project-bottom" @click="handleProjectConfirm">
          <div class="project-bottom-btn">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    defaultProject: {
      type: Number,
      default: 0
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      transform: false,
      curProject: this.defaultProject
    };
  },
  watch: {
    visible(visible) {
      if (visible) {
        setTimeout(() => {
          this.transform = true;
        }, 50);
      } else {
        this.transform = false;
      }
      this.$emit('visibleChange', visible);
    },
    defaultProject(defaultProject) {
      console.log('defaultProject', defaultProject);
      this.curProject = defaultProject;
    }
  },
  methods: {
    async animationend() {
      setTimeout(() => {
        this.$emit('open');
      }, 100);
    },
    close() {
      this.$emit('close', false);
      this.curProject = this.defaultProject;
    },
    handleProjectSelect(event) {
      const skuId = Number(event.currentTarget.dataset.id);
      this.curProject = skuId;
    },
    handleProjectConfirm() {
      this.$emit('close', false);
      this.$emit('project-change', this.curProject);
    }
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(900px);
  &.fade-in {
    .mask {
      opacity: 1;
    }
    .pannel {
      transform: translateY(0);
    }
  }
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 2px);
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.7);
  transition: opacity 0.2s;
}
.pannel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.2s;
  background-color: #ffffff;
  &.safe {
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
}
.title {
  .flex-align-center;
  font-family: PingFangSC-Medium;
  justify-content: center;
  height: 52 * 2rpx;
  font-size: 32rpx;
  color: #030303;
  font-weight: 500;
}
.close {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 28rpx;
  background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1726026826488.png)
    no-repeat center / 100%;
  z-index: 9;
}

.project-bottom-modal-content {
  position: relative;
  display: block;
  // 70 UI 要求占屏幕高
  // 104 modal 的 title bar 高度
  height: calc(45vh - 104rpx);
  box-sizing: border-box;
  padding: 0 30rpx;
  .project-list {
    box-sizing: border-box;
    margin: 20rpx 0;
    height: calc(40vh - 64rpx - 104rpx - 40rpx);
    overflow-y: auto;
    .project-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 36rpx;
      padding: 28rpx 20rpx;
      box-sizing: border-box;
      background: #f2f2f2;
      position: relative;
      border: 2px solid transparent;
      margin-bottom: 20rpx;
      .cashback {
        position: absolute;
        right: 0;
        top: 0;
        font-family: Outfit-Regular;
        font-size: 20rpx;
        color: #030303;
        font-weight: 400;
        background: #89dc65;
        line-height: 26rpx;
        padding: 0 10rpx;
        box-sizing: border-box;
      }
      &.active {
        background: #fff;
        border: 2px solid #333333;
        .duihao {
          transform: opaproject, 0.3s;
          opacity: 1;
        }
      }
      .name {
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
      }
      .duihao {
        opacity: 0;
        position: absolute;
        right: 0;
        bottom: 0;
        width: 36rpx;
        height: 24rpx;
        background: url(https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1747194665843.png)
          no-repeat center center transparent;
        background-size: 100% 100%;
      }
    }
  }
  .project-bottom {
    .project-bottom-btn {
      background: #333333;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      height: 84rpx;
      line-height: 84rpx;
      color: #ffffff;
      text-align: center;
      font-weight: 500;
    }
  }
  .project-empty {
    box-sizing: border-box;
    padding-top: 30rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
}
</style>
<style lang="less">
.hospital-choose-input-placeholder {
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #bababa;
  font-weight: 400;
}
</style>
