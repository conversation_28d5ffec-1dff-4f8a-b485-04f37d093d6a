<template>
  <view class="refund-explain">
    <view class="title">{{ explain.title }}</view>
    <view class="tips">{{ explain.tips }}</view>
    <view class="content" v-for="(item, index) in explain.content" :key="index">
      <view>{{ item.p }}</view>
      <view v-if="item.child">
        <view v-for="(val, i) in item.child" :key="i">{{ val }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'refundExplain',
  data() {
    return {
      explain: {
        title: '退款说明',
        tips: '注:退款申请已提交后不可撤销',
        content: [
          {
            p: '1.退款申请提交后，支付款项将原路退回，退款时间1-5个工作日;'
          },
          {
            p: '2.使用定金膨胀的订单，申请退款后，仅支持退回用户实际支付金额;'
          },
          {
            p: '3.使用红包的订单，申请退款后尾款红包金额将不予退还;'
          },
          {
            p: '4.已参与核销返现的订单，原则上服务/套餐一经验证不予退款。如遇特殊情况，需扣除该商品返现金额后根据退还(退款金额=已支付金额-已核销金额);'
          },
          {
            p: '5.如订单未使用:可随时退还全部已支付金额。若订单显示已过期，可随时申请退款,申请成功后退回全部已支付金额;'
          },
          {
            p: '6.购买多次套餐服务，如订单已验证，退剩余次数:退款金额=已支付金额-已消耗金额;',
            child: [
              '(1)已消耗金额=(单次套餐售价-优惠金额/套餐次数)*已做次数(如3次套餐售价900元，购买时实付600元，消费一次后退款，退款时单次卡售价500元。则退款金额=600-(500-300/3)*1=200元);',
              '(2)可退金额≤0时，不可退款;'
            ]
          },
          {
            p: '7.参与新氧优享活动下单的用户在退单后，不再享受活动规则中下单附带的福利。如参与下单送面膜活动，退单后面膜将不再赠送;'
          },
          {
            p: '8.若购买服务/产品到期未达成团人数或主办方取消团购活动，系统将自动发起退款。'
          }
        ]
      }
    };
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.refund-explain {
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  letter-spacing: 0;
  font-weight: 400;
  padding: 40rpx 50rpx 40rpx;
  .title {
    color: #555555;
  }
  .tips {
    color: #ed5c5c;
    margin-top: 20rpx;
  }
  .content {
    color: #aaabb3;
  }
}
</style>
