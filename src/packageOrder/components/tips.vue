<template>
  <view class="second-refund-intercept-wrap" @click.stop="$emit('onCancel')">
    <view class="second-refund-intercept">
      <view class="header">商品很抢手，确认要退单么？</view>
      <view class="footer">
        <button plain @click.stop="$emit('onCancel')">不退了</button>
        <button plain class="primary" @click.stop="$emit('onSubmit')">
          继续退款
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {};
</script>

<style lang="scss" scoped>
.second-refund-intercept-wrap {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  .second-refund-intercept {
    width: 600rpx;
    height: 250rpx;
    padding: 60rpx 60rpx 40rpx;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 20rpx;
    .header {
      font-family: PingFangSC-Medium;
      font-size: 30rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
      margin-bottom: 40rpx;
      text-align: center;
    }
    .footer {
      display: flex;
      justify-content: space-between;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #222222;
      letter-spacing: 0;
      font-weight: 400;
      padding: 0 14rpx;
      button {
        padding: 0;
        margin: 0;
        width: 208rpx;
        height: 68rpx;
        border: 2rpx solid #dedede !important;
        border-radius: 34rpx;
        font-size: 26rpx;
        background: #ffffff;
      }
      .primary {
        font-family: PingFangSC-Medium;
        color: #ffffff;
        font-weight: 500;
        background: #00ab84;
        border: none !important;
      }
    }
  }
}
</style>
