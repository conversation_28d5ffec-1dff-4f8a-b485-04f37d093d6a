<template>
  <view class="refund-wrap">
    <view class="refund">
      <view class="refund-info">
        <view class="refund-info-item">
          <text>退款金额</text>
          <text class="refunf-info-detail"
            >¥{{ refundInfo.refund_price_str || 0 }}</text
          >
        </view>
        <view class="refund-info-item">
          <text>退款方式</text>
          <text class="refunf-info-detail">预计原路返回</text>
        </view>
        <view class="refund-info-item">
          <text>退款时间</text>
          <text class="refunf-info-detail">预计1-5个工作日</text>
        </view>
      </view>
      <view class="refund-reason">
        <view class="refund-reason-title">退款原因</view>
        <label
          v-for="item in reasonList"
          :key="item.id"
          class="radio-item"
          @tap="handleSelect(item)"
        >
          <view class="radio-text">{{ item.name }}</view>
          <view class="radio-btn">
            <image
              style="width: 34rpx; height: 34rpx"
              :src="
                item.selected
                  ? 'https://static.soyoung.com/sy-design/m6nemdnykrbc1726715721918.png'
                  : 'https://static.soyoung.com/sy-design/8k1ijrc526id1726715721966.png'
              "
            />
          </view>
        </label>
      </view>
      <view class="refund-detail-reason" v-if="selectedReason">
        <view class="refund-reason-title">请选择具体退款原因</view>
        <label
          v-for="(detail, index) in selectedReason.child"
          :key="detail.id"
          @tap="handleDetailSelect(detail)"
        >
          <view
            class="radio-item"
            :class="{ 'last': index === selectedReason.child.length - 1 }"
          >
            <view class="radio-text">{{ detail.name }}</view>
            <view class="radio-btn">
              <image
                style="width: 34rpx; height: 34rpx"
                mode="widthFix"
                :src="
                  detail.selected
                    ? 'https://static.soyoung.com/sy-design/m6nemdnykrbc1726715721918.png'
                    : 'https://static.soyoung.com/sy-design/8k1ijrc526id1726715721966.png'
                "
              />
            </view>
          </view>
          <view
            v-if="detail.selected && detail.action === 'subopt'"
            class="detail-item-wrap"
          >
            <view
              class="detail-item"
              :class="{ 'selected': child.selected }"
              v-for="child in detail.child"
              :key="child.id"
              @tap.stop="handleDetailChildSelect(child)"
              >{{ child.name }}</view
            >
          </view>
          <view
            class="refund-reason-text"
            v-if="detail.selected && detail.action === 'textarea'"
          >
            <textarea
              v-model="detail.text"
              placeholder="请输入"
              auto-height
              selection-start="0"
              cursor="0"
              disable-default-padding="true"
              placeholder-style="color:#dedede;"
            />
          </view>
        </label>
      </view>
      <RefundExplain />
    </view>
    <view class="refund-footer">
      <button
        @tap="handleSubmit"
        class="refund-btn"
        :class="{ 'disabled': disabled || isDisabled }"
      >
        申请退款
      </button>
    </view>
    <!-- 第一次拦截弹窗 -->
    <confirmation-window
      class="first-confirmation-window"
      ref="firstConfirmation"
      button-left="继续退款"
      button-right="查看可预约时间"
      title="没有约到合适的时间？换个门店试试"
      :automatic-closing="true"
      @onInteractive="handleFirstInteraction"
    >
      <template v-slot:custom-content>
        <view class="custom-content">
          您可以更换服务门店，查看更多可预约时间，或者「订阅可预约提醒」等位。如有问题，请<text
            class="link"
            @tap="onContactService"
            >联系客服</text
          >为您服务
        </view>
      </template>
    </confirmation-window>
    <!-- 第二次拦截弹窗 -->
    <ConfirmationWindow
      class="second-confirmation-window"
      ref="secondConfirmation"
      button-left="不退了"
      button-right="继续退款"
      content="商品很抢手，确认要退单么？"
      :automatic-closing="true"
      @onInteractive="handleSecondInteraction"
    />
  </view>
</template>

<script>
import ConfirmationWindow from '@/packageOrder/components/confirmation-window.vue';
import { RefundExplain } from './components/refund-explain.vue';
import { GetRefundInfo, SubmitRefund } from '@/api/refund';
import { mapState } from 'vuex';

const eventBus = getApp().globalData.eventBus;

export default {
  name: 'refund',
  components: {
    RefundExplain,
    ConfirmationWindow
  },
  data() {
    return {
      reasonList: [
        {
          id: 1,
          name: '个人方面的原因',
          selected: false,
          child: [
            {
              id: 11,
              name: '没有时间', // 弹窗拦截 1. 点击继续退款--申请退款 2. 点查看可预约时间，跳转订单详情，调起预约弹窗（分为有预约，无预约）3. 点空白处关闭弹窗
              selected: false,
              action: 'popup'
            },
            {
              id: 12,
              name: '买多/买错/计划有变',
              selected: false
            },
            {
              id: 13,
              name: '未使用优惠/项目降价',
              selected: false
            },
            {
              id: 14,
              name: '商品信息描述不清',
              selected: false
            },
            {
              id: 15,
              name: '更换其他限度，购买了其他项目',
              selected: false
            },
            {
              id: 16,
              name: '个人身体原因',
              selected: false
            },
            {
              id: 17,
              name: '订单已过期',
              selected: false
            },
            {
              id: 18,
              name: '其他原因', // 要有富文本框，输入原因
              selected: false,
              text: '',
              action: 'textarea'
            }
          ]
        },
        {
          id: 2,
          name: '医院方面的原因',
          selected: false,
          child: [
            {
              id: 21,
              name: '预约不到', // 弹窗拦截 1. 点击继续退款--申请退款 2. 点查看可预约时间，跳转订单详情，调起预约弹窗（分为有预约，无预约）3. 点空白处关闭弹窗
              selected: false,
              action: 'popup'
            },
            {
              id: 22,
              name: '商品信息描述与描述不符',
              selected: false,
              child: [
                {
                  id: 221,
                  name: '限购未标明',
                  selected: false
                },
                {
                  id: 222,
                  name: '剂量不符',
                  selected: false
                },
                {
                  id: 223,
                  name: '仪器不符',
                  selected: false
                },
                {
                  id: 224,
                  name: '操作医师不符',
                  selected: false
                },
                {
                  id: 225,
                  name: '额外加价',
                  selected: false
                }
              ],
              action: 'subopt'
            },
            {
              id: 23,
              name: '商家让退掉店里更优惠',
              selected: false
            },
            {
              id: 24,
              name: '以各种理由拒绝服务',
              selected: false
            },
            {
              id: 25,
              name: '服务态度差，不友好',
              selected: false
            },
            {
              id: 26,
              name: '评价不好',
              selected: false
            },
            {
              id: 27,
              name: '更多不爽必须吐槽', // 富文本框 必填
              selected: false,
              text: '',
              action: 'textarea'
            }
          ]
        }
      ],
      selectedReason: null, // 保存选中的原因
      selectedDetailReason: null, // 保存选中的具体退款原因
      firstRefundInterceptVisible: false, //首次拦截退款弹窗
      secondRefundInterceptVisible: false, // 二次拦截弹窗
      refundInfo: null,
      orderId: null,
      uid: null,
      isDisabled: false
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    disabled() {
      if (this.selectedDetailReason && this.selectedDetailReason.selected) {
        if (this.selectedDetailReason.action === 'subopt') {
          return !(
            this.selectedDetailReason.selected &&
            this.selectedDetailReason.child.filter((item) => item.selected)
              .length > 0
          );
        } else if (this.selectedDetailReason.action === 'textarea') {
          return !this.selectedDetailReason.text;
        } else {
          return !this.selectedDetailReason.selected;
        }
      }
      // 如果 selectedReason 不存在或其 selected 为 false，则禁用按钮
      return true;
    }
  },
  onLoad(opt) {
    console.log(opt, 'opt');
    console.log(this.userInfo, 'userInfo');
    this.orderId = opt.order_id;
    this.getRefundInfo(opt.order_id);
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_apply_for_refund_page',
      ext: {
        order_id: this.orderId
      }
    });
  },
  methods: {
    // 获取退款信息
    async getRefundInfo(order_id) {
      const { errorCode, errorMsg, responseData } = await GetRefundInfo({
        order_id
      });
      if (errorCode !== 0) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      this.refundInfo = responseData;
      console.log(this.refundInfo, 'this.refundInfo');
    },
    // 选择退款原因
    handleSelect(item) {
      console.log(item, '退款原因');
      if (!item.selected) {
        this.reasonList.forEach((i) => {
          i.selected = false;
          if (i.id === item.id) {
            i.selected = true;
            i.child.forEach((child) => {
              child.selected = false;
            });
          }
        });
        this.selectedReason = { ...item, selected: true };
        this.selectedDetailReason = null;
      }
    },
    // 选择具体退款原因
    handleDetailSelect(detail) {
      console.log(detail, '具体原因退款原因');
      // 切换不同选项时清空状态
      if (!detail.selected) {
        this.selectedReason.child.forEach((item) => {
          if (item.id !== detail.id) {
            item.selected = false;
            if (item.child) {
              item.child.forEach((child) => {
                child.selected = false;
              });
            }
            if (item.text) {
              item.text = '';
            }
          }
        });
      }
      detail.selected = true;
      this.selectedDetail;
      this.selectedDetailReason = detail;
      if (detail.action === 'popup') {
        // this.firstRefundInterceptVisible = true;
        this.showFirstWindow();
      }
    },
    // 选择具体原因的子选项
    handleDetailChildSelect(child) {
      console.log(child, '具体原因的子选项');
      child.selected = !child.selected;
    },
    // 展示第一次拦截弹窗
    showFirstWindow() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_apply_for_refund:refund_stay_pop_exposure',
        ext: {}
      });
      this.$refs.firstConfirmation.open();
    },
    // 展示第二次拦截弹窗
    showSecondWindow() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_page:liyidian_tc_exposure',
        ext: {
          order_id: this.orderId
        }
      });
      this.$refs.secondConfirmation.open();
    },
    // 提交退款申请
    handleSubmit() {
      if (this.isDisabled) return;
      this.$reportData({
        info: 'sy_wxtuan_tuan_apply_for:tui_kuan_click',
        ext: {
          order_id: this.orderId,
          content: '申请退款'
        }
      });
      console.log(this.selectedReason, this.selectedDetailReason, '退款原因');
      if (!this.selectedReason) {
        uni.showToast({
          title: '请选择或填写退款原因',
          icon: 'none'
        });
        return;
      }
      if (!this.selectedDetailReason) {
        uni.showToast({
          title: '请选择或填写退款原因',
          icon: 'none'
        });
        return;
      }
      if (
        this.selectedDetailReason.action === 'textarea' &&
        this.selectedDetailReason.selected &&
        this.selectedDetailReason.text === ''
      ) {
        uni.showToast({
          title: '请选择或填写退款原因',
          icon: 'none'
        });
        return;
      }
      if (
        this.selectedDetailReason.action === 'subopt' &&
        this.selectedDetailReason.selected &&
        this.selectedDetailReason.child.filter((item) => item.selected)
          .length === 0
      ) {
        uni.showToast({
          title: '请选择或填写退款原因',
          icon: 'none'
        });
        return;
      }
      this.showSecondWindow();
    },
    // 第一次拦截-联系客服
    onContactService() {
      // console.log('第一次拦截联系客服');
      this.firstRefundInterceptVisible = false;
      const pid = this.refundInfo ? this.refundInfo.sku_id : '';
      const url = `/packageAccount/consult?type=3&skuid=${pid}&scene=2`;
      // 判断是否已加C，加C后跳转到客服会话页
      this.$toKefuDialog({
        source: 'sy_yx_mini_program_private_msg_customer_service_order',
        pid: pid,
        unJoinFn: () => {
          // 未加C时执行
          this.$bridge({
            url: url
          });
        }
      });
    },
    // 第一次拦截-查看可预约时间-跳转到订单详情页-调起预约弹窗
    onAppointment() {
      this.firstRefundInterceptVisible = false;
      eventBus.$emit('openAppointment', {
        from: 'refund',
        orderId: this.orderId
      });
      uni.navigateBack();
    },
    // 处理第一次拦截弹窗交互
    handleFirstInteraction({ type }) {
      console.log(type, '第一次拦截弹窗交互');
      let click_type = '';
      switch (type) {
        case 'buttonLeft':
          click_type = '继续退款';
          break;
        case 'buttonRight':
          // 联系客服
          click_type = '查看可预约时间';
          break;
        case 'maskClick':
          click_type = '关闭';
          break;
        default:
          break;
      }
      console.log(click_type, 'click_type');
      this.$reportData({
        info: 'sy_wxtuan_tuan_apply_for_refund:refund_stay_pop_click',
        ext: {
          click_type: click_type
        }
      });
      if (type === 'buttonLeft') {
        // 继续退款，调起第二拦截弹窗
        this.showSecondWindow();
      } else if (type === 'buttonRight') {
        // 查看可预约时间
        this.onAppointment();
      }
    },
    // 处理二次拦截弹窗交互
    handleSecondInteraction(event) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_page:bt_click',
        ext: {
          order_id: this.orderId,
          type: 0
        }
      });
      if (event.type === 'buttonLeft') {
        // 不退了，返回订单页
        uni.navigateBack();
      } else if (event.type === 'buttonRight') {
        // 继续退款，提交退款申请
        this.onSecondSubmit();
      }
    },
    // 第二次拦截-申请退款
    async onSecondSubmit() {
      console.log('第二次拦截--申请退款');
      let reason = '';
      let other_reason = '';
      if (this.selectedDetailReason.action === 'textarea') {
        reason = `${this.selectedReason.name}_${this.selectedDetailReason.name}`;
        other_reason = this.selectedDetailReason.text;
      } else if (this.selectedDetailReason.action === 'subopt') {
        const sub = this.selectedDetailReason.child
          .filter((item) => item.selected)
          .map((item) => item.name)
          .join('_');
        console.log(sub, 'sub');
        reason = `${this.selectedReason.name}_${this.selectedDetailReason.name}_${sub}`;
      } else {
        reason = `${this.selectedReason.name}_${this.selectedDetailReason.name}`;
      }
      const params = {
        order_id: this.orderId,
        uid: this.userInfo.uid,
        sku_id: this.refundInfo ? this.refundInfo.sku_id : '',
        reason: reason,
        other_reason: other_reason
      };
      console.log('params', params);
      const { errorCode, errorMsg } = await SubmitRefund(params);
      if (+errorCode !== 0) {
        uni.showModal({
          showCancel: false,
          // content: '当前项目正在执行中，请跟您的小管家核实治疗状态后再退~',
          content: errorMsg,
          success: function (res) {
            console.log(res, 'res');
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
        return;
      }
      uni.showToast({
        icon: 'success',
        title: '退款成功',
        duration: 2000
      });
      this.isDisabled = true;
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
    }
  }
};
</script>
<style scoped lang="less">
@px: 2rpx;
.refund-wrap {
  background: #f8f8f8;
  padding: 0 10rpx;
  height: 100vh;
  overflow-y: auto;
  .refund {
    height: calc(100vh - 180rpx);
    overflow-y: scroll;
    &-info {
      margin-top: 14rpx;
      width: 730rpx;
      height: 280rpx;
      // border-radius: 16rpx;
      box-sizing: border-box;
      background-color: #fff;
      padding: 40rpx 50rpx 0 50rpx;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #030303;
      font-weight: 400;
      &-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 50rpx;
        .refunf-info-detail {
          font-family: PingFangSC-Medium;
          color: #222222;
          font-weight: 500;
        }
      }
    }
    &-reason,
    &-detail-reason {
      margin-top: 14rpx;
      width: 730rpx;
      background: #fff;
      // border-radius: 16rpx;
      padding: 40rpx 50rpx 0 50rpx;
      box-sizing: border-box;
      &-title {
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        color: #222222;
        font-weight: 500;
        // margin-bottom: 44rpx;
      }
      .radio-item {
        display: flex;
        justify-content: space-between;
        padding: 40rpx 0;
        border-bottom: 1rpx solid #f2f2f2;
        &:nth-last-of-type(1) {
          border-bottom: none;
        }
        &.last {
          padding-bottom: 0rpx;
        }
        .radio-text {
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #030303;
          font-weight: 400;
        }
        .radio-btn {
          width: 40rpx;
          height: 40rpx;
          radio {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
      .detail-item-wrap {
        margin-top: -30rpx;
        display: flex;
        flex-wrap: wrap;
        margin-right: -20rpx; /* 撤销多余的右边距 */
        .detail-item {
          padding: 12rpx 20rpx;
          background: #f2f2f2;
          color: #333333;
          font-weight: 400;
          // border-radius: 15px;
          font-size: 24rpx;
          line-height: 36rpx;
          text-align: center;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          &.selected {
            background: #ebfbdc;
            color: #61b43e;
            font-family: PingFangSC-Medium;
            font-weight: 500;
          }
        }
      }
      .refund-reason-text {
        width: 690rpx;
        padding-top: 30rpx;
        border-bottom: 1rpx solid #f0f0f0;
        textarea {
          width: 690rpx !important;
          font-family: PingFangSC-Regular;
          font-size: 28rpx;
          color: #222222;
          font-weight: 400;
          text-indent: 0;
          text-align: left;
          line-height: 42rpx;
        }
      }
    }
    &-detail-reason {
      padding-bottom: 50rpx;
    }
  }
  .refund-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    background: #fff;
    width: 100%;
    height: 180rpx;
    padding: 12rpx 30rpx 80rpx 30rpx;
    display: flex;
    box-sizing: border-box;
    align-items: center;
    z-index: 10;
    .refund-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 690rpx;
      height: 88rpx;
      padding: 0;
      background: #333333;
      // border-radius: 44rpx;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      &.disabled {
        background: #ccc;
        border: none !important;
      }
      &::after {
        border: none !important;
      }
    }
  }
}
::v-deep .first-confirmation-window {
  .confirmation-window-title {
    font-size: 15 * @px !important;
    color: #333 !important;
  }
  .custom-content {
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    color: #333333;
    letter-spacing: 0;
    text-align: justify;
    line-height: 23 * @px;
    font-weight: 400;
    margin-bottom: 20 * @px;
    .link {
      color: #333333;
    }
  }
}
::v-deep .second-confirmation-window {
  .confirmation-window-content {
    height: 21 * @px !important;
    font-family: PingFangSC-Medium !important;
    font-size: 15 * @px !important;
    color: #333333 !important;
    letter-spacing: 0;
    font-weight: 500 !important;
  }
}
</style>
