<script>
import Vue from 'vue';
import { mapState } from 'vuex';
import Popup from '@/components/uni/popup.vue';
import ApptHospital from '@/components/appt/confirm-order-appt-hospital.vue';
import ChooseProject from './components/ChooseProject.vue';
import Agreement from '@/components/appt/agreement.vue';
import ApptWranText from '@/components/order/appt-warn-text.vue';
import pay, { subscribeTmpl } from '@/components/newPay';
import NewCard from '@/components/coupon/new-card.vue';
import SubscribeMixins from '@/mixins/subscribe';
// const eventBus = getApp().globalData.eventBus;

export default {
  name: 'confirmOrder',
  components: {
    NewCard,
    Agreement,
    Popup,
    ApptHospital,
    ApptWranText,
    ChooseProject
  },
  mixins: [SubscribeMixins],
  data() {
    return {
      initializeCityId: 0,
      background: '',
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      pageInfo: {
        product_module: {},
        user_module: {},
        order_module: {},
        city_id: 0,
        activity_id: 0
      },
      chainCityList: [],
      couponList: {
        unvalid: null,
        valid: null
      },
      newCouponList: {
        unvalid: null,
        valid: null
      },
      confirmOrderData: null,
      disablePageScrolling: false, // 禁止页面滚动
      agreementVisible: false, // 协议未勾选提示弹窗
      intentionData: null,
      payLock: false, // true 锁定 false 未锁定
      ApptHospitalLoading: false,
      confirmCouponInfo: null,
      projectVisible: false,
      curProject: '',
      curProjectName: ''
    };
  },
  watch: {
    agreement() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_proinfo:sla_click',
        ext: {}
      });
    },
    'pageInfo.city_id': function (newVal, oldVal) {
      console.log('pageInfo.city_id', newVal, oldVal);
      if (Number(oldVal) && Number(newVal) !== Number(oldVal)) {
        this.getOrderDetailsData();
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    /**
     * 可用优惠券底弹窗选中券ID
     * 兼容首次进入 deposit_coupon_code_id = -1 情况
     * */
    selectCouponId() {
      if (
        Number(
          this.pageInfo?.product_module?.[this.product.pid]
            ?.deposit_coupon_code_id
        ) === -1
      ) {
        const data = this.product?.reduce_detail_list?.find(
          (item) => item?.type === 'deposit_coupon'
        );
        return Number(data?.code_id);
      } else {
        return Number(
          this.pageInfo?.product_module?.[this.product.pid]
            ?.deposit_coupon_code_id
        );
      }
    },
    /**
     * 新版优惠券选中券ID
     * */
    selectCouponV2Id() {
      if (
        Number(
          this.pageInfo?.product_module?.[this.product.pid]
            ?.new_deposit_coupon_code_id
        ) === -1
      ) {
        const data = this.product?.reduce_detail_list?.find(
          (item) => item?.type === 'deposit_coupon_new_v2'
        );
        return Number(data?.code_id);
      } else {
        return Number(
          this.pageInfo?.product_module?.[this.product.pid]
            ?.new_deposit_coupon_code_id
        );
      }
    },
    backIcon() {
      return this.background === '#ffffff'
        ? 'https://static.soyoung.com/sy-design/3cj8rc3ipek931726282046858.png'
        : 'https://static.soyoung.com/sy-design/2my50ua05v4r41726282046878.png';
    },
    agreement() {
      return this.confirmOrderData?.agreement?.[0]?.is_selected;
    },
    /**
     * 单商品数据
     * */
    product() {
      return this.confirmOrderData?.product_list?.[0] || {};
    },

    depositCoupon() {
      if (!this.product?.reduce_detail_list) {
        return null;
      }
      return this.product?.reduce_detail_list?.find(
        (item) => item?.type === 'deposit_coupon'
      );
    },
    depositCouponNewV2() {
      if (!this.product?.reduce_detail_list) {
        return null;
      }
      return this.product?.reduce_detail_list?.find(
        (item) => item?.type === 'deposit_coupon_new_v2'
      );
    },
    /**
     * 津贴数据
     * */
    allowanceReduceModule() {
      return {
        title:
          this.confirmOrderData?.promotion_data?.allowance_reduce_module
            ?.title || '选择津贴',
        allowance_reduce_list: this.confirmOrderData?.promotion_data
          ?.allowance_reduce_module?.allowance_reduce_list?.length
          ? this.confirmOrderData?.promotion_data?.allowance_reduce_module
              ?.allowance_reduce_list
          : []
      };
    },
    pageMetaStyle() {
      return `overflow-y: ${this.disablePageScrolling ? 'hidden' : 'scroll'}`;
    },
    appointmentReminderShow() {
      // 强制预约
      if (!this.intentionData?.time?.from || !this.intentionData?.time?.to) {
        if (
          Number(this.confirmOrderData?.appointment_info?.force_reserve) === 1
        ) {
          return {
            text: '当前订单需先选择预约意向再支付',
            type: '',
            visible: true
          };
        } else {
          return {
            text: '您尚未选择意向到店时间，请选择时间后提交意向',
            type: '',
            visible: true
          };
        }
      }
      return {
        text: '预约将在平台确认后生效，请以最终确认结果为准',
        type: 'warning',
        visible: true
      };
    },
    intention() {
      if (
        this.intentionData &&
        this.intentionData.time &&
        this.intentionData.date
      ) {
        const year = this.intentionData.date.year;
        const month =
          this.intentionData.date.month < 10
            ? `0${this.intentionData.date.month}`
            : String(this.intentionData.date.month);
        const day =
          this.intentionData.date.day < 10
            ? `0${this.intentionData.date.day}`
            : String(this.intentionData.date.day);
        return {
          appoint_city_id: this.pageInfo.city_id || 0,
          appoint_hospital_id: this.intentionData.hospital_id,
          appoint_start_date: this.intentionData.time.from
            ? `${year}-${month}-${day} ${this.intentionData.time.from}`
            : '',
          appoint_end_date: this.intentionData.time.to
            ? `${year}-${month}-${day} ${this.intentionData.time.to}`
            : ''
        };
      }
      return {
        appoint_city_id: this.pageInfo.city_id || 0,
        appoint_hospital_id: this.intentionData?.hospital_id, // 意向城市
        appoint_start_date: '',
        appoint_end_date: ''
      };
    },
    /**
     * 支付类型
     * @return {{ pay_type: number }} pay_type 3 微信支付 27 分期支付
     * */
    payType() {
      const payment_module = this.confirmOrderData?.payment_module?.find(
        (find) => Number(find.is_selected) === 1
      );
      const number_periods = payment_module?.child_module?.find(
        (find) => Number(find.is_selected) === 1
      );
      return {
        pay_type: payment_module?.pay_type,
        loan_num: number_periods?.loan_num
      };
    },
    payButtonText() {
      if (Number(this.payType.pay_type) === 27) {
        return '立即申请';
      }
      if (this.intentionData?.time?.from && this.intentionData?.time?.to) {
        return '支付并提交预约';
      }
      return '立即支付';
    },
    /**
     * 拼团模块
     * */
    groupModule() {
      return this.confirmOrderData?.promotion_data?.group_module;
    },
    /**
     * 团类型
     * @return {number} 0 非拼团商品 1 开团 2 参团
     * */
    groupType() {
      if (
        Number(
          this.pageInfo?.product_module?.[this.product.pid]?.is_group_mode
        ) === 1
      ) {
        if (Number(this.pageInfo?.order_module?.group_id) === 0) {
          return 1;
        }
        return 2;
      }
      return 0;
    }
  },
  methods: {
    handleProjectChange(param) {
      console.log('handleProjectChange', param);
      this.curProject = param;
      this.curProjectName = this.confirmOrderData.appointment_sku_list.find(
        (item) => item.sku_id === param
      ).title;
      this.getChainCityList();
      this.projectVisible = false;
    },
    handleConfirmCoupon(status) {
      const action = this.confirmCouponInfo.action;
      const popRef =
        action === 'deposit_coupon' ? 'coupon-popup' : 'coupon-popup-v2';
      if (status === 'confirm') {
        this.pageInfo.product_module[this.product.pid][
          action === 'deposit_coupon'
            ? 'deposit_coupon_code_id'
            : 'new_deposit_coupon_code_id'
        ] = this.confirmCouponInfo.code_id;
        this.pageInfo.product_module[this.product.pid].action = action;
        this.getOrderDetailsData();
        setTimeout(() => {
          this.$refs[popRef].close('bottom');
        }, 150);
      }
      this.$refs.confirmCouponPopup.close('bottom');
    },
    redEnvelopeConfirmation(status) {
      if (status === 'confirm') {
        this.pageInfo.product_module[
          this.product.pid
        ].deposit_coupon_code_id = 0;
        this.getOrderDetailsData();
      }
      setTimeout(() => {
        this.$refs['coupon-popup'].close('bottom');
      }, 150);
      this.$refs.redEnvelopeSecondaryConfirmation.close('bottom');
    },
    redEnvelopeConfirmationV2(status) {
      if (status === 'confirm') {
        this.pageInfo.product_module[
          this.product.pid
        ].new_deposit_coupon_code_id = 0;
        this.getOrderDetailsData();
      }
      setTimeout(() => {
        this.$refs['coupon-popup-v2'].close('bottom');
      }, 150);
      this.$refs.redEnvelopeSecondaryConfirmationV2.close('bottom');
    },
    handlePopClose(ref) {
      this.$refs[ref].close('bottom');
    },
    couponPopupClose() {
      this.$refs['coupon-popup'].close('bottom');
    },
    couponV2PopupClose() {
      this.$refs['coupon-popup-v2'].close('bottom');
    },
    cardSelect(item) {
      let confirmCouponInfo = null;
      // 判断是否取消选择，兼容首次进入 deposit_coupon_code_id = -1 情况
      if (
        Number(this.selectCouponId) === Number(item.code_info.code_id) ||
        (Number(this.selectCouponId) === -1 &&
          Number(this.selectCouponId) === Number(item.code_info.code_id))
      ) {
        this.$refs.redEnvelopeSecondaryConfirmation.open('bottom');
      } else {
        const otherType = this.depositCouponNewV2?.is_mutex_old_new || 0;
        if (otherType != 0) {
          if (item.extend_info.is_mutex_old_new === 1) {
            confirmCouponInfo = {
              action: 'deposit_coupon',
              code_id: item.code_info.code_id
            };
          } else if (
            item.extend_info.is_mutex_old_new === 2 &&
            otherType == 1
          ) {
            confirmCouponInfo = {
              action: 'deposit_coupon',
              code_id: item.code_info.code_id
            };
          }
        }
        if (confirmCouponInfo) {
          this.confirmCouponInfo = confirmCouponInfo;
          this.$refs.confirmCouponPopup.open('bottom');
          return;
        }
        this.pageInfo.product_module[this.product.pid].deposit_coupon_code_id =
          item.code_info.code_id;
        this.pageInfo.product_module[this.product.pid].action =
          'deposit_coupon';
        this.getOrderDetailsData();
        setTimeout(() => {
          this.$refs['coupon-popup'].close('bottom');
        }, 150);
      }
    },
    cardSelectV2(item) {
      let confirmCouponInfo = null;
      // 判断是否取消选择，兼容首次进入 deposit_coupon_code_id = -1 情况
      if (
        Number(this.selectCouponV2Id) === Number(item.code_info.code_id) ||
        (Number(this.selectCouponV2Id) === -1 &&
          Number(this.selectCouponV2Id) === Number(item.code_info.code_id))
      ) {
        this.$refs.redEnvelopeSecondaryConfirmationV2.open('bottom');
      } else {
        const otherType = this.depositCoupon?.is_mutex_old_new || 0;
        if (otherType != 0) {
          if (item.extend_info.is_mutex_old_new === 1) {
            confirmCouponInfo = {
              action: 'deposit_coupon_new_v2',
              code_id: item.code_info.code_id
            };
          } else if (
            item.extend_info.is_mutex_old_new === 2 &&
            otherType == 1
          ) {
            confirmCouponInfo = {
              action: 'deposit_coupon_new_v2',
              code_id: item.code_info.code_id
            };
          }
        }
        if (confirmCouponInfo) {
          this.confirmCouponInfo = confirmCouponInfo;
          this.$refs.confirmCouponPopup.open('bottom');
          return;
        }
        this.pageInfo.product_module[
          this.product.pid
        ].new_deposit_coupon_code_id = item.code_info.code_id;
        this.pageInfo.product_module[this.product.pid].action =
          'deposit_coupon_new_v2';
        this.getOrderDetailsData();
        setTimeout(() => {
          this.$refs['coupon-popup-v2'].close('bottom');
        }, 150);
      }
    },
    handleBack() {
      if (getCurrentPages().length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack();
      }
    },
    timeFormattingMS(time) {
      try {
        if (!time) {
          return '';
        }
        const date = new Date(time);
        return `${date.getFullYear()}.${
          date.getMonth() + 1 < 10
            ? '0' + (date.getMonth() + 1)
            : date.getMonth() + 1
        }.${date.getDate() < 10 ? '0' + date.getDate() : date.getDate()} ${
          date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
        }:${
          date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        }`;
      } catch (error) {
        return '';
      }
    },
    /**
     * 滚动到预约位置
     */
    async scroll2ApptStock() {
      try {
        const [{ top: containerTop }, { top }] = await Promise.all([
          this.$queryFirstNode('.confirm-order'),
          this.$queryFirstNode('.confirm-order-service')
        ]);
        uni.pageScrollTo({
          scrollTop: top - containerTop
        });
      } catch (e) {
        uni.$log('scroll2ApptStock', e, 'error');
      }
    },
    /**
     * 获取优惠券列表
     * */
    async getCouponPackage(type = 'deposit_coupon') {
      try {
        // 判断用户是否有立减红包
        const coupon = this.product.reduce_detail_list.find(
          (item) => item.type === type
        );
        if (!coupon) {
          return;
        }
        const requestBody = {
          sku_id: this.confirmOrderData.appointment_info.top_sku_id,
          code_id: coupon?.code_id,
          real_reduce_amount: coupon?.reduce_price,
          reduce_type: 1,
          product_param: JSON.stringify({
            price_online: this.product.price_online,
            price_deposit: this.product.price_deposit,
            price_final: this.product.price_online - this.product.price_deposit,
            amount: this.product.amount,
            hospital_id: this.product.hospital_id
          })
        };
        if (type === 'deposit_coupon_new_v2') {
          requestBody.is_new_coupon = 1;
        }
        if (coupon?.is_selected === 0) {
          delete requestBody.code_id;
          delete requestBody.real_reduce_amount;
        }
        const response = await Vue.$request({
          url: '/syChainTrade/wxapp/coupon/couponPackage',
          data: requestBody
        })
          .then((res) => res.data)
          .catch((error) => error);
        if (response.errorCode !== 0) {
          throw new Error(response.errorMsg);
        }
        if (type === 'deposit_coupon_new_v2') {
          this.newCouponList = response.responseData;
        } else {
          this.couponList = response.responseData;
        }
      } catch (error) {
        console.log(error);
        uni.hideLoading();
        uni.showToast({
          title: error.message,
          icon: 'error'
        });
      }
    },
    /**
     * 获取城市列表
     * */
    async getChainCityList() {
      try {
        const response = await Vue.$request({
          url: '/syGroupBuy/chain/reservation/chainCityList',
          data: {
            sku_id: this.confirmOrderData.appointment_info.top_sku_id,
            ingredients_id:
              this.curProject ||
              this.confirmOrderData.appointment_info.ingredients_id,
            select_city_id: Number(this.pageInfo.city_id) || 0,
            total_amount:
              this.confirmOrderData.button.final_deposit_for_save_order
          }
        })
          .then((res) => res.data)
          .catch((error) => error);
        if (response.errorCode !== 200 && response.errorCode !== 0) {
          throw new Error(response.errorMsg);
        }
        this.chainCityList = response.responseData;
      } catch (error) {
        console.error(error.message);
        uni.hideLoading();
        uni.showToast({
          title: error.message,
          icon: 'error'
        });
      }
    },
    /**
     * 订单数据校验
     * */
    async orderCheck() {
      return new Promise((resolve) => {
        // 协议签署
        if (Number(this.confirmOrderData.agreement[0].is_selected) !== 1) {
          this.agreementVisible = true;
          resolve(false);
          return;
        }
        // 意向到店服务强制选择
        if (
          Number(this.confirmOrderData.appointment_info.force_reserve) === 1 &&
          (!this.intentionData ||
            !this.intentionData.time ||
            !this.intentionData.time.from ||
            !this.intentionData.time.to)
        ) {
          // 滚动到相对应的位置
          this.scroll2ApptStock();
          uni.showToast({
            title: '请先选择预约到店时间',
            icon: 'none'
          });
          resolve(false);
          return;
        }
        resolve(true);
      });
    },
    /**
     * 生单接口
     * */
    async saveOrder() {
      try {
        if (!(await this.orderCheck())) {
          return;
        }
        if (this.payLock) {
          return;
        }
        this.payLock = true;
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const response = await Vue.$request({
          url: `/syChainTrade/wxapp/order/saveOrder?key=${this.confirmOrderData.key_for_save_order}`,
          method: 'post',
          data: {
            ...this.intention,
            pay_type: this.payType.pay_type,
            expand_common_key: 'pusher_id',
            expand_common_val: this.userInfo.psid || 0,
            appoint_sku_id: this.curProject
          }
        })
          .then((res) => res.data)
          .catch((error) => error);
        if (
          response.errorMsg === '请先选择预约时间' ||
          response.errorMsg === '您尚未选择意向到店时间，请选择时间后提交意向'
        ) {
          uni.hideLoading();
          uni.showToast({
            title: response.errorMsg,
            icon: 'none'
          });
          // 滚动到相对应的位置
          await this.scroll2ApptStock();
          return;
        }
        if (response.errorCode !== 0) {
          setTimeout(() => {
            uni.navigateBack({
              delta: 1
            });
          }, 1500);
          throw { status: -1, message: response.errorMsg };
        }
        this.$reportData({
          info: 'sy_wxtuan_tuan_orders:pay_click',
          ext: {
            order_id: response.responseData.order_id_str,
            skuid: this.product.pid,
            activity_id: this.pageInfo.activity_id,
            activity_type: 2,
            card_type: this.groupType
          }
        });
        const payResult = await pay(
          {
            oid: response.responseData.oid,
            pay_type: this.payType.pay_type,
            yfbei_num: this.payType.loan_num
          },
          async () =>
            this.createGroupBySub(subscribeTmpl, [response.responseData.oid])
        );
        console.log('订单返回数据', payResult);
        switch (payResult.status) {
          case -1:
            // 支付失败
            throw { status: -1, message: payResult.message };
          case 0:
            // await this.createGroupBySub(
            //   [
            //     '5v3EjUo21KQ8D1jjb7eBBDNOAeQuY7bg5pRpKxJSgp0',
            //     'c1w_2vGA0TdnYgZIsHO19v1jsyhQI5bMdyZSAZTb65U',
            //     'siVw2L2pYZVqo9P79iZLJKdh_yhP6cVUftprSvK0aX8'
            //   ],
            //   [response.responseData.order_id_str]
            // );
            // 支付成功
            uni.redirectTo({
              url: `/packageOrder/paySuccess?id=${response.responseData.order_id_str}&pid=${this.product.pid}&activity_id=${this.pageInfo.activity_id}&groupType=${this.groupType}`
            });
            break;
          case 1:
            // 取消支付
            uni.navigateTo({
              url: `/packageOrder/order-detail?orderId=${response.responseData.order_id_str}`
            });
            break;
          case 2:
            // 分期支付
            break;
        }
        console.log('===>', payResult);
        uni.hideLoading();
      } catch (error) {
        console.log(error);
        uni.hideLoading();
        if (error.status === -1) {
          uni.showToast({
            title: error.message,
            icon: 'none'
          });
        }
      }
      this.payLock = false;
    },
    /**
     * 获取订单详情数据
     * */
    async getOrderDetailsData() {
      try {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        // 更新用户 uid
        this.pageInfo.user_module.uid = Number(this.userInfo.uid);
        // 注入用户open_id
        this.pageInfo.order_module.open_id = this.userInfo.openId;
        // 注入城市id
        this.pageInfo.order_module.appoint_city_id =
          Number(this.pageInfo.city_id) || 0;
        // 拼团数据类型转换
        if (this.pageInfo.order_module.group_id) {
          this.pageInfo.order_module.group_id = Number(
            this.pageInfo.order_module.group_id
          );
        }
        const response = await Vue.$request({
          url: '/syChainTrade/wxapp/order/confirmOrder',
          data: {
            channel: 3,
            user_module: JSON.stringify(this.pageInfo.user_module),
            order_module: JSON.stringify(this.pageInfo.order_module),
            product_module: JSON.stringify(this.pageInfo.product_module)
          }
        })
          .then((res) => res.data)
          .catch((error) => error);
        if (response.errorCode !== 0) {
          throw { status: -1, message: response.errorMsg };
        }
        this.confirmOrderData = response.responseData;
        if (!this.curProject) {
          this.curProject = (
            (this.confirmOrderData.appointment_sku_list || [])[0] || {}
          ).sku_id;
          this.curProjectName = (
            (this.confirmOrderData.appointment_sku_list || [])[0] || {}
          ).title;
        }

        this.pageInfo.product_module[
          this.product.pid
        ].new_deposit_coupon_code_id = this.depositCouponNewV2?.code_id || 0;
        this.pageInfo.product_module[this.product.pid].deposit_coupon_code_id =
          this.depositCoupon?.code_id || 0;

        uni.hideLoading();
      } catch (error) {
        console.error(error);
        uni.hideLoading();
        if (error.status === -1) {
          uni.showToast({
            title: error.message,
            icon: 'none'
          });
        }
      }
    },
    /**
     * 分期阶段选择
     * @param {Object} param1 {
     *    "icon": "https://static.soyoung.com/sy-pre/1weeg5m2ihzfg-1631502600797.png",
     *    "pay_type": 27,
     *    "pay_type_name": "氧分呗分期",
     *    "pay_type_desc": "线上分期申请成功仅代表您享有一定分期额度，线下到店后核销时进入实际支付流程",
     *    "is_selected": 0,
     *    "child_module": {param2}[]
     * }
     * @param {Object} param2 {
     *    "icon_desc": "免息",
     *    "loan_num": 3,
     *    "sub_title": "¥333.33x3期",
     *    "desc": "免服务费",
     *    "is_selected": 0
     * }
     * */
    stageSelection(param1, param2) {
      if (Number(param1.is_selected) !== 1) {
        for (let item of this.confirmOrderData.payment_module) {
          item.is_selected =
            Number(param1.pay_type) === Number(item.pay_type) ? 1 : 0;
        }
        this.pageInfo.order_module.payment_channel = param1.pay_type;
      }
      for (let item of param1.child_module) {
        item.is_selected = 0;
      }
      param2.is_selected = 1;
      this.pageInfo.order_module.installment_type = param2.loan_num;
      this.getOrderDetailsData();
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_confirm:fenqi_click',
        ext: {}
      });
    },
    /**
     * 支付类型选择
     * @param {Object} param {
     *    "icon": "https://static.soyoung.com/sy-pre/1weeg5m2ihzfg-1631502600797.png",
     *    "pay_type": 27,
     *    "pay_type_name": "氧分呗分期",
     *    "pay_type_desc": "线上分期申请成功仅代表您享有一定分期额度，线下到店后核销时进入实际支付流程",
     *    "is_selected": 0,
     *    "child_module": []
     * }
     * */
    paymentTypeSelection(param) {
      for (let item of this.confirmOrderData.payment_module) {
        // 清空child_module模块中选中态
        if (
          item.child_module.length &&
          Number(item.pay_type) !== Number(param.pay_type)
        ) {
          for (let item_s of item.child_module) {
            item_s.is_selected = 0;
          }
          this.pageInfo.order_module.installment_type = -1;
        }
        item.is_selected =
          Number(item.pay_type) === Number(param.pay_type) ? 1 : 0;
      }
      console.log('支付选择方式', param);
      // 修改支付渠道
      this.pageInfo.order_module.payment_channel = param.pay_type;
      this.getOrderDetailsData();
      switch (Number(param.pay_type)) {
        case 3:
          this.$reportData({
            info: 'sy_wxtuan_tuan_pay:wx_click',
            ext: {}
          });
          break;
        case 27:
          this.$reportData({
            info: 'sy_wxtuan_tuan_order_confirm:fenqi_click',
            ext: {}
          });
          break;
      }
    },
    /**
     * 优惠选择
     * @param {Object} param {
     *    "type": "deposit_coupon",
     *    "title": "预约金红包",
     *    "title_warning": "",
     *    "desc": "",
     *    "subtitle": "-￥7",
     *    "reduce_price": 7,
     *    "level": 2,
     *    "url": "https://devh5inapp112.sy.soyoung.com/appm/red/nred?amount=1\u0026card_num=1\u0026code_id=27568764412\u0026is_tuan=0\u0026pid=10606009\u0026price_buy_type=2\u0026type=1\u0026usedcupons=%5B%7B%22spid%22%3A10606009%2C%22scode_id%22%3A27568764412%7D%5D",
     *    "dyurl": "/packageOrder/my-coupon/index?amount=1\u0026card_num=1\u0026code_id=27568764412\u0026is_tuan=0\u0026pid=10606009\u0026price_buy_type=2\u0026type=1\u0026usedcupons=%5B%7B%22spid%22%3A10606009%2C%22scode_id%22%3A27568764412%7D%5D",
     *    "code_id": 27568764412,
     *    "can_use_count": 1,
     *    "is_selected": 0,
     *    "sort": 1
     * }
     * */
    discountOptions(param) {
      switch (param.type) {
        case 'deposit_coupon_new_v2':
          this.$refs['coupon-popup-v2'].open('bottom');
          break;
        case 'deposit_coupon':
          // 预约金红包
          // uni.navigateTo({
          //   url: `/packageCoupon/choose-eb?pid=${this.product.pid}&code_id=${param.code_id}`
          // });
          this.$refs['coupon-popup'].open('bottom');
          break;
        case 'allowance_amount':
          // 津贴立减
          this.$refs.popup.open('bottom');
          break;
      }
    },
    onVisibleChange(param) {
      this.disablePageScrolling = param;
    },
    /**
     * 意向模块数据更新回调
     * @param {number} param.hospital_id
     * @param {number} param.date.year
     * @param {number} param.date.month
     * @param {number} param.date.day
     * @param {string} param.time.from
     * @param {string} param.time.to
     * */
    onApptChange(param) {
      this.intentionData = param;
    },
    // onCityChange(param) {
    //   this.pageInfo.city_id = param;
    // },
    /**
     * 津贴选择
     * @param {Object} param {
     *    "type": 2,
     *    "title": "111元",
     *    "subtitle": "无门槛立减津贴",
     *    "desc": "本单可抵扣111元",
     *    "is_selected": 1,
     *    "allowance_id": 22096,
     *    "amount": 111
     * }
     * */
    selectAllowance(param) {
      for (let item of this.allowanceReduceModule.allowance_reduce_list) {
        if (Number(param.allowance_id) === Number(item.allowance_id)) {
          param.is_selected = Number(param.is_selected) === 1 ? 0 : 1;
          // 更新津贴数据
          this.pageInfo.order_module.allowance_id =
            Number(param.is_selected) === 1 ? param.allowance_id : 0;
          this.$refs.popup.close('bottom');
          this.getOrderDetailsData();
        } else {
          item.is_selected = 0;
        }
      }
    },
    /**
     * 格式化时间
     * 可恶的后端 @孙亚东
     * @param {string} start 津贴使用开始时间
     * @param {string} end 津贴使用结束时间
     * */
    timeFormatting(start, end) {
      if (start && end) {
        return `${this.timeFormattingMS(
          start * 1000
        )} - ${this.timeFormattingMS(end * 1000)}`;
      }
      return '';
    },
    /**
     * 跳转查看拼团规则
     * */
    viewGroupingRules() {
      if (this.confirmOrderData.promotion_data.group_module.url) {
        uni.navigateTo({
          url: `/pages/h5?url=${encodeURIComponent(
            this.confirmOrderData.promotion_data.group_module.url
          )}`
        });
      }
    },
    handleProjectClick() {
      if (this.confirmOrderData.appointment_sku_list.length <= 1) {
        return;
      }
      this.projectVisible = true;
    }
  },
  /**
   * 入参规范
   * @desc 生单参数：
   *  @param {Object} options.product_module - {[pid]:{"pid": "", "amount": 写死 1,"times_card": 写死 1,"action": 写死 "deposit_coupon","deposit_coupon_code_id": "红包code_id"}}
   *  @param {number} options.channel - 写死 2
   *  @param {Object} options.user_module - {"is_selected":写死 1,"uid": "用户id"}
   *  @param {Object} options.order_module - {"allowance_id": 津贴id -1,"payment_channel": 分期类型 -1,"installment_type": 支付渠道 -1}
   *  @param {number} options.city_id - 必传参数
   *  @param {number} options.activity_id - 必传参数
   * */
  async onLoad(options) {
    console.log('options', options);
    console.log('this.pageInfo', this.pageInfo);
    this.menuRect = uni.getMenuButtonBoundingClientRect();
    this.initializeCityId = Number(options.city_id) || 0;
    for (let key of Object.keys(this.pageInfo)) {
      switch (key) {
        case 'city_id':
          if (options.city_id) {
            this.pageInfo.city_id = Number(options.city_id || 0);
          }
          break;
        case 'activity_id':
          if (options.activity_id) {
            this.pageInfo.activity_id = Number(options.activity_id);
          }
          break;
        default:
          if (options[key] !== undefined) {
            this.pageInfo[key] = JSON.parse(options[key]);
          }
      }
    }
    await this.getOrderDetailsData();
    if (this.confirmOrderData) {
      this.getCouponPackage();
      this.getCouponPackage('deposit_coupon_new_v2');
      await this.getChainCityList();
    } else {
      setTimeout(() => {
        uni.navigateBack({
          delta: 1
        });
      }, 800);
      return;
    }
    // eventBus.$on('coupon-select', (e) => {
    //   this.pageInfo.product_module[this.product.pid].deposit_coupon_code_id =
    //     e.code_id || 0;
    //   this.getOrderDetailsData();
    //   console.log('收到coupon-select', e);
    // });
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_order_confirm_page',
      ext: {
        product_id: this.product.pid,
        type: 1
      }
    });
    this.$registerExposure(
      '.confirm-order-payment-method-item-3',
      () => {
        this.$reportData({
          info: 'sy_wxtuan_tuan_pay:wx_exposure',
          ext: {}
        });
      },
      this
    );
    this.$registerExposure(
      '.confirm-order-payment-method-item-27',
      () => {
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_confirm:fenqi_exposure',
          ext: {}
        });
      },
      this
    );
  },
  onPageScroll(e) {
    if (e.scrollTop < 100) {
      this.background = `rgba(255,255,255,${e.scrollTop / 100})`;
    } else {
      this.background = `#ffffff`;
    }
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_order_confirm_page',
      ext: {
        product_id: this.product.pid,
        type: 1
      }
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_order_confirm_page',
      ext: {
        product_id: this.product.pid,
        type: 1
      }
    });
  }
};
</script>

<template>
  <page-meta :page-style="pageMetaStyle">
    <div
      v-if="menuRect.top"
      class="pageTop"
      :style="{ height: menuRect.bottom + 8 + 'px', background }"
    >
      <div
        class="nav"
        :style="{
          top: menuRect.top + 'px',
          width: menuRect.width + 'px',
          height: menuRect.height + 'px'
        }"
      >
        <div class="backBox" @click="handleBack">
          <image class="back" :src="backIcon" />
        </div>
      </div>
    </div>
    <view class="confirm-order" :style="{ opacity: confirmOrderData ? 1 : 0 }">
      <block v-if="confirmOrderData">
        <!-- 商品信息 & 优惠 模块 -->
        <view
          class="confirm-order-header"
          :style="{ paddingTop: menuRect.bottom + 10 + 'px' }"
        >
          <view class="confirm-order-header__title">
            <view class="confirm-order-header__title-left">
              {{ product.spu_title }}
            </view>
            <view class="confirm-order-header__title-right">
              x{{ product.amount }}
            </view>
          </view>
          <view class="confirm-order-header__subtitle">
            <view class="confirm-order-header__subtitle-left">
              {{ product.title }}
            </view>
            <view class="confirm-order-header__subtitle-right">
              ¥{{ product.price_online }}
            </view>
          </view>
          <!--          <view class="confirm-order-header-product">-->
          <!--            <view class="confirm-order-header-product-avatar">-->
          <!--              <image-->
          <!--                class="head-diagram"-->
          <!--                :src="product.img_cover"-->
          <!--                alt="商品头图"-->
          <!--              ></image>-->
          <!--            </view>-->
          <!--            <view class="confirm-order-header-product-right">-->
          <!--              <view class="title">-->
          <!--                {{ product.title }}-->
          <!--              </view>-->
          <!--              <view class="quantity-price">-->
          <!--                <text class="quantity"> x{{ product.amount }} </text>-->
          <!--                <text class="price"> ¥{{ product.price_online }} </text>-->
          <!--              </view>-->
          <!--            </view>-->
          <!--          </view>-->
          <!--          <view-->
          <!--            v-for="(item, index) in product.reduce_detail_list"-->
          <!--            class="confirm-order-header-other"-->
          <!--            :key="index"-->
          <!--            @click="discountOptions(item)"-->
          <!--          >-->
          <!--            <view class="left"> {{ item.title }} </view>-->
          <!--            <view class="right">-->
          <!--              <text class="price">{{ item.subtitle }}</text>-->
          <!--              <image-->
          <!--                v-if="item.type !== 'group_buy'"-->
          <!--                src="https://static.soyoung.com/sy-pre/2cyqm1lucbfxw-1710328200676.png"-->
          <!--                alt="查看更多"-->
          <!--              ></image>-->
          <!--            </view>-->
          <!--          </view>-->
        </view>
        <!-- 小计金额模块 -->
        <view class="confirm-order-subtotal">
          <view
            v-for="(item, index) in product.reduce_detail_list"
            class="confirm-order-subtotal-item"
            :key="index"
            @click="discountOptions(item)"
          >
            <view>
              {{ item.title }}
            </view>
            <view>
              <text>{{ item.subtitle }}</text>
              <image
                v-if="item.type !== 'group_buy'"
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726282046863.png"
              ></image>
            </view>
          </view>
          <view
            v-if="
              confirmOrderData.button.deposit_desc &&
              confirmOrderData.button.final_price_deposit_str
            "
            class="confirm-order-subtotal-subtotal"
          >
            <view>
              {{ confirmOrderData.button.deposit_desc }}
            </view>
            <view>
              <text>¥</text>
              <text>{{ confirmOrderData.button.final_price_deposit_str }}</text>
            </view>
          </view>
          <view
            class="confirm-order-subtotal-subtotal-price-label"
            v-if="intentionData.time.back_money"
          >
            {{
              `到院核销再返¥${intentionData.time.back_money}，到手¥${
                confirmOrderData.button.final_deposit_for_save_order -
                intentionData.time.back_money
              }`
            }}
          </view>
        </view>
        <!-- 拼团模块 -->
        <view v-if="groupModule" class="confirm-order-pin">
          <view class="confirm-order-pin-title"> 拼团流程 </view>
          <image
            v-if="groupModule.desc_url"
            class="confirm-order-pin-flow"
            src="https://static.soyoung.com/sy-pre/20240923-165531-1727079000632.png"
          ></image>
          <view
            v-if="groupModule.url"
            class="confirm-order-pin-parting-line"
          ></view>
          <view
            v-if="groupModule.url"
            class="confirm-order-pin-footer"
            @click="viewGroupingRules"
          >
            <view class="confirm-order-pin-footer-left">
              <text>{{ groupModule.desc_text }}</text>
            </view>
            <view class="confirm-order-pin-footer-right">
              <text>{{ groupModule.subtitle }}</text>
              <image
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png"
              ></image>
            </view>
          </view>
        </view>
        <!-- 意向服务模块 -->
        <view class="confirm-order-service">
          <view class="confirm-order-project" @click="handleProjectClick">
            <view class="confirm-order-project-title"> 选择项目 </view>
            <view class="confirm-order-project-item">
              <span class="confirm-order-project-item-name">{{
                curProjectName || '请选择项目'
              }}</span>
              <image
                v-if="confirmOrderData.appointment_sku_list.length > 1"
                mode="widthFix"
                class="confirm-order-project-item-arrow"
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png"
              ></image>
            </view>
          </view>
          <ApptHospital
            :city_id.sync="pageInfo.city_id"
            :force-reserve="
              Boolean(confirmOrderData.appointment_info.force_reserve)
            "
            :ingredients_id="
              curProject || confirmOrderData.appointment_info.ingredients_id
            "
            :sku_id="confirmOrderData.appointment_info.top_sku_id"
            :cityList="chainCityList"
            :price_online="product.price_online"
            :reduce_list="JSON.stringify(product.reduce_detail_list)"
            :total_amount="confirmOrderData.button.final_deposit_for_save_order"
            :loading.sync="ApptHospitalLoading"
            @visibleChange="onVisibleChange"
            @change="onApptChange"
          ></ApptHospital>
        </view>
        <!-- 支付方式模块 -->
        <view class="confirm-order-payment-method">
          <view class="confirm-order-payment-method-title"> 支付方式 </view>
          <view
            v-for="(item, index) in confirmOrderData.payment_module"
            class="confirm-order-payment-method-item"
            :style="{ marginTop: index === 0 ? '0rpx' : '46rpx' }"
            :class="'confirm-order-payment-method-item-' + item.pay_type"
            :key="index"
          >
            <view
              class="confirm-order-payment-method-item-top"
              @click="paymentTypeSelection(item)"
            >
              <view class="confirm-order-payment-method-item-left">
                <image :src="item.icon"></image>
                <text class="title">{{ item.pay_type_name }}</text>
                <!--                <text v-if="item.pay_type_desc" class="label">-->
                <!--                  {{ item.pay_type_desc }}-->
                <!--                </text>-->
              </view>
              <image
                class="confirm-order-payment-method-item-right"
                :src="
                  Number(item.is_selected) === 0
                    ? 'https://static.soyoung.com/sy-design/8k1ijrc526id1726026826498.png'
                    : 'https://static.soyoung.com/sy-design/1i3fb7pl05wlg1726026826479.png'
                "
              ></image>
            </view>
            <scroll-view
              v-if="item.child_module.length"
              scroll-x
              enable-flex
              class="confirm-order-payment-method-item-class-list"
            >
              <view
                v-for="(item_s, index_s) in item.child_module"
                class="confirm-order-payment-method-item-class"
                :class="{
                  'confirm-order-payment-method-item-class-select':
                    Number(item_s.is_selected) === 1
                }"
                :key="index_s"
                @click="stageSelection(item, item_s)"
              >
                <text class="title">{{ item_s.sub_title }}</text>
                <text class="subtitle">{{ item_s.desc }}</text>
                <!--                <text v-if="item_s.icon_desc" class="label">{{-->
                <!--                  item_s.icon_desc-->
                <!--                }}</text>-->
              </view>
              <view
                class="confirm-order-payment-method-item-class-list-empty"
              ></view>
            </scroll-view>
          </view>
          <!--<view class="confirm-order-payment-method-tips">-->
          <!--分期流程：申请分期&#45;&#45;分期审核&#45;&#45;到店核-->
          <!--</view>-->
        </view>
        <!-- 联系电话模块 -->
        <view class="confirm-order-payment-phone">
          <view class="confirm-order-payment-phone-header">
            <view class="title"> 联系电话 </view>
            <view class="number">
              <text>{{ confirmOrderData.user_module.mobile }}</text>
              <!--              <image-->
              <!--                src="https://static.soyoung.com/sy-design/3ifhzljkcqyc31716473683464.png"-->
              <!--                alt="修改"-->
              <!--              ></image>-->
            </view>
          </view>
          <view class="confirm-order-payment-phone-subtitle"> 号码保护中 </view>
          <view class="confirm-order-payment-phone-tips">
            {{ confirmOrderData.user_module.phone_protect_subtitle }}
          </view>
        </view>
        <!-- 协议签署模块 -->
        <view class="confirm-order-agreement">
          <Agreement
            :checked.sync="confirmOrderData.agreement[0].is_selected"
            :visible.sync="agreementVisible"
          />
        </view>
        <!-- 底部购买模块 -->
        <view class="confirm-order-footer">
          <view
            v-if="appointmentReminderShow.visible"
            class="confirm-order-footer-prompt"
          >
            <ApptWranText
              :type="appointmentReminderShow.type"
              :title="appointmentReminderShow.text"
              :isShowSubText="false"
            />
          </view>
          <view class="confirm-order-footer-operation-area">
            <view class="price">
              <view style="display: flex; align-items: center">
                <text class="post-coupon-price">
                  ￥{{ confirmOrderData.button.final_price_deposit_str }}
                </text>
                <text
                  v-if="Number(confirmOrderData.button.final_to_pay_price_str)"
                  class="discounted-price"
                >
                  {{ confirmOrderData.button.to_pay_desc }}￥{{
                    confirmOrderData.button.final_to_pay_price_str
                  }}
                </text>
              </view>
              <view
                class="confirm-order-footer-operation-area-price-label"
                v-if="intentionData.time.back_money"
              >
                {{ `核销再返¥${intentionData.time.back_money}` }}
              </view>
            </view>
            <button class="pay-button payable" @click="saveOrder">
              {{ payButtonText }}
            </button>
          </view>
        </view>
      </block>
      <Popup ref="popup" @maskClick="$refs.popup.close('bottom')">
        <view class="popup-allowance">
          <view class="popup-allowance-header">
            <text>{{ allowanceReduceModule.title }}</text>
            <image
              src="https://static.soyoung.com/sy-design/1azvpt7asrrzj1717149928155.png"
              @click="$refs.popup.close('bottom')"
            ></image>
          </view>
          <scroll-view scroll-y class="popup-allowance-scroll">
            <view
              v-for="(
                item, index
              ) in allowanceReduceModule.allowance_reduce_list"
              class="popup-allowance-scroll-item"
              :key="index"
              @click="selectAllowance(item)"
            >
              <view class="popup-allowance-scroll-item-1">
                <view class="popup-allowance-scroll-item-1-left">
                  <text class="title">{{ item.title }}</text>
                  <text class="label">{{ item.desc }}</text>
                </view>
                <view class="popup-allowance-scroll-item-1-right">
                  <text>￥</text>
                  <text>{{ item.amount }}</text>
                </view>
              </view>
              <view class="popup-allowance-scroll-item-2">
                <text class="popup-allowance-scroll-item-2-left">{{
                  timeFormatting(item.valid_start, item.valid_end)
                }}</text>
                <text class="popup-allowance-scroll-item-2-right">{{
                  item.subtitle
                }}</text>
              </view>
              <view class="popup-allowance-scroll-item-3">
                <text class="popup-allowance-scroll-item-3-left"
                  >限平台项目可用</text
                >
                <image
                  class="popup-allowance-scroll-item-3-right"
                  :src="
                    Number(item.is_selected) === 1
                      ? 'https://static.soyoung.com/sy-pre/output-1718968200635.png'
                      : 'https://static.soyoung.com/sy-pre/o1212utput-1718968200635.png'
                  "
                ></image>
              </view>
            </view>
            <view class="popup-allowance-scroll-footer"></view>
          </scroll-view>
        </view>
      </Popup>
      <Popup ref="coupon-popup" @maskClick="couponPopupClose">
        <view class="coupon-popup">
          <view class="coupon-popup-header">
            <text>可用优惠券</text>
            <image
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1726282046857.png"
              @click="couponPopupClose"
            ></image>
          </view>
          <scroll-view scroll-y class="popup-coupon-scroll">
            <view style="height: 10rpx"></view>
            <view
              v-for="(item, index) in couponList.valid"
              style="margin-bottom: 30rpx"
              :key="index"
            >
              <new-card
                :card-data="item"
                :source="'confirmOrder'"
                :select-coupon-id="selectCouponId"
                @card-select="cardSelect"
              ></new-card>
            </view>
            <view style="height: 30rpx"></view>
          </scroll-view>
        </view>
      </Popup>
      <Popup ref="coupon-popup-v2" @maskClick="couponV2PopupClose">
        <view class="coupon-popup">
          <view class="coupon-popup-header">
            <text>优惠</text>
            <image
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1726282046857.png"
              @click="couponV2PopupClose"
            ></image>
          </view>
          <scroll-view scroll-y class="popup-coupon-scroll">
            <view style="height: 10rpx"></view>
            <view
              v-for="(item, index) in newCouponList.valid"
              style="margin-bottom: 30rpx"
              :key="index"
            >
              <new-card
                :card-data="item"
                :source="'confirmOrder'"
                :select-coupon-id="selectCouponV2Id"
                @card-select="cardSelectV2"
              ></new-card>
            </view>
            <view style="height: 30rpx"></view>
          </scroll-view>
        </view>
      </Popup>
      <Popup
        ref="redEnvelopeSecondaryConfirmation"
        style="background-color: #fff"
      >
        <view class="redEnvelopeSecondaryConfirmation">
          <view class="redEnvelopeSecondaryConfirmation-header">
            <text>温馨提示</text>
            <image
              @click="handlePopClose('redEnvelopeSecondaryConfirmation')"
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1728466885054.png"
            ></image>
          </view>
          <view class="redEnvelopeSecondaryConfirmation-body">
            是否取消使用优惠券
          </view>
          <view class="redEnvelopeSecondaryConfirmation-footer">
            <button @click="redEnvelopeConfirmation('cancel')">取消</button>
            <button @click="redEnvelopeConfirmation('confirm')">是</button>
          </view>
        </view>
      </Popup>
      <Popup
        ref="redEnvelopeSecondaryConfirmationV2"
        style="background-color: #fff"
      >
        <view class="redEnvelopeSecondaryConfirmation">
          <view class="redEnvelopeSecondaryConfirmation-header">
            <text>温馨提示</text>
            <image
              @click="handlePopClose('redEnvelopeSecondaryConfirmationV2')"
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1728466885054.png"
            ></image>
          </view>
          <view class="redEnvelopeSecondaryConfirmation-body">
            是否取消使用优惠券
          </view>
          <view class="redEnvelopeSecondaryConfirmation-footer">
            <button @click="redEnvelopeConfirmationV2('cancel')">取消</button>
            <button @click="redEnvelopeConfirmationV2('confirm')">是</button>
          </view>
        </view>
      </Popup>

      <Popup ref="confirmCouponPopup" style="background-color: #fff">
        <view v-if="confirmCouponInfo" class="redEnvelopeSecondaryConfirmation">
          <view class="redEnvelopeSecondaryConfirmation-header">
            <text>温馨提示</text>
            <image
              @click="handlePopClose('confirmCouponPopup')"
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1728466885054.png"
            ></image>
          </view>
          <view class="redEnvelopeSecondaryConfirmation-body">
            该券无法与已选中的{{
              confirmCouponInfo.action === 'deposit_coupon'
                ? '专属券'
                : '满减券'
            }}同时使用
          </view>
          <view class="redEnvelopeSecondaryConfirmation-footer">
            <button @click="handleConfirmCoupon('cancel')">取消</button>
            <button @click="handleConfirmCoupon('confirm')">仍要选择</button>
          </view>
        </view>
      </Popup>
      <ChooseProject
        :visible.sync="projectVisible"
        :default-project="curProject"
        :dataList="confirmOrderData.appointment_sku_list || []"
        @close="projectVisible = false"
        @visibleChange="onVisibleChange"
        @project-change="handleProjectChange"
      />
    </view>
  </page-meta>
</template>

<style lang="less" scoped>
@px: 2rpx;

.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;
  .share {
    display: flex;
    position: fixed;
    align-items: center;
    height: 88rpx;
    right: -100%;
    .shareIcon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  .title {
    position: fixed;
    left: 50%;
    height: 88rpx;
    font-family: PingFangSC-Medium;
    font-size: 36rpx;
    color: #333333;
    display: flex;
    align-items: center;
    transform: translateX(-50%);
  }
  .nav {
    position: fixed;
    display: flex;
    align-items: center;
    .back {
      width: 44 * @px;
      height: 44 * @px;
      display: block;
    }
  }
}

.coupon-popup {
  width: 100%;
  min-height: 85vh;
  max-height: 85vh;
  background-color: #ffffff;

  .coupon-popup-header {
    height: 52 * @px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    text {
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #030303;
      letter-spacing: 0;
      font-weight: 500;
    }

    image {
      width: 20 * @px;
      height: 20 * @px;
      position: absolute;
      right: 15 * @px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .popup-coupon-scroll {
    width: 100%;
    height: calc(85vh - 52 * @px);
    box-sizing: border-box;
    padding: 0 15 * @px;
  }
}

.confirm-order {
  transition: opacity 400ms;
  opacity: 0;
  background-color: #f2f2f2;
  //padding: 5 * @px;
  box-sizing: border-box;
  min-height: 100vh;
  position: relative;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-bottom: calc(
    37 * @px + 38 * @px + 54 * @px + env(safe-area-inset-bottom)
  );
}

.confirm-order-module {
  background: #ffffff;
  //border-radius: 2 * @px;
  box-sizing: border-box;
  width: calc(100vw - 30 * @px);
  margin: 0 auto;
}

.confirm-order-header {
  padding: 0 30 * @px 36 * @px;
  background-color: #030303;

  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
      font-family: PingFangSC-Semibold;
      font-size: 24 * @px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 600;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: 10 * @px;
    }

    &-right {
      font-family: Outfit-SemiBold;
      font-size: 15 * @px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 600;
      padding-left: 5 * @px;
    }
  }

  &__subtitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    padding-top: 3 * @px;

    &-left {
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #bababa;
      letter-spacing: 0;
      font-weight: 500;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &-right {
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #bababa;
      letter-spacing: 0;
      font-weight: 500;
      padding-left: 5 * @px;
      white-space: nowrap;
    }
  }

  .confirm-order-header-product {
    display: flex;
    padding: 15 * @px 10 * @px;
    .confirm-order-header-product-avatar {
      min-width: 91 * @px;
      max-width: 91 * @px;
      width: 91 * @px;
      height: 68 * @px;
      .head-diagram {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }
    .confirm-order-header-product-right {
      padding-left: 10 * @px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .title {
        font-family: PingFangSC-Medium;
        font-size: 13 * @px;
        color: #222222;
        line-height: 20 * @px;
        font-weight: 500;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        overflow: hidden;
        -webkit-box-orient: vertical;
      }
      .quantity-price {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .quantity {
          font-family: PingFangSC-Regular;
          font-size: 13 * @px;
          color: #7e8ba0;
          font-weight: 400;
        }
        .price {
          font-family: PingFangSC-Regular;
          font-size: 13 * @px;
          color: #354052;
          text-align: right;
          font-weight: 400;
        }
      }
    }
  }
  .confirm-order-header-other {
    width: calc(100% - 20 * @px);
    margin: 0 auto;
    border-top: 0.5 * @px solid #f0f0f0;
    box-sizing: border-box;
    height: 60 * @px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      font-family: PingFangSC-Medium;
      font-size: 14 * @px;
      color: #222222;
      font-weight: 500;
    }
    .right {
      display: flex;
      align-items: center;
      .price {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: @text-color;
        text-align: right;
        font-weight: 400;
      }
      image {
        width: 7 * @px;
        height: 11 * @px;
        margin-left: 5 * @px;
      }
    }
  }
}

.confirm-order-subtotal {
  .confirm-order-module;
  //height: 70 * @px;
  margin-top: -20 * @px;
  margin-bottom: 10 * @px;
  padding: 25 * @px 15 * @px;

  & > view {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .confirm-order-subtotal-item {
    margin-bottom: 20 * @px;

    & > view:first-child {
      font-family: PingFangSC-Medium;
      font-size: 14 * @px;
      color: #333333;
      font-weight: 500;
    }

    & > view:last-child {
      display: flex;
      align-items: center;
      font-family: OutFit-Regular;
      font-size: 14 * @px;
      color: #61b43e;
      font-weight: 500;

      image {
        width: 7 * @px;
        height: 9 * @px;
        min-width: 6 * @px;
        margin-left: 7 * @px;
      }
    }
  }

  .confirm-order-subtotal-subtotal {
    view:nth-child(1) {
      font-family: PingFangSC-Medium;
      font-size: 14 * @px;
      font-weight: 500;
      color: #030303;
      transform: translateY(2 * @px);
    }
    view:nth-child(2) {
      display: flex;
      align-items: baseline;

      text {
        font-family: OutFit-Regular;
        color: #222222;
        font-weight: 500;
      }

      text:nth-child(1) {
        font-size: 14 * @px;
        padding-right: 1 * @px;
      }

      text:nth-child(2) {
        font-size: 21 * @px;
      }
    }
  }
  .confirm-order-subtotal-subtotal-price-label {
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #fe6631;
    text-align: right;
    font-weight: 400;
    justify-content: flex-end;
  }
}

.confirm-order-pin {
  .confirm-order-module;
  //border-radius: 8 * @px;
  background-color: #ffffff;
  margin-bottom: 10 * @px;
  padding-top: 25 * @px;
  //padding: 25 * @px 10 * @px 12 * @px;
  .confirm-order-pin-title {
    font-family: PingFangSC-Medium;
    font-size: 16 * @px;
    color: #333333;
    font-weight: 500;
    padding-bottom: 22 * @px;
    box-sizing: border-box;
    padding-left: 15 * @px;
  }
  .confirm-order-pin-flow {
    width: 314 * @px;
    height: 75 * @px;
    margin: 0 auto;
    display: block;
  }
  .confirm-order-pin-parting-line {
    margin: 20 * @px auto 0;
    height: 1 * @px;
    background: #f0f0f0;
  }
  .confirm-order-pin-footer {
    width: 100%;
    height: 52 * @px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18 * @px 20 * @px;
    box-sizing: border-box;
    .confirm-order-pin-footer-left {
      display: flex;
      align-items: center;
      flex: 1;
      text {
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #8c8c8c;
        font-weight: 400;
      }
      image {
        margin-right: 5 * @px;
        width: 14 * @px;
        min-width: 14 * @px;
        height: 14 * @px;
      }
    }
    .confirm-order-pin-footer-right {
      display: flex;
      align-items: center;
      text {
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
      }
      image {
        width: 7 * @px;
        min-width: 7 * @px;
        height: 9 * @px;
        margin-left: 5 * @px;
      }
    }
  }
}

.confirm-order-service {
  .confirm-order-project {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 44rpx;
    padding: 0 30rpx;
    .confirm-order-project-title {
      box-sizing: border-box;
      font-size: 28rpx;
      height: 40rpx;
      font-weight: 500;
      color: #030303;
      flex-shrink: 0;
    }
    .confirm-order-project-item {
      display: flex;
      align-items: center;
      .confirm-order-project-item-name {
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #333333;
        letter-spacing: 0;
        text-align: right;
        line-height: 40rpx;
        font-weight: 400;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: break-all;
        max-width: 460rpx;
      }
      .confirm-order-project-item-arrow {
        width: 7 * @px;
        min-width: 7 * @px;
        height: 9 * @px;
        margin-left: 5 * @px;
      }
    }
  }
  .confirm-order-module;
  margin-bottom: 10 * @px;
  overflow: hidden;
  .confirm-order-service-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15 * @px;
    box-sizing: border-box;
    padding: 0 10 * @px;
    .left {
      font-family: PingFangSC-Medium;
      font-size: 14 * @px;
      color: #222222;
      font-weight: 500;
      position: relative;
      &:before {
        content: '*';
        position: absolute;
        right: -10 * @px;
        top: 0;
        font-family: PingFangSC-Medium;
        font-size: 14 * @px;
        color: @text-color;
        font-weight: 500;
      }
    }
    .right {
      display: flex;
      align-items: center;
      text {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #222222;
        font-weight: 400;
      }
      image {
        width: 12 * @px;
        height: 10 * @px;
        margin-left: 3 * @px;
      }
    }
  }
  .confirm-order-service-store-list {
    width: 100%;
    height: 71 * @px;
    flex-direction: row;
    box-sizing: border-box;
    overflow: auto;
    display: flex;
    margin-bottom: 20 * @px;
    .confirm-order-service-store {
      align-self: center;
      min-width: 256 * @px;
      height: 100%;
      box-sizing: border-box;
      padding: 15 * @px;
      overflow: hidden;
      background-color: #f8f8f8;
      //border-radius: 6 * @px;
      margin-right: 8 * @px;
      &:nth-last-child(2) {
        margin-right: 0;
      }
      &:first-child {
        margin-left: 10 * @px;
      }
      & > view {
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;
        & > text:nth-child(1) {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-right: 20 * @px;
        }
      }
      .confirm-order-service-store-name {
        margin-bottom: 5 * @px;
        .name {
          font-family: PingFangSC-Medium;
          font-size: 14 * @px;
          color: #333333;
          font-weight: 500;
        }
        .city-icon {
          width: 17 * @px;
          height: 17 * @px;
          min-width: 17 * @px;
        }
      }
      .confirm-order-service-store-city {
        text {
          font-family: PingFangSC-Regular;
          font-size: 11 * @px;
          color: #777777;
          font-weight: 400;
        }
      }
    }
  }
  .confirm-order-service-time-flow {
    width: 345 * @px;
    height: 38 * @px;
    object-fit: cover;
    margin: 0 10 * @px 15 * @px;
  }
}

.confirm-order-payment-method {
  .confirm-order-module;
  padding: 25 * @px 0;
  margin-bottom: 10 * @px;

  .confirm-order-payment-method-title {
    font-family: PingFangSC-Medium;
    font-size: 14 * @px;
    color: #030303;
    font-weight: 500;
    box-sizing: border-box;
    padding: 0 15 * @px 18 * @px;
  }

  .confirm-order-payment-method-item {
    .confirm-order-payment-method-item-top {
      box-sizing: border-box;
      padding: 0 15 * @px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .confirm-order-payment-method-item-class-list {
      margin-top: 16 * @px;
      display: flex;
      height: 60 * @px;
      .confirm-order-payment-method-item-class-list-empty {
        width: 15 * @px;
        min-width: 15 * @px;
        max-width: 15 * @px;
        height: 100%;
      }
      .confirm-order-payment-method-item-class {
        min-width: 120 * @px;
        height: 60 * @px;
        margin-left: 10 * @px;
        background-color: #f2f2f2;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 9.5 * @px;
        box-sizing: border-box;
        justify-content: center;
        position: relative;
        //transform: translateY(6 * @px);

        text {
          white-space: nowrap;
        }

        .title {
          font-family: PingFangSC-Medium;
          font-size: 13 * @px;
          color: #222222;
          font-weight: 500;
        }
        .subtitle {
          font-family: PingFangSC-Regular;
          font-size: 11 * @px;
          color: #8c8c8c;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
        }
        .label {
          box-sizing: border-box;
          padding: 0 3 * @px;
          height: 13 * @px;
          line-height: 13 * @px;
          background-color: #00ab84;
          border-radius: 4 * @px 4 * @px 0 4 * @px;
          font-family: PingFangSC-Medium;
          font-size: 9 * @px;
          color: #ffffff;
          font-weight: 500;
          position: absolute;
          right: 0;
          top: -6 * @px;
          z-index: 10;
        }
      }
      .confirm-order-payment-method-item-class-select {
        //background: #e2f8f1;
        border: 2 * @px solid #333333;
        background-image: url('https://static.soyoung.com/sy-design/1j4frrsjsg7l51726208257251.png');
        background-size: 18 * @px 12.72 * @px;
        background-repeat: no-repeat;
        background-position: calc(100% + 1 * @px) calc(100% + 1 * @px);

        .title {
          //color: #00ab84;
        }

        .subtitle {
          color: #61b43e;
        }

        .label {
          top: -7 * @px;
          right: -1 * @px;
        }
      }
    }
    .confirm-order-payment-method-item-left {
      display: flex;
      align-items: center;
      overflow: hidden;
      image {
        width: 16 * @px;
        height: 16 * @px;
        margin-right: 5 * @px;
        min-width: 16 * @px;
      }
      .title {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #222222;
        font-weight: 400;
        white-space: nowrap;
      }
      .label {
        box-sizing: border-box;
        padding: 0 5 * @px;
        font-family: PingFangSC-Regular;
        font-size: 10 * @px;
        color: #f85d2d;
        font-weight: 400;
        border: 1 * @px solid #f85d2d;
        margin-left: 8 * @px;
        height: 17 * @px;
        display: inline-block;
        line-height: 15 * @px;
        margin-right: 20 * @px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .confirm-order-payment-method-item-right {
      min-width: 17 * @px;
      max-width: 17 * @px;
      height: 17 * @px;
    }
  }
  .confirm-order-payment-method-tips {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #aaabb3;
    font-weight: 400;
    padding-top: 10 * @px;
  }
}

.confirm-order-payment-phone {
  .confirm-order-module;
  padding: 25 * @px 15 * @px;
  .confirm-order-payment-phone-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10 * @px;
    .title {
      font-family: PingFangSC-Medium;
      font-size: 14 * @px;
      color: #222222;
      font-weight: 500;
    }
    .number {
      display: flex;
      align-items: center;
      text {
        font-weight: 400;
        font-family: Outfit-Regular;
        font-size: 12 * @px;
        color: #8c8c8c;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
      image {
        width: 12 * @px;
        height: 12 * @px;
        margin-left: 5 * @px;
      }
    }
  }
  .confirm-order-payment-phone-subtitle {
    margin-bottom: 5 * @px;
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #8c8c8c;
    letter-spacing: 0;
    font-weight: 400;
  }
  .confirm-order-payment-phone-tips {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #8c8c8c;
    letter-spacing: 0;
    font-weight: 400;
  }
}

.confirm-order-agreement {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 5 * @px;
  .confirm-order-agreement-icon {
    width: 14 * @px;
    height: 14 * @px;
    min-width: 14 * @px;
  }
  .confirm-order-agreement-text {
    padding-left: 5 * @px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    text {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #aaabb3;
      font-weight: 400;
    }
    .agreement {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #aaabb3;
      font-weight: 400;
      white-space: inherit;
    }
  }
}

.confirm-order-footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  height: min-content;
  background-color: #ffffff;
  z-index: 1000;
  .confirm-order-footer-prompt {
    height: 38 * @px;
    //background: #e2f8f1;
    //display: flex;
    //align-items: center;
    //box-sizing: border-box;
    //padding: 0 12 * @px;
    image {
      width: 14 * @px;
      height: 15 * @px;
      margin-right: 10 * @px;
    }
    text {
      font-family: PingFangSC-Medium;
      font-size: 12 * @px;
      color: @text-color;
      letter-spacing: 0;
      font-weight: 500;
    }
    /deep/.warn-text {
      border-radius: initial !important;
    }
  }
  .confirm-order-footer-operation-area {
    height: 52 * @px;
    box-sizing: border-box;
    padding: 5 * @px 15 * @px 5 * @px 0;
    margin-bottom: calc(env(safe-area-inset-bottom));
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10 * @px;
    .price {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      //padding-right: 20 * @px;
      .post-coupon-price {
        white-space: nowrap;
        font-size: 18 * @px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 500;
        padding-bottom: 1.5 * @px;
        font-family: OutFit-Medium;
      }
      .discounted-price {
        white-space: nowrap;
        font-family: OutFit-Regular;
        font-size: 12 * @px;
        color: #777777;
        letter-spacing: 0;
        font-weight: 400;
        margin-left: 10rpx;
      }
    }
    .pay-button {
      flex: 2;
      max-width: 240 * @px;
      height: 42 * @px;
      text-align: center;
      line-height: 42 * @px;
      background-color: #dedede;
      border-radius: 0;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 500;
      margin: 0;
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      &:after {
        border: none;
      }
    }
    .payable {
      background-color: @border-color;
      color: #ffffff;
    }
    .confirm-order-footer-operation-area-price-label {
      font-family: PingFangSC-Medium;
      font-size: 24rpx;
      color: #61b43e;
      font-weight: 500;
    }
  }
}

.popup-allowance {
  width: 100%;
  min-height: 85vh;
  max-height: 85vh;
  background-color: #f8f8f8;
  border-radius: 15 * @px 15 * @px 0 0;
  box-sizing: border-box;
  padding: 0 6 * @px 34 * @px;
  .popup-allowance-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 55 * @px;
    text {
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #222222;
      font-weight: 500;
    }
    image {
      width: 13 * @px;
      height: 13 * @px;
      position: absolute;
      right: 10 * @px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .popup-allowance-scroll {
    width: 100%;
    height: calc(85vh - 55 * @px - 34 * @px);
    box-sizing: border-box;
    padding: 0 9 * @px;
    border-radius: 8 * @px;
    background-color: #ffffff;
    position: relative;
    //&:before {
    //  content: '';
    //  width: 100%;
    //  height: 21 * @px;
    //  position: absolute;
    //  top: -1 * @px;
    //  left: 0;
    //  z-index: 10;
    //  border-top-left-radius: 8 * @px;
    //  border-top-right-radius: 8 * @px;
    //  background-image: linear-gradient(
    //    to bottom,
    //    rgba(255, 255, 255, 1) 30%,
    //    rgba(255, 255, 255, 0) 100%
    //  );
    //}
    //&:after {
    //  content: '';
    //  width: 100%;
    //  height: 21 * @px;
    //  position: absolute;
    //  bottom: -1 * @px;
    //  left: 0;
    //  z-index: 10;
    //  border-bottom-left-radius: 8 * @px;
    //  border-bottom-right-radius: 8 * @px;
    //  background-image: linear-gradient(
    //    to top,
    //    rgba(255, 255, 255, 1) 30%,
    //    rgba(255, 255, 255, 0) 100%
    //  );
    //}
    .popup-allowance-scroll-item:first-child {
      margin-top: 20 * @px;
    }
    .popup-allowance-scroll-footer {
      width: 100%;
      height: 20 * @px;
    }
    .popup-allowance-scroll-item {
      width: 100%;
      box-sizing: border-box;
      padding: 15 * @px;
      background-image: linear-gradient(90deg, #fff2e2 0%, #ffeae2 100%);
      border-radius: 8 * @px;
      margin-bottom: 10 * @px;
      & > view {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .popup-allowance-scroll-item-1 {
        padding-bottom: 1 * @px;
        .popup-allowance-scroll-item-1-left {
          display: flex;
          align-items: center;
          box-sizing: border-box;
          padding-right: 20 * @px;
          & > text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .title {
            font-family: PingFangSC-Medium;
            font-size: 15 * @px;
            color: #222222;
            font-weight: 500;
            padding-right: 6 * @px;
          }
          .label {
            font-family: PingFangSC-Regular;
            font-size: 10 * @px;
            color: #b68155;
            font-weight: 400;
            box-sizing: border-box;
            padding: 2 * @px 5 * @px;
            background-color: rgba(#d0842e, 0.15);
            border-radius: 2 * @px;
          }
        }
        .popup-allowance-scroll-item-1-right {
          display: flex;
          align-items: baseline;
          & > text {
            overflow: hidden;
            white-space: nowrap;
          }
          text:first-child {
            font-family: PingFangSC-Medium;
            font-size: 13 * @px;
            color: #b68155;
            font-weight: 500;
          }
          text:last-child {
            font-family: PingFangSC-Medium;
            font-size: 24 * @px;
            color: #b68155;
            font-weight: 500;
          }
        }
      }
      .popup-allowance-scroll-item-2 {
        .popup-allowance-scroll-item-2-left {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          color: #222222;
          font-weight: 400;
          padding-right: 20 * @px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .popup-allowance-scroll-item-2-right {
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          color: #777777;
          font-weight: 400;
        }
      }
      .popup-allowance-scroll-item-3 {
        padding-top: 16 * @px;
        margin-top: 5 * @px;
        border-top: 1 * @px dashed #e7b280;
        .popup-allowance-scroll-item-3-left {
          font-family: PingFangSC-Regular;
          font-size: 11 * @px;
          color: #777777;
          font-weight: 400;
        }
        .popup-allowance-scroll-item-3-right {
          width: 18 * @px;
          height: 18 * @px;
          min-width: 18 * @px;
        }
      }
    }
  }
}

.redEnvelopeSecondaryConfirmation {
  background-color: #fff;
  box-sizing: border-box;
  padding-bottom: calc(
    10 * @px + constant(safe-area-inset-bottom)
  ); //兼容 IOS<11.2
  padding-bottom: calc(10 * @px + env(safe-area-inset-bottom));

  .redEnvelopeSecondaryConfirmation-header {
    height: 52 * @px;
    line-height: 52 * @px;
    position: relative;
    text-align: center;

    text {
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }

    image {
      width: 20 * @px;
      height: 20 * @px;
      position: absolute;
      right: 15 * @px;
      top: 15 * @px;
    }
  }

  .redEnvelopeSecondaryConfirmation-body {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #333333;
    letter-spacing: 0.2 * @px;
    line-height: 22 * @px;
    font-weight: 400;
    padding: 15 * @px 25 * @px 25 * @px;
    box-sizing: border-box;
  }

  .redEnvelopeSecondaryConfirmation-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0 25 * @px;

    button {
      padding: 0;
      margin: 0;
      border-radius: 0;
      border: none;
      background-color: transparent;
      flex: 1;
      height: 42 * @px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      text-align: center;
      font-weight: 500;

      &:after {
        border: none;
      }
    }

    button:nth-child(1) {
      background-color: #ffffff;
      border: 1 * @px solid #333333;
      color: #333;
    }

    button:nth-child(2) {
      color: #fff;
      background-color: #333333;
      margin-left: 15 * @px;
    }
  }
}
</style>
