<script>
import Vue from 'vue';
import Popup from '@/components/uni/popup.vue';
import Poster from '@/components/pintuan/poster.vue';
import NewBanner from '@/components/NewBanner.vue';
import PinTuan from '@/components/pintuan/card.vue';
import CashBackNotice from '@/components/appointNew/CashBackNotice.vue';
import { mapMutations, mapState } from 'vuex';
import { accredit } from '@/packageOrder/locationAuthorization';
import SubscribeMixins from '@/mixins/subscribe';

// const eventBus = getApp().globalData.eventBus;
// import mockData from '/Users/<USER>/Library/Application Support/JetBrains/WebStorm2024.1/scratches/B用户数据.json';

export default {
  name: 'paySuccess',
  mixins: [SubscribeMixins],
  components: {
    Popup,
    Poster,
    PinTuan,
    CashBackNotice,
    NewBanner
  },
  data() {
    return {
      payment: null,
      pageInfo: {},
      popupTipsData: {
        type: 0, // 0 普通提示 1 富文本提示
        text: '', // 提示内容
        title: '', // 提示标题
        buttonText: '确认'
      },
      background: '',
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      popupInfo: {}, // 支付成功弹窗信息
      carouselBannerList: []
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),

    footerButtonVisible() {
      return (
        [0, 3].includes(this.appointmentStatus.status) ||
        (this.payment &&
          this.payment.pay_reserve_card_info &&
          this.payment.pay_reserve_card_info.real_reserve_num > 0) ||
        false
      );
    },

    /**
     * 预约状态
     * @return {{subtitle: string, icon: string, title: string, status: number, color: string}} status 0 未预约 1 预约确认中 2 预约成功 3 预约失败
     * */
    appointmentStatus() {
      if (
        this.payment &&
        this.payment.pay_reserve_card_info &&
        Number(this.payment.pay_reserve_card_info.reserve_finish_status) === 0
      ) {
        return {
          status: 0,
          title: '服务尚未预约',
          subtitle: '',
          color: '#61B43E',
          icon: ''
        };
      } else if (
        this.payment &&
        this.payment.pay_reserve_card_info &&
        Number(this.payment.pay_reserve_card_info.reserve_finish_status) === 3
      ) {
        return {
          status: 3,
          title: '服务预约失败',
          color: '#FE6631',
          subtitle: '',
          icon: 'https://static.soyoung.com/sy-pre/20240914-113010-1726283400654.png'
        };
      } else if (
        this.payment &&
        this.payment.pay_reserve_card_info &&
        [1, 2].includes(
          Number(this.payment.pay_reserve_card_info.reserve_finish_status)
        )
      ) {
        if (
          this.payment &&
          this.payment.pay_reserve_card_info &&
          Number(this.payment.pay_reserve_card_info.reserve_confirm_yn) === 0
        ) {
          return {
            status: 1,
            title: '预约时间确认中',
            color: '#61B43E',
            subtitle: '确认后生效',
            icon: ''
          };
        } else if (
          this.payment &&
          this.payment.pay_reserve_card_info &&
          Number(this.payment.pay_reserve_card_info.reserve_confirm_yn) === 1
        ) {
          return {
            status: 2,
            title: '预约时间已确认',
            color: '#61B43E',
            subtitle: '',
            icon: 'https://static.soyoung.com/sy-design/2hca637nb8ad51725936196515.png'
          };
        }
      }
      return {
        status: 0,
        title: '服务尚未预约',
        color: '#61B43E',
        subtitle: '',
        icon: ''
      };
    }
  },
  methods: {
    ...mapMutations('global', ['setUserFn']),
    handleBack() {
      if (getCurrentPages().length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack();
      }
    },
    async authorizedGeographicLocation() {
      try {
        if (
          this.payment &&
          this.payment.pay_reserve_card_info &&
          this.payment.pay_reserve_card_info.hospital_info &&
          this.payment.pay_reserve_card_info.hospital_info.juli
        ) {
          return;
        }
        const data = await accredit();
        console.log('定位信息', data);
        this.setUserFn(
          Object.assign({}, this.userInfo, {
            lng: data.lng,
            lat: data.lat
          })
        );
        await this.paymentSuccess();
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * @param {string} param 日期时间
     * */
    dateConversion(param) {
      console.log(param);
      try {
        const date = new Date(param.replace(/-/g, '/'));
        return `${date.getFullYear()}年${
          date.getMonth() + 1
        }月${date.getDate()}日 ${date.getHours()}:${
          date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        }`;
      } catch (error) {
        return param;
      }
    },
    /**
     * 获取订单数据
     * */
    async paymentSuccess() {
      try {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const response = await Vue.$request({
          url: '/syChainTrade/wxapp/payment/getPaySuccessInfo',
          data: {
            order_id: this.pageInfo.id
          }
        })
          .then((res) => res.data)
          .catch((error) => error);
        if (response.errorCode !== 200 && response.errorCode !== 0) {
          throw new Error(response.errorMsg);
        }
        this.payment = response.responseData;

        // 处理后端返回的弹窗数据
        if (response.responseData && response.responseData.popup_info) {
          this.popupInfo = response.responseData.popup_info;
        }

        if (
          response.responseData.bottom_banners &&
          response.responseData.bottom_banners.list &&
          response.responseData.bottom_banners.list.length > 0
        ) {
          this.carouselBannerList =
            response.responseData.bottom_banners.list.map((item, index) => ({
              ...item,
              img_url: item.img, // 转换为NewBanner组件需要的字段
              index // 添加索引用于埋点
            }));
        }

        uni.hideLoading();
      } catch (error) {
        console.log(error);
        uni.hideLoading();
        uni.showToast({
          title: error.message,
          icon: 'none'
        });
      }
    },
    /**
     * 查看订单详情
     * @param {number} [param] - 1 修改预约 2 立即预约
     * */
    orderDetails(param) {
      let url = `/packageOrder/order-detail?orderId=${this.pageInfo.id}`;
      if (param === 1) {
        this.$reportData({
          info: 'sy_wxtuan_tuan_pay_result:subscribe_click',
          ext: {}
        });
        url += `&openAppointment=edit&reserveId=${this.payment.pay_reserve_card_info.reserve_id}`;
      } else if (param === 2) {
        this.$reportData({
          info: 'sy_wxtuan_tuan_pay_result:subscribe_click',
          ext: {}
        });
        url += `&openAppointment=now`;
      } else {
        this.$reportData({
          info: 'sy_wxtuan_tuan_view_order:btn_click',
          ext: {
            order_id: this.pageInfo.id
          }
        });
      }
      uni.navigateTo({
        url
      });
    },
    /**
     * 查询注意事项
     * */
    mattersNeedingAttention() {
      this.popupTipsData.type = 1;
      this.popupTipsData.text = this.payment.pay_reserve_card_info.attention;
      this.popupTipsData.title = '注意事项';
      this.popupTipsData.buttonText = '确认';
      this.$refs.popupTips.open('bottom');
    },
    /**
     * 联系专员
     * */
    contactProfessionals() {
      if (this.payment.join_card_info.img_url) {
        this.$refs.popupCommissioner.open('bottom');
      } else {
        uni.showToast({
          title: '该门店暂无预约专员联系方式，如有问题，请联系客服',
          icon: 'none'
        });
      }
    },
    /**
     * 拨打电话
     * */
    dial() {
      if (
        Number(
          this.payment.pay_reserve_card_info.hospital_info.is_work_time
        ) === 0
      ) {
        this.popupTipsData.text =
          this.payment.pay_reserve_card_info.hospital_info.msg;
        this.popupTipsData.title = '提示';
        this.popupTipsData.buttonText = '确定';
        this.$refs.popupTips.open('bottom');
        this.popupTipsData.type = 0;
      } else {
        uni.makePhoneCall({
          phoneNumber: this.payment.pay_reserve_card_info.hospital_info.mobile
        });
      }
    },
    /**
     * 跳转地图页面
     * @param {number} type 1 机构地图页 2 微信地图页
     * */
    jumpToMap(type) {
      try {
        switch (type) {
          case 1:
            uni.navigateTo({
              url: `/packageHospital/hospital-map?hospital_id=${this.payment.pay_reserve_card_info.hospital_info.hospital_id}`
            });
            break;
          case 2:
            uni.openLocation({
              latitude: this.payment.pay_reserve_card_info.hospital_info.lat,
              longitude: this.payment.pay_reserve_card_info.hospital_info.lon,
              scale: 18,
              name: this.payment.pay_reserve_card_info.hospital_info.name_cn,
              address: this.payment.pay_reserve_card_info.hospital_info.address,
              fail: (e) => {
                uni.showToast({
                  title: JSON.stringify(e),
                  icon: 'none'
                });
              }
            });
            break;
        }
      } catch (error) {
        uni.navigateTo({
          url: `/packageHospital/hospital-map?hospital_id=${this.payment.pay_reserve_card_info.hospital_info.hospital_id}`
        });
      }
    },
    /**
     * 邀请好友
     * */
    inviteFriends() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_pay_result:yaoqinghaoyou_click',
        ext: {
          product_id: this.pageInfo.pid,
          activity_id: this.pageInfo.activity_id,
          // TODO 开团，参团
          card_type: '',
          group_id: this.payment.group_info.group_id
        }
      });
      // 订阅消息
      // TODO @风尘
      // this.createGroupBySub(
      //   [
      //     'WVEq1xRbHUtILERdzzBl88FzQJU8wQA0ZdfdCE1dUKs',
      //     'eGnRAuGNgOmj8uFVNQqZbiaMMssCfKSiLeIjNZTTVS4',
      //     'E-3yPJELuSVKOUIkV8q0NY2L9J-LGsu_jEf5ZIVOSDU'
      //   ],
      //   [this.pageInfo.pid]
      // );
    },
    /**
     * 关闭二次确认弹窗
     * */
    closePopup() {
      if (
        this.popupTipsData.type === 0 &&
        this.popupTipsData.buttonText === '重新预约'
      ) {
        this.orderDetails(2);
      }
      this.$refs.popupTips.close('bottom');
    },
    /**
     * 处理图片弹窗点击
     */
    handleImagePopupClick() {
      if (!this.popupInfo) return;

      this.$reportData({
        info: 'sy_wxtuan_tuan_pay_result:popup_click',
        ext: {
          order_id: this.pageInfo.id,
          url: this.popupInfo.jump_url
        }
      });

      this.$refs.popupImage.close('center');

      // 根据跳转类型处理跳转
      switch (this.popupInfo.jump_type) {
        case 1: // H5
          uni.navigateTo({
            url: '/pages/h5?url=' + encodeURIComponent(this.popupInfo.jump_url)
          });
          break;
        case 2: // 小程序
          if (this.popupInfo.mini_app_id === 'wx4c984b5d0eb25e91') {
            const path = [
              '/pages/index',
              '/pages/item',
              '/pages/coupon-center',
              '/pages/my'
            ];
            if (path.includes(this.popupInfo.jump_url)) {
              uni.switchTab({
                url: this.popupInfo.jump_url
              });
            } else {
              uni.navigateTo({
                url: this.popupInfo.jump_url
              });
            }
          } else {
            uni.navigateToMiniProgram({
              appId: this.popupInfo.mini_app_id,
              path: this.popupInfo.jump_url,
              success(res) {
                console.log('跳转小程序成功', res);
              },
              fail(err) {
                console.log('跳转小程序失败', err);
                uni.showToast({
                  title: '跳转失败',
                  icon: 'none'
                });
              }
            });
          }
          break;
        case 3: // app
          // 处理app内部跳转
          uni.navigateTo({
            url: this.popupInfo.jump_url,
            fail: (err) => {
              console.log('跳转失败', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
          break;
      }
    },

    /**
     * 关闭图片弹窗
     */
    closeImagePopup() {
      this.$refs.popupImage.close('center');
    },
    handleBannerClick(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_pay_result:banner_click',
        ext: {
          serial_num: item.index + 1,
          order_id: this.pageInfo.id,
          url: item.jump_url
        }
      });

      // 根据jump_type处理不同的跳转方式
      if (+item.jump_type === 1) {
        // H5跳转
        uni.navigateTo({
          url: `/pages/h5?url=${encodeURIComponent(item.jump_url)}`
        });
      } else if (+item.jump_type === 2) {
        // 小程序跳转
        if (item.need_auth && !this.isLogin) {
          this.judgeLogin();
          return;
        }
        if (item.mini_app_id === 'wx4c984b5d0eb25e91') {
          const path = [
            '/pages/index',
            '/pages/item',
            '/pages/coupon-center',
            '/pages/my'
          ];
          if (path.includes(item.jump_url)) {
            uni.switchTab({
              url: item.jump_url
            });
          } else {
            uni.navigateTo({
              url: item.jump_url
            });
          }
        } else {
          uni.navigateToMiniProgram({
            appId: item.mini_app_id,
            path: item.jump_url,
            success(res) {
              console.log('跳转小程序成功', res);
            },
            fail(err) {
              console.log('跳转小程序失败', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        }
      }
    },
    // 轮播图曝光埋点
    handleBannerExpose(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_pay_result:banner_exposure',
        ext: {
          serial_num: item.index + 1,
          order_id: this.pageInfo.id,
          url: item.jump_url
        }
      });
    }
  },
  /**
   * 入参规范
   * @desc 生单参数：
   * @param {Number} options.id 订单id
   * @param {Number} options.pid 商品ID
   * @param {Number} options.activity_id 活动ID
   * @param {Number} options.groupType 0 非拼团商品 1 开团 2 参团
   * */
  async onLoad(options) {
    this.menuRect = uni.getMenuButtonBoundingClientRect();
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    this.pageInfo = options;
    await new Promise((resolve) => {
      setTimeout(async () => {
        await this.paymentSuccess();
        resolve();
      }, 700);
    });
    uni.hideLoading();
    // 判断是否预约失败
    if (
      this.payment &&
      this.payment.pay_reserve_card_info &&
      Number(this.payment.pay_reserve_card_info.reserve_finish_status) === 3
    ) {
      this.popupTipsData.type = 0;
      this.popupTipsData.title = '预约失败';
      this.popupTipsData.buttonText = '重新预约';
      this.popupTipsData.text = `很抱歉，您预约的${
        this.payment.pay_reserve_card_info.reserve_start_time || ''
      }服务时段抢占失败，请尽快重新预约时间。`;
      this.$refs.popupTips.open('bottom');
    }

    // 检查是否有弹窗信息并显示
    if (this.payment && this.payment.popup_info) {
      this.popupInfo = this.payment.popup_info;
      this.$refs.popupImage.open('center');
      this.$reportData({
        info: 'sy_wxtuan_tuan_pay_result:popup_exposure',
        ext: {
          order_id: this.pageInfo.id,
          url: this.popupInfo.jump_url
        }
      });
    }

    if (this.payment.group_info) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_pay_result:yaoqinghaoyou_exposure',
        ext: {
          product_id: this.pageInfo.pid,
          activity_id: this.pageInfo.activity_id,
          card_type: options.groupType,
          group_id: this.payment.group_info.group_id
        }
      });
    }
  },
  onShow() {
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_pay_result_page',
      ext: {}
    });
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_pay_result_page',
      ext: {}
    });
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_pay_result_page',
      ext: {}
    });
    this.$reportData({
      info: 'sy_wxtuan_tuan_pay_result:back_btn_click',
      ext: {}
    });
  },
  onPageScroll(e) {
    if (e.scrollTop < 300) {
      this.background = `rgba(255,255,255,${e.scrollTop / 300})`;
    } else {
      this.background = `#ffffff`;
    }
  },
  onShareAppMessage() {
    return {
      promise: this.$refs.poster.shareInfo({
        sku_id: this.pageInfo.pid,
        group_id: this.payment.group_info.group_id,
        activity_id: this.pageInfo.activity_id
      })
    };
  }
};
</script>

<template>
  <view
    class="pay-success"
    :class="{
      'pay-success-skeleton-diagram': !payment,
      'pay-success-padding-bottom': footerButtonVisible
    }"
    :style="{
      paddingTop: menuRect.bottom + 15 + 'px'
    }"
  >
    <div
      v-if="menuRect.top"
      class="pageTop"
      :style="{ height: menuRect.bottom + 8 + 'px', background }"
    >
      <div
        class="nav"
        :style="{
          top: menuRect.top + 'px',
          width: menuRect.width + 'px',
          height: menuRect.height + 'px'
        }"
      >
        <div class="backBox" @click="handleBack">
          <image
            class="back"
            src="https://static.soyoung.com/sy-design/3cj8rc3ipek931726026826755.png"
          />
        </div>
      </div>
    </div>
    <block v-if="payment">
      <view class="pay-success-header">
        <text class="status">{{
          payment.order_info.pay_status !== 1 ? '支付失败' : '支付成功'
        }}</text>
        <view class="operation-area">
          <button class="view-orders" @click="orderDetails">查看订单</button>
          <button
            v-if="payment.group_info && payment.group_info.status !== 1"
            class="invite-friends"
            open-type="share"
            @click="inviteFriends"
          >
            邀请好友参团
          </button>
        </view>
      </view>
      <view v-if="payment.group_info" class="pay-success-pin">
        <PinTuan :groupInfo="payment.group_info" />
      </view>
      <view class="pay-success-appointment">
        <view class="pay-success-appointment-header">
          <view class="pay-success-appointment-subtitle">
            <image
              v-if="appointmentStatus.icon"
              :src="appointmentStatus.icon"
            ></image>
            <text class="title" :style="{ color: appointmentStatus.color }">{{
              appointmentStatus.title
            }}</text>
            <text v-if="appointmentStatus.subtitle" class="subtitle">{{
              appointmentStatus.subtitle
            }}</text>
          </view>
          <block v-if="appointmentStatus.status !== 3">
            <block v-if="appointmentStatus.status === 0">
              <view style="padding-top: 18rpx">
                <rich-text
                  class="pay-success-appointment-tips-rich-text"
                  :nodes="payment.pay_reserve_card_info.notice_info[0]"
                ></rich-text>
              </view>
            </block>
            <block v-else-if="appointmentStatus.status === 1">
              <view class="pay-success-appointment-tips-1">
                您的预约专员将在48小时内联系您确认，请留意电话。
              </view>
              <view class="pay-success-appointment-tips-2">
                您也可以主动
                <text style="color: #61b43e" @click="contactProfessionals">
                  联系专员
                </text>
                快速确认。
              </view>
            </block>
            <block v-else-if="appointmentStatus.status === 2">
              <view class="pay-success-appointment-tips-1">
                您的预约已确认，请按照预约时间，按时到院。
              </view>
            </block>
          </block>
        </view>
        <view
          v-if="appointmentStatus.status !== 0"
          class="pay-success-appointment-product"
          :class="{
            'pay-success-appointment-product-fail':
              appointmentStatus.status === 3
          }"
        >
          <view class="pay-success-appointment-product-header">
            <view class="pay-success-appointment-product-title">
              {{ payment.pay_reserve_card_info.item_product_name }}
            </view>
            <view
              class="pay-success-appointment-product-warning"
              @click="mattersNeedingAttention"
            >
              <text>查看治疗注意事项</text>
              <image
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726026826568.png"
              ></image>
            </view>
          </view>
          <view class="pay-success-appointment-product-body">
            <view class="pay-success-appointment-product-intention">
              <view class="pay-success-appointment-product-intention-title">
                意向服务门店
              </view>
              <view class="pay-success-appointment-product-intention-content">
                <view
                  class="pay-success-appointment-product-intention-content-left"
                >
                  <view
                    class="pay-success-appointment-product-intention-content-left-hospital-name"
                    :style="{
                      'text-decoration':
                        appointmentStatus.status === 3 ? 'line-through' : 'none'
                    }"
                  >
                    {{ payment.pay_reserve_card_info.hospital_info.name_cn }}
                  </view>
                  <view
                    class="pay-success-appointment-product-intention-content-left-hospital-city"
                    @click="authorizedGeographicLocation"
                  >
                    <block
                      v-if="payment.pay_reserve_card_info.hospital_info.juli"
                    >
                      <text
                        v-if="payment.pay_reserve_card_info.hospital_info.juli"
                      >
                        {{ payment.pay_reserve_card_info.hospital_info.juli }}
                      </text>
                    </block>
                    <block v-else>
                      <text> 授权地理位置，查看距离 </text>
                      <image
                        src="https://static.soyoung.com/sy-design/3ssb1a7i8o5nw1721014921032.png"
                      ></image>
                    </block>
                  </view>
                </view>
                <view
                  class="pay-success-appointment-product-intention-content-right"
                >
                  <view
                    v-if="
                      payment.pay_reserve_card_info &&
                      payment.pay_reserve_card_info.hospital_info &&
                      payment.pay_reserve_card_info.hospital_info.mobile
                    "
                    class="pay-success-appointment-product-intention-content-right-item"
                    @click="dial"
                  >
                    <image
                      src="https://static.soyoung.com/sy-design/2hca7nc2ykif91725936196531.png"
                    ></image>
                    <!--                    <text> 电话 </text>-->
                  </view>
                  <view
                    v-if="appointmentStatus.status !== 3"
                    class="pay-success-appointment-product-intention-content-right-item"
                    @click="jumpToMap(2)"
                  >
                    <image
                      src="https://static.soyoung.com/sy-design/2hca79az1qgeq1725936196511.png"
                    ></image>
                    <!--                    <text> 地址 </text>-->
                  </view>
                </view>
                <!--              <image-->
                <!--                v-if="appointmentStatus.status !== 3"-->
                <!--                class="pay-success-appointment-product-intention-content-right"-->
                <!--                src="https://static.soyoung.com/sy-design/3oil1rtphxfl41717411370177.png"-->
                <!--                @click="jumpToMap(2)"-->
                <!--              ></image>-->
              </view>
            </view>
            <view
              class="pay-success-appointment-product-intention"
              style="margin-bottom: 20rpx"
            >
              <view class="pay-success-appointment-product-intention-title">
                意向到店时间
              </view>
              <view class="pay-success-appointment-product-intention-content">
                <view
                  class="pay-success-appointment-product-intention-content-left"
                >
                  <text
                    class="pay-success-appointment-product-intention-content-left-time"
                    style="font-weight: 500"
                    :style="{
                      'text-decoration':
                        appointmentStatus.status === 3 ? 'line-through' : 'none'
                    }"
                  >
                    {{
                      dateConversion(
                        payment.pay_reserve_card_info.reserve_start_time
                      )
                    }}
                  </text>
                </view>
                <!--                <image-->
                <!--                  v-if="appointmentStatus.status !== 3"-->
                <!--                  style="min-width: 44rpx; width: 44rpx; height: 44rpx"-->
                <!--                  src="https://static.soyoung.com/sy-design/322rit9dn1fqh1721014921023.png"-->
                <!--                  @click="orderDetails(1)"-->
                <!--                ></image>-->
              </view>
            </view>
            <CashBackNotice
              :bg-color="'rgba(186,186,186,0.20)'"
              v-if="payment.pay_reserve_card_info.back_money_notice"
              style="margin-top: 20rpx; margin-bottom: 42rpx"
              :notice="payment.pay_reserve_card_info.back_money_notice"
              :show-notice="false"
            />
            <view class="pay-success-appointment-product-body-button-list">
              <button
                v-if="appointmentStatus.status !== 3"
                class="modify-appointment"
                @click="orderDetails(1)"
              >
                修改预约
              </button>
              <button
                v-if="appointmentStatus.status === 1"
                class="commissioner"
                @click="contactProfessionals"
              >
                <text>联系专员</text>
                <text>｜快速确认</text>
              </button>
            </view>
          </view>
        </view>
      </view>
      <!-- 加C pay-success-c -->
      <!-- 加群 pay-success-group -->
      <!-- TODO 加C逻辑暂时去除 -->
      <view v-if="false" class="pay-success-c-group pay-success-c">
        <view class="pay-success-top">
          <view class="pay-success-left">
            <image class="avatar" src=""></image>
            <view class="pay-success-left-title">
              <text class="title">优享客服</text>
              <text class="subtitle">1v1客服在线专业解答</text>
            </view>
          </view>
          <view class="pay-success-right">
            <button>立即咨询</button>
          </view>
        </view>
        <view class="pay-success-bottom">
          <view v-for="item in 4" class="pay-success-bottom-tips" :key="item">
            <image src=""></image>
            <text>专属活动</text>
          </view>
        </view>
      </view>
      <!-- 轮播图区域 start -->
      <div
        v-if="carouselBannerList && carouselBannerList.length > 0"
        class="carousel-banner-container"
      >
        <NewBanner
          :list="carouselBannerList"
          height="80px"
          width="360px"
          class="carousel-banner"
          @click="handleBannerClick"
          @expose="handleBannerExpose"
        ></NewBanner>
      </div>
      <!-- 轮播图区域 end -->
      <view v-if="footerButtonVisible" class="pay-success-footer">
        <button
          v-if="appointmentStatus.status === 0"
          class="pay-success-footer-button"
          @click="orderDetails(2)"
        >
          立即预约
        </button>
        <button
          v-else-if="appointmentStatus.status === 3"
          class="pay-success-footer-button"
          @click="orderDetails(2)"
        >
          重新预约
        </button>
        <button
          v-else-if="payment.pay_reserve_card_info.real_reserve_num > 0"
          class="pay-success-footer-button"
          @click="orderDetails"
        >
          继续预约
        </button>
      </view>
      <Poster ref="poster" title="好友邀你一起拼单，不同部位不同城市可混拼！" />
      <Popup ref="popupCommissioner">
        <view class="popup-commissioner">
          <image
            class="popup-commissioner-close"
            src="https://static.soyoung.com/sy-design/1azvpt7asrrzj1717411370224.png"
            alt="关闭"
            @click="$refs.popupCommissioner.close('bottom')"
          ></image>
          <view class="popup-commissioner-header">
            <text class="title"> 感谢您预约 </text>
            <text class="subtitle">
              {{ payment.join_card_info.hospital_name }}
            </text>
          </view>
          <view class="popup-commissioner-body">
            <image
              class="code"
              :show-menu-by-longpress="true"
              :src="payment.join_card_info.img_url"
            ></image>
            <text
              v-if="payment.join_card_info.hospital_tel"
              class="customer-service"
              @click="dial"
            >
              电话联系客服
            </text>
          </view>
        </view>
      </Popup>
      <Popup ref="popupTips">
        <view class="popup-tips">
          <view v-if="popupTipsData.title" class="popup-tips-title">
            {{ popupTipsData.title }}
            <image
              src="https://static.soyoung.com/sy-design/bzsokyai5osd1728466885054.png"
            ></image>
          </view>
          <view class="popup-tips-content">
            <view v-if="popupTipsData.type === 0">
              {{ popupTipsData.text }}
            </view>
            <rich-text
              v-if="popupTipsData.type === 1"
              :nodes="popupTipsData.text"
            ></rich-text>
          </view>
          <view class="popup-tips-footer">
            <button class="popup-tips-footer-confirm" @click="closePopup">
              {{ popupTipsData.buttonText }}
            </button>
          </view>
        </view>
      </Popup>

      <!-- 支付成功弹窗 -->
      <Popup ref="popupImage" :mask-click="false">
        <view class="popup-image" @click="handleImagePopupClick">
          <view class="popup-image-content-wrapper">
            <image
              v-if="popupInfo && popupInfo.img"
              class="popup-image"
              :src="popupInfo.img"
            ></image>
            <image
              class="popup-image-close"
              src="https://static.soyoung.com/sy-design/3sk7d34tv56xy1747376434406.png"
              @click.stop="closeImagePopup"
            ></image>
          </view>
        </view>
      </Popup>
    </block>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.pay-success {
  min-height: 100vh;
  width: 100%;
  background-color: #ffffff;
  overflow-y: scroll;
  overflow-x: hidden;
  box-sizing: border-box;
  padding-top: 9 * @px;
}

.pay-success-padding-bottom {
  padding-bottom: calc(80 * @px + constant(safe-area-inset-bottom));
  padding-bottom: calc(80 * @px + env(safe-area-inset-bottom));
}

.pay-success-skeleton-diagram {
  //background-image: url('https://static.soyoung.com/sy-pre/20240621-154933-1718953800631.jpeg');
  //background-size: 100%;
  //background-repeat: no-repeat;
}

.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;
  .share {
    display: flex;
    position: fixed;
    align-items: center;
    height: 88rpx;
    right: -100%;
    .shareIcon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  .title {
    position: fixed;
    left: 50%;
    height: 88rpx;
    font-family: PingFangSC-Medium;
    font-size: 36rpx;
    color: #333333;
    display: flex;
    align-items: center;
    transform: translateX(-50%);
  }
  .nav {
    position: fixed;
    display: flex;
    align-items: center;
    .back {
      width: 44 * @px;
      height: 44 * @px;
      display: block;
    }
  }
}

.pay-success-module {
  width: calc(100vw - 30 * @px);
  margin: 0 auto;
  box-sizing: border-box;
}

.pay-success-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 37 * @px;
  box-sizing: border-box;
  margin-bottom: 30 * @px;
  padding: 0 20 * @px 0 25 * @px;
  .status {
    font-weight: 600;
    font-family: PingFangSC-Semibold;
    font-size: 25 * @px;
    color: #333333;
    letter-spacing: 0;
  }
  .operation-area {
    display: flex;
    align-items: center;
    .view-orders {
      height: 37 * @px;
      font-size: 13 * @px;
      font-weight: 400;
      width: 88 * @px;
      border-radius: 0;
      border: 1 * @px solid #333333;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      box-sizing: border-box;
      font-family: PingFangSC-Regular;
      color: #030303;
      letter-spacing: 0;
      text-align: center;

      &:after {
        border: none;
      }
    }
    .invite-friends {
      height: 37 * @px;
      width: 98 * @px;
      font-size: 13 * @px;
      font-weight: 400;
      background-color: #ffffff;
      border: 1 * @px solid #61b43e;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0;
      margin-left: 10 * @px;
      font-family: PingFangSC-Regular;
      color: #61b43e;
      letter-spacing: 0;
      text-align: center;
      padding: 0;
      white-space: nowrap;

      &:after {
        border: none;
      }
    }
  }
}

.pay-success-pin {
  .pay-success-module;
  background-color: #f8f8f8;
  margin-bottom: 6 * @px;
}

.pay-success-header-teamwork {
  //margin-bottom: 10 * @px;
}

.pay-success-appointment {
  .pay-success-module;
  //padding: 15 * @px;
  //background-color: #ffffff;
  .pay-success-appointment-header {
    box-sizing: border-box;
    padding: 15 * @px;
    background-color: #f8f8f8;
  }
  .pay-success-appointment-subtitle {
    display: flex;
    align-items: center;
    image {
      width: 21 * @px;
      height: 21 * @px;
      min-width: 21 * @px;
      margin-right: 5 * @px;
    }
    .title {
      font-weight: 500;
      white-space: nowrap;
      box-sizing: border-box;
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #61b43e;
      letter-spacing: 0;
    }

    .subtitle {
      position: relative;
      padding-left: 20 * @px;
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #61b43e;
      letter-spacing: 0;
      line-height: 18px;
      font-weight: 500;

      &:before {
        content: '';
        position: absolute;
        left: 10.5 * @px;
        top: 50%;
        transform: translateY(-50%);
        width: 1 * @px;
        height: 13 * @px;
        background-color: #333333;
      }
    }
  }
  .pay-success-appointment-tips {
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #646464;
    letter-spacing: 0;
    line-height: 24 * @px;
    font-weight: 400;
    //font-family: PingFangSC-Regular;
    //font-size: 13 * @px;
    //font-weight: 400;
    //.orange {
    //  font-family: PingFangSC-Medium;
    //  font-size: 13 * @px;
    //  color: #fb6535;
    //  font-weight: 500;
    //}
    //.red {
    //  font-family: PingFangSC-Medium;
    //  font-size: 13 * @px;
    //  color: #ed5c5c;
    //  font-weight: 500;
    //  text-decoration: underline;
    //  padding: 0 2 * @px;
    //}
  }
  .pay-success-appointment-tips-rich-text {
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #646464;
    font-weight: 400;
    //padding-top: 9 * @px;
    line-height: 24 * @px;
  }
  .pay-success-appointment-tips-1 {
    .pay-success-appointment-tips;
    margin-top: 9 * @px;
    font-family: PingFangSC-Regular;
    font-size: 13px;
    color: #646464;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: 400;
  }
  .pay-success-appointment-tips-2 {
    .pay-success-appointment-tips;
    margin-top: 6 * @px;
  }
  .pay-success-appointment-product {
    width: 100%;
    box-sizing: border-box;
    background-color: #ffffff;
    padding-top: 20 * @px;
    position: relative;

    &:before {
      content: '';
      width: 100vw + 10 * @px;
      height: 2 * @px;
      position: absolute;
      top: 0;
      background-image: linear-gradient(
        to right,
        #f2f2f2 70%,
        rgba(0, 0, 0, 0) 0%
      );
      background-size: 10 * @px 10 * @px;
      left: 50%;
      transform: translateX(-50%);
    }

    .pay-success-appointment-product-header {
      width: 100%;
      padding: 15 * @px;
      box-sizing: border-box;
      background-color: #f8f8f8;
    }

    .pay-success-appointment-product-title {
      font-size: 16 * @px;
      color: #222222;
      font-weight: 500;
      position: relative;
      padding-left: 16 * @px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: PingFangSC-Medium;
      letter-spacing: 0;

      &:before {
        content: '';
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        position: absolute;
        width: 6 * @px;
        height: 13 * @px;
        background-color: #333333;
      }
    }
    .pay-success-appointment-product-warning {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding-top: 7 * @px;

      text {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;
        letter-spacing: 0;
        font-weight: 400;
      }

      image {
        width: 6 * @px;
        height: 8 * @px;
        margin-left: 8 * @px;
      }
    }

    .pay-success-appointment-product-body {
      box-sizing: border-box;
      padding: 20 * @px 15 * @px;
      border: 1 * @px solid #f2f2f2;
      border-top: none;
    }

    .pay-success-appointment-product-body-button-list {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 20 * @px;

      button {
        width: 153 * @px;
        height: 37 * @px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        padding: 0;
        margin: 0;
        border-radius: 0;

        &:after {
          border: none;
        }
      }

      .modify-appointment {
        font-family: PingFangSC-Medium;
        font-size: 12 * @px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        border: 1 * @px solid #333333;
      }

      .commissioner {
        border: 1 * @px solid #61b43e;
        display: flex;
        align-items: center;
        margin-left: 8 * @px;

        text:nth-child(1) {
          font-family: PingFangSC-Medium;
          font-size: 12 * @px;
          color: #61b43e;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }

        text:nth-child(2) {
          font-family: PingFangSC-Regular;
          font-size: 11 * @px;
          color: #61b43e;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
    }
    .pay-success-appointment-product-intention {
      margin-bottom: 23 * @px;
      .pay-success-appointment-product-intention-title {
        font-size: 13 * @px;
        font-weight: 400;
        padding-bottom: 7 * @px;
        font-family: PingFangSC-Regular;
        color: #8c8c8c;
        line-height: 20px;
      }
      .pay-success-appointment-product-intention-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .pay-success-appointment-product-intention-content-left {
          overflow: hidden;
          max-height: 42 * @px;

          .pay-success-appointment-product-intention-content-left-hospital-name {
            font-family: PingFangSC-Medium;
            font-size: 15 * @px;
            line-height: 20 * @px;
            font-weight: 500;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            overflow: hidden;
            -webkit-box-orient: vertical;
            color: #333333;
          }
          .pay-success-appointment-product-intention-content-left-hospital-city {
            display: flex;
            align-items: center;
            text {
              font-family: PingFangSC-Regular;
              font-size: 12 * @px;
              color: #777777;
              line-height: 20 * @px;
              font-weight: 400;
            }
            image {
              width: 7 * @px;
              height: 9 * @px;
              margin-left: 2 * @px;
            }
          }
          .pay-success-appointment-product-intention-content-left-time {
            font-family: OutFit-Regular;
            font-size: 15 * @px;
            color: #333333;
            line-height: 20 * @px;
            font-weight: 500;
          }
        }
        .pay-success-appointment-product-intention-content-right {
          display: flex;
          align-items: center;
          .pay-success-appointment-product-intention-content-right-item {
            margin-left: 20 * @px;
            display: flex;
            flex-direction: column;
            align-items: center;
            image {
              width: 22 * @px;
              min-width: 22 * @px;
              height: 22 * @px;
              margin-bottom: 4 * @px;
            }
            text {
              font-family: PingFangSC-Regular;
              font-size: 10 * @px;
              color: #777777;
              font-weight: 400;
              white-space: nowrap;
            }
          }
        }
      }
    }
    .pay-success-appointment-product-button {
      display: flex;
      align-items: center;
      padding-top: 20 * @px;
      button {
        height: 38 * @px;
        padding: 0 20 * @px;
        background-color: #e2f8f1;
        border-radius: 19 * @px;
        display: flex;
        align-items: center;
        justify-content: center;
        text:first-child {
          font-family: PingFangSC-Medium;
          font-size: 13 * @px;
          color: #00ab84;
          font-weight: 500;
        }
        text:last-child {
          font-family: PingFangSC-Regular;
          font-size: 11 * @px;
          color: #00ab84;
          font-weight: 400;
        }
        &:after {
          border: none;
        }
      }
    }
  }
  .pay-success-appointment-product-fail {
    .pay-success-appointment-product-intention-content-left {
      text {
        //text-decoration: line-through;
        //color: #777777;
      }
    }
  }
  .pay-success-appointment-button {
    width: 100%;
    & > button {
      margin-top: 20 * @px;
      width: 100%;
      height: 44 * @px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 44 * @px;
      &:after {
        border: none;
      }
    }
    .non-hollow {
      background-color: @border-color;
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #ffffff;
      font-weight: 500;
    }
    .hollow {
      background-color: #ffffff;
      border: 2 * @px solid @border-color;
      font-family: PingFangSC-Medium;
      font-size: 15 * @px;
      color: @text-color;
      font-weight: 500;
    }
  }
}

.pay-success-appointment-red {
  background-image: linear-gradient(
    180deg,
    #ffe7e7 0%,
    #ffffff 160 * @px,
    rgba(255, 248, 244, 0) 99%
  );
}

.pay-success-appointment-green {
  background-image: linear-gradient(
    180deg,
    #e8f9f4 0%,
    #ffffff 160 * @px,
    rgba(255, 248, 244, 0) 99%
  );
}

.pay-success-c-group {
  .pay-success-module;
  margin-top: 5 * @px;
  background-color: #ffffff;
  .pay-success-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .pay-success-bottom {
    margin-top: 15 * @px;
    padding-top: 15 * @px;
    border-top: 1 * @px solid #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .pay-success-bottom-tips {
      display: flex;
      align-items: center;
      image {
        width: 11 * @px;
        height: 11 * @px;
        min-width: 11 * @px;
        margin-right: 5 * @px;
      }
      text {
        font-family: PingFangSC-Regular;
        font-size: 14 * @px;
        color: #666666;
        font-weight: 400;
      }
    }
  }
  .pay-success-left {
    display: flex;
    align-items: center;
    flex: 1;
    padding-right: 20 * @px;
    box-sizing: border-box;
    .avatar {
      min-width: 40 * @px;
      height: 40 * @px;
      width: 40 * @px;
      margin-right: 9 * @px;
      border-radius: 50%;
      object-fit: cover;
    }
    .pay-success-left-title {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      overflow: hidden;
      .title {
        font-family: PingFangSC-Medium;
        font-size: 15 * @px;
        color: #232321;
        font-weight: 500;
        flex: 1;
      }
      .subtitle {
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        color: #999999;
        font-weight: 400;
        flex: 1;
      }
    }
  }
  .pay-success-right {
    button {
      height: 30 * @px;
      padding: 6.5 * @px 19 * @px;
      box-sizing: border-box;
      background-color: @border-color;
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #ffffff;
      font-weight: 400;
      white-space: nowrap;
      border-radius: 30 * @px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:after {
        border: none;
      }
    }
  }
}

.pay-success-footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding-top: 10 * @px;
  padding-left: 15 * @px;
  padding-right: 15 * @px;
  padding-bottom: calc(
    10 * @px + constant(safe-area-inset-bottom)
  ); //兼容 IOS<11.2
  padding-bottom: calc(10 * @px + env(safe-area-inset-bottom));
}

.pay-success-footer-button {
  width: calc(100vw - 30 * @px);
  height: 42 * @px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Medium;
  font-size: 13 * @px;
  color: #ffffff;
  text-align: center;
  font-weight: 500;
  border-radius: 0;
  padding: 0;
  margin: 0;
  background-color: #333333;
  //position: fixed;
  //left: 50%;
  //bottom: calc(10 * @px + env(safe-area-inset-bottom));
  //transform: translateX(-50%);
  z-index: 1000;

  &:after {
    border: none;
  }
}

.popup-commissioner {
  width: 100%;
  min-height: 622 * @px;
  background-image: url('https://static.soyoung.com/sy-design/bg1717411370761.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding-top: 15 * @px;
  padding-bottom: 34 * @px;
  position: relative;
  .popup-commissioner-close {
    position: absolute;
    right: 10 * @px;
    top: 16 * @px;
    z-index: 10;
    width: 20 * @px;
    height: 20 * @px;
  }
  .popup-commissioner-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20 * @px;
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 16 * @px;
      color: #333333;
      font-weight: 600;
      padding-bottom: 10 * @px;
    }
    .subtitle {
      font-family: PingFangSC-Semibold;
      font-size: 23 * @px;
      color: #222222;
      font-weight: 600;
      width: calc(100% - 50 * @px);
      display: block;
      text-align: center;
    }
  }
  .popup-commissioner-body {
    width: 337 * @px;
    height: 489 * @px;
    margin: 0 auto;
    background-image: url('https://static.soyoung.com/sy-design/1h1aj4adjaclb1717411370484.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .code {
      position: absolute;
      width: 184 * @px;
      height: 184 * @px;
      left: 50%;
      transform: translateX(-50%);
      top: 167 * @px;
    }
    .customer-service {
      font-family: PingFangSC-Regular;
      font-size: 13 * @px;
      color: #354052;
      text-align: center;
      font-weight: 400;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 16 * @px;
      text-decoration: underline;
    }
  }
}

.popup-tips {
  width: 100%;
  //box-sizing: border-box;
  //padding: 15 * @px 20 * @px 20 * @px;
  background-color: #ffffff;
  //border-radius: 10 * @px;
  .popup-tips-title {
    display: flex;
    height: 52 * @px;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-bottom: 10 * @px;
    font-family: PingFangSC-Medium;
    font-size: 16 * @px;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    position: relative;

    image {
      width: 20 * @px;
      height: 20 * @px;
      position: absolute;
      right: 15 * @px;
      top: 15 * @px;
    }
  }

  .popup-tips-content {
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    color: #333333;
    font-weight: 400;
    padding: 15 * @px 25 * @px 15 * @px;
  }

  .popup-tips-footer {
    width: 100%;
    height: 62 * @px;
    .popup-tips-footer-confirm {
      margin: 10 * @px 25 * @px 0;
      text-align: center;
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #333333;
      border-radius: 0;
      height: 42 * @px;
      &:after {
        border: none;
      }
    }
  }
}

.pay-success-c {
  padding: 12 * @px 15 * @px 15 * @px;
}

.pay-success-group {
  padding: 28 * @px 15 * @px 25 * @px;
}

.popup-image {
  position: relative;
  .popup-image-content-wrapper {
    width: 270 * @px;
    height: 381 * @px;
    .popup-image {
      width: 100%;
      height: 100%;
    }
  }

  .popup-image-close {
    width: 30 * @px;
    height: 30 * @px;
    position: absolute;
    bottom: -50 * @px;
    right: 50%;
    transform: translateX(50%);
    z-index: 10;
  }
}

.carousel-banner-container {
  margin-top: -8rpx;
  padding: 30rpx;
  background-color: #ffffff;
  .carousel-banner {
    margin: 0 auto;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    width: 100%;
    // height: auto;
    overflow: hidden;
    // border-radius: 8rpx;
  }
}
</style>
