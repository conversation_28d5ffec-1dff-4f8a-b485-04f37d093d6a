<template>
  <div class="order-box">
    <img
      class="bg"
      v-show="!showDetail"
      src="https://static.soyoung.com/sy-pre/od-1717755000638.png"
      alt=""
    />
    <div v-show="showDetail">
      <!--      <div v-if="groupInfo" class="top-bg-pintuan"></div>-->
      <div class="top-bg"></div>
      <div class="order-detail">
        <div
          v-if="menuRect.top"
          class="pageTop"
          :style="{ height: menuRect.bottom + 8 + 'px', background }"
        >
          <div
            class="nav"
            :style="{
              top: menuRect.top + 'px',
              width: menuRect.width + 'px',
              height: menuRect.height + 'px'
            }"
          >
            <div class="backBox" @click="handleBack">
              <image class="back" :src="headBar.backIcon" />
            </div>
          </div>
          <view class="title" :style="{ color: headBar.color }">
            订单详情
          </view>
        </div>
        <!--        <nav-bar-->
        <!--          title="订单详情"-->
        <!--          :hasFixedHome="true"-->
        <!--          :background="titleBarColor"-->
        <!--          @navbarMounted="navbarMounted"-->
        <!--          @onBack="goBack"-->
        <!--        ></nav-bar>-->
        <div class="detail-content">
          <!--      <div class="top-bg"></div>-->
          <!-- orderStatus -->
          <!--            OrderStatusToPay            = 0  //未支付，去支付-->
          <!--            OrderStatusToPayInvalid     = 1  //未支付，且已过有效期，不可支付-->
          <!--            OrderStatusPayed            = 2  //已支付-->
          <!--            OrderStatusPayedUsed        = 3  //已使用-->
          <!--            OrderStatusPayedInvalid     = 4  //已支付且已过期-->
          <!--            OrderStatusPayedBacked      = 5  //已退款-->
          <!--            OrderStatusPayedBacking     = 6  //退款中-->
          <!-- 普通订单 未支付 orderStatus=0 -->
          <div class="pay-status" v-if="basicOrderModule.orderStatus === 0">
            <div class="left-box">
              <div class="status">{{ basicOrderModule.status_str }}</div>
              <div class="time" v-show="basicOrderModule.pay_type === 27">
                分期支付信息未填写完成
              </div>
              <div
                class="time"
                v-show="
                  basicOrderModule.pay_type !== 27 &&
                  formattedTime !== '00:00:00'
                "
              >
                剩余付款时间{{ formattedTime }}
              </div>
            </div>
            <div class="right-box">
              <div
                class="btn-cancel"
                @click="cancelOrder(basicOrderModule, 'cancel')"
                v-if="basicOrderModule.orderStatus === 0"
              >
                取消订单
              </div>
            </div>
          </div>
          <!-- 已取消 orderStatus=1 -->
          <div
            class="pay-status"
            v-else-if="basicOrderModule.orderStatus === 1"
          >
            <div class="left-box">
              <div class="status" style="color: #bababa">
                {{ basicOrderModule.status_str }}
              </div>
            </div>
            <div class="right-box"></div>
          </div>
          <!-- 普通订单 已支付 orderStatus=2 -->
          <!-- 普通订单 已使用 orderStatus=3 -->
          <div
            class="pay-status"
            v-else-if="
              basicOrderModule.orderStatus === 2 ||
              basicOrderModule.orderStatus === 3
            "
          >
            <div class="left-box">
              <div
                class="status"
                :style="{
                  color:
                    basicOrderModule.orderStatus === 3 ? '#BABABA' : '#89DC65'
                }"
              >
                {{ basicOrderModule.status_str }}
              </div>
              <div
                v-if="
                  Number(basicOrderModule.is_crm_customer_level_order) === 1 &&
                  basicOrderModule.str_notice
                "
                class="refund-info"
                @click="validity_period_description"
              >
                {{ basicOrderModule.str_notice }}
                <img
                  v-if="
                    basicOrderModule.crm_customer_level_info &&
                    basicOrderModule.crm_customer_level_info.desc
                  "
                  class="tips"
                  src="https://static.soyoung.com/sy-design/32q76aat6dbg71717402777291.png"
                />
              </div>
            </div>
            <div class="right-box">
              <div class="text" v-if="basicOrderModule.reservation_sku_cnt > 0">
                包含{{ basicOrderModule.reservation_sku_cnt }}个项目
              </div>
            </div>
          </div>
          <!-- 普通订单 已退款 orderStatus=5 -->
          <div
            class="pay-status"
            v-else-if="basicOrderModule.orderStatus === 5"
          >
            <div class="left-box">
              <div class="status" style="color: #bababa">
                {{ basicOrderModule.status_str }}
              </div>
              <div
                v-if="
                  Number(basicOrderModule.is_crm_customer_level_order) === 1 &&
                  basicOrderModule.str_notice
                "
                class="refund-info"
              >
                {{ basicOrderModule.str_notice }}
              </div>
              <div
                class="refund-info"
                @click="refundTips"
                v-else-if="basicOrderModule.str_notice"
              >
                {{ basicOrderModule.str_notice }}
                <img
                  v-if="basicOrderModule.is_hide_popup !== 1"
                  class="tips"
                  src="https://static.soyoung.com/sy-design/32q76aat6dbg71717402777291.png"
                />
              </div>
            </div>
            <div class="right-box">
              <div class="text" v-if="basicOrderModule.orderStatus === 5">
                ¥ {{ priceInfoModule.pay_price_info.final_price }}
              </div>
            </div>
          </div>

          <!--          OrderStatusGroupStatusIng      = 17 // 拼团中-->
          <!--          OrderStatusGroupStatusSuccess  = 18 // 拼团成功-->
          <!--          OrderStatusGroupStatusFail     = 19 // 拼团失败-->
          <div
            class="pay-status"
            v-else-if="
              basicOrderModule.orderStatus === 17 ||
              basicOrderModule.orderStatus === 18 ||
              basicOrderModule.orderStatus === 19
            "
          >
            <div class="left-box pintuan">
              <!--              // 拼团中 icon-->
              <!--              <img-->
              <!--                v-if="basicOrderModule.orderStatus === 17"-->
              <!--                src="https://static.soyoung.com/sy-design/alolo8bred9p1718252535438.png"-->
              <!--                alt=""-->
              <!--                class="pintuan-icon"-->
              <!--              />-->
              <!--              拼团成功 icon-->
              <!--              <img-->
              <!--                v-if="basicOrderModule.orderStatus === 18"-->
              <!--                src="https://static.soyoung.com/sy-design/2hca9ziqfkuif1718252535322.png"-->
              <!--                alt=""-->
              <!--                class="pintuan-icon"-->
              <!--              />-->
              <!--              // 拼团失败 icon-->
              <!--              <img-->
              <!--                v-if="basicOrderModule.orderStatus === 19"-->
              <!--                src="https://static.soyoung.com/sy-design/3d7sz56v8syvg1718252535286.png"-->
              <!--                alt=""-->
              <!--                class="pintuan-icon"-->
              <!--              />-->
              <div
                class="status"
                :style="{
                  color:
                    basicOrderModule.orderStatus === 17
                      ? '#89DC65'
                      : basicOrderModule.orderStatus === 18
                      ? '#ffffff'
                      : '#FE6631'
                }"
              >
                {{ basicOrderModule.status_str }}
              </div>
            </div>
            <div class="right-box">
              <div class="text" v-if="basicOrderModule.reservation_sku_cnt > 0">
                包含{{ basicOrderModule.reservation_sku_cnt }}个项目
              </div>
            </div>
          </div>

          <!-- 氧分呗授权信息审核中 orderStatus=15 -->
          <!-- 氧分呗分期放款失败 orderStatus=16 -->
          <!-- 其他所有状态 -->
          <div class="pay-status" v-else>
            <div class="left-box">
              <div class="status">{{ basicOrderModule.status_str }}</div>
              <div
                class="refund-info"
                v-if="basicOrderModule.str_notice !== ''"
              >
                {{ basicOrderModule.str_notice }}
              </div>
            </div>
            <div class="right-box"></div>
          </div>
        </div>
        <div class="pintuan-card" v-if="groupInfo">
          <Card
            :groupInfo="groupInfo"
            :shareBtnVisible="true"
            :reportInfo="{
              order_id: groupInfo.order_id,
              product_id: basicOrderModule.product_id,
              group_id: groupInfo.group_id,
              activity_id: groupInfo.activity_id
            }"
            @countdownEnd="onCountdownEnd"
          />
        </div>
        <!--        <div class="pintuan-card">-->
        <!--          <Card :groupInfo="groupInfo" @countdownEnd="onCountdownEnd" />-->
        <!--        </div>-->
        <div
          class="product-card"
          v-if="product.length > 0"
          @click="goDetail(product[0].pinfo, 'productCard')"
        >
          <div class="img-box">
            <img
              v-if="product[0].pinfo"
              :src="product[0].pinfo.img_cover"
              alt=""
            />
          </div>
          <div class="product-info">
            <div class="product-title">
              {{ product[0].pinfo.title }}
            </div>
            <div class="meta">
              <div class="price">
                <text style="font-size: 18rpx; padding-right: 2rpx">¥</text>
                <text>{{ product[0].pinfo.product_online }}</text>
              </div>
              <div class="num">数量{{ product[0].pinfo.amount }}</div>
            </div>
          </div>
        </div>
        <div
          class="intention-card"
          v-if="
            basicOrderModule.reservation_hospital_name &&
            basicOrderModule.orderStatus !== 18
          "
        >
          <div class="line">
            <div class="text-key">意向服务门店：</div>
            <div class="value">
              {{ basicOrderModule.reservation_hospital_name }}
            </div>
          </div>
          <div class="line">
            <div class="text-key">意向到店时间：</div>
            <div class="value">{{ basicOrderModule.reservation_time }}</div>
          </div>
          <div
            v-if="basicOrderModule.reservation_notice.is_show"
            class="tips"
            :class="
              basicOrderModule.reservation_notice.text_type === 2
                ? 'green'
                : 'red'
            "
          >
            <img
              src="https://static.soyoung.com/sy-design/1xfji21ja78g11727086071790.png"
              alt=""
              class="icon"
            />
            {{ basicOrderModule.reservation_notice.text || '' }}
          </div>
        </div>
        <!--      预约card-->
        <div
          class="appointment-card"
          v-for="(item, index) in projectReservationInfoModule"
          :key="index"
        >
          <div class="top-bg-appointment"></div>
          <div class="product-title">
            <div
              class="title"
              :style="{
                'text-decoration':
                  basicOrderModule.orderStatus === 3 ? 'line-through' : 'none'
              }"
            >
              {{ item.sku_name }}
            </div>
            <div class="num">x{{ item.card_num }}</div>
          </div>
          <div class="appointment-box">
            <div class="remaining-times" v-if="item.show_type === 1">
              剩余{{ item.remaining_num }}次可用
              <div class="end" v-if="item.reservation_num === 0">
                （暂未预约）
              </div>
              <div class="end" v-else>（已约{{ item.reservation_num }}次）</div>
            </div>
            <div v-else class="remaining-times">{{ item.show_type_str }}</div>
            <!--            <div-->
            <!--              class="btn-appointment"-->
            <!--              v-for="(btn, bi) in item.button_list"-->
            <!--              :key="bi"-->
            <!--              @click="goAppointment(item.reservation_param, 'click')"-->
            <!--            >-->
            <!--              {{ btn.name }}-->
            <!--            </div>-->
          </div>
          <!--        v-if="item.note_msg && item.note_url"-->
          <div class="notice">
            <!--        <div class="notice" @click="goNotice(item.note_url)">-->
            <div class="text" @click="goNotice(item)">
              <!--              <img-->
              <!--                class="notice-img"-->
              <!--                src="https://static.soyoung.com/sy-design/2hca79az1qgeq1717402777807.png"-->
              <!--                alt=""-->
              <!--              />-->
              <text> 查看{{ item.notice.title }} </text>
              <image
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png"
              ></image>
            </div>
            <div
              class="btn-appointment"
              v-for="(btn, bi) in item.button_list"
              :key="bi"
              @click="goAppointment(item.reservation_param, 'click')"
            >
              <span class="button-label" v-if="btn.back_money_notice">{{
                btn.back_money_notice
              }}</span>
              {{ btn.name }}
            </div>
            <!--            <div class="text-btn">-->
            <!--              查看详情-->
            <!--              <img-->
            <!--                class="arrow-right"-->
            <!--                src="https://static.soyoung.com/sy-design/3ulassv8vi4gu1717471069339.png"-->
            <!--                alt=""-->
            <!--              />-->
            <!--            </div>-->
          </div>

          <block
            v-for="(card, cardIndex) in item.reservation_card"
            :key="`${index}-${cardIndex}`"
          >
            <div class="appointment-status-card" v-show="card.show">
              <!--          card状态 status 1待确认 2已确认  3已到院  4已使用-->
              <!--          // 预约时间确认中-->
              <!-- <div class="unconfirmed-head" v-if="card.card_info.status">
                <div class="unconfirmed-status">
                  <img
                    class="unconfirmed-icon"
                    src="https://static.soyoung.com/sy-design/2qin50e74h0gc1717402777713.png"
                    alt=""
                  />
                  <div class="main-text">预约时间确认中</div>
                  <div class="line"></div>
                  <div class="sub-text">确认后生效</div>
                </div>
                <div class="unconfirmed-desc">
                  您的预约专员将在48小时内联系您确认，请留意电话。
                </div>
                <div class="unconfirmed-desc">
                  您也可以主动
                  <div
                    class="text-btn"
                    @click="contactService(JSON.parse(JSON.stringify(card)))"
                  >
                    联系专员
                  </div>
                  快速确认。
                </div>
              </div> -->
              <!--          card状态 status 1待确认 2已确认  3已到院  4已使用-->
              <!--          // 预约时间已确认-->
              <!-- <div class="confirmed-head" v-if="card.card_info.status">
                <div class="confirmed-status">
                  <img
                    class="confirmed-icon"
                    src="https://static.soyoung.com/sy-pre/20241217-165018-1734423000620.png"
                    alt=""
                  />
                  <div class="main-text">预约时间已确认</div>
                </div>
                <div class="unconfirmed-desc">
                  您的预约已确认，请按照预约时间，按时到院。
                </div>
              </div> -->
              <!--          card状态 status 1待确认 2已确认  3已到院  4已使用-->
              <!-- <div class="reach-head" v-if="card.card_info.status">
                <div class="confirmed-status">
                  <img
                    class="confirmed-icon"
                    src="https://static.soyoung.com/sy-design/2cqx5i3pk5wiz1717471069171.png"
                    alt=""
                  />
                  <div class="main-text">已到院</div>
                </div>
              </div> -->
              <!-- <div class="used-already" v-if="card.card_info.status">
                已使用
              </div> -->
              <div
                class="appointment-head"
                :style="{
                  'background-color':
                    card.card_info.title_info.content_bg_color,
                  'border-color': card.card_info.title_info.content_border_color
                }"
              >
                <div class="appt-head-status">
                  <img
                    class="appt-head-icon"
                    v-if="card.card_info.title_info.icon_url"
                    :src="card.card_info.title_info.icon_url"
                  />
                  <div
                    class="appt-head-main-text"
                    :style="{
                      color: card.card_info.title_info.title.text_color,
                      'background-color':
                        card.card_info.title_info.title.background_color
                    }"
                  >
                    {{ getCardPropText(card, 'title') }}
                  </div>
                  <template v-if="getCardPropText(card, 'sub_title')">
                    <div
                      class="appt-head-line"
                      :style="{
                        'background-color':
                          card.card_info.title_info.sub_title.text_color
                      }"
                    ></div>
                    <div
                      class="appt-head-sub-text"
                      :style="{
                        color: card.card_info.title_info.sub_title.text_color,
                        'background-color':
                          card.card_info.title_info.sub_title.background_color
                      }"
                    >
                      {{ getCardPropText(card, 'sub_title') }}
                    </div>
                  </template>
                </div>
                <div
                  class="appt-head-desc"
                  v-if="card.card_info.title_info.desc"
                  :style="{
                    color: card.card_info.title_info.desc.text_color,
                    'background-color':
                      card.card_info.title_info.desc.background_color
                  }"
                >
                  <div
                    v-for="d in getCardDesc(card)"
                    :key="d.text"
                    :class="{
                      'appt-head-desc-text': true
                    }"
                  >
                    <Text
                      v-if="d.contact"
                      class="em"
                      @click="contactService(JSON.parse(JSON.stringify(card)))"
                    >
                      {{ d.text }}
                    </Text>
                    <template v-else>{{ d.text }}</template>
                  </div>
                </div>
              </div>
              <div class="appointment-body">
                <div class="text-sub-title">
                  {{
                    card.card_info.status === 4 ? '服务门店' : '意向服务门店'
                  }}
                </div>
                <div class="hospital-name">
                  <div class="hospital-name-left">
                    <div class="hospital-name-left-hospital-name">
                      {{ card.hospital_info.name_cn }}
                    </div>
                    <div
                      class="hospital-name-left-hospital-city"
                      @click="
                        authorizedGeographicLocation(
                          JSON.parse(JSON.stringify(card))
                        )
                      "
                    >
                      <block v-if="card.hospital_info.juli">
                        <text>
                          {{ card.hospital_info.juli }}
                        </text>
                      </block>
                      <block v-else>
                        <text> 授权地理位置，查看距离 </text>
                        <image
                          src="https://static.soyoung.com/sy-design/3ssb1a7i8o5nw1721014921032.png"
                        ></image>
                      </block>
                    </div>
                    <!--                    <img-->
                    <!--                      v-if="card.hospital_info.is_flag_ship"-->
                    <!--                      class="arrow-flagship"-->
                    <!--                      src="https://static.soyoung.com/sy-design/2x6zsxixmwvul1717471069174.png"-->
                    <!--                      alt=""-->
                    <!--                    />-->
                    <!--                    <img-->
                    <!--                      src="https://static.soyoung.com/sy-design/path1717471069217.png"-->
                    <!--                      alt=""-->
                    <!--                      class="arrow-right"-->
                    <!--                    />-->
                  </div>
                  <div class="hospital-name-right">
                    <div
                      class="hospital-name-right-item"
                      @click="
                        dial(
                          JSON.parse(
                            JSON.stringify(card.hospital_tel_info.mobile)
                          ),
                          JSON.parse(JSON.stringify(card.hospital_tel_info))
                        )
                      "
                    >
                      <image
                        src="https://static.soyoung.com/sy-design/2hca9lhmiqshw1727086071802.png"
                      ></image>
                      <!--                      <text> 电话 </text>-->
                    </div>
                    <div
                      v-if="card.hospital_info"
                      class="hospital-name-right-item"
                      @click="
                        goHospitalAddress(
                          JSON.parse(JSON.stringify(card.hospital_info)),
                          'map'
                        )
                      "
                    >
                      <image
                        src="https://static.soyoung.com/sy-design/2hca79az1qgeq1727086071767.png"
                      ></image>
                      <!--                      <text> 地址 </text>-->
                    </div>
                  </div>
                  <!--                  <img-->
                  <!--                    src="https://static.soyoung.com/sy-design/ow5hnzwncte1717471069384.png"-->
                  <!--                    alt=""-->
                  <!--                    class="location"-->
                  <!--                    @click="-->
                  <!--                      goHospitalAddress(-->
                  <!--                        JSON.parse(JSON.stringify(card.hospital_info)),-->
                  <!--                        'map'-->
                  <!--                      )-->
                  <!--                    "-->
                  <!--                  />-->
                </div>
                <div class="text-sub-title">
                  {{
                    card.card_info.status === 4 ? '服务时间' : '意向服务时间'
                  }}
                </div>
                <div class="time">{{ card.card_info.show_time }}</div>
                <CashBackNotice
                  v-if="card.card_info.back_money_notice"
                  :notice="card.card_info.back_money_notice"
                  :notice-url="card.card_info.back_money_role"
                />
                <div class="btn-box" v-if="card.card_info.status === 1">
                  <div
                    class="btn-change"
                    @click="
                      goAppointment(
                        JSON.parse(JSON.stringify(card.reservation_param)),
                        'click'
                      )
                    "
                  >
                    修改预约
                  </div>
                  <div
                    class="btn-contacts"
                    @click="contactService(JSON.parse(JSON.stringify(card)))"
                  >
                    联系专员
                    <div class="sub-text">｜快速确认</div>
                  </div>
                </div>
                <div
                  class="btn-box one"
                  v-if="
                    card.card_info.status !== 1 &&
                    card.card_info.reserve_modify_yn === 1
                  "
                >
                  <div
                    class="btn-change"
                    @click="
                      goAppointment(
                        JSON.parse(JSON.stringify(card.reservation_param)),
                        'click'
                      )
                    "
                  >
                    修改预约
                  </div>
                </div>
              </div>
            </div>
          </block>

          <div class="function" v-if="item.reservation_card.length > 1">
            <div
              class="launch"
              @click="appointmentShowMore(index)"
              v-show="!item.showMore"
            >
              查看全部预约<img
                src="https://static.soyoung.com/sy-design/3u5f7j35qkn711726715721954.png"
                alt=""
                class="arrow-down"
              />
            </div>
            <div
              class="shrink"
              @click="appointmentHideMore(index)"
              v-show="item.showMore"
            >
              收起<img
                src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726715721920.png"
                alt=""
                class="arrow-up"
              />
            </div>
          </div>
        </div>
        <div class="pay-info">
          <div class="line">
            <div class="text-key">商品总额</div>
            <div class="value">
              ¥{{ priceInfoModule.pay_price_info.order_total_original_price }}
            </div>
          </div>
          <!--        红包优惠-->
          <div
            class="line"
            v-for="(item, index) in priceInfoModule.deposit_price_info.bottom"
            :key="index"
          >
            <div class="text-key">{{ item.title }}</div>
            <div class="value green">¥{{ item.price }}</div>
          </div>
          <div class="division-line"></div>
          <block v-if="loanInfoModule && loanInfoModule.loan_data">
            <!--      // 分期信息-->
            <div class="line line2">
              <div class="text-key total-title">
                {{ loanInfoModule.loan_data.loan_info.title }}
              </div>
              <div class="btn-box">
                <!--                分期授权-->
                <div
                  class="loan-btn application"
                  :class="{
                    'no-apl':
                      loanInfoModule.loan_data.loan_info.submit_btn.is_grey ===
                      1
                  }"
                  v-if="loanInfoModule.loan_data.loan_info.submit_btn.is_show"
                  @click="
                    loanApplication(
                      loanInfoModule.loan_data.loan_info.submit_btn.url,
                      loanInfoModule.loan_data.loan_info.submit_btn
                    )
                  "
                >
                  {{ loanInfoModule.loan_data.loan_info.submit_btn.name }}
                </div>
                <!--                取消分期-->
                <div
                  class="loan-btn cancel-loan"
                  @click="cancelOrder(basicOrderModule, 'stages')"
                  v-if="loanInfoModule.loan_data.loan_info.cancel_btn.is_show"
                >
                  {{ loanInfoModule.loan_data.loan_info.cancel_btn.name }}
                </div>
              </div>
            </div>
            <div class="line">
              <div class="loan-info">
                {{ loanInfoModule.loan_data.loan_info.desc }}
              </div>
            </div>
            <div class="line line2">
              <div class="text-key">
                {{ loanInfoModule.loan_data.loan_money.title }}
              </div>
              <div class="value">
                ¥{{ loanInfoModule.loan_data.loan_money.money }}
              </div>
            </div>
            <div
              class="line"
              v-if="loanInfoModule.loan_data.loan_money.description"
            >
              <div class="loan-info">
                {{ loanInfoModule.loan_data.loan_money.description }}
              </div>
            </div>
          </block>
          <div v-else class="line line2">
            <div class="text-key total-title">
              {{ priceInfoModule.pay_price_info.final_price_desc }}
            </div>
            <div class="value">
              ¥{{ priceInfoModule.pay_price_info.final_price }}
            </div>
          </div>
        </div>
        <div
          class="by-stages"
          v-if="loanInfoModule.loan_data.loan_info.detail_btn.is_show"
          @click="goStages(loanInfoModule)"
        >
          <!--        <div class="by-stages" @click="goStages">-->
          <div class="line">
            <div class="text-key">分期金额</div>
            <div class="text-btn">
              查看分期信息
              <img
                class="arrow-right"
                src="https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="order-info">
          <div class="line">
            <div class="text-key">订单编号：</div>
            <div
              class="value"
              @click="copyText(`${basicOrderModule.order_id}`)"
            >
              {{ basicOrderModule.order_id }}
              <img
                src="https://static.soyoung.com/sy-design/copy1717471069266.png"
                alt=""
                class="icon"
              />
            </div>
          </div>
          <div class="line">
            <div class="text-key">订单时间：</div>
            <div class="value">{{ basicOrderModule.create_date }}</div>
          </div>
          <div class="line" v-if="basicOrderModule.pay_type_str">
            <div class="text-key">支付方式：</div>
            <div class="value">{{ basicOrderModule.pay_type_str }}</div>
          </div>
          <div class="line">
            <div class="text-key">手机号码：</div>
            <div class="value">{{ basicOrderModule.mobile }}</div>
          </div>
          <div class="line">
            <div class="text-key">咨询电话：</div>
            <div
              class="phone-box"
              @click="makePhoneCall(basicOrderModule.service_mobile)"
            >
              <div class="value">
                {{ basicOrderModule.service_mobile
                }}{{ basicOrderModule.service_time }}
              </div>
              <img
                src="https://static.soyoung.com/sy-design/2hca9lhmiqshw1727086071802.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <!--        <div class="refund-btn" v-if="showButtonList.length > 0">-->
        <!--        退款按钮-->
        <div class="refund-btn" v-if="refundButton">
          <div class="text" @click="refund(basicOrderModule.order_id)">
            {{ refundButton.name }}
          </div>
        </div>
        <view
          v-else-if="other_module && other_module.bottom_notice"
          class="no-refund-tips"
        >
          <text style="text-align: center">{{
            other_module.bottom_notice
          }}</text>
        </view>
        <!--        <div-->
        <!--          class="no-refund-tips"-->
        <!--          v-if="-->
        <!--            basicOrderModule.app_id === 126 && basicOrderModule.pay_status === 1-->
        <!--          "-->
        <!--        >-->
        <!--          <div class="text">该订单暂不支持退款</div>-->
        <!--          <div class="text">如有需要请联系您的专属管家</div>-->
        <!--        </div>-->

        <!--      已取消-->
        <!--      已退款-->
        <div class="bottom-fixed-has-btn" v-if="detailButton">
          <div class="kefu" @click="consult">
            <img
              class="kefu-icon"
              src="https://static.soyoung.com/sy-design/2oz4gvql823qq1729076376122.png"
              alt=""
            />
            <div class="text">咨询管家</div>
          </div>
          <div
            class="text-btn"
            @click="
              goDetail(
                {
                  product_id: basicOrderModule.product_id
                },
                'buyButton'
              )
            "
          >
            <!--            再次购买-->
            {{ detailButton.name }}
          </div>
        </div>
        <!--      待支付-->
        <div class="bottom-fixed-has-btn" v-else-if="payButton">
          <div class="kefu" @click="consult">
            <img
              class="kefu-icon"
              src="https://static.soyoung.com/sy-design/2oz4gvql823qq1729076376122.png"
              alt=""
            />
            <div class="text">咨询管家</div>
          </div>
          <div class="text-btn" @click="goPay(payButton)">
            <!--            立即支付-->
            {{ payButton.name }}
          </div>
        </div>
        <!--      立即申请分期button-->
        <div class="bottom-fixed-has-btn" v-else-if="applicationButton">
          <div class="kefu" @click="consult">
            <img
              class="kefu-icon"
              src="https://static.soyoung.com/sy-design/2oz4gvql823qq1729076376122.png"
              alt=""
            />
            <div class="text">咨询管家</div>
          </div>
          <div class="text-btn" @click="goPay(payButton)">
            <!--            立即支付-->
            {{ applicationButton.name }}
          </div>
        </div>
        <!--      其他-->
        <!--      仅客服状态-->
        <div v-else class="bottom-fixed" @click="consult">
          <div class="text-kefu">联系客服</div>
        </div>
      </div>
      <!--    <SecondRefundIntercept-->
      <!--      v-if="cancelOrderVisible"-->
      <!--      @onCancel="refundCancel"-->
      <!--      @onSubmit="refundSubmit"-->
      <!--    />-->
      <ApptDialog
        v-if="visible"
        :order_id="apptDialog.order_id"
        :top_order_id="apptDialog.top_order_id"
        :reserve_id="apptDialog.reserve_id"
        :city_id="apptDialog.city_id"
        :sku_id="apptDialog.sku_id"
        :order_app_id="apptDialog.order_app_id"
        :order_hospital_id="apptDialog.order_hospital_id"
        :ingredients_id="apptDialog.ingredients_id"
        :visible.sync="visible"
        @visible="onVisibleChange"
        @success="onApptSuccess"
        @onSuccessSubscribe="onSubscribeSuccess"
      />
      <ConfirmationWindow
        ref="confirmation"
        button-left="不退了"
        button-right="继续退款"
        content="商品很抢手，确认要退单么？"
        :automatic-closing="true"
        position="bottom"
        @onInteractive="confirmationCallback"
      />
      <ConfirmationWindow
        ref="stages"
        title="取消分期"
        button-left="取消"
        button-right="确认"
        content="是否确认取消分期"
        :automatic-closing="true"
        position="bottom"
        @onInteractive="confirmationCallback"
      />
      <ConfirmationWindow
        ref="cancel"
        button-left="我再想想"
        button-right="坚持取消"
        content="商品很抢手，确认取消？"
        :automatic-closing="true"
        position="bottom"
        @onInteractive="confirmationCallback"
      />
      <ConfirmationWindow
        ref="notice"
        :title="noticeDialogTitle"
        button-right="确认"
        content="商品很抢手，确认要退单么？"
        :automatic-closing="true"
        position="bottom"
        @onInteractive="noticeCallback"
      >
        <template v-slot:custom-content>
          <div
            class="notice-content"
            v-for="(item, index) in noticeDialogContent"
            :key="index"
          >
            {{ item }}
          </div>
        </template>
      </ConfirmationWindow>
    </div>
    <JoincDialog
      v-model="visibleDialogStatus"
      :hospital_id="contactSpecialistHospitalId"
    ></JoincDialog>
    <Poster ref="poster" title="好友邀你一起拼单，不同部位不同城市可混拼！" />
  </div>
</template>

<script>
// import NavBar from '@/components/NavBar.vue';
import Vue from 'vue';
import moment from 'moment';
// import { SecondRefundIntercept } from './components/second-refund-intercept.vue';
import ApptDialog from '@/components/appointNew/index.vue';
import { mapMutations, mapState } from 'vuex';
import pay, { subscribeTmpl } from '@/components/newPay';
// import config from '@/config.js';
import ConfirmationWindow from '@/packageOrder/components/confirmation-window.vue';
import Card from '@/components/pintuan/card';
import JoincDialog from '@/components/joinc.vue';
import Poster from '@/components/pintuan/poster.vue';
import SubscribeMixins from '@/mixins/subscribe';
import { accredit } from '@/packageOrder/locationAuthorization';
import CashBackNotice from '@/components/appointNew/CashBackNotice.vue';
const eventBus = getApp().globalData.eventBus;
const _ = require('lodash');
export default {
  components: {
    Poster,
    ApptDialog,
    Card,
    // NavBar,
    ConfirmationWindow,
    JoincDialog,
    CashBackNotice
  },
  mixins: [SubscribeMixins],
  // pageTrackConfig: 'sy_wxtuan_tuan_order_list_page',
  data() {
    // const { cityId } = this.userInfo;
    // console.log(, 123123)

    return {
      menuRect: {
        top: 0,
        bottom: 0,
        width: 0,
        height: 0
      },
      background: '',
      visibleDialogStatus: false,
      contactSpecialistHospitalId: '', // 联系专员底弹窗机构ID
      orderId: '',
      groupInfo: null,
      service: {
        hospitalName: '',
        codeUrl: '',
        phoneNumber: '',
        msg: '',
        is_workTime: null
      },
      firstInit: true,
      showDetail: false,
      noticeDialogTitle: '',
      noticeDialogContent: [],
      apptDialog: {
        order_id: '',
        top_order_id: '',
        reserve_id: null,
        city_id: null,
        sku_id: null,
        order_app_id: null,
        order_hospital_id: null,
        ingredients_id: null
      },
      visible: false,
      // titleBarColor: 'rgba(255,255,255,0)', // titlebar的颜色，为了渐变
      basicOrderModule: {},
      other_module: {},
      endDate: null,
      intervalId: null,
      formattedTime: '00:00:00',
      product: [],
      projectReservationInfoModule: [],
      // 分期数据
      loanInfoModule: {},
      // 商品数据
      priceInfoModule: {},
      // showButtonList: [],
      // 退款按钮
      refundButton: null,
      // 支付按钮
      payButton: null,
      // 立即申请分期按钮
      applicationButton: null,
      // 详情按钮
      detailButton: null
      // cancelOrderVisible: false // 二次拦截弹窗
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    headBar() {
      return {
        backIcon:
          this.background === '#ffffff'
            ? 'https://static.soyoung.com/sy-design/3cj8rc3ipek931726282046858.png'
            : 'https://static.soyoung.com/sy-design/2my50ua05v4r41726282046878.png',
        color: this.background === '#ffffff' ? '#030303' : this.background
      };
    }
  },
  methods: {
    ...mapMutations('global', ['setUserFn']),
    getCardPropText(card, prop) {
      const obj = card.card_info?.title_info?.[prop];
      return obj?.text || '';
    },
    getCardDesc(card) {
      const desc = card.card_info?.title_info?.desc;
      if (!desc) return [];
      const list = desc.text.split(' 联系专员 ');
      if (list.length === 2) {
        const [left, right] = list;
        return [
          {
            color: desc.text_color,
            bgColor: desc.background_color,
            text: left
          },
          {
            color: desc.text_color,
            bgColor: desc.background_color,
            text: '联系专员',
            contact: true
          },
          {
            color: desc.text_color,
            bgColor: desc.background_color,
            text: right
          }
        ];
      } else {
        return [
          {
            color: desc.text_color,
            bgColor: desc.background_color,
            text: desc.text
          }
        ];
      }
    },
    validity_period_description() {
      if (this.basicOrderModule?.crm_customer_level_info?.desc) {
        uni.showModal({
          title:
            this.basicOrderModule?.crm_customer_level_info?.title || '温馨提示',
          content: this.basicOrderModule?.crm_customer_level_info?.desc,
          showCancel: false
        });
      }
    },
    /**
     * 拨打电话
     * @param {number|string} phone - 电话
     * @param data
     * */
    dial(phone, data) {
      if (data.is_workTime === 1) {
        uni.makePhoneCall({
          phoneNumber: phone,
          fail: (err) => {
            if (err.errMsg !== 'makePhoneCall:fail cancel') {
              uni.showToast({
                title: err.errMsg,
                icon: 'none'
              });
            }
          }
        });
      } else {
        uni.showModal({
          content: data.msg,
          confirmText: '确定',
          success(resp) {
            console.log(resp);
          }
        });
      }
    },
    handleBack() {
      if (getCurrentPages().length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack();
      }
    },
    onCountdownEnd() {},
    async authorizedGeographicLocation(param) {
      try {
        if (param.hospital_info.juli) {
          return;
        }
        const data = await accredit();
        console.log('定位信息', data);
        this.setUserFn(
          Object.assign({}, this.userInfo, {
            lng: data.lng,
            lat: data.lat
          })
        );
        await this.getData();
      } catch (error) {
        console.log(error);
      }
    },
    contactService(card) {
      this.$reportData({
        info: 'sy_wxtuan_or_order_info:contact_consultant_click',
        ext: {}
      });
      // service: {
      //   hospitalName: '',
      //     codeUrl: '',
      //     phoneNumber: '',
      //     msg: '',
      //     is_workTime: null,
      // },
      if (!card?.add_c_qr_code) {
        uni.showModal({
          // title: '提示',
          content: '该门店暂无预约专员联系方式，如有问题，请联系客服。',
          showCancel: false
        });
        return;
      }
      this.service.hospitalName = card?.hospital_info?.name_cn;
      this.service.codeUrl = card?.add_c_qr_code;
      this.service.phoneNumber = card?.hospital_tel_info?.mobile;
      this.service.msg = card?.hospital_tel_info?.msg;
      this.service.is_workTime = card?.hospital_tel_info?.is_workTime;

      this.contactSpecialistHospitalId = card?.hospital_info?.hospital_id;

      this.visibleDialogStatus = true;
    },
    // 分期授权
    loanApplication(url, data) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:qianshu_click',
        ext: {
          card_type: this.basicOrderModule.status_str
        }
      });
      if (data?.is_grey === 1) {
        return;
      }
      this.$bridge({
        url: '/pages/h5?url=' + encodeURIComponent(url)
      });
    },
    goStages(data) {
      console.log(data);
      this.$bridge({
        url:
          '/pages/h5?url=' +
          encodeURIComponent(data?.loan_data?.loan_info?.detail_btn?.url)
      });
    },
    async noticeCallback(data) {
      console.log(data);
    },
    async confirmationCallback(data) {
      if (data.type === 'buttonRight') {
        const res = await Vue.$request({
          url: '/syChainTrade/wxapp/order/cancel',
          // 待支付 1221111111111215622
          // 已支付 1221111111111215623
          // 已取消 1221111111111215657
          // 已退款 1221111111111215661
          // 已完成 1221111111111215036
          // 可使用 1221111111111215674 盛伟
          // 可使用 1221111111111215723 杰鹏
          // 可使用 1221111111111215653 杰鹏 带预约card
          data: {
            order_id: this.orderId
          },
          method: 'POST'
        });
        if (res.data.errorCode === 0) {
          // this.cancelOrderVisible = false;
          uni.showToast({
            title: '取消成功',
            icon: 'success',
            duration: 2000
          });
          this.getData();
        } else {
          // this.cancelOrderVisible = false;
          uni.showToast({
            title: res.data.errorMsg,
            icon: 'none',
            duration: 2000
          });
        }
      }
    },
    async getCityID() {
      const res = await Vue.$request({
        url: '/syGroupBuy/reservation/getDistrictInfoByIp',
        data: {},
        method: 'GET'
      });
      return res.data.responseData.city_id;
    },
    // 预约
    async goAppointment(data, type) {
      if (type === 'click') {
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:subscribe_click',
          ext: {
            order_id: data.order_id
          }
        });
      }
      console.log('胜伟数据', JSON.stringify(data));
      // if (!this.apptDialog.city_id) {
      //   this.apptDialog.city_id = await this.getCityID();
      // }

      this.apptDialog.city_id = data.city_id;
      this.apptDialog.order_app_id = data.order_app_id;
      this.apptDialog.order_id = data.order_id;
      this.apptDialog.top_order_id = data.top_order_id;
      this.apptDialog.reserve_id = data.reserve_id;
      this.apptDialog.sku_id = data.sku_id;
      this.apptDialog.order_hospital_id = data.order_hospital_id;
      this.apptDialog.ingredients_id = data.ingredients_id;
      console.log(JSON.stringify(this.apptDialog));

      this.visible = true;
      // if (this.userInfo.uid === 23286252) {
      //   this.visible = true;
      // }

      // ingredients_id: 11116560
      // order_hospital_id: 27469
      // order_id: "1221111111111216060"
      // reserve_id: 2443
      // sku_id: 11116560
      // top_order_id: "1221111111111216060"
      // console.log(data)
    },
    // 去支付
    async goPay(payButton) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:pay_btn_click',
        ext: {
          order_id: this.orderId,
          content: payButton?.name,
          tab_type: this.basicOrderModule.pay_type_str
        }
      });
      const payResult = await pay(
        {
          oid: this.basicOrderModule.oid,
          pay_type: this.basicOrderModule.pay_type
        },
        async () =>
          this.createGroupBySub(subscribeTmpl, [this.basicOrderModule.oid])
      );
      console.log('订单返回数据', payResult);
      if (payResult.status < 0) {
        throw new Error(payResult.message);
      }
      console.log('成功');
      // 刷新订单
      this.getData();
      // console.log('===>', payResult);
    },
    onVisibleChange() {
      this.getData();
      this.visible = false;
      console.log('onVisibleChange');
    },
    onApptSuccess() {
      this.getData();
      this.visible = false;
      console.log('onApptSuccess');
    },
    onSubscribeSuccess() {
      this.getData();
      this.visible = false;
      console.log('onSubscribeSuccess');
    },
    /**
     * 联系客服
     * */
    consult() {
      const pid = this.basicOrderModule.product_id;
      const url = `/packageAccount/consult?type=3&skuid=${pid}&scene=2`;

      // 判断是否已加C，加C后跳转到客服会话页
      // this.$toKefuDialog({
      //   source: 'sy_yx_mini_program_private_msg_customer_service_order',
      //   pid: pid,
      //   unJoinFn: () => {
      //     // 未加C时执行
      //     this.$bridge({
      //       url: url
      //     });
      //   }
      // });
      this.$reportData({
        info: 'sy_yx_mini_program_private_msg_customer_service_order',
        ext: {}
      });
      this.$bridge({
        url: url
      });
    },
    // 显示 更多card
    appointmentShowMore(index) {
      console.log(index);
      this.projectReservationInfoModule[index].showMore =
        !this.projectReservationInfoModule[index].showMore;
      this.projectReservationInfoModule[index].reservation_card =
        this.projectReservationInfoModule[index].reservation_card.map(
          (data) => {
            return {
              ...data,
              show: true
            };
          }
        );
    },
    // 隐藏 第一条之外card
    appointmentHideMore(index) {
      console.log(index);
      this.projectReservationInfoModule[index].showMore =
        !this.projectReservationInfoModule[index].showMore;
      this.projectReservationInfoModule[index].reservation_card =
        this.projectReservationInfoModule[index].reservation_card.map(
          (data, index) => {
            if (index === 0) {
              return {
                ...data,
                show: true
              };
            }
            return {
              ...data,
              show: false
            };
          }
        );
    },
    // 取消订单弹窗 - 取消按钮
    // refundCancel() {
    //   // this.cancelOrderVisible = false;
    // },
    // // 取消订单弹窗 - 确认按钮
    // async refundSubmit() {
    //   // 这里添加取消订单逻辑
    //   const res = await Vue.$request({
    //     url: '/syGroupBuy/chain/order/cancel',
    //     // 待支付 1221111111111215622
    //     // 已支付 1221111111111215623
    //     // 已取消 1221111111111215657
    //     // 已退款 1221111111111215661
    //     // 已完成 1221111111111215036
    //     // 可使用 1221111111111215674 盛伟
    //     // 可使用 1221111111111215723 杰鹏
    //     // 可使用 1221111111111215653 杰鹏 带预约card
    //     data: {
    //       order_id: this.orderId
    //     },
    //     method: 'POST'
    //   });
    //   if (res.data.errorCode === 0) {
    //     // this.cancelOrderVisible = false;
    //     uni.showToast({
    //       title: '取消成功',
    //       icon: 'success',
    //       duration: 2000
    //     });
    //     this.getData();
    //   } else {
    //     // this.cancelOrderVisible = false;
    //     uni.showToast({
    //       title: res.data.errorMsg,
    //       icon: 'none',
    //       duration: 2000
    //     });
    //   }
    // },
    // 申请退款逻辑
    async refund(orderId) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_refund:btn_click',
        ext: {
          order_id: orderId
        }
      });
      uni.navigateTo({
        url: `/packageOrder/refund?order_id=${orderId}`, // 拼接 URL 和查询参数
        success: function () {
          console.log('跳转成功');
        },
        fail: function (err) {
          console.error('跳转失败', err);
        }
      });
    },
    refundTips() {
      if (+this.basicOrderModule.is_hide_popup === 1) {
        return;
      }
      uni.showModal({
        title: '提示',
        content: '退款将退还您实际支付的金额',
        showCancel: false
      });
    },
    goDetail(data, type) {
      // if (type === 'buyButton') {
      //
      // }
      if (type === 'productCard') {
        if (data.sale_type === 2) {
          return;
        }
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:product_card_click',
          ext: {
            product_id: data.product_id,
            order_id: this.orderId
          }
        });
      }
      // console.log(JSON.stringify(data))
      uni.navigateTo({
        url: `/pages/product?id=${data.product_id}`, // 拼接 URL 和查询参数
        success: function () {
          console.log('跳转成功');
        },
        fail: function (err) {
          console.error('跳转失败', err);
        }
      });
    },
    copyText(text) {
      uni.setClipboardData({
        data: text, // 需要复制的内容
        success() {
          console.log('复制成功');
        },
        fail() {
          console.error('复制失败');
        }
      });
    },
    makePhoneCall(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber: phoneNumber, // 电话号码
        success: function () {
          console.log('拨打电话成功');
        },
        fail: function (err) {
          console.error('拨打电话失败', err);
        }
      });
    },
    cancelOrder(order, type) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:cancel_click',
        ext: {
          order_id: order.order_id
        }
      });
      console.log(order);
      if (type === 'stages') {
        this.$refs.stages.open();
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:quxiaofenqi_click',
          ext: {
            card_type: this.basicOrderModule.status_str
          }
        });
      } else if (type === 'cancel') {
        this.$refs.cancel.open();
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:cancel_popup_exposure',
          ext: {}
        });
      } else {
        this.$refs.confirmation.open();
      }
      // console.log(order.order_id);
      // this.cancelOrderVisible = true;

      // console.log(orderId)
      // return
      // const res = await Vue.$request({
      //   url: '/syGroupBuy123/chain/order/cancel?',
      //   // 待支付 1221111111111215622
      //   // 已支付 1221111111111215623
      //   // 已取消 1221111111111215657
      //   // 已退款 1221111111111215661
      //   // 已完成 1221111111111215036
      //   // 可使用 1221111111111215674 盛伟
      //   // 可使用 1221111111111215723 杰鹏
      //   // 可使用 1221111111111215653 杰鹏 带预约card
      //   data: {
      //     order_id: '1221111111111215653',
      //     // uid: '19852379588'
      //   },
      //   method: 'GET'
      // });
      // console.log(res)
      // uni.showModal({
      //   // title: '提示',
      //   content: '商品很抢手，确认要取消？',
      //   cancelText: '我再想想',
      //   cancelColor: '',
      //   confirmText: '坚持取消',
      //   confirmColor: '',
      //   success: (res) => {
      //     if (res.confirm) {
      //       console.log('确认');
      //     } else if (res.cancel) {
      //       console.log('取消');
      //     }
      //   }
      // });
    },
    // 跳转机构数据ID
    goHospitalAddress(info, type) {
      if (type == 'map') {
        uni.openLocation({
          latitude: +info.lat,
          longitude: +info.lng,
          scale: 18,
          name: info.name_cn,
          address: info.address,
          fail: (e) => {
            uni.showToast({
              title: JSON.stringify(e),
              icon: 'none'
            });
          }
        });
      }
      if (type == 'card') {
        uni.navigateTo({
          url: `/packageHospital/hospital-map?hospital_id=${info}`, // 拼接 URL 和查询参数
          success: function () {
            console.log('跳转成功');
          },
          fail: function (err) {
            console.error('跳转失败', err);
          }
        });
      }
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:map_btn_click',
        ext: {}
      });
    },
    goNotice(data) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:chakan_click',
        ext: {
          status: this.basicOrderModule.status_str
        }
      });
      console.log(data);
      this.noticeDialogTitle = data?.notice?.title;
      this.noticeDialogContent = data?.notice?.content;
      this.$refs.notice.open();

      // this.$toH5(url);
    },
    // navbar 渲染完之后计算出高度
    navbarMounted(info) {
      this.shimHeight = info.height * 2 + 'rpx';
    },
    goBack() {
      uni.navigateBack();
    },
    dealData(res) {
      const { errorCode, responseData } = res.data;
      if (errorCode !== 0) {
        uni.showToast({
          title: res?.data?.errorMsg,
          icon: 'none'
        });
        return;
      }
      this.showDetail = true;
      this.refundButton = null;
      this.payButton = null;
      this.detailButton = null;
      try {
        responseData?.show_button_module?.show_button_list.forEach((data) => {
          // type:  67 立即付款， 68  再次购买， 6 退款
          if (data.type === 6) {
            // 退款
            // 转赠、换购订单不支持线上退款，transaction_type 2 转赠 3 换购，PM：玉龙｜六一
            if (
              [2, 3].indexOf(
                Number(responseData?.basic_order_module?.transaction_type)
              ) < 0
            ) {
              this.refundButton = data;
            }
          } else if (data.type === 67) {
            // 立即付款
            this.payButton = data;
          } else if (data.type === 69) {
            // 立即申请
            this.applicationButton = data;
          } else if (data.type === 68) {
            // 再次购买
            this.detailButton = data;
          }
        });

        console.log(this.refundButton, 'this.refundButton');

        console.log(responseData, 'responseData?.basic_order_module');
        this.other_module = responseData?.other_module;
        this.basicOrderModule = responseData?.basic_order_module;
        this.basicOrderModule.create_date = moment(
          this.basicOrderModule.create_date,
          'YYYY-MM-DDTHH:mm:ss'
        ).format('YYYY-MM-DD HH:mm:ss');
        this.product = responseData?.product_list_module;
        // 待支付 倒计时
        if (this.basicOrderModule.orderStatus === 0) {
          this.endDate = moment(
            this.basicOrderModule?.expire_date,
            'YYYY-MM-DDTHH:mm:ss'
          );
          this.updateCountdown();
          this.intervalId = setInterval(this.updateCountdown, 1000);
        }

        // 列表中，第一个projectReservationInfoModule的card，全部显示预约card
        // 其他的只显示第一个card
        this.projectReservationInfoModule =
          responseData?.project_reservation_info_module.map((data) => {
            data.reservation_card = data?.reservation_card.map(
              (card, index) => {
                // if (pIndex === 0) {
                //   return {
                //     ...card,
                //     show: true
                //   };
                // }
                if (index === 0) {
                  return {
                    ...card,
                    show: true
                  };
                }
                return {
                  ...card,
                  show: false
                };
              }
            );
            return {
              ...data,
              showMore: false
            };
          });

        // 分期数据
        this.loanInfoModule = responseData?.loan_info_module;
        // 商品数据
        this.priceInfoModule = responseData?.price_info_module;
        // console.log(responseData?.group_info, 11111)
        // 拼团数据
        this.groupInfo = responseData?.group_info;
      } catch (e) {
        this.showDetail = false;
        uni.showToast({
          title: e.message,
          icon: 'none'
        });
        console.log(e);
      }
    },
    async getData(type) {
      // onload来的请求，首次改变状态
      if (type === 'firstInit') {
        this.firstInit = false;
      }
      // 过滤onshow的重复请求
      if (this.firstInit === true) {
        return;
      }
      const res = await Vue.$request({
        url: '/syChainTrade/wxapp/order/getOrderInfo',
        // 待支付 1221111111111215622
        // 已支付 1221111111111215623
        // 已取消 1221111111111215657
        // 已退款 1221111111111215661
        // 已完成 1221111111111215036
        // 可使用 1221111111111215674 盛伟
        // 可使用 1221111111111215723 杰鹏
        // 可使用 1221111111111215653 杰鹏 带预约card
        // 可使用 1221111111111216260 分期数据
        data: {
          order_id: this.orderId
          // order_id: '1221111111111215623'
          // uid: '19852379588'
        },
        method: 'GET'
      });

      this.dealData(res);
    },
    updateCountdown() {
      const timeLeft = this.endDate.diff(moment());
      if (timeLeft < 0) {
        clearInterval(this.intervalId);
        this.formattedTime = '00:00:00';
        return;
      }
      this.formattedTime = moment.utc(timeLeft).format('HH:mm:ss');
    }
  },
  onPageScroll(e) {
    // console.log(e.scrollTop);
    // if (e.scrollTop < 300) {
    //   this.titleBarColor = `rgba(255,255,255,${e.scrollTop / 300})`;
    // } else {
    //   this.titleBarColor = `#ffffff`;
    // }

    if (e.scrollTop < 100) {
      this.background = `rgba(255,255,255,${e.scrollTop / 100})`;
    } else {
      this.background = `#ffffff`;
    }
  },
  async onShow() {
    // if (!Number(this.userInfo.lng) || !Number(this.userInfo.lat)) {
    //   const data = await accredit();
    //   console.log('定位信息', data);
    //   this.setUserFn(
    //     Object.assign({}, this.userInfo, {
    //       lng: data.lng,
    //       lat: data.lat
    //     })
    //   );
    // }
    await this.getData();
    this.$reportPageShow({
      info: 'sy_wxtuan_tuan_order_info_page',
      ext: {
        order_id: this.orderId,
        status: this.basicOrderModule.status_str
      }
    });
    // this.$reportPageShow({
    //   info: 'sy_wxtuan_tuan_order_info_page',
    //   ext: {
    //     order_id: this.orderId,
    //     status: this.basicOrderModule.status_str
    //   }
    // });
    // if (this.firstInit === false) {
    //   this.getData();
    // }
    // console.log('onShow');
    // this.getData('firstInit');
  },
  async onLoad(options) {
    console.log(options, 'optionsoptionsoptionsoptionsoptions');
    this.menuRect = uni.getMenuButtonBoundingClientRect();
    this.orderId = options.orderId;
    // const positionJurisdiction = await new Promise((resolve, reject) => {
    //   wx.getSetting({
    //     success(res) {
    //       resolve(res.authSetting['scope.userLocation']);
    //     },
    //     fail(arg) {
    //       reject(arg);
    //     }
    //   });
    // });
    // if (positionJurisdiction === false) {
    //   this.apptDialog.city_id = Number(this.userInfo.cityId) || 1;
    // } else {
    //   const city = await this.$getCityId();
    //   this.apptDialog.city_id = city.cityId;
    // }
    // const city = await this.$getCityId();
    // this.apptDialog.city_id = city.cityId;
    this.apptDialog.city_id = Number(this.userInfo.cityId) || 1;
    await this.getData('firstInit');
    // TODO 打开加 C 底弹窗
    eventBus.$on('openAppointment', (e) => {
      // e.from 来源
      // 退款 refund
      // 订单列表 orderList
      // paySuccess 支付成功
      console.log('from', e.from);
      console.log(
        'projectReservationInfoModule',
        this.projectReservationInfoModule
      );
      if (e.from === 'refund') {
        // 以上数据，判断每一个item的remaining_num-reservation_num是否等于0，
        // 如果全部都等于0，取第一条item数据下reservation_card列表中第一条数据的reservation_param返回，
        // 如果都不等于0，取第一条item数据的reservation_param返回，
        // 如果部分不等于0，那就找到最近的一条不等于0的数据，取item数据的reservation_param返回
        // 退款情况回来的 特殊处理
        const items = this.projectReservationInfoModule;
        // 检查所有 item 的 remaining_num 和 reservation_num 是否相等
        const allEqual = _.every(
          items,
          (item) => item.remaining_num === item.reservation_num
        );

        // 根据条件获取 reservation_param
        let selectedReservationParam;
        if (allEqual) {
          // 如果全部相等，取第一条 item 的 reservation_card 中第一条数据的 reservation_param
          selectedReservationParam =
            items[0].reservation_card[0].reservation_param;
        } else {
          // 如果有不相等的，找到最近的一条不等于 0 的 item 的 reservation_param
          const firstUnequalItem = _.find(
            items,
            (item) => item.remaining_num !== item.reservation_num
          );
          selectedReservationParam = firstUnequalItem
            ? firstUnequalItem.reservation_param
            : items[0].reservation_param;
        }
        this.goAppointment(selectedReservationParam);
      }
    });
    // 链接上携带openAppointment参数，自动唤起立即预约
    if (options.openAppointment) {
      // eslint-disable-next-line no-inner-declarations
      function getReservationParam(list, options) {
        if (!options || typeof options !== 'object') {
          return undefined; // 如果 options 不是一个对象，返回 undefined
        }

        if (options.openAppointment === 'now') {
          // 如果选项是 'now'，返回第一个 item 的 reservation_param
          return list.length > 0 ? list[0].reservation_param : undefined;
        } else if (options.openAppointment === 'edit') {
          // 如果选项是 'edit'，找到匹配的 reservation_card 并返回其 reservation_param

          const reservationParam = _.find(list, (item) => {
            return _.some(item.reservation_card, (card) => {
              return (
                `${card.reservation_param.reserve_id}` ===
                `${options.reserveId}`
              );
            });
          });
          console.log(options, 'optionsoptionsoptionsoptionsoptions');
          console.log(
            reservationParam,
            'reservationParamreservationParamreservationParamreservationParam'
          );

          if (reservationParam) {
            const res = _.find(reservationParam.reservation_card, (item) => {
              return (
                `${item.reservation_param.reserve_id}` ===
                `${options.reserveId}`
              );
            });
            if (res) {
              return res.reservation_param;
            }
            return undefined;
          }
          return undefined;
        }
        // 如果 options.openAppointment 不是 'now' 或 'edit'，包括 'continue' 的情况，不执行任何操作
        return undefined;
      }
      const res = getReservationParam(
        this.projectReservationInfoModule,
        options
      );
      console.log(res, 'resresresresresres');
      if (res) {
        this.goAppointment(res);
      }
    }
    this.$registerExposure(
      '.appointment-card .notice',
      () => {
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:chakan_exposure',
          ext: {
            status: this.basicOrderModule.status_str
          }
        });
      },
      this
    );
    // application cancel-loan
    this.$registerExposure(
      '.application',
      () => {
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:qianshu_exposure',
          ext: {
            status: this.basicOrderModule.status_str
          }
        });
      },
      this
    );
    this.$registerExposure(
      '.cancel-loan',
      () => {
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:quxiaofenqi_exposure',
          ext: {
            status: this.basicOrderModule.status_str
          }
        });
      },
      this
    );
  },
  onUnload() {
    this.$reportPageUnload({
      info: 'sy_wxtuan_tuan_order_info_page',
      ext: {
        order_id: this.orderId,
        status: this.basicOrderModule.status_str
      }
    });
    this.$reportData({
      info: 'sy_wxtuan_tuan_order_info:back_btn_click',
      ext: {}
    });
  },
  onShareAppMessage() {
    return {
      promise: this.$refs.poster.shareInfo({
        sku_id: this.basicOrderModule.product_id,
        group_id: this.groupInfo.group_id,
        activity_id: this.groupInfo.activity_id
      })
    };
  },
  onHide() {
    this.$reportPageHide({
      info: 'sy_wxtuan_tuan_order_info_page',
      ext: {
        order_id: this.orderId,
        status: this.basicOrderModule.status_str
      }
    });
  }
};
</script>

<style scoped lang="less">
@px: 2rpx;

.pageTop {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 9999;
  .share {
    display: flex;
    position: fixed;
    align-items: center;
    height: 88rpx;
    right: -100%;
    .shareIcon {
      width: 60rpx;
      height: 60rpx;
    }
  }
  .title {
    position: absolute;
    left: 50%;
    bottom: 12px;
    //height: 88rpx;
    font-family: PingFangSC-Medium;
    font-size: 17 * @px;
    color: transparent;
    display: flex;
    align-items: center;
    font-weight: 500;
    transform: translateX(-50%);
    white-space: nowrap;
  }
  .nav {
    position: fixed;
    display: flex;
    align-items: center;
    //.title {
    //  font-family: PingFangSC-Medium;
    //  font-size: 17 * @px;
    //  letter-spacing: 0;
    //  font-weight: 500;
    //  position: absolute;
    //  left: 50%;
    //  top: 50%;
    //  transform: translate(-50%, -50%);
    //  white-space: nowrap;
    //}
    .back {
      width: 44 * @px;
      height: 44 * @px;
      display: block;
    }
  }
}

.order-box {
  min-height: 100vh;
  background-color: #f8f8f9;
}

.bg {
  width: 100vw;
  height: 100vh;
}

.notice-content {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: justify;
  line-height: 23px;
  font-weight: 400;
  padding-bottom: 3px;

  &:last-child {
    padding-bottom: 20px;
  }
}

.top-bg {
  position: absolute;
  top: 0;
  width: 100vw;
  height: 180 * @px;
  background-color: #030303;
}

.top-bg-pintuan {
  position: absolute;
  width: 100vw;
  height: 256px;
  opacity: 0.74;
  background-image: linear-gradient(
    180deg,
    #f9e3df 0%,
    rgba(249, 227, 223, 0) 70%
  );
}

.order-detail {
  position: relative;
  padding-bottom: calc(90 * @px + constant(safe-area-inset-bottom));
  padding-bottom: calc(90 * @px + env(safe-area-inset-bottom));
  padding-top: 158 * @px;
  .detail-content {
    z-index: 999;
    padding-top: 88px;
    width: 100%;
    position: absolute;
    top: 0;

    .pay-status {
      display: flex;
      justify-content: space-between;
      padding: 10 * @px 15 * @px 6 * @px 30 * @px;
      //height: 95 * @px;
      box-sizing: border-box;
      align-items: center;

      .left-box {
        display: flex;
        flex-direction: column;

        &.pintuan {
          display: flex;
          flex-direction: initial;
          align-items: center;

          .status {
            color: #fff;
          }
        }

        .pintuan-icon {
          width: 25px;
          height: 25px;
          display: flex;
          margin-right: 7px;
        }

        .status {
          font-family: PingFangSC-Semibold;
          font-size: 24 * @px;
          color: #89dc65;
          letter-spacing: 0;
          font-weight: 600;
        }

        .refund-info {
          display: flex;
          align-items: center;
          font-family: PingFangSC-Regular;
          font-size: 13 * @px;
          color: #bababa;
          letter-spacing: 0;
          line-height: 24 * @px;
          font-weight: 500;

          .tips {
            width: 14 * @px;
            height: 14 * @px;
            margin-left: 5 * @px;
          }
        }

        .time {
          font-family: OutFit-Regular;
          font-size: 13 * @px;
          color: #bababa;
          letter-spacing: 0;
          font-weight: 500;
          padding-top: 3 * @px;
        }
      }

      .right-box {
        display: flex;

        &.vc {
          align-items: center;
        }

        .btn-cancel {
          //border: 1px solid #e7e7e7;
          //border-radius: 19px;
          //width: 82px;
          //height: 34px;
          //display: flex;
          //justify-content: center;
          //align-items: center;
          width: 88 * @px;
          height: 37 * @px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1 * @px solid #333;
          font-family: PingFangSC-Regular;
          font-size: 12 * @px;
          color: #bababa;
          letter-spacing: 0;
          font-weight: 400;
        }

        .text {
          font-family: PingFangSC-Medium;
          font-size: 13 * @px;
          color: #bababa;
          letter-spacing: 0;
          text-align: right;
          font-weight: 500;
          height: 30px;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .pintuan-card {
    margin: 0 15 * @px 10 * @px;
    background-color: #fff;
  }

  .product-card {
    display: flex;
    padding: 15 * @px;
    margin: 0 15 * @px;
    background-color: #ffffff;

    .img-box {
      width: 79.97 * @px;
      height: 59.67 * @px;
      background: #f5f5f5;
      margin-right: 10 * @px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 2 * @px;
      }
    }

    .product-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;

      .product-title {
        font-family: PingFangSC-Medium;
        font-size: 13 * @px;
        color: #333333;
        line-height: 20 * @px;
        font-weight: 500;
        margin-bottom: 4 * @px;
      }

      .meta {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-family: PingFangSC-Regular;

        .num {
          font-family: Outfit-Regular;
          font-size: 13 * @px;
          color: #8c8c8c;
          font-weight: 400;
          padding-left: 17 * @px;
          position: relative;

          &:before {
            content: '';
            width: 1 * @px;
            height: 8 * @px;
            background-color: #8c8c8c;
            position: absolute;
            left: 8 * @px;
            top: 50%;
            transform: translateY(-50%);
          }
        }

        .price {
          display: flex;
          align-items: baseline;

          text {
            font-family: PingFangSC-Regular;
            font-size: 13 * @px;
            color: #8c8c8c;
            font-weight: 400;
          }
        }
      }
    }
  }

  .intention-card {
    padding: 25 * @px 15 * @px;
    background-color: #ffffff;
    margin: 10 * @px 15 * @px 0;

    .line {
      display: flex;
      align-items: center;
      margin-bottom: 15 * @px;

      .text-key {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;
        font-weight: 400;
        white-space: nowrap;
      }

      .value {
        font-family: PingFangSC-Medium;
        font-size: 15 * @px;
        color: #333333;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .tips {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      letter-spacing: 0;
      line-height: 18 * @px;
      font-weight: 400;
      display: flex;
      align-items: center;
      height: 27 * @px;
      //border-radius: 4px;
      padding: 0 10 * @px;
      color: #61b43e;
      background-color: #ebfbdc;

      .icon {
        width: 12 * @px;
        height: 12 * @px;
        margin-right: 5px;
      }
    }
    //.tips.red {
    //  color: #ed5c5c;
    //  background: #ffeded;
    //}
    //
    //.tips.green {
    //  color: #00ab84;
    //  background: #e2f8f1;
    //}
  }

  .appointment-card {
    //z-index: 1;
    //border: 1px solid #ffffff;
    //border-radius: 8px 8px 10px 10px;
    //width: 335px;
    //padding: 25px 14px 20px 14px;
    padding: 25 * @px 10 * @px;
    margin-bottom: 10 * @px;
    position: relative;
    margin-left: 15 * @px;
    margin-right: 15 * @px;
    background: #ffffff;
    background-color: #fff;
    //margin-bottom: 7px;

    .top-bg-appointment {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      display: flex;
      flex: 1;
      height: 120px;
      //background-image: linear-gradient(
      //  180deg,
      //  #e5f6ee 0%,
      //  rgba(243, 243, 252, 0) 100%
      //);
      background-blend-mode: initial;
      z-index: 0;
      border-radius: 8px;
    }

    .product-title {
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #222222;
      letter-spacing: 0;
      font-weight: 500;
      position: relative;
      padding-left: 16 * @px;
      display: flex;
      justify-content: space-between;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 11 * @px;
        transform: translateY(-50%);
        width: 6 * @px;
        height: 13 * @px;
        background-color: #333333;
      }
    }

    .appointment-box {
      padding-top: 10 * @px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;

      .remaining-times {
        letter-spacing: 0;
        font-weight: 400;
        display: flex;
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;

        .end {
          //color: #999999;
        }
      }
    }

    .notice {
      display: flex;
      justify-content: space-between;
      //background-image: linear-gradient(270deg, #fafbfc 0%, #f3f4f8 100%);
      //border-radius: 4px;
      //padding: 6px 10px;
      margin-top: 10 * @px;
      position: relative;

      .text {
        display: flex;
        align-items: center;
        letter-spacing: 0;
        font-weight: 400;
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;

        image {
          width: 7 * @px;
          height: 9 * @px;
          min-width: 7 * @px;
          margin-left: 7 * @px;
        }

        .notice-img {
          width: 14px;
          height: 14px;
          margin-right: 5px;
        }
      }

      .text-btn {
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
        display: flex;
        align-items: center;

        .arrow-right {
          width: 6px;
          height: 10px;
          margin-left: 3px;
        }
      }
      .btn-appointment {
        border: 1 * @px solid #333333;
        //border-radius: 19px;
        width: 69 * @px;
        height: 28 * @px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: PingFangSC-Medium;
        font-size: 12 * @px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        position: relative;
        .button-label {
          position: absolute;
          font-family: Outfit-Regular;
          font-size: 20rpx;
          color: #030303;
          font-weight: 400;
          background: #89dc65;
          padding: 0 4rpx;
          top: -42rpx;
          &::after {
            content: '';
            position: absolute;
            display: block;
            left: 50%;
            top: calc(100% - 2rpx);
            transform: translateX(-50%);
            width: 0rpx;
            height: 6rpx;
            border: 10rpx solid transparent;
            border-top-color: #89dc65;
          }
        }
      }
    }

    .appointment-status-card {
      //border-radius: 8px;
      margin-top: 20 * @px;

      // &.confirmed {
      //   background: #e8f9f4;
      // }

      // 未确认
      // &.unconfirmed {
      //   background: #f8f8f8;
      //   //border-color: #ffdcdc;
      // }

      // 已到院
      // &.reach {
      //   background: #e8f9f4;
      // }

      // .unconfirmed-head {
      //   padding: 15 * @px;
      //   position: relative;

      //   .unconfirmed-status {
      //     display: flex;
      //     align-items: center;
      //     margin-bottom: 8 * @px;
      //     font-family: PingFangSC-Medium;
      //     font-size: 16 * @px;
      //     color: #61b43e;
      //     letter-spacing: 0;
      //     font-weight: 500;
      //     line-height: 18 * @px;

      //     .unconfirmed-icon {
      //       width: 20px;
      //       height: 20px;
      //       margin-right: 4px;
      //     }

      //     .main-text {
      //       //font-family: PingFangSC-Medium;
      //       //font-size: 18px;
      //       //color: #ed5c5c;
      //       //letter-spacing: 0;
      //       //font-weight: 500;
      //     }

      //     .line {
      //       width: 1 * @px;
      //       height: 13 * @px;
      //       background: #333333;
      //       margin: 0 10 * @px;
      //     }

      //     .sub-text {
      //       //font-family: PingFangSC-Medium;
      //       //font-size: 13px;
      //       //color: #ed5c5c;
      //       //letter-spacing: 0;
      //       //line-height: 18px;
      //       //font-weight: 500;
      //     }
      //   }

      //   .unconfirmed-desc {
      //     //font-weight: 400;
      //     display: flex;
      //     align-items: center;
      //     font-family: PingFangSC-Regular;
      //     font-size: 13 * @px;
      //     color: #646464;
      //     letter-spacing: 0;
      //     line-height: 24 * @px;
      //     font-weight: 400;

      //     .text-btn {
      //       text-decoration: underline;
      //       padding: 0 3 * @px;
      //       font-family: PingFangSC-Medium;
      //       font-size: 13 * @px;
      //       color: #030303;
      //       letter-spacing: 0;
      //       line-height: 24 * @px;
      //       font-weight: 500;
      //     }
      //   }
      // }

      // .reach-head {
      //   background: #e8f9f4;
      //   padding: 20px 8px 15px 8px;
      //   position: relative;

      //   .confirmed-status {
      //     display: flex;
      //     align-items: center;
      //     //margin-bottom: 7px;

      //     .confirmed-icon {
      //       width: 20px;
      //       height: 20px;
      //       margin-right: 4px;
      //     }

      //     .main-text {
      //       font-family: PingFangSC-Medium;
      //       font-size: 18px;
      //       color: #646464;
      //       letter-spacing: 0;
      //       font-weight: 500;
      //     }
      //   }
      // }

      // .used-already {
      //   background: #f8f8f8;
      //   height: 52 * @px;
      //   display: flex;
      //   justify-content: flex-start;
      //   align-items: center;
      //   font-family: PingFangSC-Medium;
      //   font-size: 16 * @px;
      //   color: #8c8c8c;
      //   letter-spacing: 0;
      //   font-weight: 500;
      //   box-sizing: border-box;
      //   padding: 0 15 * @px;
      //   border-bottom: 1 * @px solid #f2f2f2;
      // }

      // // 已使用
      // &.done {
      //   background: #fefefe;
      //   border-color: #eef0f7;
      //   position: relative;

      //   &:after {
      //     content: none;
      //     position: absolute;
      //     right: 7px;
      //     top: 0;
      //     width: 55px;
      //     height: 48px;
      //     //border-radius: 8px;
      //     //border: 1px solid #EEF0F7;
      //     //z-index: -1;
      //     background-image: url('https://static.soyoung.com/sy-design/group1717471069428.png');
      //     background-repeat: no-repeat;
      //     z-index: 10;
      //     background-size: 55px 48px;
      //   }
      // }

      // // 已确认
      // &.confirmed {
      //   border-radius: 8px;
      //   border-color: #dcf4ed;
      // }

      // .confirmed-head {
      //   padding: 15 * @px;
      //   position: relative;
      //   background-color: transparent;

      //   .unconfirmed-desc {
      //     font-family: PingFangSC-Regular;
      //     font-size: 13px;
      //     color: #555555;
      //     letter-spacing: 0;
      //     line-height: 24px;
      //     font-weight: 400;
      //   }
      // }

      .appointment-head {
        padding: 15 * @px;
        position: relative;
        border: 2rpx solid transparent;
        .appt-head-status {
          display: flex;
          align-items: center;
          .appt-head-icon {
            width: 20px;
            height: 20px;
            margin-right: 4px;
          }
          .appt-head-main-text {
            font-family: PingFangSC-Medium;
            font-size: 32rpx;
            letter-spacing: 0;
            font-weight: 500;
          }
          .appt-head-line {
            width: 1 * @px;
            height: 13 * @px;
            background: #333333;
            margin: 0 10 * @px;
          }
          .appt-head-sub-text {
            font-family: PingFangSC-Semibold;
            font-size: 24rpx;
            letter-spacing: 0;
            font-weight: 600;
          }
        }
        .appt-head-desc {
          margin-top: 7px;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          letter-spacing: 0;
          line-height: 34rpx;
          font-weight: 400;
          .appt-head-desc-text {
            display: inline;
          }
          .em {
            text-decoration: underline;
            padding: 0 3 * @px;
            font-family: PingFangSC-Medium;
            font-size: 24rpx;
            color: #030303;
            letter-spacing: 0;
            line-height: 24 * @px;
            font-weight: 500;
          }
        }
      }

      .appointment-body {
        background: #ffffff;
        //border-radius: 8px;
        border: 1 * @px solid #f2f2f2;
        border-top: none;
        padding: 20 * @px 15 * @px;
        position: relative;
        box-sizing: border-box;

        .text-sub-title {
          font-family: PingFangSC-Regular;
          font-size: 13 * @px;
          color: #8c8c8c;
          line-height: 20px;
          font-weight: 400;
          padding-bottom: 7px;
        }

        .hospital-name {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 20 * @px;

          //.text {
          //  font-family: PingFangSC-Medium;
          //  font-size: 15px;
          //  color: #222222;
          //  line-height: 20px;
          //  font-weight: 500;
          //  display: flex;
          //  align-items: center;
          //
          //  .arrow-right {
          //    width: 8px;
          //    height: 13px;
          //    margin-left: 5px;
          //  }
          //
          //  .arrow-flagship {
          //    width: 71px;
          //    height: 18px;
          //    margin-left: 5px;
          //  }
          //}

          .hospital-name-left {
            max-height: 42 * @px;
            overflow: hidden;
            .hospital-name-left-hospital-name {
              font-family: PingFangSC-Medium;
              font-size: 15 * @px;
              color: #333;
              line-height: 20 * @px;
              font-weight: 500;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              overflow: hidden;
              -webkit-box-orient: vertical;
            }
            .hospital-name-left-hospital-city {
              display: flex;
              align-items: center;
              text {
                font-family: PingFangSC-Regular;
                font-size: 12 * @px;
                color: #777777;
                line-height: 20 * @px;
                font-weight: 400;
              }
              image {
                width: 7 * @px;
                height: 9 * @px;
                margin-left: 2 * @px;
              }
            }
          }

          .hospital-name-right {
            display: flex;
            align-items: center;
            .hospital-name-right-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-left: 20 * @px;
              image {
                width: 22 * @px;
                height: 22 * @px;
                min-width: 22 * @px;
                margin-bottom: 4 * @px;
              }
              text {
                font-family: PingFangSC-Regular;
                font-size: 10 * @px;
                color: #777777;
                font-weight: 400;
                white-space: nowrap;
              }
            }
          }
        }

        .time {
          font-family: OutFit-Regular;
          font-size: 15 * @px;
          color: #333333;
          line-height: 20 * @px;
          font-weight: 500;
          margin-bottom: 10rpx;
        }

        .btn-box {
          display: flex;
          justify-content: center;
          //justify-content: space-between;
          margin-top: 20 * @px;

          &.one {
            .btn-change {
              margin-right: 60px;
              margin-left: 60px;
            }
          }

          .btn-change {
            font-family: PingFangSC-Medium;
            font-size: 12 * @px;
            color: #333;
            letter-spacing: 0;
            text-align: center;
            font-weight: 500;
            //width: 143px;
            height: 37 * @px;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1 * @px solid #333333;
          }

          .btn-contacts {
            display: flex;
            justify-content: center;
            align-items: center;
            //background: #e2f8f1;
            //border-radius: 19px;
            //width: 145px;
            flex: 1;
            max-width: 215 * @px;
            border: 1 * @px solid #61b43e;
            height: 37 * @px;
            font-family: PingFangSC-Medium;
            font-size: 12 * @px;
            color: #61b43e;
            letter-spacing: 0;
            text-align: center;
            font-weight: 500;
            margin-left: 15 * @px;

            .sub-text {
              font-weight: 400;
              font-family: PingFangSC-Regular;
            }
          }
        }
      }
    }

    .function {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #333;
      letter-spacing: 0;
      font-weight: 400;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 20 * @px;

      .arrow-down {
        height: 7 * @px;
        width: 9 * @px;
        margin-left: 5px;
      }

      .arrow-up {
        height: 7 * @px;
        width: 9 * @px;
        margin-left: 5px;
      }
    }
  }

  .order-info {
    padding: 25 * @px 15 * @px;
    background: #ffffff;
    margin: 10 * @px 15 * @px 0;

    .line {
      display: flex;
      align-items: center;
      margin-bottom: 15 * @px;

      .text-key {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;
        font-weight: 400;
        padding-right: 10 * @px;
      }

      .value {
        font-family: Outfit-Regular;
        font-size: 13 * @px;
        color: #333333;
        font-weight: 400;
        display: flex;
        align-items: center;

        .icon {
          width: 14px;
          height: 14px;
          margin-left: 15px;
        }
      }

      .phone-box {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: space-between;

        .value {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #666666;
          letter-spacing: 0;
          font-weight: 400;
          line-height: 24px;
          display: flex;
          align-items: center;
        }

        img {
          width: 24 * @px;
          height: 24 * @px;
          //margin-right: 10px;
        }
      }
    }

    .line:last-child {
      margin-bottom: 0;
    }
  }

  .pay-info {
    padding: 25 * @px 15 * @px 10 * @px;
    background: #ffffff;
    margin: 10 * @px 15 * @px 0;

    .line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15 * @px;

      .loan-info {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #999999;
        line-height: 19px;
        font-weight: 400;
      }

      .text-key {
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #555555;
        font-weight: 400;
      }

      .btn-box {
        display: flex;

        .loan-btn {
          letter-spacing: 0;
          text-align: center;
          border: 1px solid #e7e7e7;
          font-family: PingFangSC-Medium;
          font-size: 12 * @px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 500;
          //border-radius: 19px;
          width: 69px;
          height: 28px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 15px;

          &:last-child {
            margin-right: 0;
          }

          &.application {
            background: #00a077;
            //border: 1px solid #00a077;
            color: #ffffff;
            border: none;

            &.no-apl {
              background-color: #bababa;
              //border: 1px solid #f8f8f9;
              color: #fff;
            }
          }
        }
      }

      .value {
        display: flex;
        align-items: center;
        font-family: OutFit-Regular;
        font-size: 13 * @px;
        color: #222222;
        text-align: right;
        font-weight: 500;

        &.green {
          color: #61b43e;
          font-weight: 400;
        }
      }

      &.line2 .value {
        font-family: OutFit-Regular;
        font-size: 18 * @px;
        color: #222222;
        text-align: right;
        font-weight: 500;
      }

      .total-title {
        font-family: PingFangSC-Medium;
        font-size: 14 * @px;
        color: #222222;
        font-weight: 500;
      }
    }

    .division-line {
      height: 1 * @px;
      background: #f2f2f2;
      margin-bottom: 15 * @px;
    }
  }

  .by-stages {
    background: #ffffff;
    padding: 25 * @px 15 * @px;
    margin: 10 * @px 15 * @px;
    //border-radius: 8px;

    .line {
      display: flex;
      justify-content: space-between;

      .text-key {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #333333;
        font-weight: 500;
      }

      .text-btn {
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        color: #333333;
        font-weight: 400;
        display: flex;
        align-items: center;

        .arrow-right {
          width: 6.5 * @px;
          height: 8 * @px;
          margin-left: 13 * @px;
        }
      }
    }
  }
}

.refund-btn {
  display: flex;
  justify-content: center;
  padding: 30px 0 40px 0;

  .text {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #222222;
    font-weight: 400;
    border-bottom: 1px solid #222222;
    width: 120rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.no-refund-tips {
  padding: 27px 0;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #aaabb3;
  font-weight: 400;
  display: flex;
  flex-direction: column;
  align-items: center;

  .text {
    padding-bottom: 3px;
  }
}

.bottom-fixed {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  background: #ffffff;
  z-index: 999;
  box-sizing: border-box;
  padding: 10 * @px 15 * @px;
  padding-bottom: calc(10 * @px + constant(safe-area-inset-bottom));
  padding-bottom: calc(10 * @px + env(safe-area-inset-bottom));

  .text-kefu {
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    height: 42 * @px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border: 1 * @px solid #333333;
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #333333;
  }
}

.bottom-fixed-has-btn {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 999;
  display: flex;
  box-sizing: border-box;
  padding: 10 * @px 15 * @px 10 * @px;
  padding-bottom: calc(10 * @px + constant(safe-area-inset-bottom));
  padding-bottom: calc(10 * @px + env(safe-area-inset-bottom));

  .kefu {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 15 * @px;
    margin-right: 30 * @px;

    .kefu-icon {
      width: 28 * @px;
      height: 28 * @px;
      padding-bottom: 1 * @px;
    }

    .text {
      font-family: PingFangSC-Regular;
      font-size: 10 * @px;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }
  }

  .text-btn {
    background-color: #333333;
    flex: 1;
    height: 42 * @px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFangSC-Medium;
    font-size: 13 * @px;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.popup-commissioner {
  width: 100%;
  min-height: 622 * @px;
  background-image: url('https://static.soyoung.com/sy-design/bg1717411370761.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding-top: 15 * @px;
  padding-bottom: 34 * @px;
  position: relative;

  .popup-commissioner-close {
    position: absolute;
    right: 10 * @px;
    top: 16 * @px;
    z-index: 10;
    width: 20 * @px;
    height: 20 * @px;
  }

  .popup-commissioner-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20 * @px;

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 16 * @px;
      color: #333333;
      font-weight: 600;
      padding-bottom: 10 * @px;
    }

    .subtitle {
      font-family: PingFangSC-Semibold;
      font-size: 23 * @px;
      color: #222222;
      font-weight: 600;
      width: calc(100% - 50 * @px);
      display: block;
      text-align: center;
    }
  }

  .popup-commissioner-body {
    width: 337 * @px;
    height: 489 * @px;
    margin: 0 auto;
    background-image: url('https://static.soyoung.com/sy-design/1h1aj4adjaclb1717411370484.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;

    .code {
      position: absolute;
      width: 184 * @px;
      height: 184 * @px;
      left: 50%;
      transform: translateX(-50%);
      top: 167 * @px;
    }

    .customer-service {
      font-family: PingFangSC-Regular;
      font-size: 13 * @px;
      color: #354052;
      text-align: center;
      font-weight: 400;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 16 * @px;
      text-decoration: underline;
    }
  }
}
</style>
