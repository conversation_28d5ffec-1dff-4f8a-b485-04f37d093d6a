// accuracy: 65
// errMsg: "getLocation:ok"
// horizontalAccuracy: 65
// latitude: 39.9219
// longitude: 116.44355
// speed: -1
// verticalAccuracy: 65

async function getLocation() {
  return new Promise((resolve, reject) => {
    wx.getLocation({
      type: 'gcj02', // 'wgs84',
      success: (res) => {
        if (res.errMsg === 'getLocation:ok' && res.longitude && res.latitude) {
          resolve({
            lat: res.latitude,
            lng: res.longitude
          });
        }
      },
      fail: (errMsg) => {
        reject(errMsg);
      }
    });
  });
}
export async function accredit() {
  return new Promise((resolve) => {
    uni.getSystemInfo({
      success(resSystemInfo) {
        let locationEnabled = resSystemInfo.locationEnabled;
        let locationAuthorized = resSystemInfo.locationAuthorized;
        if (locationEnabled === false || locationAuthorized === false) {
          uni.showToast({
            title: '请打开系统定位服务功能',
            icon: 'none'
          });
        } else {
          uni.getSetting({
            success(resGetSetting) {
              let scopeUserLocation =
                resGetSetting.authSetting['scope.userLocation'];
              if (scopeUserLocation) {
                // 获取地理位置
                getLocation().then((res) => {
                  resolve(res);
                });
              } else {
                uni.authorize({
                  scope: 'scope.userLocation',
                  success(resAuthorize) {
                    // 获取地理位置
                    console.log(resAuthorize);
                    getLocation().then((res) => {
                      resolve(res);
                    });
                  },
                  fail() {
                    uni.showModal({
                      content: '需要授权位置信息',
                      confirmText: '确认授权',
                      success(res_showModal) {
                        if (res_showModal.confirm) {
                          uni.openSetting({
                            success(resOpenSetting) {
                              if (
                                resOpenSetting.authSetting['scope.userLocation']
                              ) {
                                uni.showToast({
                                  title: '授权成功',
                                  icon: 'none'
                                });
                                getLocation().then((res) => {
                                  resolve(res);
                                });
                              } else {
                                uni.showToast({
                                  title: '授权失败',
                                  icon: 'none'
                                });
                              }
                            }
                          });
                        } else {
                          resolve({
                            lat: 0,
                            lng: 0
                          });
                        }
                      }
                    });
                  }
                });
              }
            }
          });
        }
      }
    });
  });
}
