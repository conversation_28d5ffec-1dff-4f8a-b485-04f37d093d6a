<template>
  <div
    class="arrival-at-hospital-page"
    :style="{
      marginTop: (maxHeight + 6) * 2 + 'rpx',
      height: `calc(100vh - ${(maxHeight + 6) * 2}rpx)`
    }"
  >
    <!-- 导航栏 start -->
    <Navbar :background="`rgba(255, 255, 255, 1)`" :hasBack="false">
      <div class="arrival-at-hospital-page-header">
        <block>
          <div class="back" @click="handleBack"></div>
          <div class="title">待到店</div>
        </block>
      </div>
    </Navbar>
    <!-- 导航栏 end -->

    <!-- 轮播图区域 start -->
    <div
      v-if="
        carouselBannerList && carouselBannerList.length > 0 && list.length !== 0
      "
      class="arrival-at-hospital-page-banner-container"
    >
      <NewBanner
        :list="carouselBannerList"
        height="80px"
        width="345px"
        class="carousel-banner"
        @click="handleBannerClick"
        @expose="handleBannerExpose"
      ></NewBanner>
    </div>
    <!-- 轮播图区域 end -->

    <!-- 待到店list start -->
    <div v-if="!loading" class="arrival-at-hospital-page-list">
      <div v-if="list.length === 0" class="arrival-at-hospital-page-list-empty">
        <img
          src="https://static.soyoung.com/sy-design/3cytu2gk96aru1727081679552.png"
          alt=""
        />
        <span>暂无待到店预约</span>
      </div>
      <template v-else>
        <div
          v-for="(item, index) in list"
          :key="index"
          class="already-appointment-card"
        >
          <div class="already-appointment-card-header">
            <div class="already-appointment-card-header__point"></div>
            <div class="already-appointment-card-header__time">
              {{ item.date }}
            </div>
            <template v-if="item.date_name !== ''">
              <div class="already-appointment-card-header__line"></div>
            </template>
            <div class="already-appointment-card-header__text">
              {{ item.date_name }}
            </div>
            <div class="already-appointment-card-header__total">
              共{{ item.total }}条预约
            </div>
          </div>
          <div
            v-for="child in item.reserve_list"
            :key="child.id"
            class="already-appointment-card-content"
          >
            <div class="already-appointment-card-content__line"></div>
            <div class="already-appointment-card-content-right">
              <div
                v-if="child.start_time !== ''"
                class="already-appointment-card-content-right__time"
              >
                {{ child.start_time }}
              </div>
              <div class="already-appointment-card-content-right-body">
                <div
                  class="right-body-tips"
                  :style="{
                    'background-color': child.confirm_info.bg_color,
                    'border-color': child.confirm_info.border_color,
                    color: child.confirm_info.text_color
                  }"
                >
                  <div class="right-body-tips-title">
                    <span class="right-body-tips-title__sp1">{{
                      child.confirm_info.status_name
                    }}</span>
                    <template v-if="child.confirm_info.short_name">
                      <span
                        class="right-body-tips-title__sp2"
                        :style="{
                          backgroundColor: child.confirm_info.text_color
                        }"
                      ></span>
                      <span class="right-body-tips-title__sp3">{{
                        child.confirm_info.short_name
                      }}</span>
                    </template>
                  </div>
                  <div class="right-body-tips-description">
                    {{ child.confirm_info.notice }}
                  </div>
                </div>
                <div class="right-body-hospital-card">
                  <!-- <div
                    v-if="child.confirm_info.status !== 1"
                    class="right-body-hospital-card__identify"
                  >
                    {{ child.confirm_info.status_name }}
                  </div> -->
                  <div class="right-body-hospital-card-header">
                    {{ child.product_name }}
                  </div>
                  <div
                    class="right-body-hospital-card-address"
                    :style="{
                      paddingBottom: child.btn_list.length !== 0 ? '20rpx' : '0'
                    }"
                  >
                    <span>{{ child.hospital_name }}</span>
                    <img
                      src="https://static.soyoung.com/sy-design/o2eve6mngspv1726218394517.png"
                      alt=""
                      @click="handleMap(child)"
                    />
                  </div>
                  <CashBackNotice
                    :bg-color="'rgba(186,186,186,0.20)'"
                    v-if="child.cashback"
                    style="margin-bottom: 20rpx"
                    :notice="child.cashback"
                    :show-notice="false"
                  />
                  <div
                    v-if="child.btn_list.length !== 0"
                    :class="{
                      'right-body-hospital-card-btn': true,
                      'right-body-hospital-card-btn__service':
                        child.confirm_info.status === 3
                    }"
                  >
                    <template>
                      <template v-for="btn in child.btn_list">
                        <div
                          v-if="btn.type !== 2"
                          :key="btn.type"
                          class="right-body-hospital-card-btn-item"
                          @click="handleBtnChange(btn, child)"
                        >
                          {{ btn.title }}
                        </div>
                        <div
                          :key="btn.type"
                          v-if="
                            btn.type === 2 &&
                            btn.store_guide_imgs &&
                            btn.store_guide_imgs.length > 0
                          "
                          class="right-body-hospital-card-btn-item"
                          @click="handleBtnChange(btn, child)"
                        >
                          {{ btn.title }}
                        </div>
                      </template>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 待到店list end -->

    <!-- loading start -->
    <div
      v-if="loading"
      class="page-loading"
      :style="{ height: `calc(100vh - ${maxHeight * 2}rpx)` }"
    >
      <img
        src="https://static.soyoung.com/sy-pre/9lklkdzpl9nx-1727251800646.gif"
        alt=""
      />
    </div>
    <!-- loading end -->

    <!-- 联系客服专员弹窗 start -->
    <JoincDialog
      v-model="visibleDialogStatus"
      :hospital_id="hospitalId"
    ></JoincDialog>
    <!-- 联系客服专员弹窗 end -->

    <!-- 修改预约 start -->
    <template v-if="visible">
      <ApptDialog
        :order_id="apptDialog.order_id"
        :top_order_id="apptDialog.top_order_id"
        :reserve_id="apptDialog.reserve_id"
        :city_id="apptDialog.city_id"
        :sku_id="apptDialog.sku_id"
        :order_app_id="apptDialog.order_app_id"
        :order_hospital_id="apptDialog.order_hospital_id"
        :ingredients_id="apptDialog.ingredients_id"
        :visible.sync="visible"
        @visible="onVisibleChange"
        @success="onApptSuccess"
        @onSuccessSubscribe="onSubscribeSuccess"
      />
    </template>
    <!-- 修改预约 end -->

    <!-- weak -->
    <WeakNetwork
      @refresh="refresh"
      :path="['/syGroupBuy/chain/visit/getWaitArriveList']"
      :visible.sync="isNetwork"
    />
  </div>
</template>

<script>
import Navbar from '@/components/NavBar.vue';
import NewBanner from '@/components/NewBanner.vue';
import JoincDialog from '@/components/joinc.vue';
import ApptDialog from '@/components/appointNew/index.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import CashBackNotice from '@/components/appointNew/CashBackNotice.vue';
import { getWaitArriveList } from '@/api/packageAppointment';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_tuan_wait_to_store_page',
      ext: {}
    };
  },
  components: {
    ApptDialog,
    Navbar,
    JoincDialog,
    WeakNetwork,
    CashBackNotice,
    NewBanner
  },
  data() {
    return {
      maxHeight: 0,
      visible: false,
      visibleDialogStatus: false, // 客服专员弹窗
      list: [],
      hospitalId: '', // 医院id
      apptDialog: {},
      loading: true,
      isNetwork: false,
      carouselBannerList: []
    };
  },
  mounted() {
    this.maxHeight = uni.getMenuButtonBoundingClientRect().bottom;
    this._initData();
  },
  methods: {
    async _initData() {
      const data = await getWaitArriveList({});
      if (data.carousel && data.carousel.length > 0) {
        this.carouselBannerList = data.carousel.map((item, index) => ({
          ...item,
          img_url: item.img, // 转换为NewBanner组件需要的字段
          index // 添加索引用于埋点
        }));
      }
      if (data && data.list !== null) {
        this.list =
          data.list.map((item) => {
            item.reserve_list.map((child, cIndex) => {
              // 如何后一个数据的start_time与前一个start_time值一样，那么需要将后一个数据的start_time置为空
              if (
                cIndex !== 0 &&
                child.start_time === item.reserve_list[0].start_time
              ) {
                child.start_time = '';
              }
              return child;
            });
            return item;
          }) || [];
        this.isNetwork = false;
      } else {
        this.list = [];
      }
      this.loading = false;
    },
    async refresh() {
      this.loading = true;
      await this._initData();
      this.loading = false;
    },
    handleBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    handleMap(item) {
      this.$reportData({
        info: 'sy_chain_store_tuan_wait_to_store:map_click',
        ext: {
          id: item.id
        }
      });
      uni.navigateTo({
        url: `/packageHospital/hospital-map?hospital_id=${item.hospital_id}`
      });
    },
    handleBtnChange(btn, item) {
      console.log(btn, item, '待到店');
      if (btn.type === 1) {
        this.$reportData({
          info: 'sy_chain_store_tuan_wait_to_store:contact_assistant_click',
          ext: {
            id: item.id
          }
        });
        this.visibleDialogStatus = true;
        this.hospitalId = item.hospital_id || '';
      } else if (btn.type === 2) {
        this.$reportData({
          info: 'sy_chain_store_tuan_wait_to_store:store_guide_click',
          ext: {
            id: item.id
          }
        });
        this.$bridge({
          url:
            '/pages/h5?url=' +
            encodeURIComponent(
              `https://m.soyoung.com/hospital/hospitalGuide?hospital_id=${item.hospital_id}`
            ) // 到店指引URL
        });
      } else if (btn.type === 4) {
        this.$reportData({
          info: 'sy_chain_store_tuan_wait_to_store:service_guide_click',
          ext: {
            id: item.visit_id
          }
        });
        this.$bridge({
          url:
            '/pages/h5?url=' +
            encodeURIComponent(`https://m.soyoung.com/tmwap25795#/`) // 服务指引URL
        });
      } else {
        this.$reportData({
          info: 'sy_chain_store_tuan_wait_to_store:modify_reservation_click',
          ext: {
            id: item.id
          }
        });
        this.apptDialog.city_id = item.city_id || 0;
        this.apptDialog.order_app_id = item.order_app_id;
        this.apptDialog.order_id = item.order_id;
        this.apptDialog.top_order_id = item.top_order_id;
        this.apptDialog.reserve_id = item.id || 0;
        this.apptDialog.sku_id = item.sku_id;
        this.apptDialog.order_hospital_id = item.order_hospital_id;
        this.apptDialog.ingredients_id = item.ingredients_id;
        this.visible = true;
      }
    },
    handleBannerClick(item) {
      console.log('handleBannerClick', item);
      this.$reportData({
        info: 'sy_chain_store_tuan_wait_to_store:banner_click',
        ext: {
          // 位置序号:serial_num-数据在list或者feed中的位置序号；从1开始递增，与sever保持一致 链接:url-图片、视频或h5的链接
          serial_num: item.index + 1,
          url: item.jump_url
        }
      });
      // 根据jump_type处理不同的跳转方式
      if (+item.jump_type === 1) {
        // H5跳转
        uni.navigateTo({
          url: `/pages/h5?url=${encodeURIComponent(item.jump_url)}`
        });
      } else if (+item.jump_type === 2) {
        // 小程序跳转
        if (item.need_auth && !this.isLogin) {
          this.judgeLogin();
          return;
        }
        if (item.mini_app_id === 'wx4c984b5d0eb25e91') {
          const path = [
            '/pages/index',
            '/pages/order-list',
            '/pages/coupon-center',
            '/pages/my'
          ];
          if (path.includes(item.jump_url)) {
            uni.switchTab({
              url: item.jump_url
            });
          } else {
            uni.navigateTo({
              url: item.jump_url
            });
          }
        } else {
          uni.navigateToMiniProgram({
            appId: item.mini_app_id,
            path: item.jump_url,
            success(res) {
              console.log('跳转小程序成功', res);
            },
            fail(err) {
              console.log('跳转小程序失败', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        }
      }
    },
    // 轮播图曝光埋点
    handleBannerExpose(item) {
      this.$reportData({
        info: 'sy_chain_store_tuan_wait_to_store:banner_exposure',
        ext: {
          // 位置序号:serial_num-数据在list或者feed中的位置序号；从1开始递增，与sever保持一致 链接:url-图片、视频或h5的链接
          serial_num: item.index + 1,
          url: item.jump_url
        }
      });
    },
    onApptSuccess() {
      this.visible = false;
      this._initData();
      console.log('onApptSuccess');
    }
  }
};
</script>

<style lang="less" scoped>
.arrival-at-hospital-page {
  width: 100%;
  background: #f2f2f2;
  &-header {
    position: relative;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;

    .back {
      position: absolute;
      left: 6px;
      top: 50%;
      height: 88rpx;
      width: 88rpx;
      z-index: 1;
      background: url(https://static.soyoung.com/sy-design/3cj8rc3ipek931725437086652.png)
        no-repeat center center transparent;
      background-size: 22rpx 36rpx;
      transform: translateY(-50%);
    }

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #000000;
      font-weight: 600;
    }
  }
  &-banner-container {
    margin-top: -8rpx;
    padding: 40rpx 50rpx 40rpx 50rpx;
    background-color: #ffffff;
    .carousel-banner {
      margin: 0 auto;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      width: 100%;
      overflow: hidden;
    }
  }
  &-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 52rpx 50rpx;
    box-sizing: border-box;
    background: #f2f2f2;
    .already-appointment-card {
      width: 100%;
      display: flex;
      flex-direction: column;
      &-header {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-bottom: 10rpx;
        &__point {
          width: 16rpx;
          height: 16rpx;
          background: #030303;
          border-radius: 50%;
          margin-right: 20rpx;
        }
        &__time {
          font-family: OutFit-Regular;
          font-size: 14px;
          color: #646464;
          font-weight: 500;
        }
        &__line {
          width: 2rpx;
          height: 22rpx;
          background: #646464;
          margin: 0 20rpx;
        }
        &__text {
          flex: 1;
          font-family: PingFangSC-Medium;
          font-size: 14px;
          color: #646464;
          font-weight: 500;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
        }
        &__total {
          min-width: 128rpx;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #646464;
          font-weight: 400;
        }
      }
      &-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        &__line {
          width: 2rpx;
          margin: 0 27rpx 0 7rpx;
          background-image: linear-gradient(
            to bottom,
            #bababa 50%,
            transparent 50%
          );
          background-size: 100% 8rpx;
          background-repeat: repeat-y;
        }
        &-right {
          flex: 1;
          display: flex;
          flex-direction: column;
          &__time {
            font-family: OutFit-Regular;
            font-size: 32rpx;
            color: #030303;
            font-weight: 500;
            padding-bottom: 30rpx;
          }
          &-body {
            width: 100%;
            display: flex;
            flex-direction: column;
            .right-body-tips {
              width: 100%;
              padding: 30rpx;
              box-sizing: border-box;
              background: rgba(137, 220, 101, 0.15);
              position: relative;
              display: flex;
              flex-direction: column;
              &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                border: 1px solid rgba(137, 220, 101, 0.35);
                border-bottom: 0;
                width: 200%;
                height: 200%;
                transform: scale(0.5);
                transform-origin: 0 0;
                box-sizing: border-box;
              }
              &-title {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-start;
                padding-bottom: 10rpx;
                &__sp1 {
                  font-family: PingFangSC-Semibold;
                  font-size: 32rpx;
                  letter-spacing: 0;
                  font-weight: 600;
                }
                &__sp2 {
                  width: 2rpx;
                  height: 22rpx;
                  display: flex;
                  margin: 0 20rpx;
                }
                &__sp3 {
                  font-family: PingFangSC-Semibold;
                  font-size: 24rpx;
                  font-weight: 600;
                }
              }
              &-description {
                font-family: PingFangSC-Regular;
                font-size: 24rpx;
                color: #555555;
                font-weight: 400;
              }
            }
            .right-body-hospital-card {
              width: 100%;
              position: relative;
              display: flex;
              flex-direction: column;
              position: relative;
              padding: 50rpx 30rpx;
              box-sizing: border-box;
              background: #ffffff;
              margin-bottom: 52rpx;
              &__identify {
                position: absolute;
                right: 0;
                top: 0;
                padding: 8rpx 20rpx;
                font-family: PingFangSC-Medium;
                font-size: 24rpx;
                color: #61b43e;
                font-weight: 500;
                background: #ebfbdc;
              }
              &-header {
                font-family: PingFangSC-Semibold;
                font-size: 32rpx;
                color: #030303;
                font-weight: 600;
                padding-bottom: 10rpx;
              }
              &-address {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                padding-bottom: 8rpx;
                span {
                  font-family: PingFangSC-Regular;
                  font-size: 24rpx;
                  color: #646464;
                  font-weight: 400;
                  max-width: 432rpx;
                  //overflow: hidden;
                  //text-overflow: ellipsis;
                  //white-space: nowrap;
                }
                img {
                  width: 50rpx;
                  height: 50rpx;
                }
              }
              &-btn {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: flex-end;
                margin-top: 30rpx;
                &-item {
                  flex: 1;
                  max-width: 176rpx;
                  margin-right: 16rpx;
                  border: 1px solid #333333;
                  font-family: PingFangSC-Regular;
                  font-size: 24rpx;
                  color: #030303;
                  font-weight: 400;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                  padding: 12rpx 0;
                  &:last-child {
                    margin-right: 0;
                  }
                }
              }
              &-btn__service {
                justify-content: flex-end;
                .item176 {
                  width: 176rpx;
                  border: 1px solid #333333;
                  font-family: PingFangSC-Regular;
                  font-size: 24rpx;
                  color: #030303;
                  font-weight: 400;
                  padding: 12rpx 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
      &-more {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40rpx 0 0;
        span {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #333333;
          font-weight: 400;
        }
        img {
          margin-left: 10rpx;
          width: 10rpx;
          height: 20rpx;
          transform: rotate(180deg);
        }
      }
      &:last-child
        .already-appointment-card-content
        .already-appointment-card-content-right
        .already-appointment-card-content-right-body
        .right-body-hospital-card {
        margin-bottom: 0;
      }
    }
    &-empty {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 200rpx;
      img {
        width: 70rpx;
        height: 70rpx;
        margin-bottom: 40rpx;
      }
      span {
        font-family: PingFangSC-Medium;
        font-size: 28rpx;
        color: #030303;
        font-weight: 500;
      }
    }
  }
}
.page-loading {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100rpx;
    height: 100rpx;
  }
}
</style>
