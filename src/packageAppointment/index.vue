<template>
  <page-meta :page-style="computedStyle">
    <div
      class="appointment-page"
      :style="{
        marginTop: (maxHeight + 6) * 2 + 'rpx',
        height: `calc(100vh - ${(maxHeight + 6) * 2}rpx)`
      }"
    >
      <!-- 导航栏 start -->
      <Navbar :background="`rgba(255, 255, 255, 1)`" :hasBack="false">
        <div class="appointment-page-header">
          <block>
            <div class="back" @click="handleBack"></div>
            <div class="title">预约</div>
          </block>
        </div>
      </Navbar>
      <!-- 导航栏 end -->

      <div v-if="!loading" class="appointment-page-content">
        <template v-if="reserveIndexHeader.tab.length > 0">
          <!-- 待到店、服务记录Tab start -->
          <div class="appointment-page-content-tab">
            <div
              class="appointment-page-content-tab-item"
              v-for="(item, index) in reserveIndexHeader.tab"
              :key="index"
              @click="handleClickGo(item)"
            >
              <div
                v-if="item.cnt != 0"
                class="tab-item__identify"
                :style="{
                  borderRadius: index === 0 ? '50%' : '20rpx',
                  padding: index !== 0 ? '2.48rpx 8.24rpx' : '',
                  minWidth: index === 0 && item.cnt > 99 ? '46rpx' : '30rpx'
                }"
              >
                {{ index === 0 && item.cnt > 99 ? '99+' : item.cnt }}
              </div>
              <img
                class="tab-item__img1"
                :src="
                  index === 0
                    ? 'https://static.soyoung.com/sy-design/2hca4x4bkq4bk1726209647243.png'
                    : 'https://static.soyoung.com/sy-design/32q76aat6dbg71726218394523.png'
                "
                alt=""
              />
              <span>{{ item.name }}</span>
              <img
                class="tab-item__img2"
                src="https://static.soyoung.com/sy-design/3i7wqo1vm5woz1727319431028.png"
                alt=""
              />
            </div>
          </div>
          <!-- 待到店、服务记录Tab end -->
          <!-- 轮播图区域 start -->
          <div
            v-if="
              carouselBannerList &&
              carouselBannerList.length > 0 &&
              appointmentList.length !== 0
            "
            class="appointment-page-content-banner-container"
          >
            <NewBanner
              :list="carouselBannerList"
              height="80px"
              width="345px"
              class="carousel-banner"
              @click="handleBannerClick"
              @expose="handleBannerExpose"
            ></NewBanner>
          </div>
          <!-- 轮播图区域 end -->
          <div class="appointment-page-content__line"></div>

          <!-- 外露预约时间最近的1条已预约数据（未取消且未到院） start -->
          <div
            class="appointment-page-content-already-appointment"
            v-if="reserveIndexHeader.wait_visit.reserve_list.length > 0"
          >
            <div class="already-appointment__title">
              {{ reserveIndexHeader.wait_visit.title }}
            </div>
            <div class="already-appointment-card">
              <div class="already-appointment-card-header">
                <div class="already-appointment-card-header__point"></div>
                <div class="already-appointment-card-header__time">
                  {{ reserveIndexHeader.wait_visit.date }}
                </div>
                <div class="already-appointment-card-header__line"></div>
                <div class="already-appointment-card-header__text">
                  {{ reserveIndexHeader.wait_visit.date_name }}
                </div>
                <div class="already-appointment-card-header__total">
                  共{{ reserveIndexHeader.wait_visit.total }}条预约
                </div>
              </div>
              <div class="already-appointment-card-content">
                <div class="already-appointment-card-content__line"></div>
                <div class="already-appointment-card-content-right">
                  <div class="already-appointment-card-content-right__time">
                    {{
                      reserveIndexHeader.wait_visit.reserve_list[0].start_time
                    }}
                  </div>
                  <div
                    class="already-appointment-card-content-right-body"
                    v-for="item in reserveIndexHeader.wait_visit.reserve_list"
                    :key="item.id"
                  >
                    <div
                      class="right-body-tips"
                      v-if="item.confirm_info.status === 1"
                    >
                      <div class="right-body-tips-title">
                        <span class="right-body-tips-title__sp1">{{
                          item.confirm_info.status_name
                        }}</span>
                        <span class="right-body-tips-title__sp2"></span>
                        <span class="right-body-tips-title__sp3">{{
                          item.confirm_info.short_name
                        }}</span>
                      </div>
                      <div class="right-body-tips-description">
                        {{ item.confirm_info.notice }}
                      </div>
                    </div>
                    <div class="right-body-hospital-card">
                      <div
                        v-if="item.confirm_info.status === 2"
                        class="right-body-hospital-card__identify"
                      >
                        预约已确认
                      </div>
                      <div class="right-body-hospital-card-header">
                        {{ item.product_name }}
                      </div>
                      <div class="right-body-hospital-card-address">
                        <span>{{ item.hospital_name }}</span>
                        <img
                          src="https://static.soyoung.com/sy-design/o2eve6mngspv1726218394517.png"
                          alt=""
                          @click="handleMap(item)"
                        />
                      </div>
                      <CashBackNotice
                        :bg-color="'rgba(186,186,186,0.20)'"
                        v-if="item.cashback"
                        style="margin-bottom: 20rpx"
                        :notice="item.cashback"
                        :show-notice="false"
                      />
                      <div class="right-body-hospital-card-btn">
                        <div
                          class="right-body-hospital-card-btn-item"
                          v-for="child in item.btn_list"
                          :key="child.type"
                          @click="handleBtnChange(child, item)"
                        >
                          {{ child.title }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="reserveIndexHeader.wait_visit.day_3_cnt > 1"
                class="already-appointment-card-more"
                @click="handleClickGo({ name: '待到店' })"
              >
                <span>查看全部</span>
                <img
                  src="https://static.soyoung.com/sy-design/3i7wqa0rpbuog1727319431246.png"
                  alt=""
                />
              </div>
            </div>
          </div>
          <!-- 外露预约时间最近的1条已预约数据（未取消且未到院） end -->

          <div
            v-if="reserveIndexHeader.wait_visit.reserve_list.length > 0"
            class="appointment-page-content__line"
          ></div>
        </template>

        <!-- 新增预约 start -->
        <div class="appointment-page-content-increase">
          <div class="appointment-page-content-increase__title">新增预约</div>
          <div
            v-if="appointmentList.length === 0"
            class="appointment-page-content-empty"
          >
            <img
              src="https://static.soyoung.com/sy-design/274yk6xzfldae1727081679543.png"
              alt=""
            />
            <span>暂无预约</span>
          </div>
          <div v-else class="appointment-page-content-increase-list">
            <AppointmentCard
              v-for="(item, index) in appointmentList"
              :key="item.sku_id"
              :data="item"
              :serial_num="index + 1"
              @nowAppointment="nowAppointment"
            ></AppointmentCard>
          </div>
          <div
            v-if="!hasMore && appointmentList.length > 10"
            class="appointment-page-content-increase__no-more"
          >
            没有更多了
          </div>
        </div>
        <!-- 新增预约 end -->
      </div>

      <!-- loading start -->
      <div
        v-if="loading"
        class="page-loading"
        :style="{ height: `calc(100vh - ${maxHeight * 2}rpx)` }"
      >
        <img
          src="https://static.soyoung.com/sy-pre/9lklkdzpl9nx-1727251800646.gif"
          alt=""
        />
      </div>
      <!-- loading end -->

      <!-- 联系客服专员弹窗 start -->
      <JoincDialog
        v-model="visibleDialogStatus"
        :hospital_id="hospitalId"
      ></JoincDialog>
      <!-- 联系客服专员弹窗 end -->

      <!-- 修改预约 start -->
      <template v-if="visible">
        <ApptDialog
          :order_id="apptDialog.order_id"
          :top_order_id="apptDialog.top_order_id"
          :reserve_id="apptDialog.reserve_id"
          :city_id="apptDialog.city_id"
          :sku_id="apptDialog.sku_id"
          :order_app_id="apptDialog.order_app_id"
          :order_hospital_id="apptDialog.order_hospital_id"
          :ingredients_id="apptDialog.ingredients_id"
          :visible.sync="visible"
          @success="onApptSuccess"
          @update="updatePage"
        />
      </template>
      <!-- 修改预约 end -->

      <!-- weak -->
      <WeakNetwork
        @refresh="refresh"
        :path="['/syGroupBuy/chain/reservation/getReserveIndexHeader']"
        :visible.sync="isNetwork"
      />
    </div>
  </page-meta>
</template>

<script>
import Navbar from '@/components/NavBar.vue';
import NewBanner from '@/components/NewBanner.vue';
import AppointmentCard from './component/appointmentCard.vue';
import JoincDialog from '@/components/joinc.vue';
import ApptDialog from '@/components/appointNew/index.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import CashBackNotice from '@/components/appointNew/CashBackNotice.vue';
import { mapGetters } from 'vuex';
import {
  getReserveIndexHeader,
  getReserveIndexList
} from '@/api/packageAppointment';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_tuan_reservation_list_page',
      ext: {}
    };
  },
  components: {
    ApptDialog,
    Navbar,
    AppointmentCard,
    JoincDialog,
    WeakNetwork,
    CashBackNotice,
    NewBanner
  },
  data() {
    return {
      maxHeight: 0,
      page: 0,
      visible: false,
      visibleDialogStatus: false, // 客服专员弹窗
      reserveIndexHeader: {
        tab: [],
        wait_visit: {
          date: '2024-09-06',
          date_name: '今日',
          start_time: '14:30',
          title: '近3日到院（共4条)',
          total: 2,
          reserve_list: []
        }
      },
      appointmentList: [],
      hospitalId: '', // 医院id
      apptDialog: {},
      hasMore: false,
      loading: true,
      isNetwork: false,
      computedStyle: '',
      carouselBannerList: []
    };
  },
  computed: {
    ...mapGetters(['isLogin', 'hasWxAvatar'])
  },
  watch: {
    visible: {
      handler(val) {
        console.log(val, '更新');
        this.computedStyle = val ? 'overflow: hidden;' : 'overflow: scroll;';
      },
      deep: true
    }
  },
  onShow() {
    this.page = 0;
    this._initData('show');
  },
  mounted() {
    this.maxHeight = uni.getMenuButtonBoundingClientRect().bottom;
    this.judgeLogin();
  },
  methods: {
    _initData(identify) {
      this._getReserveIndexHeader();
      this.getReserveIndexList(identify);
      this.loading = false;
    },
    async judgeLogin() {
      if (!this.isLogin) {
        const isAuth = await this.$login();
        if (!isAuth) return;
      }
    },
    async _getReserveIndexHeader() {
      const res = await getReserveIndexHeader({});
      console.log(res, '连锁预约头部信息获取');
      if (res) {
        this.reserveIndexHeader = res;
        if (res.carousel && res.carousel.length > 0) {
          this.carouselBannerList = res.carousel.map((item, index) => ({
            ...item,
            img_url: item.img, // 转换为NewBanner组件需要的字段
            index // 添加索引用于埋点
          }));
        }
        this.hospitalId =
          res.wait_visit.reserve_list.length > 0
            ? res.wait_visit.reserve_list[0].hospital_id || ''
            : '';
        this.isNetwork = false;
      }
      if (res.wait_visit.reserve_list.length > 0) {
        this.$reportData({
          info: 'sy_chain_store_tuan_reservation_list:3_days_to_store_exposure',
          ext: {}
        });
      }
    },
    async getReserveIndexList(identify) {
      const res = await getReserveIndexList({ index: this.page, limit: 10 });
      if (res) {
        if (identify === 'show') {
          this.appointmentList = [];
        }
        console.log(res, '连锁预约列表数据');
        if (this.page === 0) {
          this.appointmentList = res.list || [];
        } else {
          this.appointmentList = this.appointmentList.concat(res.list);
        }
        if (res.has_more) {
          this.page++;
        }
        this.hasMore = res.has_more === 1;
      }
    },
    async refresh() {
      this.loading = true;
      await this._getReserveIndexHeader();
      this.loading = false;
    },
    handleClickGo(item) {
      console.log(item, 'Tab切换数据');
      if (item.name === '待到店') {
        this.$reportData({
          info: 'sy_chain_store_tuan_reservation_list:wait_to_store_click',
          ext: {
            nums: item.cnt
          }
        });
        uni.navigateTo({
          url: '/packageAppointment/arrivalAtHospital'
        });
      } else {
        this.$reportData({
          info: 'sy_chain_store_tuan_reservation_list:service_record_click',
          ext: {
            nums: item.cnt
          }
        });
        uni.navigateTo({
          url: '/packageAppointment/serviceRecord'
        });
      }
    },
    handleBack() {
      const pages = getCurrentPages();
      if (pages.length === 1) {
        uni.switchTab({
          url: '/pages/index'
        });
      } else {
        uni.navigateBack({
          delta: 1
        });
      }
    },
    handleBtnChange(child, item) {
      console.log(child, 'Btn', item);
      if (child.type === 1) {
        this.visibleDialogStatus = true;
        this.hospitalId = item.hospital_id || '';
      } else if (child.type === 2) {
        this.$bridge({
          url:
            '/pages/h5?url=' +
            encodeURIComponent(
              `https://m.soyoung.com/hospital/hospitalGuide?hospital_id=${item.hospital_id}`
            ) // 到店指引URL
        });
      } else {
        this.apptDialog.city_id = item.city_id || 0; // server说写死0
        this.apptDialog.order_app_id = item.order_app_id; // 没有
        this.apptDialog.order_id = item.order_id;
        this.apptDialog.top_order_id = item.top_order_id;
        this.apptDialog.reserve_id = item.id || 0; // server说写死0
        this.apptDialog.sku_id = item.sku_id;
        this.apptDialog.order_hospital_id = item.order_hospital_id; // 没有
        this.apptDialog.ingredients_id = item.ingredients_id;
        this.visible = true;
      }
    },
    nowAppointment(item) {
      this.apptDialog.city_id = item.city_id || 0; // server说写死0
      this.apptDialog.order_app_id = item.order_app_id; // 没有
      this.apptDialog.order_id = item.order_id;
      this.apptDialog.top_order_id = item.top_order_id;
      this.apptDialog.reserve_id = item.reserve_id || 0; // server说写死0
      this.apptDialog.sku_id = item.sku_id;
      this.apptDialog.order_hospital_id = item.order_hospital_id; // 没有
      this.apptDialog.ingredients_id = item.ingredients_id;
      this.visible = true;
    },
    onApptSuccess() {
      this.visible = false;
      this.page = 0;
      this._initData('show');
      console.log('onApptSuccess');
    },
    handleMap(item) {
      uni.navigateTo({
        url: `/packageHospital/hospital-map?hospital_id=${item.hospital_id}`
      });
    },
    handleBannerClick(item) {
      this.$reportData({
        info: 'sy_chain_store_tuan_reservation_list:banner_click',
        ext: {
          // 位置序号:serial_num-数据在list或者feed中的位置序号；从1开始递增，与sever保持一致 链接:url-图片、视频或h5的链接
          serial_num: item.index + 1,
          url: item.jump_url
        }
      });

      // 根据jump_type处理不同的跳转方式
      if (+item.jump_type === 1) {
        // H5跳转
        uni.navigateTo({
          url: `/pages/h5?url=${encodeURIComponent(item.jump_url)}`
        });
      } else if (+item.jump_type === 2) {
        // 小程序跳转
        if (item.need_auth && !this.isLogin) {
          this.judgeLogin();
          return;
        }
        if (item.mini_app_id === 'wx4c984b5d0eb25e91') {
          const path = [
            '/pages/index',
            '/pages/item',
            '/pages/coupon-center',
            '/pages/my'
          ];
          if (path.includes(item.jump_url)) {
            uni.switchTab({
              url: item.jump_url
            });
          } else {
            uni.navigateTo({
              url: item.jump_url
            });
          }
        } else {
          uni.navigateToMiniProgram({
            appId: item.mini_app_id,
            path: item.jump_url,
            success(res) {
              console.log('跳转小程序成功', res);
            },
            fail(err) {
              console.log('跳转小程序失败', err);
              uni.showToast({
                title: '跳转失败',
                icon: 'none'
              });
            }
          });
        }
      }
    },
    // 轮播图曝光埋点
    handleBannerExpose(item) {
      this.$reportData({
        info: 'sy_chain_store_tuan_reservation_list:banner_exposure',
        ext: {
          // 位置序号:serial_num-数据在list或者feed中的位置序号；从1开始递增，与sever保持一致 链接:url-图片、视频或h5的链接
          serial_num: item.index + 1,
          url: item.jump_url
        }
      });
    }
  },
  onReachBottom() {
    if (this.hasMore) {
      this.getReserveIndexList('page');
    }
  }
};
</script>

<style lang="less" scoped>
.appointment-page {
  width: 100%;
  &-header {
    position: relative;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    .back {
      position: absolute;
      left: 6px;
      top: 50%;
      height: 88rpx;
      width: 88rpx;
      z-index: 1;
      background: url(https://static.soyoung.com/sy-design/3cj8rc3ipek931725437086652.png)
        no-repeat center center transparent;
      background-size: 22rpx 36rpx;
      transform: translateY(-50%);
    }
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #000000;
      font-weight: 600;
    }
  }
  &-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    &-tab {
      width: 100%;
      padding: 30rpx 50rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      &-item {
        position: relative;
        flex: 1;
        padding: 34rpx 0;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        background: #f2f2f2;
        margin-right: 30rpx;
        &:last-child {
          margin-right: 0;
        }
        span {
          font-family: PingFangSC-Medium;
          font-size: 32rpx;
          color: #030303;
          font-weight: 500;
          margin-right: 20rpx;
        }
        .tab-item__identify {
          min-width: 30rpx;
          position: absolute;
          right: 0;
          top: -12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fe6631;
          font-family: PingFangSC-Medium;
          font-size: 10px;
          color: #ffffff;
          font-weight: 500;
        }
        .tab-item__img1 {
          width: 32rpx;
          height: 32rpx;
          margin-right: 10rpx;
        }
        .tab-item__img2 {
          width: 12rpx;
          height: 18rpx;
        }
      }
    }
    &-banner-container {
      margin-top: -8rpx;
      padding: 20rpx 50rpx 40rpx 50rpx;
      background-color: #ffffff;
      .carousel-banner {
        margin: 0 auto;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        width: 100%;
        overflow: hidden;
      }
    }
    &__line {
      width: 100%;
      height: 14rpx;
      background: #f8f8f8;
    }
    &-already-appointment {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 50rpx 50rpx 40rpx;
      box-sizing: border-box;
      .already-appointment__title {
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color: #030303;
        font-weight: 600;
        padding-bottom: 36rpx;
        box-sizing: border-box;
      }
      .already-appointment-card {
        width: 100%;
        display: flex;
        flex-direction: column;
        &-header {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-bottom: 10rpx;
          &__point {
            width: 16rpx;
            height: 16rpx;
            background: #030303;
            border-radius: 50%;
            margin-right: 20rpx;
          }
          &__time {
            font-family: OutFit-Regular;
            font-size: 14px;
            color: #646464;
            font-weight: 500;
          }
          &__line {
            width: 2rpx;
            height: 22rpx;
            background: #bababa;
            margin: 0 20rpx;
          }
          &__text {
            flex: 1;
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #646464;
            font-weight: 500;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
          }
          &__total {
            min-width: 128rpx;
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #646464;
            font-weight: 400;
          }
        }
        &-content {
          width: 100%;
          display: flex;
          flex-direction: row;
          &__line {
            min-width: 2rpx;
            margin: 0 27rpx 0 7rpx;
            background-image: linear-gradient(
              to bottom,
              #bababa 50%,
              transparent 50%
            );
            background-size: 100% 4px;
            background-repeat: repeat-y;
          }
          &-right {
            flex: 1;
            display: flex;
            flex-direction: column;
            &__time {
              font-family: OutFit-Regular;
              font-size: 32rpx;
              color: #030303;
              font-weight: 500;
              padding-bottom: 30rpx;
            }
            &-body {
              width: 100%;
              display: flex;
              flex-direction: column;
              .right-body-tips {
                width: 100%;
                padding: 30rpx;
                box-sizing: border-box;
                // background: rgba(137, 220, 101, 0.15);
                background: #fee8e1;
                position: relative;
                display: flex;
                flex-direction: column;
                &::after {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  // border: 1px solid rgba(137, 220, 101, 0.35);
                  border: 1px solid #ffe0d5;
                  border-bottom: 0;
                  width: 200%;
                  height: 200%;
                  transform: scale(0.5);
                  transform-origin: 0 0;
                  box-sizing: border-box;
                }
                &-title {
                  width: 100%;
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: flex-start;
                  padding-bottom: 10rpx;
                  &__sp1 {
                    font-family: PingFangSC-Semibold;
                    font-size: 32rpx;
                    // color: #61b43e;
                    color: #fe6631;
                    letter-spacing: 0;
                    font-weight: 600;
                  }
                  &__sp2 {
                    width: 2rpx;
                    height: 22rpx;
                    display: flex;
                    margin: 0 20rpx;
                    // background: #61b43e;
                    background: #fe6631;
                  }
                  &__sp3 {
                    font-family: PingFangSC-Semibold;
                    font-size: 24rpx;
                    // color: #61b43e;
                    color: #fe6631;
                    font-weight: 600;
                  }
                }
                &-description {
                  font-family: PingFangSC-Regular;
                  font-size: 24rpx;
                  color: #555555;
                  font-weight: 400;
                }
              }
              .right-body-hospital-card {
                width: 100%;
                position: relative;
                display: flex;
                flex-direction: column;
                position: relative;
                padding: 50rpx 30rpx;
                box-sizing: border-box;
                background: #f2f2f2;
                &__identify {
                  position: absolute;
                  right: 0;
                  top: 0;
                  padding: 8rpx 20rpx;
                  font-family: PingFangSC-Medium;
                  font-size: 24rpx;
                  color: #61b43e;
                  font-weight: 500;
                  background: #ebfbdc;
                }
                &-header {
                  font-family: PingFangSC-Semibold;
                  font-size: 32rpx;
                  color: #030303;
                  font-weight: 600;
                  padding-bottom: 10rpx;
                }
                &-address {
                  width: 100%;
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: space-between;
                  padding-bottom: 8rpx;
                  span {
                    font-family: PingFangSC-Regular;
                    font-size: 24rpx;
                    color: #646464;
                    font-weight: 400;
                    max-width: 432rpx;
                    //overflow: hidden;
                    //text-overflow: ellipsis;
                    //white-space: nowrap;
                  }
                  img {
                    width: 50rpx;
                    height: 50rpx;
                  }
                }
                &-btn {
                  width: 100%;
                  display: flex;
                  flex-direction: row;
                  align-items: center;
                  justify-content: flex-end;
                  margin-top: 30rpx;
                  &-item {
                    flex: 1;
                    max-width: 176rpx;
                    margin-right: 16rpx;
                    border: 1px solid #333333;
                    font-family: PingFangSC-Regular;
                    font-size: 24rpx;
                    color: #030303;
                    font-weight: 400;
                    padding: 12rpx 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    &:last-child {
                      margin-right: 0;
                    }
                  }
                }
              }
            }
          }
        }
        &-more {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 40rpx 0 0;
          span {
            font-family: PingFangSC-Regular;
            font-size: 24rpx;
            color: #333333;
            font-weight: 400;
          }
          img {
            margin-left: 10rpx;
            width: 12rpx;
            height: 18rpx;
          }
        }
      }
    }
    &-increase {
      width: 100%;
      padding: 50rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      &__title {
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        color: #030303;
        font-weight: 500;
        padding-bottom: 30rpx;
      }
      &-list {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
      .appointment-page-content-empty {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: 200rpx;
        img {
          width: 70rpx;
          height: 70rpx;
        }
        span {
          font-family: PingFangSC-Medium;
          font-size: 28rpx;
          color: #030303;
          font-weight: 500;
          padding-top: 40rpx;
        }
      }
      &__no-more {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #8c8c8c;
        font-weight: 400;
        padding-top: 30rpx;
      }
    }
  }
}
.page-loading {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100rpx;
    height: 100rpx;
  }
}
</style>
