<template>
  <div class="appointment-page-content-increase-list-item">
    <span class="increase-list-item__sp1">{{ data.product_name }}</span>
    <span v-if="data.sub_title !== ''" class="increase-list-item__sp2"
      >包含项目：{{ data.sub_title }}</span
    >
    <div class="increase-list-item-div">
      <div class="increase-list-item-div-left">
        <div class="left-top">
          <span
            >剩余<em>{{ data.remain_num }}</em
            >次</span
          >
          <span class="left-div__line"></span>
          <span
            >可预约<em>{{ data.count_num }}</em
            >次</span
          >
        </div>
        <div class="left-bottom">
          <span class="increase-list-item__sp3"
            >订单ID：<em>{{ data.top_order_id }}</em></span
          >
        </div>
      </div>
      <div class="increase-list-item-div-right" @click="handleQuickAppointment">
        <span class="button-label" v-if="data.cashback">{{
          data.cashback
        }}</span>
        {{ data.btn.title || '立即预约' }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    serial_num: {
      type: Number,
      default: 1
    }
  },
  methods: {
    handleQuickAppointment() {
      this.$reportData({
        info: 'sy_chain_store_tuan_reservation_list:reserve_click',
        ext: {
          order_id: this.data.top_order_id,
          id: this.data.order_id,
          serial_num: this.serial_num
        }
      });
      this.$emit('nowAppointment', this.data);
    }
  }
};
</script>

<style lang="less" scoped>
.appointment-page-content-increase-list-item {
  width: 100%;
  display: flex;
  background: #f2f2f2;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  .increase-list-item__sp1 {
    width: 100%;
    text-align: left;
    font-family: PingFangSC-Semibold;
    font-size: 32rpx;
    color: #030303;
    font-weight: 600;
    padding-bottom: 10rpx;
  }
  .increase-list-item__sp2 {
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #646464;
    line-height: 36rpx;
    font-weight: 400;
    margin-bottom: 20rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    width: 100%;
  }
  .increase-list-item-div {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    &-left {
      display: flex;
      flex-direction: column;
      flex: 1;
      .left-top,
      .left-bottom {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      span {
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #030303;
        font-weight: 400;
        display: flex;
        align-items: center;
        &.left-div__line {
          width: 2rpx;
          height: 20rpx;
          background: #333333;
          margin: 0 10rpx;
        }
        em {
          font-family: Outfit-Regular;
          font-size: 24rpx;
          color: #030303;
          font-weight: 500;
        }
      }

      .left-bottom {
        flex: 1;
        padding-top: 40rpx;
        .increase-list-item__sp3 {
          width: 100%;
          text-align: left;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #8c8c8c;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          em {
            font-style: normal;
            font-family: Outfit-Regular;
            color: #8c8c8c;
          }
        }
      }
    }
    &-right {
      width: 176rpx;
      padding: 12rpx 0;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      background: #333333;
      font-family: PingFangSC-Medium;
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 500;
      position: relative;
      .button-label {
        position: absolute;
        font-family: Outfit-Regular;
        font-size: 20rpx;
        color: #030303;
        font-weight: 400;
        background: #89dc65;
        padding: 0 4rpx;
        top: -38rpx;
        &::after {
          content: '';
          position: absolute;
          display: block;
          left: 50%;
          top: calc(100% - 2rpx);
          transform: translateX(-50%);
          width: 0rpx;
          height: 6rpx;
          border: 10rpx solid transparent;
          border-top-color: #89dc65;
        }
      }
    }
  }
}
</style>
