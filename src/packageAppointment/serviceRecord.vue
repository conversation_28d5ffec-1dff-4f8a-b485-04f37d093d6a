<template>
  <div
    class="service-record"
    :style="{
      marginTop: (maxHeight + 6) * 2 + 'rpx',
      height: `calc(100vh - ${(maxHeight + 6) * 2}rpx)`
    }"
  >
    <!-- 导航栏 start -->
    <Navbar :background="`rgba(255, 255, 255, 1)`" :hasBack="false">
      <div class="service-record-header">
        <block>
          <div class="back" @click="handleBack"></div>
          <div class="title">服务记录</div>
        </block>
      </div>
    </Navbar>
    <!-- 导航栏 end -->

    <!-- 服务记录 list start -->
    <div v-if="!loading" class="service-record-list">
      <div
        v-if="serviceRecordList.length === 0"
        class="service-record-list__empty"
      >
        <img
          src="https://static.soyoung.com/sy-design/3up7pnqhzxgn81727081679671.png"
          alt=""
        />
        <span>暂无服务记录</span>
      </div>
      <template v-else>
        <div
          v-for="(item, index) in serviceRecordList"
          :key="item.visit_id"
          class="service-record-list-item"
          @click="handleGoService(item)"
        >
          <div class="list-item__identify">
            {{ item.is_in_store ? '进行中' : '已完成' }}
          </div>
          <div class="list-item__title">{{ item.arrive_time }}到店</div>
          <div class="list-item__address">{{ item.tenant_name }}</div>
          <div
            class="list-item-menu"
            :style="{
              paddingBottom: item.service_item_list.length > 2 ? '0' : '40rpx'
            }"
          >
            <template v-if="item.is_in_store && item.current_service !== null">
              <!-- 若当前到访仍未离店，则展示当前的服务节点 start -->
              <div class="list-item-menu-node">
                <div class="list-item-menu-node__title">当前节点</div>
                <div class="list-item-menu-node-content">
                  <div class="list-item-menu-node-content__line"></div>
                  <div class="list-item-menu-node-content-description">
                    <span class="description__point"></span>
                    <span class="description__title">{{
                      item.current_service.title
                    }}</span>
                    <span
                      v-if="
                        item.current_service.prefix !== '' &&
                        item.current_service.suffix !== ''
                      "
                      class="description__line"
                    ></span>
                    <span class="description__nums"
                      >{{
                        item.current_service.prefix.startsWith('（')
                          ? item.current_service.prefix.substring(1)
                          : item.current_service.prefix
                      }}
                      <em v-if="item.current_service.show_num !== 0">{{
                        item.current_service.show_num
                      }}</em
                      >{{
                        item.current_service.suffix.includes('）')
                          ? item.current_service.suffix.slice(0, -1)
                          : item.current_service.suffix
                      }}</span
                    >
                  </div>
                  <div class="list-item-menu-node-content__line"></div>
                </div>
              </div>
              <!-- 若当前到访仍未离店，则展示当前的服务节点 end -->
            </template>
            <template
              v-if="!item.is_in_store && item.service_item_list !== null"
            >
              <!-- 若当次到访有划扣记录，则展示划扣数据 start -->
              <div class="list-item-menu-top">
                本次服务共<em>{{ item.service_item_list.length }}</em
                >个项目
              </div>
              <div
                class="list-item-menu-list"
                :style="{
                  height:
                    item.show || item.service_item_list.length === 1
                      ? '100%'
                      : '92rpx'
                }"
              >
                <div
                  class="list-item-menu-list__item"
                  v-for="(child, cIndex) in item.service_item_list"
                  :key="cIndex"
                >
                  <div class="list-item-menu-list__item-point"></div>
                  <div class="list-item-menu-list__item-title">
                    {{ child.title }}
                  </div>
                  <div class="list-item-menu-list__item-counts">
                    X{{ child.times }}
                  </div>
                </div>
              </div>
              <div
                v-if="item.service_item_list.length > 2"
                class="list-item-menu-list__item-more"
                @click.stop="handleToggle(index)"
              >
                <span>{{ item.show ? '收起' : '展开' }}</span>
                <img
                  src="https://static.soyoung.com/sy-design/3i7wqa0rpbuog1727319430977.png"
                  alt=""
                  :style="{
                    transform: item.show ? 'rotate(180deg)' : 'rotate(0deg)'
                  }"
                />
              </div>
              <!-- 若当次到访有划扣记录，则展示划扣数据 end -->
            </template>

            <!-- 若当次到访没有划扣记录，则展示兜底文案 start -->
            <div
              v-if="!item.is_in_store && !item.service_item_list"
              class="list-item-menu__noData"
            >
              本次暂无项目服务记录
            </div>
            <!-- 若当次到访没有划扣记录，则展示兜底文案 end -->
          </div>
          <div
            v-if="
              item.has_compare_photo ||
              item.has_xiaofu ||
              item.is_in_store ||
              !item.has_comment
            "
            class="list-item-btn"
          >
            <div
              v-if="item.has_compare_photo"
              class="list-item-btn-item"
              @click.stop="handleGo('picture', '', item)"
            >
              对比照
            </div>
            <div
              v-if="item.has_xiaofu"
              class="list-item-btn-item"
              @click.stop="handleGo('record', item.xiaofu_url, item)"
            >
              测肤记录
            </div>
            <div
              v-if="item.is_in_store"
              class="list-item-btn-item list-item-btn-item__active"
              @click.stop="handleGo('guide', '', item)"
            >
              查看指引
            </div>
            <div
              v-if="!item.is_in_store && !item.has_comment"
              class="list-item-btn-item list-item-btn-item__active"
              @click.stop="handleGo('comment', '', item)"
            >
              服务评价
            </div>
            <div
              v-if="item.sign_info && item.sign_info.is_show === 1"
              class="list-item-btn-item list-item-btn-item__active"
              @click.stop="handleGoSign(item)"
            >
              <div
                v-if="item.sign_info.cnt && item.sign_info.cnt > 0"
                class="list-item-btn-item-badge"
              >
                {{ item.sign_info.cnt }}
              </div>
              病历签署
            </div>
          </div>
        </div>
      </template>
      <div
        v-if="serviceRecordList.length === total && total !== 0"
        class="service-record-list__no-more"
      >
        没有更多了
      </div>
    </div>
    <!-- 服务记录 list end -->

    <!-- loading start -->
    <div
      v-if="loading"
      class="page-loading"
      :style="{ height: `calc(100vh - ${maxHeight * 2}rpx)` }"
    >
      <img
        src="https://static.soyoung.com/sy-pre/9lklkdzpl9nx-1727251800646.gif"
        alt=""
      />
    </div>
    <!-- loading end -->

    <!-- weak -->
    <WeakNetwork
      @refresh="refresh"
      :path="['/syGroupBuy/chain/visit/getServiceRecordList']"
      :visible.sync="isNetwork"
    />
  </div>
</template>

<script>
import Navbar from '@/components/NavBar.vue';
import WeakNetwork from '@/components/WeakNetwork.vue';
import { getServiceRecordList } from '@/api/packageAppointment';
export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_tuan_service_record_list_page',
      ext: {}
    };
  },
  components: {
    Navbar,
    WeakNetwork
  },
  data() {
    return {
      pageInit: false,
      maxHeight: 0,
      serviceRecordList: [],
      page: 1,
      hasMore: false,
      total: 0,
      loading: true,
      isNetwork: false
    };
  },
  mounted() {
    this.maxHeight = uni.getMenuButtonBoundingClientRect().bottom;
    this._initData();
  },
  methods: {
    async _initData() {
      const data = await getServiceRecordList({
        page: this.page,
        page_size: 10
      });
      if (data) {
        console.log(data, '服务列表数据');
        if (this.page === 1) {
          this.serviceRecordList =
            data.list && data.list.length > 0
              ? data.list.map((item) => {
                  return {
                    ...item,
                    show: false
                  };
                })
              : [];
        } else {
          let cutData =
            data.list && data.list.length > 0
              ? data.list.map((item) => {
                  return {
                    ...item,
                    show: false
                  };
                })
              : [];
          this.serviceRecordList = this.serviceRecordList.concat(cutData);
        }
        this.hasMore = data.has_more;
        this.total = data.total;
        this.page = data.has_more ? this.page + 1 : this.page;
        this.isNetwork = false;
      }
      this.pageInit = true;
      this.loading = false;
    },

    async refresh() {
      this.loading = true;
      await this._initData();
      this.loading = false;
    },
    handleBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    handleToggle(index) {
      this.serviceRecordList[index].show = !this.serviceRecordList[index].show;
    },
    handleGo(identify, url, item) {
      console.log(identify, '跳转', url, item);
      switch (identify) {
        case 'guide':
          this.$reportData({
            info: 'sy_chain_store_tuan_service_record_list:service_guide_click',
            ext: {
              id: item.visit_id
            }
          });
          break;
        case 'picture':
          this.$reportData({
            info: 'sy_chain_store_tuan_service_record_list:comparison_pic_click',
            ext: {
              id: item.visit_id
            }
          });
          break;
        case 'record':
          this.$reportData({
            info: 'sy_chain_store_tuan_service_record_list:skin_detection_click',
            ext: {
              id: item.visit_id
            }
          });
          break;
        case 'comment':
          this.$reportData({
            info: 'sy_chain_store_tuan_service_record_list:service_evaluate_click',
            ext: {
              id: item.visit_id
            }
          });
          this.$bridge({
            url:
              '/pages/h5?url=' +
              encodeURIComponent(
                `https://m.soyoung.com/chain/questionnaire?visit_id=${item.visit_id}&tenant_id=${item.tenant_id}&channel_id=226`
              ) // 均跳转App下载引导
          });
          break;
      }

      if (identify !== 'comment') {
        this.$bridge({
          url:
            '/pages/h5?url=' +
            encodeURIComponent(`https://m.soyoung.com/tmwap25795#/`) // 均跳转App下载引导
        });
      }
    },
    handleGoSign(item) {
      this.$bridge({
        url:
          '/pages/h5?url=' +
          encodeURIComponent(item.jump_url || `/chain/case/batchSign`) // 均跳转App下载引导
      });
    },
    handleGoService() {
      this.$bridge({
        url:
          '/pages/h5?url=' +
          encodeURIComponent(`https://m.soyoung.com/tmwap25795#/`)
      });
    }
  },
  onShow() {
    if (!this.pageInit) {
      return;
    }
    this.page = 1;
    this.refresh();
  },
  onReachBottom() {
    if (this.hasMore) {
      this._initData();
    }
  }
};
</script>

<style lang="less" scoped>
.service-record {
  width: 100%;
  background: #f2f2f2;
  &-header {
    position: relative;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;

    .back {
      position: absolute;
      left: 6px;
      top: 50%;
      height: 88rpx;
      width: 88rpx;
      z-index: 1;
      background: url(https://static.soyoung.com/sy-design/3cj8rc3ipek931725437086652.png)
        no-repeat center center transparent;
      background-size: 22rpx 36rpx;
      transform: translateY(-50%);
    }

    .title {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #000000;
      font-weight: 600;
    }
  }
  &-list {
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background: #f2f2f2;
    &-item {
      width: 100%;
      position: relative;
      background: #ffffff;
      padding: 50rpx 30rpx;
      box-sizing: border-box;
      margin-bottom: 30rpx;
      display: flex;
      flex-direction: column;
      .list-item__identify {
        position: absolute;
        right: 0;
        top: 0;
        padding: 4rpx 20rpx;
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        color: #61b43e;
        font-weight: 500;
        background: #ebfbdc;
      }
      .list-item__title {
        font-family: OutFit-Regular;
        font-size: 32rpx;
        color: #030303;
        font-weight: 500;
        padding-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .list-item__address {
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #646464;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 30rpx;
      }
      .list-item-menu {
        background: #f2f2f2;
        padding: 40rpx 30rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        &-node {
          width: 100%;
          display: flex;
          flex-direction: column;
          &__title {
            font-family: PingFangSC-Regular;
            font-size: 24rpx;
            color: #030303;
            font-weight: 400;
            padding-bottom: 20rpx;
          }
          &-content {
            width: 100%;
            &__line {
              width: 2rpx;
              height: 20rpx;
              background-image: linear-gradient(
                to bottom,
                #646464 50%,
                transparent 50%
              );
              background-size: 100% 4px;
              background-repeat: repeat-y;
              margin-left: 5rpx;
            }
            &-description {
              width: 100%;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: flex-start;
              .description__point {
                width: 12rpx;
                height: 12rpx;
                background: #030303;
                margin-right: 16rpx;
                border-radius: 50%;
              }
              .description__title {
                font-family: PingFangSC-Semibold;
                font-size: 28rpx;
                color: #030303;
                font-weight: 600;
              }
              .description__line {
                width: 2rpx;
                height: 26rpx;
                background: #030303;
                margin: 0 20rpx;
              }
              .description__nums {
                font-family: PingFangSC-Regular;
                font-size: 28rpx;
                color: #030303;
                font-weight: 400;
                display: flex;
                align-items: center;
                em {
                  font-style: normal;
                  font-weight: 600;
                }
              }
            }
          }
        }

        &__noData {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #030303;
          font-weight: 400;
        }

        &-top {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #030303;
          font-weight: 400;
          padding-bottom: 20rpx;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          em {
            font-style: normal;
            font-weight: 600;
            margin: 0 2rpx;
          }
        }
        &-list {
          width: 100%;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          &__item {
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            margin-bottom: 20rpx;
            &:last-child {
              margin-bottom: 0;
            }
            &-point {
              width: 12rpx;
              height: 12rpx;
              margin-right: 16rpx;
              border-radius: 50%;
              background: #a9ea6a;
              margin-top: 13rpx;
            }
            &-title {
              flex: 1;
              font-family: PingFangSC-Semibold;
              font-size: 28rpx;
              color: #030303;
              font-weight: 600;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            &-counts {
              font-family: Outfit-Regular;
              font-size: 28rpx;
              color: #030303;
              font-weight: 400;
              margin-left: 30rpx;
            }
          }
        }
        .list-item-menu-list__item-more {
          width: 100%;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          padding: 20rpx 0 40rpx;
          box-sizing: border-box;
          span {
            font-family: PingFangSC-Regular;
            font-size: 24rpx;
            color: #333333;
            font-weight: 400;
            margin-right: 10rpx;
          }
          img {
            width: 18rpx;
            height: 12rpx;
          }
        }
      }
      .list-item-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-top: 30rpx;
        &-item {
          padding: 12rpx 0rpx;
          width: 144rpx;
          text-align: center;
          border: 2rpx solid #333333;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #030303;
          font-weight: 400;
          margin-right: 16rpx;
          position: relative;
          &:last-child {
            margin-right: 0;
          }
          &-badge {
            position: absolute;
            top: -10rpx;
            right: -10rpx;
            width: 26rpx;
            height: 26rpx;
            border-radius: 50%;
            background: #fe6631;
            line-height: 26rpx;
            font-family: PingFangSC-Medium;
            font-size: 18rpx;
            color: #ffffff;
            text-align: center;
            font-weight: 500;
          }
        }

        &-item__active {
          background: #333333;
          color: #ffffff;
        }
      }
    }
    &__empty {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 200rpx;
      img {
        width: 70rpx;
        height: 70rpx;
        margin-bottom: 40rpx;
      }
      span {
        font-family: PingFangSC-Medium;
        font-size: 28rpx;
        color: #030303;
        font-weight: 500;
      }
    }
    &__no-more {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #8c8c8c;
      font-weight: 400;
      padding-bottom: 30rpx;
    }
  }
}
.page-loading {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 100rpx;
    height: 100rpx;
  }
}
</style>
