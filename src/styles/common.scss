@import './variables.scss';

// 重置样式
* {
  box-sizing: border-box;
}

page {
  background-color: $background-color-base;
  font-size: $font-size-base;
  color: $text-color-primary;
  line-height: 1.5;
}

// 布局类
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

// 文字对齐
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 文字颜色
.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-secondary {
  color: $text-color-secondary;
}

.text-disabled {
  color: $text-color-disabled;
}

// 字体大小
.font-xs {
  font-size: $font-size-xs;
}

.font-sm {
  font-size: $font-size-sm;
}

.font-base {
  font-size: $font-size-base;
}

.font-lg {
  font-size: $font-size-lg;
}

.font-xl {
  font-size: $font-size-xl;
}

// 间距
.m-xs {
  margin: $spacing-xs;
}

.m-sm {
  margin: $spacing-sm;
}

.m-base {
  margin: $spacing-base;
}

.m-lg {
  margin: $spacing-lg;
}

.m-xl {
  margin: $spacing-xl;
}

.p-xs {
  padding: $spacing-xs;
}

.p-sm {
  padding: $spacing-sm;
}

.p-base {
  padding: $spacing-base;
}

.p-lg {
  padding: $spacing-lg;
}

.p-xl {
  padding: $spacing-xl;
}

// 背景颜色
.bg-white {
  background-color: $background-color-white;
}

.bg-light {
  background-color: $background-color-light;
}

.bg-base {
  background-color: $background-color-base;
}

// 边框
.border {
  border: 1px solid $border-color-base;
}

.border-light {
  border: 1px solid $border-color-light;
}

.border-bottom {
  border-bottom: 1px solid $border-color-split;
}

// 圆角
.rounded-sm {
  border-radius: $border-radius-sm;
}

.rounded {
  border-radius: $border-radius-base;
}

.rounded-lg {
  border-radius: $border-radius-lg;
}

// 阴影
.shadow {
  box-shadow: $box-shadow-base;
}

.shadow-light {
  box-shadow: $box-shadow-light;
}

// 隐藏/显示
.hidden {
  display: none;
}

.visible {
  display: block;
}

// 省略号
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
