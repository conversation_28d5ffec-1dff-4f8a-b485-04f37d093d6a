import { apiSubscribe } from '@/api/order';
import { apiSetSubscribe } from '@/api/home';

/**
 * 模板ID对应的场景值（不全）
用户支付成功后第2天未预约12点给用户推送订阅  scene      9
                        template_id SpF-TaBRBQkRFWWWDBn33o4rcSylRaN4fpwWuVA3JLY
用户支付成功后第4天未预约12点给用户推送订阅  scene     13
                        template_id S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q

用户支付成功后第7天未预约12点给用户推送订阅  scene     14
                        template_id 待定

用户支付成功后第25天未预约12点给用户推送订阅  scene    15
                        template_id S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q

核销后有下一次，但未预约，第2天12点给用户推送订阅 scene  10
                           template_id  S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q

核销后有下一次，但未预约，第5天12点给用户推送订阅 scene  16
                           template_id  SpF-TaBRBQkRFWWWDBn33o4rcSylRaN4fpwWuVA3JLY

套餐商品核销A商品，未预约B商品第2天12点推送订阅   scene 17
                           template_id  S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q
套餐商品核销A商品，未预约B商品第5天12点推送订阅   scene 18
                           template_id  SpF-TaBRBQkRFWWWDBn33o4rcSylRaN4fpwWuVA3JLY
*/
export default {
  methods: {
    /**
     *
     * 调用返回promise，调用返回的函数返送脚本
     * @param {*} map {模板ID：[场景值]}
       eg: {
          'SpF-TaBRBQkRFWWWDBn33o4rcSylRaN4fpwWuVA3JLY':[4],
          '6lbjS-X0Q6pdPafDas7sw7_rrnXgpdvacJnK0UZrYc4':[5],
          'SpF-TaBRBQkRFWWWDBn33o4rcSylRaN4fpwWuVA3JLY':[9],
          'S-URJ-C09rkJ_MYfYxtWcrMsGcrvQNAIFpsD0KPcu4Q':[10]
        }
     * @returns Promise<Function>
     */
    createSubscribe(map, type = '') {
      console.log('提交授权的数据', map);
      const tmpIds = Object.keys(map);
      console.log('需要授权的模板消息ID', tmpIds.join());
      return this.$subscribeMsg(tmpIds)
        .then((res) => {
          const scene = [];
          tmpIds.forEach((key) => {
            if (type === 'need-accept') {
              // 这种情况下，需要用户同意订阅，才调接口
              if (
                res?.[key] === 'accept' &&
                map[key] &&
                map[key] instanceof Array &&
                map[key].length > 0
              ) {
                scene.push(...map[key]);
              }
            } else {
              // 这种情况下，不管用户是否同意都调接口
              if (
                map[key] &&
                map[key] instanceof Array &&
                map[key].length > 0
              ) {
                scene.push(...map[key]);
              }
            }
          });
          console.log('授权结果', res, scene.join());
          return (opts) => {
            scene.forEach((num) =>
              apiSubscribe({
                scene: num,
                city_id:
                  opts?.city_id || uni.getStorageSync('user_info')?.cityId || 0,
                order_id: opts?.order_id,
                hospital_id: opts?.hospital_id,
                pid: opts?.pid,
                subscribe_date: opts?.date
              })
            );
            // 接受订阅，执行回调
            scene.length > 0 && opts?.callback && opts?.callback();
          };
        })
        .catch((msg) => {
          console.log(msg);
        });
    },
    /**
     * https://soyoung.feishu.cn/docx/GLlXdZ4KvoIFDUxdrNOc3DqAnxe
     * 新需求增加的订阅方法
     * @param {*} tmpIds   模版id
     * @param {*} entityIds  实体id(不同的模版id取不同的id<券id/订单id/预约id>)
     */
    async createGroupBySub(tmpIds, entityIds) {
      return new Promise(async (resolve) => {
        try {
          if (!tmpIds?.length) {
            throw new Error('');
          }
          const res = await this.$subscribeMsg(tmpIds);
          const templateList = tmpIds.reduce((prev, key) => {
            if (res?.[key] === 'accept') {
              if (entityIds?.length) {
                prev.push(
                  ...entityIds.map((id) => ({
                    template_id: key,
                    entity_id: id
                  }))
                );
              } else {
                prev.push({
                  template_id: key
                });
              }
            }
            return prev;
          }, []);
          // if (!templateList.length) {
          //   throw new Error('');
          // }
          await apiSetSubscribe({
            template_list: templateList
          });
        } catch (error) {
          console.log(error);
        } finally {
          resolve();
        }
      });
    }
  }
};
