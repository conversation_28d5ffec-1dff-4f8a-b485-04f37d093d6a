import { uniLogin, syLogin, sySignout } from './login';
import config from '@/config';
import common from '@/utils/common.js';
import { checkUserJoinCJumpUrlApi } from '@/api/user.js';

const systemInfo = uni.getSystemInfoSync();

function noop() {}

import SMSdk from '@/common/fp.min';

// 数美
SMSdk.initConf({
  organization: 'vy4mR7dCx1I4xsC1fLWR',
  channel: '新氧优享小程序' // 作为区分来源的，可以自定义
});

export default {
  install(Vue) {
    Vue.prototype.SMSdk = SMSdk;
    /**
     * <AUTHOR>
     * @date 2020-03-10
     * @Description: 获取用户网络类型
     */
    Vue.prototype.$getNetType = function () {
      return new Promise((resolve, reject) => {
        uni.getNetworkType({
          success(res) {
            if (res.errMsg === 'getNetworkType:ok') {
              resolve(res.networkType);
            } else {
              reject(res.errMsg);
            }
          }
        });
      });
    };
    Vue.$request = Vue.prototype.$request = common.request;
    Vue.$deepClone = Vue.prototype.$deepClone = function (obj) {
      try {
        if (!obj || typeof obj !== 'object') {
          return obj;
        }
        const target = Array.isArray(obj) ? [] : {};
        if (Array.isArray(obj)) {
          obj.forEach((item, i) => {
            target[i] = typeof item === 'object' ? this.$deepClone(item) : item;
          });
        } else {
          for (const i in obj) {
            // eslint-disable-next-line no-prototype-builtins
            if (obj.hasOwnProperty(i)) {
              target[i] =
                typeof obj[i] === 'object' ? this.$deepClone(obj[i]) : obj[i];
            }
          }
        }
        return target;
      } catch (error) {
        console.log(error, 'error');
      }
    };
    /**
     * 跳转链接，传参为一个对象，包含 type 和 url
     * @type {String} h5-h5链接 mini-小程序链接
     * @url 链接URL
     */
    Vue.$jumpUniversal = Vue.prototype.$jumpUniversal = function ({
      type,
      url
    }) {
      switch (type) {
        case 'h5':
          // H5链接
          url = '/pages/h5?url=' + encodeURIComponent(url);
          Vue.$bridge({ url: url });
          break;
        default:
          // 小程序链接
          Vue.$bridge({
            url: url
          });
          break;
      }
    };
    /**
     * <AUTHOR>
     * @date 2020-03-11
     * @Description: 路由跳转，已适配tab切换
     */

    Vue.$bridge = Vue.prototype.$bridge = function ({
      url = '',
      callbackSuccess = noop,
      callbackFail = noop,
      isLoading = false,
      events = {}
    }) {
      const params = {
        url,
        callbackSuccess,
        callbackFail,
        isLoading
      };
      if (isLoading) {
        uni.showLoading({
          title: '跳转中',
          mask: true
        });
      }
      let actionName;
      if (
        [
          '/pages/index',
          '/pages/my',
          '/pages/item',
          '/pages/coupon-center'
        ].includes(params.url)
      ) {
        // tab中的4个页面
        actionName = 'switchTab';
      } else {
        actionName = getCurrentPages().length < 9 ? 'navigateTo' : 'redirectTo';
      }
      uni[actionName]({
        url: params.url,
        events,
        success(res) {
          if (typeof params.callbackSuccess === 'function') {
            params.callbackSuccess(res);
          }
        },
        fail(res) {
          if (typeof params.callbackFail === 'function') {
            params.callbackFail(res);
          }
        },
        complete() {
          if (!params.isLoading) {
            uni.hideLoading();
          }
        }
      });
    };
    Vue.$toH5 = Vue.prototype.$toH5 = function (url) {
      Vue.$bridge({
        url: `/pages/h5?url=${encodeURIComponent(url)}`
      });
    };
    /**
     * <AUTHOR>
     * @date 2019-07-30
     * @Description: 设置用户信息到本地储存
     */
    Vue.$setUserInfoToStorage = Vue.prototype.$setUserInfoToStorage = function (
      info = {}
    ) {
      console.log(info);
      const userInfo = uni.getStorageSync('user_info');
      const targetInfo = {};
      Object.assign(targetInfo, userInfo, info);
      uni.setStorageSync('user_info', targetInfo);
      // 埋点所需参数
      uni.setStorageSync('openID', targetInfo.openId);
      uni.setStorageSync('sessionKey', targetInfo.sessionKey);
      uni.setStorageSync('unionID', targetInfo.unionId);
      uni.setStorageSync('hasWxAvatar', targetInfo.hasWxAvatar);
      uni.setStorageSync('hasAuth', !!targetInfo.uid);
      uni.setStorageSync('uid', userInfo.uid || targetInfo.uid);
      uni.setStorageSync('lng', targetInfo.lng);
      uni.setStorageSync('lat', targetInfo.lat);
      uni.setStorageSync('cityId', targetInfo.cityId);
      uni.setStorageSync('stat_sys', 8);
      Vue.prototype.$store.dispatch('global/setUser', targetInfo);
      // 为了触发没有获取到unionID的埋点重新上报
      uni.$emit('after_set_unionID');
    };
    Vue.prototype.$signout = function (reLogin) {
      return sySignout(reLogin);
    };
    /**
     * <AUTHOR>
     * @date 2019-07-30
     * @Description:
     */
    Vue.$login = Vue.prototype.$login = function (reLogin) {
      return syLogin(reLogin);
    };
    Vue.$uniLogin = Vue.prototype.$uniLogin = function () {
      return uniLogin();
    };
    Vue.prototype.isIOS = function () {
      return systemInfo.model.toLocaleLowerCase().includes('iphone');
    };
    /**
     * 保存图片
     * @param {String} imgUrl 图片地址
     */
    Vue.prototype.$downloadImg = function (imgUrl) {
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: imgUrl,
          success(res) {
            uni.saveImageToPhotosAlbum({
              filePath: res.path,
              success() {
                resolve();
              },
              fail(res) {
                const message = {
                  type: 'auth',
                  info: '保存失败,请打开设置授权',
                  ...res
                };
                reject(message);
              }
            });
          },
          fail(res) {
            const message = {
              type: 'auth',
              info: '保存失败,请检查网络',
              ...res
            };
            reject(message);
          }
        });
      });
    };
    /**
     * 发送订阅消息
     * @param {Array} tmplIds
     * @res 返回值 ‘accept’代表允许授权，'reject'代表取消授权
     */
    Vue.prototype.$subscribeMsg = function (tmplIds) {
      if (typeof tmplIds === 'string') {
        tmplIds = [tmplIds];
      }
      return new Promise((resolve, reject) => {
        uni.requestSubscribeMessage({
          tmplIds,
          success(res) {
            resolve(res);
          },
          fail(err) {
            reject(err);
          }
        });
      });
    };
    // 获取位置信息，经纬度，cityId，城市名
    // https://developers.weixin.qq.com/community/develop/doc/000aee91a98d206bc6dbe722b51801
    // 频繁的调用gelocation的问题
    const threshold = 31000;
    function invokerLocation(type) {
      invokerLocation.promise =
        invokerLocation.promise ||
        new Promise((resolve, reject) => {
          setTimeout(() => {
            invokerLocation.promise = null;
          }, threshold);
          wx.getLocation({
            type: type || 'gcj02', // 'wgs84',
            success: resolve,
            fail: (errMsg) => {
              invokerLocation.promise = null;
              reject(errMsg);
            }
          });
        });
      return invokerLocation.promise;
    }
    Vue.$getCityId = Vue.prototype.$getCityId = async function getCityId(type) {
      let position = null;
      try {
        position = await invokerLocation(type);
      } catch ({ errMsg }) {
        // 如果是授权失败 'getLocation:fail auth deny'
        return Promise.reject(errMsg);
      }

      const { latitude: lat, longitude: lng } = position;
      // 如果缓存的位置 坐标一致，将拾取本地缓存
      const userInfo = uni.getStorageSync('user_info');
      if (userInfo.lat === lat && userInfo.lng === lng) {
        return {
          lat,
          lng,
          cityId: userInfo.cityId,
          cityName: userInfo.cityName
        };
      }
      getCityId.promise =
        getCityId.promise ||
        Vue.$request({
          url: '/xinyang/yuehui/location',
          data: {
            lat,
            lng
          }
        })
          .then((res) => res.data)
          .then((res) => {
            const { errorCode, errorMsg, responseData } = res;
            if (errorCode === 0) {
              const { id, name } = responseData;
              const result = {
                lat,
                lng,
                cityId: id,
                cityName: name
              };
              Vue.$setUserInfoToStorage(result);
              // 每次定位后上报当前小程序最新地理位置
              Vue.$saveWxLastLocation(id);
              return result;
            } else {
              return Promise.reject(errorMsg);
            }
          })
          .finally(() => {
            getCityId.promise = null;
          });
      return getCityId.promise;
    };
    /**
     * <AUTHOR>
     * 方法$getRect为全局方法，用于查询节点信息
     * @param {Object} selector
     * @param {Object} all
     */
    Vue.prototype.$getRect = function (selector, all) {
      return new Promise((resolve) => {
        if (all) {
          uni
            .createSelectorQuery()
            .in(this)
            .selectAll(selector)
            .boundingClientRect((rect) => {
              if (all && Array.isArray(rect) && rect.length) {
                resolve(rect);
              }
              if (!all && rect) {
                resolve(rect);
              }
            })
            .exec();
        } else {
          uni
            .createSelectorQuery()
            .in(this)
            .select(selector)
            .boundingClientRect((rect) => {
              if (all && Array.isArray(rect) && rect.length) {
                resolve(rect);
              }
              if (!all && rect) {
                resolve(rect);
              }
            })
            .exec();
        }
      });
    };
    /**
     * <AUTHOR>
     * 方法$uploadImg为全局方法，用于上传图片
     * @param {Object} options 上传参数
     */
    Vue.$uploadImg = Vue.prototype.$uploadImg = (options) => {
      return new Promise((resolve, reject) => {
        const { url, baseUrl = '', loading = false, filePath, name } = options;
        if (loading) {
          uni.showLoading({
            title: '加载中..',
            mask: true
          });
        }
        const _url = `${config.baseApi}${baseUrl}${url}`;
        wx.uploadFile({
          url: _url,
          filePath,
          name,
          formData: {},
          success: (res) => {
            resolve(JSON.parse(res.data));
          },
          fail: (err) => {
            setTimeout(() => {
              wx.showToast({
                title: '图片上传失败，code:' + err.errMsg,
                icon: 'none'
              });
            }, 500);
            reject(err);
          },
          complete() {
            wx.hideLoading();
          }
        });
      });
    };
    /**
     * <AUTHOR>
     * 方法$setTabBar为全局方法，控制显隐底部bar
     * @param {Object} options 设置参数
     * {
     *  showMenu: Bool  // 控制底部bar显隐
     *  animation: Bool  // 显隐是否有动画
     *  selected: Number //更改底部tab选中 从左到右 为0,1,2,3
     * }
     */
    Vue.$setTabBar = Vue.prototype.$setTabBar = (options) => {
      const curPages = global?.getCurrentPages()?.[0];
      if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
        const res = curPages.getTabBar();
        res.setTabBar({
          ...options
        });
      }
    };
    /**
     * 查询节点信息
     * @param {、} selector
     * @param {*} times
     * @returns []
     */
    Vue.prototype.$queryNodes = function (selector, times = 10) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .selectAll(selector)
          .boundingClientRect((res) => {
            if (res?.length > 0) {
              resolve(res);
            } else {
              if (times--) {
                setTimeout(() => {
                  resolve(this.$queryNodes(selector, times));
                }, 50);
              } else {
                resolve([]);
              }
            }
          })
          .exec();
      });
    };

    Vue.prototype.$queryFirstNode = async function (selector) {
      const res = await this.$queryNodes(selector);
      return Array.isArray(res) && res.length > 0 ? res?.[0] : null;
    };

    Vue.prototype.$bigdecimal = function (number = 0) {
      const reg = String(number).includes('.')
        ? /\d{1,3}(?=(\d{3})+(\.))/g
        : /\d{1,3}(?=(\d{3})+$)/g;
      return String(number).replace(reg, (match) => {
        return match + ',';
      });
    };
    let timer = null;
    // 跳转客服会话页
    Vue.$toKefuDialog = Vue.prototype.$toKefuDialog = async function (
      opts = {}
    ) {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(async () => {
        const params = {
          source:
            opts.source || 'sy_yx_mini_program_private_msg_customer_service',
          timestamp: new Date().getTime(),
          pid: opts.pid || 0
        };
        const paramsArr = [];
        for (const key in params) {
          paramsArr.push(key + '=' + params[key]);
        }
        const host =
          config.env === 'development'
            ? 'https://chic.sy.soyoung.com'
            : 'https://chic.soyoung.com';
        if (opts.direct) {
          Vue.$toH5(
            `${host}/sys/html/cooperation/jump_ud.html?${paramsArr.join('&')}`
          );
          return;
        }
        const responseData = await checkUserJoinCJumpUrlApi({
          source: opts.source
        });
        // 已加C，且已走对接客服系统逻辑
        if (responseData && responseData.is_join_c && responseData.is_show) {
          const userInfo = uni.getStorageSync('user_info');
          if (!userInfo.uid || !userInfo.xyToken) {
            const isAuth = await Vue.$login().catch(() => null);
            if (!isAuth) {
              opts.unJoinFn && opts.unJoinFn();
              return;
            }
          }
          // 已加C，已登录，跳转客服会话页
          Vue.$toH5(
            `${host}/sys/html/cooperation/jump_ud.html?${paramsArr.join('&')}`
          );
        } else {
          opts.unJoinFn && opts.unJoinFn();
        }
      }, 300);
    };
    // 每次定位后上报当前小程序最新地理位置
    Vue.$saveWxLastLocation = Vue.prototype.$saveWxLastLocation =
      async function saveWxLastLocation(city_id) {
        if (city_id) {
          Vue.$request({
            url: '/syGroupBuy/user/saveWxAppUserInfo',
            data: {
              local_city_id: city_id
            }
          })
            .then(() => {})
            .finally(() => {});
        }
      };
  }
};
