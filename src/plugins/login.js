import Vue from 'vue';

// eslint-disable-next-line no-undef
const authDebug = AUTH_DEBUG;

/**
 * 接口 code 换取 openid、unionid、session_key 等信息
 * @param {*} code
 * @returns Promise
 */
function apiCode2Session(code) {
  return Vue.$request({
    url: '/Wxpassport/weixininfo',
    method: 'post',
    data: {
      code
    }
  })
    .then((res) => res.data)
    .catch(({ errMsg }) => {
      return {
        errorCode: -1000,
        errorMsg: '网络错误(100):' + errMsg,
        responseData: null
      };
    });
}

/**
 * openid、unionid、session_key 去新氧登录
 * @param {*} code
 * @returns Promise
 */
function apiSyLogin(data) {
  return Vue.$request({
    url: '/Wxpassport/loginwechat',
    method: 'post',
    data
  })
    .then((res) => res.data)
    .catch(({ errMsg }) => {
      return {
        errorCode: -1000,
        errorMsg: '网络错误(100):' + errMsg,
        responseData: null
      };
    });
}

/**
 * 校验微信登录是否过期
 * @returns Promise
 */
function checkWxSession() {
  return new Promise((resolve) => {
    uni.checkSession({
      success: () => resolve(true),
      fail: () => resolve(false)
    });
  });
}
/**
 * 获取登录凭证 code 的方法
 * @returns Promise
 */
function getCredentialsCode() {
  return new Promise((resolve, reject) => {
    uni.login({
      success: (res) => {
        if (res.code) {
          resolve(res.code);
        } else {
          reject(res.errMsg);
        }
      },
      fail: (res) => reject(res.errMsg)
    });
  });
}
/**
 * auth.code2Session使用code获取session_key,openid,unionid
 */
async function code2Session(code) {
  colorizeLog(`[auth]:微信登录成功，code值:${code}`);
  const { errorCode, errorMsg, responseData } = await apiCode2Session(code);
  return errorCode === 0 ? responseData : Promise.reject(errorMsg);
}

/**
 * 输出有颜色的log
 */
function colorizeLog(str, table = false) {
  if (process.env.NODE_ENV === 'development' && authDebug) {
    console.log(`%c${str}`, 'color:blue');
    if (table && console && console.table) console?.table(table);
  }
}

/**
 * 获取并且验证数据userInfo的数据有效性
 * 一旦出现异常，重新走链式调用
 */
function validUserStorageInfo() {
  return new Promise((resole, reject) => {
    uni.getStorage({
      key: 'user_info',
      success: ({ data, errMsg }) => {
        // 校验是否是完整的微信登录信息
        if (errMsg !== 'getStorage:ok') {
          reject();
          return;
        }
        if (
          // getStorage 一个未知的key会返回空字符串 ''
          data.toString() === '[object Object]' &&
          'openId' in data &&
          'unionId' in data &&
          'sessionKey' in data &&
          'checkKey' in data
        ) {
          colorizeLog(`[auth]:本地数据校验完整，返回：👇🏻👇🏻👇🏻`, data);
          resole(data);
        } else {
          colorizeLog(`[auth]:本地数据校验存在缺失，返回：👇🏻👇🏻👇🏻`, data);
          reject();
        }
      },
      fail: () => reject()
    });
  });
}
/**
 * 微信登录成功回调
 * @param {*} res
 * @returns userInfo
 */
function uniLoginSuccess(res) {
  colorizeLog(`[auth]:code2Session执行成功，返回：👇🏻👇🏻👇🏻`, res);
  const { openid, session_key, is_auth, check_key, unionid } = res;
  const userInfo = {
    openId: openid,
    unionId: unionid,
    sessionKey: session_key,
    checkKey: check_key,
    hasWxAvatar: !is_auth // is_auth为是否需要调起授权（用于获取微信头像），为true时需要调起授权（库里没有微信头像）后端@张志强
  };
  Vue.$setUserInfoToStorage(userInfo);
  return userInfo;
}

/**
 * 错误回调句柄
 * @param {*} msg
 * @returns null
 */
function uniLoginFail(msg) {
  // uni.showModal({
  //   content: msg,
  //   showCancel: false
  // });
  colorizeLog(`[auth]:微信登录失败:${msg}`);
  return msg;
}

/**
 * 获取登录基础参数
 * promise链
 * 1. 获取 code
 * 2. auth.code2Session 使用 code换session_key,union_id,openid
 * 3. 成功回调，处理返回数据
 * 4. 失败捕捉，promise链中错误的统一异常处理
 */
export async function getLoginBasicParameters() {
  await getCredentialsCode()
    .then(code2Session)
    .then(uniLoginSuccess)
    .catch(uniLoginFail);
}
/**
 * 微信登录方法
 * 三种状态：
 * 1. 登录态没有过期
 * 2. 登录态过期，尝试微信登录 wx.login，成功并获取code
 * 3. 登录态过期，尝试微信登录 wx.login，失败
 * @returns Promise<userInfo | null>
 */
async function _uniLogin() {
  const valid = await checkWxSession();
  colorizeLog(`[auth]:微信登录会话状态:${valid}`);
  /**
   * promise链
   * 1. 获取 code
   * 2. auth.code2Session 使用 code换session_key,union_id,openid
   * 3. 成功回调，处理返回数据
   * 4. 失败捕捉，promise链中错误的统一异常处理
   */
  const chain = () =>
    getCredentialsCode()
      .then(code2Session)
      .then(uniLoginSuccess)
      .catch(uniLoginFail);

  if (valid) {
    /**
     * 如果会话有效
     * 获取之前的登录状态信息，并且校验，三种情况 必须 重新走获取逻辑chain:
     * 1. getStorage 获取 fail
     * 2. getStorage 获取 success errMsg 不是 getStorage:ok
     * 3. getStorage 获取 success errMsg 是 getStorage:ok ,但内容缺失字段
     */
    return validUserStorageInfo().catch(chain);
  } else {
    // 会话过期重新获取
    return chain();
  }
}

/**
 * 微信登录方法:外部调用，一次 app.launch 只执行一次
 * 三种状态：
 * 1. 登录态没有过期
 * 2. 登录态过期，尝试微信登录 wx.login，成功并获取code
 * 3. 登录态过期，尝试微信登录 wx.login，失败
 * @returns Promise<userInfo | null>
 */
export function uniLogin() {
  if (!uniLogin.promise) {
    uniLogin.promise = _uniLogin();
  }
  return uniLogin.promise;
}
uniLogin.clear = function () {
  uniLogin.promise = null;
};

/**
 * 新氧平台的登录
 * @returns Promise<userInfo> | Promise.reject()
 */
async function _syLogin(flag) {
  // uniLogin捕捉处理了异常，
  const userInfo = await uniLogin();
  // 1. openid、unionid、check_key获取异常，也就是平台登录失败，返回 Promise<string>
  if (typeof userInfo === 'string') {
    return Promise.reject(`微信登录失败(node.1):${userInfo}`);
  }
  const { openId, unionId, checkKey } = userInfo;
  const { errorCode, errorMsg, responseData } = await apiSyLogin({
    open_id: openId,
    key: checkKey,
    union_id: unionId,
    is_user_exit: flag ? 0 : 1
  });
  // 2. 新氧平台登录异常，有可能是传参、服务问题、网络导致的异常，返回 Promise<null>
  if (errorCode !== 0) {
    return Promise.reject(errorMsg + '(node.2)');
  }
  // 正常的登录逻辑
  const { action } = responseData;
  switch (action) {
    case 'login_success': {
      // 在平台已经绑定过手机号，登录成功
      const { uid, xy_token } = responseData;
      Vue.$setUserInfoToStorage({
        xyToken: xy_token,
        uid
      });
      login_success();
      colorizeLog(`[auth]:新氧登录成功: uid:${uid},xy_token:${xy_token}`);
      return {
        ...userInfo,
        xyToken: xy_token,
        uid
      };
    }
    case 'new_user_bind_mobile': // 新用户，第一次登录
      return _navigateToLoginPage(action);
    case 'old_user_bind_mobile': // 老用户，但是没有绑定手机号
      return _navigateToLoginPage(action);
    default: {
      // 3.未知操作
      return Promise.reject(errorMsg + '(node.4)');
    }
  }
}

function _navigateToLoginPage(action) {
  colorizeLog(`[auth]:手机号未绑定，前往绑定手机号.${action}`);
  return new Promise((resolve, reject) => {
    uni.navigateTo({
      url: `/packageAccount/login?type=${
        action === 'new_user_bind_mobile' ? 1 : 3
      }`,
      events: {
        afterLoginSuccess() {
          const userInfo = uni.getStorageSync('user_info');
          const { uid, xyToken } = userInfo;
          if (uid && xyToken) {
            colorizeLog(
              `[auth]:绑定手机号成功，登录成功: uid:${uid},xy_token:${xyToken}`
            );
            login_success();
            setTimeout(() => {
              resolve(userInfo);
              // 300ms为了等待登录页面的返回动画，避免页面堆栈错误
            }, 300);
          } else {
            reject('绑定手机号页面，本地储存并没有uid和xytoken(node.3)');
          }
        },
        afterLoginFail: (msg) => {
          reject(msg);
        }
      },
      success(res) {
        res.eventChannel && res.eventChannel.emit('afterReachLogin');
      }
    });
  });
}

/**
 * 新氧平台的登录
 * @returns Promise<userInfo> | Promise.reject()
 */
export function syLogin(reLogin = false) {
  // 主动退出 不能再静默登录-以前
  if (uni.getStorageSync('clicked_my_page_signout_btn') === 1) {
    return _navigateToLoginPage('old_user_bind_mobile');
  }
  return _syLogin(reLogin).catch((msg) => {
    colorizeLog(`[auth]:新氧登录失败:${msg}`);
    return Promise.reject(msg);
  });
}

/**
 * 新氧平台的退出登录
 * @returns Promise<userInfo> | Promise.reject()
 */
export function sySignout() {
  Vue.$setUserInfoToStorage({
    // openId: '',
    // unionId: '',
    // sessionKey: '',
    // checkKey: '',
    xyToken: '',
    uid: '',
    nickName: '',
    avatarUrl: '',
    mobile: '',
    avatar: '',
    user_name: ''
  });
  uni.setStorageSync('clicked_my_page_signout_btn', 1);
  return new Promise((resolve, reject) => {
    const userInfo = uni.getStorageSync('user_info');
    if (userInfo && !userInfo.uid && !userInfo.xyToken) {
      resolve();
    } else {
      reject();
    }
  }).catch((msg) => {
    colorizeLog(`[auth]:新氧退出登录失败:${msg}`);
    return Promise.reject(msg);
  });
}

/**
 * 获取用户信息： avatar、mobile、user_name
 */
export async function getUserInfo() {
  const res = await Vue.$request({
    method: 'post',
    url: '/syGroupBuy/user/getUserInfo',
    data: {}
  }).catch((err) => {
    console.log(err);
    return {};
  });
  if (res?.data?.errorCode === 200 || res?.data?.errorCode === 0) {
    const { mobile, avatar, user_name } = res.data.responseData || {};
    Vue.$setUserInfoToStorage({
      mobile,
      avatar,
      user_name
    });
  }
}

export default {
  uniLogin,
  getUserInfo,
  syLogin,
  sySignout,
  getLoginBasicParameters
};

function login_success() {
  // 获取用户信息
  getUserInfo();
}
