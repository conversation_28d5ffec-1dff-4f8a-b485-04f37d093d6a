<template>
  <!-- 自营 -->
  <div class="self-operated">
    <img
      :src="
        gray
          ? 'https://static.soyoung.com/sy-pre/3qz39fptllu8t-1744009800634.png'
          : 'https://static.soyoung.com/sy-pre/hjmw3pern5-1743991800647.png'
      "
    />
  </div>
</template>

<script>
export default {
  props: {
    gray: {
      type: Boolean,
      default: false
    }
  },
  mounted() {},
  data() {
    return {};
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.self-operated {
  display: flex;
  align-items: center;
  img {
    width: 92rpx;
    height: 28rpx;
    margin-right: 6rpx;
  }
}
</style>
