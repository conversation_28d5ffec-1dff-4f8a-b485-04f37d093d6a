<template>
  <div>
    <BottomModal
      title="分享到"
      :visible="shareBarVisible"
      :bottom="detailsBottom"
      :showCancel="true"
      @close="onBottomModalClose"
    >
      <div class="share-box">
        <div class="share-type-list">
          <button class="item" @click="onShare" open-type="share">
            <image
              class="cover"
              src="https://static.soyoung.com/sy-design/weixin-31726735613365.png"
            />
            <text>微信好友</text>
          </button>
          <div
            class="item"
            :class="[fetchingCode ? 'disabled' : '']"
            @click="onPosterGen"
          >
            <image
              class="cover"
              src="https://static.soyoung.com/sy-design/dn9yxsl49te91726735613368.png"
            />
            <text>分享海报</text>
          </div>
        </div>
        <view class="button" @click="onBottomModalClose">
          <button>取消</button>
        </view>
      </div>
    </BottomModal>
    <Poster
      :visible.sync="posterGenVisible"
      :params="params"
      @afterPosterCreated="fetchingCode = false"
    />
  </div>
</template>
<script>
import BottomModal from '@/components/bottomModal.vue';
import Poster from '@/components/product/spu-poster.vue';
import { apiGetShareQrCode } from '@/api/productDetail';

export default {
  components: {
    BottomModal,
    Poster
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    opts: {
      title: String,
      cover: String,
      sku_id: Number
    }
  },
  data() {
    return {
      params: {},
      shareBarVisible: false,
      posterGenVisible: false,
      fetchingCode: true
    };
  },
  async mounted() {
    if (this.visible) {
      this.shareBarVisible = true;
      this.fetchCodeData();
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.shareBarVisible = true;
        this.fetchCodeData();
      }
    },
    posterGenVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
      }
      this.$emit('gaussianBlur', val);
    }
  },
  methods: {
    onBottomModalClose() {
      this.shareBarVisible = false;
      this.$emit('update:visible', false);
    },
    onShare() {
      this.$emit('reportStat', 'sy_wxtuan_op_product_info:share_panel_click', {
        btn_type: 1
      });
    },
    onShareTimeline() {
      // TODO 查看是否需要埋点
      // this.$emit('reportStat', 'sy_wxtuan_op_product_info:share_panel_click', {
      //   btn_type: 1
      // });
    },
    onPosterGen() {
      this.$emit('reportStat', 'sy_wxtuan_op_product_info:share_panel_click', {
        btn_type: 2
      });
      if (this.fetchingCode) {
        uni.showToast({
          title: '画报生成中,请稍等~',
          icon: 'none'
        });
        return;
      }
      this.shareBarVisible = false;
      setTimeout(() => {
        this.posterGenVisible = true;
      }, 100);
    },
    onPostClose() {
      this.$emit('update:visible', false);
    },
    async fetchCodeData() {
      console.log('fetchingCode', this.fetchingCode);
      if (!this.fetchingCode) {
        return;
      }
      console.log(this.opts, 'opts');
      const responseData = await apiGetShareQrCode(this.opts, {
        pusher_id: this.opts.pusher_id
      });
      if (responseData) {
        this.params = {
          title: this.opts.title,
          subtitle: this.opts.subtitle,
          cover: this.opts.cover,
          code: `data:image/jpeg;base64,${responseData.img}`
        };
        console.log('--->', this.params);
      }
      this.fetchingCode = false;
    }
  }
};
</script>
<style lang="less" scoped>
@px: 2rpx;

.share-box {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .share-type-list {
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    padding-top: 8 * @px;
    padding-bottom: 10 * @px;
  }

  .item {
    padding: 0;
    line-height: 1.2;
    flex-wrap: nowrap;
    display: flex;
    flex-direction: column;
    background-color: transparent;
    align-items: center;
    margin: 0;
    margin-left: 40 * @px;

    &:first-child {
      margin-left: 0;
    }

    &::after {
      border: none;
    }

    .cover {
      height: 45 * 2rpx;
      width: 45 * 2rpx;
    }

    > text {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #666666;
      letter-spacing: 0;
      font-weight: 400;
      padding-top: 10 * @px;
    }
  }

  .button {
    position: relative;
    height: 62 * @px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30rpx;
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    box-sizing: border-box;
    padding: 0 25 * @px;

    button {
      padding: 0;
      margin: 0;
      border-radius: 0;
      border: none;
      background-color: #333333;
      font-family: PingFangSC-Medium;
      font-size: 13 * @px;
      color: #ffffff;
      text-align: center;
      font-weight: 500;
      width: 100%;
      height: 42 * @px;
      line-height: 42 * @px;

      &:after {
        border: none;
      }
    }
    //&::before {
    //  content: '';
    //  position: absolute;
    //  top: 0;
    //  width: 100%;
    //  left: 0;
    //  border-bottom: 1rpx solid #f4f4f4;
    //}
  }
  .disabled {
    filter: grayscale(1);
    .text {
      color: #777;
    }
  }
}
</style>
