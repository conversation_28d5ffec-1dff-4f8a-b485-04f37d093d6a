<template>
  <div class="timeBox">
    <div class="timeItem">{{ days }}</div>
    天
    <div class="timeItem">{{ hours }}</div>
    :
    <div class="timeItem">{{ minutes }}</div>
    :
    <div class="timeItem">{{ seconds }}</div>
    后结束
  </div>
</template>

<script>
export default {
  props: {
    totalSeconds: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      remainingSeconds: this.totalSeconds,
      intervalId: null
    };
  },
  computed: {
    days() {
      return Math.floor(this.remainingSeconds / (60 * 60 * 24));
    },
    hours() {
      const hours = Math.floor((this.remainingSeconds / (60 * 60)) % 24);
      return hours < 10 ? `0${hours}` : hours;
    },
    minutes() {
      const minutes = Math.floor((this.remainingSeconds / 60) % 60);
      return minutes < 10 ? `0${minutes}` : minutes;
    },
    seconds() {
      const seconds = Math.floor(this.remainingSeconds % 60);
      return seconds < 10 ? `0${seconds}` : seconds;
    }
  },
  watch: {
    totalSeconds(newSeconds) {
      this.remainingSeconds = newSeconds;
    }
  },
  mounted() {
    this.intervalId = setInterval(() => {
      if (this.remainingSeconds > 0) {
        this.remainingSeconds--;
      } else {
        clearInterval(this.intervalId);
      }
    }, 1000);
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  }
};
</script>

<style scoped lang="scss">
.timeBox {
  display: flex;
  align-items: center;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #ffffff;
  .timeItem {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 38rpx;
    height: 30rpx;
    background: #e63348;
    border-radius: 6rpx;
    margin: 0 6rpx;
    font-weight: bold;
  }
}
</style>
