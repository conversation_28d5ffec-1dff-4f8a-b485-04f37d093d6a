<template>
  <view class="specification" v-if="specification.length">
    <!--    <view class="specification-title">包含项目</view>-->
    <view class="specification-content">
      <view
        class="specification-content-item"
        v-for="(item, index) in formatSpecification"
        :key="index"
      >
        <!-- 联合治疗和套餐的时候取child -->
        <view v-if="item.child.length" class="union">
          <view class="union-header" @tap="handleFold(item)">
            <view class="left">{{ item.sku_title }}</view>
            <view class="right">
              <text>{{ `x${item.times}` }}</text>
              <image
                v-if="item.child && item.child.length > 0"
                class="arrow"
                :style="{
                  transform: item.fold ? 'rotate(180deg)' : 'rotate(0deg)'
                }"
                src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
              />
            </view>
          </view>
          <view
            class="union-content"
            :style="{
              height: item.fold ? '0' : 'auto',
              display: item.fold ? 'none' : ''
            }"
          >
            <view
              class="union-item"
              v-for="(value, valueIndex) in item.child"
              :key="valueIndex"
            >
              <!-- item.child -->
              <!-- 联合治疗或套餐里面的复配品 -->
              <view v-if="value.items.length > 1" class="union-composite">
                <view
                  class="union-composite-title"
                  :class="{ 'unfold': !value.fold }"
                  @tap="handleFold(value)"
                  :style="{ 'margin-bottom': value.fold ? '0' : '' }"
                >
                  <view class="left">
                    <view class="left-type">
                      <view class="point"></view>
                      <view class="service">{{ `服务${valueIndex + 1}` }}</view>
                    </view>
                    <view class="name">{{ value.sku_title }}</view>
                  </view>
                  <view class="right">
                    <view>x{{ value.times }}</view>
                    <image
                      class="arrow"
                      :style="{
                        transform: value.fold
                          ? 'rotate(180deg)'
                          : 'rotate(0deg)'
                      }"
                      src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
                    />
                  </view>
                </view>
                <view
                  class="composite-content-wrap"
                  :style="{
                    height: value.fold ? '0' : 'auto',
                    display: value.fold ? 'none' : ''
                  }"
                >
                  <view
                    class="composite-content"
                    v-for="(composite, compositeIndex) in value.items"
                    :key="compositeIndex"
                  >
                    <view
                      class="composite-content-title"
                      @tap="handleFold(composite)"
                    >
                      <view class="left">
                        <view class="type">{{
                          composite.item_type === '1'
                            ? '主项目'
                            : `复配${compositeIndex}`
                        }}</view>
                        <view class="name">{{ composite.item_name }}</view>
                      </view>
                      <view class="right">
                        <image
                          class="arrow"
                          :style="{
                            transform: composite.fold
                              ? 'rotate(180deg)'
                              : 'rotate(0deg)'
                          }"
                          src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
                        />
                      </view>
                    </view>
                    <view
                      class="composite-item-wrap"
                      :style="{
                        height: composite.fold ? '0' : 'auto',
                        display: composite.fold ? 'none' : ''
                      }"
                    >
                      <view
                        class="composite-item"
                        v-for="(prop, propIndex) in composite.propertys"
                        :key="propIndex"
                      >
                        <view class="composite-item-prop">{{
                          prop.property_title
                        }}</view>
                        <view class="composite-item-value">{{
                          prop.property_values
                            .map((item) => item.property_value_title)
                            .join('、')
                        }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 联合治疗或套餐里面的单品 -->
              <view v-else class="union-single">
                <view
                  class="union-single-title"
                  @tap="handleFold(value.items[0])"
                  :style="{ 'margin-bottom': value.items[0].fold ? '0' : '' }"
                >
                  <view class="left">
                    <view class="left-type">
                      <view class="point"></view>
                      <view class="service">{{ `服务${valueIndex + 1}` }}</view>
                    </view>
                    <view class="name">{{ value.sku_title }}</view>
                  </view>
                  <view class="right">
                    <text>x{{ value.times }}</text>
                    <image
                      v-if="value.items[0].propertys.length > 0"
                      class="arrow"
                      :style="{
                        transform: value.items[0].fold
                          ? 'rotate(180deg)'
                          : 'rotate(0deg)'
                      }"
                      src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
                    />
                  </view>
                </view>
                <view
                  class="single-content"
                  :style="{
                    height: value.items[0].fold ? '0' : 'auto',
                    display: value.items[0].fold ? 'none' : ''
                  }"
                >
                  <view
                    class="single-item"
                    v-for="(prop, propIndex) in value.items[0].propertys"
                    :key="propIndex"
                  >
                    <view class="single-item-prop">{{
                      prop.property_title
                    }}</view>
                    <view class="single-item-value">{{
                      prop.property_values
                        .map((item) => item.property_value_title)
                        .join('、')
                    }}</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 单品的时候取items 且items.length = 1 -->
        <view
          v-if="item.items.length && item.items.length === 1"
          class="single"
        >
          <view
            class="single-header"
            :class="{
              'single-header-empty':
                !item.items[0].propertys.length && item.items[0].fold,
              'fold': item.items[0].fold
            }"
            @tap="handleFold(item.items[0])"
          >
            <view class="left">{{ item.sku_title }}</view>
            <view class="right">
              <view>{{ `x${item.times}` }}</view>

              <image
                v-if="item.items[0].propertys.length > 0"
                class="arrow"
                :style="{
                  transform: item.items[0].fold
                    ? 'rotate(180deg)'
                    : 'rotate(0deg)'
                }"
                src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
              />
            </view>
          </view>
          <view
            class="single-content"
            v-if="item.items[0].propertys.length"
            :style="{
              height: item.items[0].fold ? '0' : 'auto',
              display: item.items[0].fold ? 'none' : ''
            }"
          >
            <view
              class="single-item"
              v-for="(prop, propIndex) in item.items[0].propertys"
              :key="propIndex"
            >
              <view class="single-item-prop">{{ prop.property_title }}</view>
              <view class="single-item-value">{{
                prop.property_values
                  .map((item) => item.property_value_title)
                  .join('、')
              }}</view>
            </view>
          </view>
        </view>
        <!-- 复配品的时候取items 且items.length > 1-->
        <view
          v-if="item.items.length && item.items.length > 1"
          class="composite"
        >
          <view
            class="composite-header"
            :class="{ 'fold': item.fold }"
            @tap="handleFold(item)"
            :style="{ 'margin-bottom': item.fold ? '0' : '' }"
          >
            <view class="left">{{ item.sku_title }}</view>
            <view class="right">
              <view>{{ `x${item.times}` }}</view>

              <image
                class="arrow"
                :style="{
                  transform: item.fold ? 'rotate(180deg)' : 'rotate(0deg)'
                }"
                src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
              />
            </view>
          </view>
          <view
            class="composite-content-wrap"
            :style="{
              height: item.fold ? '0' : 'auto',
              display: item.fold ? 'none' : ''
            }"
          >
            <view
              class="composite-content"
              v-for="(composite, compositeIndex) in item.items"
              :key="compositeIndex"
            >
              <view
                class="composite-content-title"
                @tap="handleFold(composite)"
              >
                <view class="left">
                  <view class="type">{{
                    composite.item_type === '1'
                      ? '主项目'
                      : `复配${compositeIndex}`
                  }}</view>
                  <view class="name">{{ composite.item_name }}</view>
                </view>
                <view class="right">
                  <image
                    class="arrow"
                    :style="{
                      transform: composite.fold
                        ? 'rotate(180deg)'
                        : 'rotate(0deg)'
                    }"
                    src="https://static.soyoung.com/sy-design/2zfgcua7r2u7z1726282046862.png"
                  />
                </view>
              </view>
              <view
                class="item-wrap"
                :style="{
                  height: composite.fold ? '0' : 'auto',
                  display: composite.fold ? 'none' : ''
                }"
              >
                <view
                  class="composite-item"
                  v-for="(prop, propIndex) in composite.propertys"
                  :key="propIndex"
                >
                  <view class="composite-item-prop">{{
                    prop.property_title
                  }}</view>
                  <view class="composite-item-value">{{
                    prop.property_values
                      .map((item) => item.property_value_title)
                      .join('、')
                  }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Specification',
  props: {
    specification: {
      type: Array,
      required: true
    },
    isPackage: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {
    formatSpecification() {
      const newItem = this.specification.map((spec) => {
        if (spec.child) {
          // 联合治疗或者套餐
          spec.child.map((val) => {
            val.fold = true;
            val.items.map((item) => (item.fold = true));
            return {
              ...val
            };
          });
        }
        if (spec.items.length && spec.items.length > 1) {
          spec.fold = this.isPackage ? true : false;
          // 复配时取items
          spec.items.map((val) => {
            // 是套餐的时候，复配默认折叠
            val.fold = true;
            return {
              ...val
            };
          });
        } else {
          // 单品时取items
          spec.items.map((val) => {
            val.fold = this.isPackage ? true : false;
            return {
              ...val
            };
          });
        }
        return {
          ...spec
        };
      });
      // console.log(newItem, '看看格式化后的数据');
      return newItem;
    }
  },
  methods: {
    // 展开折叠
    handleFold(item) {
      console.log(item, 'item');
      if (
        (item.child && !item.child.length) ||
        (item.propertys && !item.propertys.length)
      ) {
        return;
      }
      this.$emit(
        'reportStat',
        'sy_wxtuan_tuan_product_info:xiangmu_card_open_click'
      );
      item.fold = !item.fold;
      this.$forceUpdate();
      // console.log(item, '看看展开折叠');
    },
    // 单品的展开折叠
    handleSingleFold(item) {
      if (this.isPackage) {
        this.$emit(
          'reportStat',
          'sy_wxtuan_tuan_product_info:xiangmu_card_open_click'
        );
        item.fold = !item.fold;
        this.$forceUpdate();
        // console.log(item, '看看展开折叠');
      }
    }
  }
};
</script>

<style lang="less" scoped>
@px: 2rpx;

.specification {
  width: calc(100vw - 30 * @px);
  background: #fff;
  .specification-title {
    margin-bottom: 20 * @px;
    letter-spacing: 0;
    font-family: PingFangSC-Semibold;
    font-size: 16 * @px;
    color: #bababa;
    font-weight: 600;
  }

  .specification-content {
    .specification-content-item {
      margin-bottom: 10 * @px;
      // width: 345 * @px;
      position: relative;
      //border-radius: 8 * @px;
      border-width: 0 !important;
      &::after {
        pointer-events: none;
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 200%;
        height: 200%;
        border: 1 * @px solid #f2f2f2;
        //border-radius: 16 * @px;
        box-sizing: border-box;
        transform-origin: 0 0;
        transform: scale(0.5);
      }
      .single-header,
      .union-header,
      .composite-header {
        width: 100%;
        padding: 10 * @px;
        box-sizing: border-box;
        //border-radius: 8 * @px 8 * @px 0 0;
        background-color: #f8f8f8;
        display: flex;
        justify-content: space-between;
        &.fold {
          //border-radius: 8 * @px;
        }
        .left {
          width: 290 * @px;
          white-space: normal;
          font-family: PingFangSC-Medium;
          font-size: 15 * @px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 500;
        }
        .right {
          height: 21 * @px;
          display: flex;
          align-items: center;
          font-family: PingFangSC-Medium;
          font-size: 15 * @px;
          color: #333333;
          letter-spacing: 0.5 * @px;
          text-align: right;
          font-weight: 500;

          .arrow {
            width: 8 * @px;
            height: 10 * @px;
            margin-left: 10 * @px;
          }
        }
      }

      .single-header,
      .composite-header {
        .left {
          width: 270 * @px;
          white-space: wrap;
        }
      }

      // 联合治疗或套餐
      .union {
        .union-header {
          margin-bottom: 15 * @px;
        }
        .union-content {
          //padding-bottom: 20 * @px;
          .union-item {
            padding-bottom: 15 * @px;
            padding-left: 10 * @px;
            padding-right: 10 * @px;
          }

          // 联合治疗或套餐里面的单品
          .union-single {
            .union-single-title {
              display: flex;
              justify-content: space-between;
              width: 325 * @px;
              padding: 7 * @px 10 * @px;
              box-sizing: border-box;
              background-color: #f8f8f8;
              position: relative;

              &:before {
                content: '';
                position: absolute;
                width: 2 * @px;
                height: 100%;
                background-color: #89dc65;
                left: 0;
                top: 0;
              }
              //border-radius: 6 * @px;
              &.fold {
                //border-radius: 6 * @px;
              }
              .left {
                font-family: PingFangSC-Medium;
                font-size: 13 * @px;
                color: #333333;
                letter-spacing: 0.43 * @px;
                font-weight: 500;
                display: flex;
                .left-type {
                  height: 18 * @px;
                  display: flex;
                  align-items: center;
                  position: relative;
                  .point {
                    //position: absolute;
                    //left: 0;
                    //top: 0;
                    //height: 100%;
                    //width: 2 * @px;
                    //background-color: #89DC65;
                    //width: 6 * @px;
                    //height: 6 * @px;
                    //background: #00a077;
                    //border-radius: 50%;
                    //margin-right: 6 * @px;
                  }

                  .service {
                    margin-right: 10 * @px;
                  }
                }

                .name {
                  width: 190 * @px;
                  //overflow: hidden;
                  // text-overflow: ellipsis;
                  white-space: break-spaces;
                }
              }

              .right {
                display: flex;
                align-items: center;
                height: 18 * @px;
                font-family: PingFangSC-Medium;
                font-size: 13 * @px;
                color: #333333;
                letter-spacing: 0.43 * @px;
                text-align: right;
                font-weight: 500;

                .arrow {
                  width: 9 * @px;
                  height: 7 * @px;
                  margin-left: 9 * @px;
                }
              }
            }
            .single-content {
              padding: 0 5 * @px 5 * @px;
              margin-top: 15 * @px;
            }
            .single-item {
              display: flex;
              margin-bottom: 10 * @px;
              font-family: PingFangSC-Regular;
              font-size: 13 * @px;
              color: #333333;
              letter-spacing: 0.43 * @px;
              font-weight: 400;

              &:last-of-type {
                margin-bottom: 0;
              }

              .single-item-prop {
                min-width: 54 * @px;
                color: #8c8c8c;
                margin-right: 10 * @px;
              }
              .single-item-value {
                text-align: left;
              }
            }
          }

          // 联合治疗或套餐里面的复配
          .union-composite {
            position: relative;
            border-width: 0 !important;
            border-radius: 6 * @px;
            &::after {
              pointer-events: none;
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              width: 200%;
              height: 200%;
              border: 1 * @px solid #e2e4e8;
              //border: 1 * @px solid green;
              border-radius: 12 * @px;
              box-sizing: border-box;
              transform-origin: 0 0;
              transform: scale(0.5);
            }
            .union-composite-title {
              display: flex;
              justify-content: space-between;
              width: 325 * @px;
              padding: 7 * @px 10 * @px;
              box-sizing: border-box;
              background: rgba(239, 242, 247, 0.56);
              border-radius: 6 * @px;
              font-family: PingFangSC-Medium;
              font-size: 13 * @px;
              color: #222222;
              letter-spacing: 0.43 * @px;
              font-weight: 500;
              &.unfold {
                border-radius: 6 * @px 6 * @px 0 0;
              }
              .left {
                display: flex;
                .left-type {
                  display: flex;
                  align-items: center;
                  height: 18 * @px;
                  .point {
                    width: 6 * @px;
                    height: 6 * @px;
                    background: #00a077;
                    border-radius: 50%;
                    margin-right: 6 * @px;
                  }

                  .service {
                    margin-right: 10 * @px;
                  }
                }
                .name {
                  width: 190 * @px;
                  //overflow: hidden;
                  // text-overflow: ellipsis;
                  white-space: break-spaces;
                }
              }

              .right {
                display: flex;
                align-items: center;
                height: 18 * @px;
                .arrow {
                  width: 10 * @px;
                  height: 6 * @px;
                  margin-left: 10 * @px;
                }
              }
            }
            .composite-content-wrap {
              padding: 0 10 * @px 15 * @px;
              .composite-content {
                .composite-content-title {
                  display: flex;
                  justify-content: space-between;
                  // align-items: center;
                  width: 305 * @px;
                  // height: 32 * @px;
                  padding: 7 * @px 10 * @px;
                  box-sizing: border-box;
                  background: rgba(239, 242, 247, 0.56);
                  border-radius: 6px;
                  font-family: PingFangSC-Medium;
                  font-size: 13 * @px;
                  color: #222222;
                  letter-spacing: 0.43 * @px;
                  font-weight: 500;
                  margin-top: 15 * @px;
                  .left {
                    display: flex;

                    .type {
                      margin-right: 10 * @px;
                      white-space: nowrap;
                    }

                    .name {
                      width: 215 * @px;
                      //overflow: hidden;
                      // text-overflow: ellipsis;
                      white-space: break-spaces;
                    }
                  }

                  .right {
                    display: flex;
                    align-items: center;
                    height: 18 * @px;

                    .arrow {
                      width: 10 * @px;
                      height: 6 * @px;
                    }
                  }
                }

                .composite-item {
                  display: flex;
                  font-family: PingFangSC-Regular;
                  font-size: 13 * @px;
                  color: #777777;
                  letter-spacing: 0.43 * @px;
                  font-weight: 400;
                  margin-top: 10 * @px;
                  &:last-of-type {
                    margin-bottom: 0;
                  }
                  .composite-item-prop {
                    min-width: 54 * @px;
                    margin-right: 10 * @px;
                  }
                  .composite-item-value {
                    color: #222222;
                    text-align: left;
                  }
                }
              }
            }
          }
        }
      }

      // 单品
      .single {
        .single-header-empty {
          //border-radius: 8 * @px 8 * @px 8 * @px 8 * @px;
        }
        .single-header {
          box-sizing: border-box;
          display: flex;
          .left {
          }
          .right {
            display: flex;
            align-items: center;
            .arrow {
              width: 9 * @px;
              height: 7 * @px;
              margin-left: 9 * @px;
            }
          }
          &.fold {
            //border-radius: 8 * @px;
          }
        }
        .single-content {
          margin-top: 15 * @px;
          padding: 0 10 * @px 5 * @px;
          box-sizing: border-box;
          .single-item {
            display: flex;
            margin-bottom: 10 * @px;
            font-family: PingFangSC-Regular;
            font-size: 13 * @px;
            color: #222222;
            letter-spacing: 0.43 * @px;
            font-weight: 400;
            .single-item-prop {
              min-width: 54 * @px;
              color: #777777;
              margin-right: 10 * @px;
            }
            .single-item-value {
              text-align: left;
            }
          }
        }
      }

      // 复配品
      .composite {
        .composite-content-wrap {
          padding: 0 10 * @px 15 * @px;
          .composite-content {
            .composite-content-title {
              display: flex;
              justify-content: space-between;
              width: 325 * @px;
              // height: 32 * @px;
              padding: 7 * @px 10 * @px;
              margin-top: 15 * @px;
              box-sizing: border-box;
              background: rgba(239, 242, 247, 0.56);
              border-radius: 6 * @px;
              font-family: PingFangSC-Medium;
              font-size: 13 * @px;
              color: #222222;
              letter-spacing: 0.43 * @px;
              font-weight: 500;

              .left {
                display: flex;
                white-space: nowrap;

                .type {
                  margin-right: 10 * @px;
                }
                .name {
                  width: 223 * @px;
                  //overflow: hidden;
                  // text-overflow: ellipsis;
                  white-space: break-spaces;
                }
              }

              .right {
                height: 18 * @px;
                display: flex;
                align-items: center;
                .arrow {
                  width: 10 * @px;
                  height: 6 * @px;
                }
              }
            }

            .composite-item {
              display: flex;
              margin-top: 10 * @px;
              font-family: PingFangSC-Regular;
              font-size: 13 * @px;
              color: #222;
              letter-spacing: 0.43 * @px;
              font-weight: 400;

              &-prop {
                min-width: 54 * @px;
                color: #777;
                margin-right: 10 * @px;
                flex-wrap: nowrap;
              }

              &-value {
                text-align: left;
              }
            }
          }
        }
      }
    }
  }
}

.arrow {
  min-width: 9 * @px !important;
  height: 7 * @px !important;
  margin-left: 9 * @px;
}
</style>
