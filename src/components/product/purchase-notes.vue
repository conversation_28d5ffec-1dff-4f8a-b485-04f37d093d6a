<template>
  <view class="purchase-notes">
    <view class="purchase-notes-title">{{ purchase_notes.title }}</view>
    <view
      class="purchase-notes-content"
      :style="{
        height: isFold ? '320rpx' : 'auto',
        overflow: isFold ? 'hidden' : 'auto'
      }"
    >
      <view
        class="item"
        v-for="(item, index) in purchase_notes.list"
        :key="index"
      >
        <view class="item-title">{{ item.title }}</view>
        <text class="item-desc">{{ item.desc }}</text>
      </view>
    </view>
    <!--    <view class="fold" @click="handleFold">-->
    <!--      <text>{{ isFold ? '展开更多' : '收起更多' }}</text>-->
    <!--      <image-->
    <!--        class="arrow"-->
    <!--        :style="{ transform: isFold ? 'rotate(0deg)' : 'rotate(180deg)' }"-->
    <!--        src="https://static.soyoung.com/sy-design/akmw6ytzxo8g1717661039303.png"-->
    <!--      />-->
    <!--    </view>-->
  </view>
</template>

<script>
export default {
  name: 'PurchaseNotes',
  props: {
    purchase_notes: {
      type: Object,
      default: () => {}
    },
    reportStat: {
      type: Function
    }
    // productId: {
    //   type: [Number, String]
    // },
    // productType: {
    //   type: [Number, String]
    // }
  },
  data() {
    return {
      isFold: false
    };
  },
  methods: {
    handleFold() {
      console.log(this.isFold, 'isFold');
      this.$emit('reportStat', 'sy_wxtuan_tuan_product_new:notice_click');
      this.isFold = !this.isFold;
    }
  }
};
</script>

<style lang="less" scoped>
@px: 2rpx;
.purchase-notes {
  width: 100%;
  margin-top: 10 * @px;
  background: #ffffff;
  padding: 15 * @px;
  box-sizing: border-box;

  .purchase-notes-title {
    letter-spacing: 0;
    font-weight: 600;
    margin-bottom: 14 * @px;
    font-family: PingFangSC-Semibold;
    font-size: 16px;
    color: #030303;
  }

  .purchase-notes-content {
    transition: height 0.3s;
    line-height: 20 * @px;
    .item {
      margin-bottom: 10 * @px;
      .item-title {
        font-family: PingFangSC-Medium;
        font-size: 13 * @px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20 * @px;
        font-weight: 500;
        margin-bottom: 5 * @px;
      }
      .item-desc {
        font-family: PingFangSC-Regular;
        font-size: 12 * @px;
        color: #8c8c8c;
        letter-spacing: 0;
        line-height: 20 * @px !important;
        font-weight: 400;
        // height: ;
      }
    }
  }
  .fold {
    position: relative;
    display: flex;
    height: 20 * @px;
    justify-content: center;
    align-items: center;
    margin-top: 15 * @px;
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    color: #646464;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    .arrow {
      width: 10 * @px;
      height: 6 * @px;
      margin-left: 3 * @px;
    }
    &::before {
      content: '';
      width: 100%;
      height: 12 * @px;
      // background: #fff;
      background-image: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0) 0%,
        #ffffff 100%
      );
      position: absolute;
      left: 0;
      top: -26 * @px;
      // bottom: 15 * @px;
      z-index: 1;
    }
  }
}
</style>
