<template>
  <view class="timeBox" :style="{ color }">
    {{ days }}
    {{ hours }} :{{ minutes }} :{{ seconds
    }}<block v-if="hideMilliseconds"> :{{ milliseconds }}</block>
  </view>
</template>

<script>
export default {
  props: {
    totalSeconds: {
      type: Number,
      required: true
    },
    hideMilliseconds: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: '#ffffff'
    }
  },
  data() {
    return {
      intervalId: null,
      remainingTime: this.totalSeconds * 1000
    };
  },
  computed: {
    days() {
      const days = Math.floor(this.remainingTime / (60 * 60 * 24 * 1000));
      return days > 0 ? `${days} 天` : '';
    },

    hours() {
      const hours = Math.floor((this.remainingTime / (60 * 60 * 1000)) % 24);
      return hours < 10 ? `0${hours}` : hours;
    },
    minutes() {
      const minutes = Math.floor((this.remainingTime / (60 * 1000)) % 60);
      return minutes < 10 ? `0${minutes}` : minutes;
    },
    seconds() {
      const seconds = Math.floor((this.remainingTime / 1000) % 60);
      return seconds < 10 ? `0${seconds}` : seconds;
    },
    milliseconds() {
      return Math.floor(this.remainingTime % 1000) / 100;
    }
  },
  watch: {
    // totalSeconds(newSeconds) {
    //   this.remainingTime = newSeconds * 1000;
    // }
  },
  mounted() {
    this.intervalId = setInterval(() => {
      if (this.remainingTime > 0) {
        this.remainingTime -= 100;
      } else {
        clearInterval(this.intervalId);
      }
    }, 100);
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  }
};
</script>

<style scoped lang="scss">
.timeBox {
  display: flex;
  align-items: center;
  font-family: OutFit-Regular;
  font-size: 24rpx;
  letter-spacing: 0;
  font-weight: 500;
  white-space: nowrap;
}
</style>
