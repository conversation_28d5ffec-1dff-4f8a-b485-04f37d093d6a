<template>
  <div class="itemBox">
    <div class="item">
      <div class="left">
        <div v-if="cardData.type === '1'" class="number price">
          {{ cardData.discount_value }}
        </div>
        <div v-else class="number discount">{{ cardData.discount_rate }}</div>
        <div class="min">{{ cardData.threshold_notice }}</div>
      </div>
      <div class="center"></div>
      <div class="right">
        <div class="name">
          <div class="tag">红包</div>
          {{ cardData.name }}
        </div>
        <div class="couponsTime">{{ cardData.use_time_notice }}</div>
        <!-- 红包的话 -->
        <div
          class="rule"
          :class="{
            fold
          }"
          @click="onRuleClick"
        >
          使用规则
        </div>
      </div>
      <block>
        <div
          v-if="!cardData.get_yn"
          class="primary-btn getCoupon"
          @click="onReceive"
        >
          领取
        </div>
        <div v-else class="received"></div>
      </block>
    </div>
    <!-- 下面的规则折叠层 -->
    <div class="rule-box" v-if="fold">
      <div class="rule-text">{{ cardData.desc }}</div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { apiGetYuYueCoupon } from '@/api/productDetail';
export default {
  mounted() {
    this.$registerExposure(
      '.getCoupon',
      () => {
        this.$reportData({
          info: 'sy_wxtuan_tuan_product:red_pop_get_exposure',
          ext: {}
        });
      },
      this
    );
  },
  data() {
    return {
      fold: false // 使用规则flag
    };
  },
  props: {
    cardData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapGetters(['isLogin'])
  },
  methods: {
    async checkSession() {
      if (!this.isLogin) {
        // 校验登录
        this.$emit('changePageLoadingStatus', true);
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        this.$emit('changePageLoadingStatus', true);
        return isAuth;
      }
    },
    async onReceive() {
      this.$emit('reportStat', 'sy_wxtuan_tuan_product:red_pop_get_click');
      const login = await this.checkSession();
      if (!login) return;
      const res = await apiGetYuYueCoupon({
        id: this.cardData.id
      });
      const { errorCode, errorMsg, responseData } = res;
      if ((+errorCode === 200 || +errorCode === 0) && responseData) {
        this.$emit('refreshPage');
      } else {
        uni.showModal({
          content: errorMsg,
          showCancel: false
        });
      }
    },
    onRuleClick() {
      this.fold = !this.fold;
    }
  }
};
</script>

<style lang="less" scoped>
@theme-color: #ff4556;
.itemBox {
  margin-top: 20rpx;
}
.couponsTime {
  display: block;
  font-family: PingFangSC-Regular;
  font-size: 22rpx;
  color: #666666;
  margin-top: 10rpx;
  line-height: 32rpx;
}
.item {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: 113 * 2rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    box-sizing: border-box;
    flex-shrink: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-content: center;
    height: 113 * 2rpx;
    width: 94 * 2rpx;
    border: 1px solid #ffddde;
    border-right: none;
    background: #fff2f2;
    border-top-left-radius: 16rpx;
    border-bottom-left-radius: 16rpx;
    .number {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      margin-bottom: -10rpx;
      font-style: normal;
      font-size: 50rpx;
      color: @theme-color;
      text-align: center;
      font-weight: 600;
    }
    .price {
      &::before {
        vertical-align: 4%;
        margin-right: -4rpx;
        content: '￥';
        font-size: 26rpx;
        color: @theme-color;
      }
    }
    .discount {
      &::after {
        vertical-align: 4%;
        margin-right: -4rpx;
        content: '折';
        font-size: 26rpx;
        color: @theme-color;
      }
    }
    .min {
      font-size: 24rpx;
      color: #999999;
      text-align: center;
      font-weight: 400;
      padding-left: 10rpx;
    }
  }
  .center {
    flex-shrink: 0;
    margin: -1px 0;
    height: 113 * 2rpx;
    width: 28rpx;
    background: url(https://static.soyoung.com/sy-pre/line-1690524600769.png)
      no-repeat center center transparent;
    background-size: 28rpx 113 * 2rpx;
  }
  .right {
    position: relative;
    flex: 1;
    height: 113 * 2rpx;
    box-sizing: border-box;
    padding-left: 14rpx;
    border: 1px solid #ffddde;
    border-left: none;
    background: #fff2f2;
    border-top-right-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
    .name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-top: 44rpx;
      color: #222222;
      line-height: 42rpx;
      height: 42rpx;
      font-weight: 600;
      // background: rgba(0, 0, 0, 0.1);
      .tag {
        position: relative;
        display: inline-block;
        margin-right: 10rpx;
        vertical-align: 14%;
        text-align: center;
        font-size: 20rpx;
        width: 60rpx;
        line-height: 32rpx;
        color: @theme-color;
        background: rgba(#ee6268, 0.15);
        border-radius: 4rpx;
        font-weight: 500;
      }
    }
    .rule {
      position: relative;
      padding-top: 26rpx;
      width: 110rpx;
      font-size: 20rpx;
      color: #999999;
      font-weight: 400;
      line-height: 28rpx;
      &.no-arrow {
        width: 180rpx;
        &::after {
          display: none;
        }
      }
      &::before {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        height: 80rpx;
        width: 110rpx;
        // background: rgba(0, 0, 0, 0.1);
        z-index: 2;
      }
      &::after {
        vertical-align: 2rpx;
        margin-left: 4rpx;
        display: inline-block;
        content: '';
        background: url(https://static.soyoung.com/sy-pre/arrow-1685340600733.png)
          no-repeat center center transparent;
        background-size: contain;
        height: 8rpx;
        width: 14rpx;
        transition: transform 0.3s;
      }
      &.fold::after {
        transform: rotate(180deg);
      }
    }
  }
}
.received {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 108rpx;
  height: 84rpx;
  background: url(https://static.soyoung.com/sy-design/92fmuhyc8ix41717488260783.png)
    no-repeat center / 100% 100%;
}
.primary-btn {
  position: absolute;
  top: 130rpx;
  right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 68 * 2rpx;
  height: 28 * 2rpx;
  background: @theme-color;
  border-radius: 28rpx;
  font-size: 26rpx;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
  z-index: 6;
}
.rule-box {
  box-sizing: border-box;
  width: calc(100% - 2px);
  height: auto;
  margin: -14rpx auto 0;
  padding: 30rpx 24rpx 16rpx;
  border: 1px solid #ffddde;
  border-radius: 0 0 16rpx 16rpx;
  background: #fff2f2;
  overflow: hidden;
  z-index: 0;
  .rule-text {
    margin: 0 auto;
    width: 100%;
    font-size: 20rpx;
    color: #999999;
    line-height: 32rpx;
    font-weight: 400;
    overflow: hidden;
  }
}
</style>
