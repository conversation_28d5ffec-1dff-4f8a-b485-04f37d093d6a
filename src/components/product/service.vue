<template>
  <view class="service-process">
    <!-- <view class="service-process-title">{{ service_process.title }}</view> -->
    <image
      :src="service_process.img"
      class="service-process-img"
      mode="widthFix"
    />
  </view>
</template>

<script>
export default {
  name: 'ServiceProcess',
  props: {
    service_process: Object
  },
  data() {
    return {};
  }
};
</script>

<style scoped lang="less">
@px: 2rpx;
.service-process {
  //margin-top: 10 * @px;
  padding: 15 * @px 0;
  background-color: transparent;
  .service-process-img {
    width: calc(100vw - 16 * @px) !important;
  }
}
</style>
