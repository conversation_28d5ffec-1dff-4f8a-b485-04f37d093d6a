<template>
  <view class="applicable-stores">
    <view v-if="isPackage" class="applicable-stores-warn">
      <text>不同项目适用门店以实际预约为准，</text>
      <text @click="viewDetails"> 了解详情> </text>
    </view>
    <view class="applicable-stores-title">
      <view class="left"
        >适用门店<text>{{ `（${totalHospitalList.length}家）` }}</text></view
      >
      <view class="right" @click="handleChooseCity">
        <text>{{ selectCityName }}</text>
        <image
          src="https://static.soyoung.com/sy-design/3dhi453czg4cw1726026826755.png"
        />
      </view>
    </view>
    <!-- 城市列表中没有定位城市或没有当前选择机构所在城市时展示改tips -->
    <!--    <view class="tips" v-if="!hiddenTips">-->
    <!--      当前{{ cityName }}暂无可服务机构，已为您展示“{{ selectCityName }}”服务机构-->
    <!--    </view>-->
    <!-- 套餐时 项目tab 和 tips -->
    <!--    <view class="applicable-stores-project" v-if="isPackage">-->
    <!--      &lt;!&ndash; 套餐的时候展示项目tab&ndash;&gt;-->
    <!--      <view class="project" v-if="projects.length">-->
    <!--        <view-->
    <!--          class="project-item"-->
    <!--          :class="{ active: projectId === item.sku_id }"-->
    <!--          v-for="item in projects"-->
    <!--          :key="item.sku_id"-->
    <!--          @click="toggleProject(item)"-->
    <!--        >-->
    <!--          {{ item.title }}-->
    <!--        </view>-->
    <!--      </view>-->
    <!--      &lt;!&ndash; 套餐时的 tips &ndash;&gt;-->
    <!--      <view class="tips">-->
    <!--        套餐不同项目服务门店存在差异，请切换项目查看适用门店。-->
    <!--      </view>-->
    <!--    </view>-->
    <!-- 有机构 必然有城市 -->
    <view class="applicable-stores-content" v-if="hospitalList.length">
      <view
        class="hospital-item"
        v-for="(item, index) in hospitalList"
        :key="index"
        :data-id="item.hospital_id"
        @tap="goHospitalMap(item)"
      >
        <!--<image class="hospital-icon" :src="item.icon" />-->
        <view class="hospital-info">
          <view class="hospital-name">
            <text>{{ item.hospital_name }}</text>
            <image
              v-if="Number(item.hospital_tag_type) === 1"
              src="https://static.soyoung.com/sy-pre/knze6xudthql-1743991800647.png"
              mode="heightFix"
            ></image>
            <image
              v-else-if="Number(item.hospital_tag_type) === 2"
              src="https://static.soyoung.com/sy-pre/hjmw3pern5-1743991800647.png"
              mode="heightFix"
            ></image>
          </view>
          <view class="hospital-addr">{{ item.hospital_addr }}</view>
          <!-- <image
            v-if="item.has_attest"
            class="hospital-verify"
            mode="widthFix"
            src="https://static.soyoung.com/sy-design/10rijhogwr0wm1717468738009.png"
          /> -->
        </view>
        <view class="hospital-distance">
          <image
            class="hospital-distance-img"
            mode="widthFix"
            src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1725959023090.png"
          />
          <text class="hospital-distance-text">{{ item.distance_str }}</text>
        </view>
      </view>
    </view>
    <!-- 机构为空，有城市 -->
    <view class="applicable-hospital-empty" v-else-if="citiesList.length">
      <view class="empty-text">暂无门店</view>
      <button @click="handleChooseCity">切换城市</button>
    </view>
    <!-- 没有机构也没有城市 -->
    <view class="applicable-stores-empty" v-else>
      <view class="empty-text">暂无门店</view>
    </view>
    <view class="fold" @click="handleFold" v-if="totalHospitalList.length > 2">
      <text>{{ isFold ? '全部门店' : '收起门店' }}</text>
      <image
        class="arrow"
        :style="{ transform: `rotate(${!isFold ? '180deg' : '0deg'})` }"
        src="https://static.soyoung.com/sy-design/3u5f7j35qkn711726026826505.png"
      />
    </view>
    <!-- 服务城市 -->
    <ChooseCity
      ref="chooseCity"
      :visible="chooseCityVisible"
      :defaultCityId="selectCityId"
      :options="citiesList"
      @visible="changeVisible"
      @change="cityChange"
    ></ChooseCity>
    <Popup ref="project-applicable-stores" @mask-click="closeDetails">
      <view class="project-applicable-stores">
        <view class="project-applicable-stores-header">
          <text>项目适用门店</text>
          <image
            src="https://static.soyoung.com/sy-design/bzsokyai5osd1727089563772.png"
            @click="closeDetails"
          ></image>
        </view>
        <scroll-view class="project-applicable-stores-product-list" scroll-x>
          <view
            v-for="(item, index) in projects"
            class="project-applicable-stores-product-item"
            :class="{
              'project-applicable-stores-product-item-select':
                projectId === item.sku_id
            }"
            :key="index"
            @click="toggleProject(item)"
          >
            {{ item.title }}
          </view>
        </scroll-view>
        <!-- 测试数据需删除 -->
        <view class="project-applicable-stores-hospital-list-title">
          {{ selectCityName }} 适用门店（{{ hospitalPopupList.length }}）
        </view>
        <scroll-view class="project-applicable-stores-hospital-list" scroll-y>
          <view
            class="hospital-item"
            v-for="(item, index) in hospitalPopupList"
            :key="index"
            :data-id="item.hospital_id"
            @tap="goHospitalMap(item)"
          >
            <!--<image class="hospital-icon" :src="item.icon" />-->
            <view class="hospital-info">
              <view class="hospital-name">
                <text>{{ item.hospital_name }}</text>
                <image
                  v-if="Number(item.hospital_tag_type) === 1"
                  src="https://static.soyoung.com/sy-pre/knze6xudthql-1743991800647.png"
                  mode="heightFix"
                ></image>
                <image
                  v-else-if="Number(item.hospital_tag_type) === 2"
                  src="https://static.soyoung.com/sy-pre/hjmw3pern5-1743991800647.png"
                  mode="heightFix"
                ></image>
              </view>
              <view class="hospital-addr">{{ item.hospital_addr }}</view>
              <!-- <image
                v-if="item.has_attest"
                class="hospital-verify"
                mode="widthFix"
                src="https://static.soyoung.com/sy-design/10rijhogwr0wm1717468738009.png"
              /> -->
            </view>
            <view class="hospital-distance">
              <image
                class="hospital-distance-img"
                mode="widthFix"
                src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1725959023090.png"
              />
              <text class="hospital-distance-text">{{
                item.distance_str
              }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </Popup>
  </view>
</template>

<script>
import {
  GetChainCityList,
  GetHospitalList,
  GetChainIngredientsList
} from '@/api/productDetail';
// import ChooseCity from './choose-city.vue';
import ChooseCity from './choose-city.vue';
import Popup from '@/components/uni/popup.vue';
export default {
  name: 'ApplicableStores',
  props: {
    userInfo: {
      required: true,
      type: Object
    },
    skuId: {
      required: true,
      type: [Number, String]
    },
    parentIngredientsId: {
      required: true,
      type: [Number, String]
    },
    isPackage: {
      required: true,
      type: Boolean
    }
  },
  components: {
    Popup,
    ChooseCity
  },
  data() {
    return {
      citiesList: [],
      hospitalList: [],
      hospitalPopupList: [], // 项目适用门店底弹窗数据，与 hospitalList 结构一致
      projects: [],
      chooseCityVisible: false, // 选择城市窗口
      projectId: '',
      cityId: null,
      cityName: '',
      isFold: false,
      totalHospitalList: [], // 所有的机构列表，用于展开折叠
      ingredientsId: null, // 原料id
      selectCityId: null,
      selectCityName: '',
      hiddenTips: false
    };
  },
  watch: {
    async skuId(newVal) {
      console.log(newVal, 'skuId');
      this.ingredientsId = this.parentIngredientsId;
      await this.getChainCityList();
      await this.getHospitalList();
      if (this.isPackage) {
        this.getChainIngredientsList();
      }
    }
  },
  async mounted() {
    this.ingredientsId = this.parentIngredientsId;
    console.log(this.userInfo.hospital, 'hospital');
    /** @since 10.0.2 为什么要获取 this.userInfo.hospital 中的 city_id 数据，外层添加判断，防止调用接口传递 city_id = NaN */
    if (this.userInfo?.hospital?.city_id) {
      this.cityId = Number(this.userInfo.hospital.city_id);
    } else {
      this.cityId = Number(this.userInfo.cityId);
    }
    // this.cityId = this.userInfo.hospital
    //   ? Number(this.userInfo.hospital.city_id)
    //   : Number(this.userInfo.cityId);
    console.log(this.cityId, 'this.cityId');
    const city_name =
      this.userInfo.hospital && this.userInfo.hospital.city_name
        ? this.userInfo.hospital.city_name
        : this.userInfo.cityName;
    console.log(city_name, 'city_name');
    this.cityName =
      this.cityId === 0 || this.cityId === 1 ? '' : `“${city_name}”`;
    await this.getChainCityList();
    if (Array.isArray(this.citiesList) && this.citiesList.length > 0) {
      const [{ city_name, city_id }] = this.citiesList;
      this.selectCityId = city_id;
      this.selectCityName = city_name;
      this.cityId = city_id;
    } else {
      this.selectCityId = this.cityId;
      this.selectCityName = this.userInfo.cityName;
    }
    await this.getHospitalList();
    if (this.isPackage) {
      this.getChainIngredientsList();
    }
    this.$emit('loadCity', this.selectCityId);
  },
  methods: {
    closeDetails() {
      this.hospitalPopupList = [];
      this.$refs['project-applicable-stores'].close('bottom');
    },
    viewDetails() {
      this.hospitalPopupList = JSON.parse(JSON.stringify(this.hospitalList));
      this.$refs['project-applicable-stores'].open('bottom');
    },
    // 获取服务城市列表
    async getChainCityList() {
      const { errorCode, errorMsg, responseData } = await GetChainCityList({
        sku_id: this.skuId,
        select_city_id: this.cityId,
        ingredients_id: this.ingredientsId
      });
      if (errorCode !== 200 && errorCode !== 0) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      this.citiesList = responseData || [];
      // this.cityId = this.selectCityId;
      // console.log(this.citiesList, 'this.citiesList');
      this.hiddenTips = this.citiesList.some(
        (item) => item.city_id === this.cityId
      );
      // console.log(this.hiddenTips, 'this.hiddenTips');
    },

    /**
     * 获取机构列表
     * @param {number} [type] - type = 1 更新项目适用门店底弹窗数据
     * */
    async getHospitalList(type = 0) {
      const { errorCode, errorMsg, responseData } = await GetHospitalList({
        sku_id: this.skuId,
        city_id: this.selectCityId,
        // city_id: 1,
        lat: this.userInfo.lat,
        lon: this.userInfo.log,
        ingredients_id: this.ingredientsId,
        sort: '0'
      });
      if (errorCode !== 200 && errorCode !== 0) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      if (type === 0) {
        this.totalHospitalList = responseData.list || [];
        this.isFold = responseData.list.length > 2;
        if (this.isFold) {
          // 是折叠的时候，只显示前两个
          this.hospitalList = this.totalHospitalList.slice(0, 2);
        } else {
          this.hospitalList = this.totalHospitalList;
        }
      } else if (type === 1) {
        this.hospitalPopupList = responseData.list || [];
      }
    },
    // 获取可预约原料列表
    async getChainIngredientsList() {
      const { errorCode, errorMsg, responseData } =
        await GetChainIngredientsList({
          sku_id: this.skuId
        });
      if (errorCode !== 200 && errorCode !== 0) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      this.projects = responseData.chain_ingredients_item;
      this.projectId = this.projects[0].sku_id;
    },
    handleChooseCity() {
      this.chooseCityVisible = true;
    },
    changeVisible(flag) {
      console.log(flag, 'flag');
      this.chooseCityVisible = flag;
    },
    cityChange(city) {
      console.log(city, 'city');
      this.$emit('reportStat', 'sy_wxtuan_tuan_product_new:city_switch_click', {
        city_id: city.city_id
      });
      // this.cityId = city.city_id;
      this.selectCityId = city.city_id;
      this.selectCityName = city.city_name;
      // this.cityName = city.city_name;
      this.chooseCityVisible = false;
      this.hiddenTips = true;
      this.getHospitalList();
      this.$emit('changeCity', this.selectCityId);
    },
    // 点击机构跳转到机构地图页
    goHospitalMap(item) {
      console.log('跳转到机构地图页', item);
      this.$emit('reportStat', 'sy_wxtuan_tuan_product_new:store_click', {
        hospital_id: item.hospital_id
      });
      uni.navigateTo({
        url: `/packageHospital/hospital-home?hospital_id=${item.hospital_id}`
      });
    },
    // 切换项目
    toggleProject(item) {
      console.log(item);
      this.projectId = item.sku_id;
      this.ingredientsId = item.sku_id;
      this.getHospitalList(1);
    },
    // 展开折叠机构列表
    handleFold() {
      this.isFold = !this.isFold;
      if (this.isFold) {
        // 是折叠的时候，只显示前两个
        this.hospitalList = this.totalHospitalList.slice(0, 2);
      } else {
        this.hospitalList = this.totalHospitalList;
      }
    }
  }
};
</script>

<style lang="less" scoped>
@px: 2rpx;

.applicable-stores {
  margin-top: 10 * @px;
  width: 100%;
  background-color: #ffffff;
  //padding: 15 * @px;
  //box-sizing: border-box;

  &-warn {
    display: flex;
    align-items: center;
    height: 31 * @px;
    background-color: #f8f8f8;
    padding: 0 15 * @px;

    text {
      font-family: PingFangSC-Regular;
      font-size: 13 * @px;
      color: #646464;
      font-weight: 400;
    }

    text:last-child {
      color: #65ab34;
      font-weight: 500;
    }
  }

  &-title {
    //margin-bottom: 5 * @px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 14 * @px;
    font-weight: 400;
    letter-spacing: 0;
    padding: 15 * @px;

    .left {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Semibold;
      font-size: 16 * @px;
      color: #030303;
      letter-spacing: 0;
      font-weight: 600;

      text {
        font-family: PingFangSC-Regular;
        color: #8c8c8c;
        font-size: 16 * @px;
        letter-spacing: 0;
        font-weight: 600;
      }
    }
    .right {
      display: flex;
      align-items: center;
      color: #333333;

      image {
        width: 8 * @px;
        height: 11 * @px;
        margin-left: 5 * @px;
      }
    }
  }
  .applicable-stores-empty,
  .applicable-hospital-empty {
    text-align: center;
    padding: 50px 0;
    .empty-img {
      margin-top: 32 * @px;
      width: 63 * @px;
      margin-bottom: 14 * @px;
    }
    .empty-text {
      font-family: PingFangSC-Medium;
      font-size: 17px;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
      margin-bottom: 25 * @px;
    }
    button {
      width: 88 * @px;
      height: 37 * @px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #333333;
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 400;
      margin-top: 20 * @px;
      border-radius: 0;

      &:after {
        border: none;
      }
    }
  }
  &-project {
    overflow: hidden;
    .project {
      // display: flex;
      overflow-x: scroll;
      width: 100%;
      margin-top: 10 * @px;
      padding-left: 10 * @px;
      white-space: nowrap;
      .project-item {
        position: relative;
        display: inline-flex;
        align-items: center;
        flex-wrap: nowrap;
        height: 34 * @px;
        box-sizing: border-box;
        padding: 0 15 * @px;
        margin-right: 10 * @px;
        border-width: 0 !important;
        background: #fff;
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #222222;
        letter-spacing: 0;
        font-weight: 400;
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          border: 1 * @px solid #dedede;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          border-radius: 17 * @px;
        }
        &:last-of-type {
          margin-right: 20 * @px;
        }
        &.active {
          border: 2 * @px solid #00ab84;
          font-family: PingFangSC-Medium;
          color: #00ab84;
          font-weight: 500;
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            border: 2 * @px solid #00ab84;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
          }
        }
      }
    }
  }
  .tips {
    margin: 10 * @px 10 * @px 0;
    width: 345 * @px;
    //height: 24 * @px;
    padding: 4 * @px 0 3 * @px;
    background: #f6f7f8;
    border-radius: 5 * @px;
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    line-height: 12 * @px;
    color: #aaabb3;
    letter-spacing: 0;
    font-weight: 400;
    padding-left: 10 * @px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    white-space: break-all;
  }
  &-content {
    box-sizing: border-box;
    padding: 0 15 * @px;
  }
  .fold {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15 * @px 0;
    font-family: PingFangSC-Regular;
    font-size: 12px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    .arrow {
      width: 10 * @px;
      height: 8 * @px;
      margin-left: 3 * @px;
    }
  }
}

.hospital-item {
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  width: 100%;
  background-color: #f2f2f2;
  padding: 12 * @px;
  padding-right: 21 * @px;
  box-sizing: border-box;
  margin-top: 15 * @px;
  //background: url('https://static.soyoung.com/sy-design/f0fgbjtr69ib1717468737988.png')
  //  no-repeat right center #f6f7f8;
  //background-size: 159 * @px 101 * @px;
  .hospital-icon {
    width: 50 * @px;
    height: 50 * @px;
    border-radius: 99.6 * @px 99.6 * @px 2.99 * @px 99.6 * @px;
    margin-right: 10 * @px;
  }
  .hospital-info {
    flex: 1;
    overflow: hidden;

    .hospital-name {
      display: flex;
      align-items: center;
      width: 100%;

      text {
        font-family: PingFangSC-Semibold;
        font-size: 16 * @px;
        color: #030303;
        letter-spacing: 0;
        font-weight: 600;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      image {
        height: 18 * @px;
        margin-left: 8 * @px;
        min-width: 57 * @px;
      }
    }

    .hospital-addr {
      padding-top: 5 * @px;
      font-family: PingFangSC-Regular;
      font-size: 13 * @px;
      color: #8c8c8c;
      letter-spacing: 0;
      font-weight: 400;
    }

    .hospital-verify {
      width: 62 * @px;
      // height: 13 * @px;
      margin-top: 5 * @px;
    }
  }
  .hospital-distance {
    margin-left: 32 * @px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .hospital-distance-img {
      width: 23 * @px;
      height: 23 * @px;
      min-width: 23 * @px;
    }

    .hospital-distance-text {
      font-family: PingFangSC-Semibold;
      font-size: 13 * @px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      white-space: nowrap;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 100%;
    }
  }
  .hospital-type {
    position: absolute;
    right: 0;
    top: 0;
    width: 53 * @px;
    height: 20 * @px;
  }

  &:first-child {
    margin-top: 0;
  }
}

.project-applicable-stores {
  background-color: #fff;
  max-height: 80vh;
  height: 80vh;
  box-sizing: border-box;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  .project-applicable-stores-header {
    height: 52 * @px;
    position: relative;
    width: 100%;
    text-align: center;

    image {
      width: 20 * @px;
      height: 20 * @px;
      right: 15 * @px;
      top: 50%;
      transform: translateY(-50%);
      position: absolute;
    }

    text {
      line-height: 52 * @px;
      text-align: center;
      font-family: PingFangSC-Medium;
      font-size: 16 * @px;
      color: #030303;
      letter-spacing: 0;
      font-weight: 500;
    }
  }

  .project-applicable-stores-product-list {
    margin-top: 8 * @px;
    height: 45 * @px;
    display: flex;
    white-space: nowrap;
    padding: 9 * @px 0;
    box-sizing: border-box;

    .project-applicable-stores-product-item {
      margin-left: 10 * @px;
      height: 27 * @px;
      box-sizing: border-box;
      padding: 0 10 * @px;
      background-color: #f2f2f2;
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      line-height: 27 * @px;
      text-align: center;
      display: inline-block;
    }

    .project-applicable-stores-product-item-select {
      background-color: #333333;
      color: #ffffff;
    }

    .project-applicable-stores-product-item:first-child {
      margin-left: 15 * @px;
    }

    .project-applicable-stores-product-item:last-child {
      margin-right: 15 * @px;
    }
  }

  .project-applicable-stores-hospital-list-title {
    padding: 15 * @px;
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #333333;
    letter-spacing: 0;
    line-height: 20 * @px;
    font-weight: 400;
  }

  .project-applicable-stores-hospital-list {
    box-sizing: border-box;
    padding: 0 15 * @px;
    height: calc(100% - 155 * @px);

    & > view {
      margin-bottom: 15 * @px;
    }
  }
}
</style>
