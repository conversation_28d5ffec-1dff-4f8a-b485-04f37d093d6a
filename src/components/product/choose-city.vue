<template>
  <div v-if="visibleT" class="date__dialog">
    <div class="date__dialog--wrap" :class="{ 'ease-leave': !visible }">
      <div class="picker-toolbar">
        <div class="picker-title">选择城市</div>
        <div class="close-btn" @click="hideServiceTimeSelect"></div>
      </div>
      <view class="find-city">
        <image
          src="https://static.soyoung.com/sy-design/1zuveb3ngazob1726735613664.png"
        ></image>
        <input type="text" placeholder="搜城市" @input="searchCity" />
      </view>
      <!--      <div class="location-ctn">-->
      <!--        <div class="gray-text">当前定位：</div>-->
      <!--        <div>-->
      <!--          <div v-if="notAllowedLoc" @click="openSetting" class="purple-text">-->
      <!--            定位未开启，前往设置-->
      <!--          </div>-->
      <!--          <div v-else class="location-text">-->
      <!--            {{ locCity }}-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <!--      <div class="gray-text choose-city-text">请选择服务城市：</div>-->
      <ul class="city-list">
        <block v-if="optionsSearch.length">
          <li
            class="city-item"
            v-for="(item, index) in optionsSearch"
            :key="index"
            @click="chooseCity(item)"
          >
            <div class="city-item-out">
              {{ item.city_name }}
            </div>
            <image
              class="select"
              :src="
                Number(checkedCity) === Number(item.city_id)
                  ? 'https://static.soyoung.com/sy-design/m6nemdnykrbc1726715721918.png'
                  : 'https://static.soyoung.com/sy-design/8k1ijrc526id1726715721966.png'
              "
            ></image>
          </li>
        </block>
        <block v-else>
          <li
            class="city-item"
            v-for="(item, index) in options"
            :key="index"
            @click="chooseCity(item)"
          >
            <div class="city-item-out">
              {{ item.city_name }}
            </div>
            <image
              class="select"
              :src="
                Number(checkedCity) === Number(item.city_id)
                  ? 'https://static.soyoung.com/sy-design/m6nemdnykrbc1726715721918.png'
                  : 'https://static.soyoung.com/sy-design/8k1ijrc526id1726715721966.png'
              "
            ></image>
          </li>
        </block>
      </ul>
    </div>
    <div
      class="date__dialog--mask"
      :class="{ 'ease-leave-b': !visible }"
      @click="hideServiceTimeSelect"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'ck-appointment-city-picker',
  props: {
    visible: { type: Boolean, default: false },
    noAutoSel: { type: Boolean, default: false },
    defaultCityId: { type: Number || String },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visibleT: false,
      locCity: '',
      locCityId: '',
      notAllowedLoc: false,
      find: true,
      checkedCity: '',
      optionsSearch: []
    };
  },
  watch: {
    visible: {
      handler() {
        if (this.visible) {
          this.visibleT = true;
          // uni.hideTabBar({
          //   animation: false
          // });
          this.$setTabBar({
            showMenu: false
          });
          this.init();
        } else {
          setTimeout(() => {
            this.visibleT = false;
            this.$setTabBar({
              showMenu: true
            });
            // uni.showTabBar({
            //   animation: false
            // });
          }, 50);
        }
      }
    }
  },
  methods: {
    searchCity(event) {
      if (!event) {
        return;
      }
      this.optionsSearch = [];
      for (let item of this.options) {
        if (item.city_name.indexOf(event.target.value) > -1) {
          this.optionsSearch.push(item);
        }
      }
    },
    openSetting() {
      console.log('点击了未开启设置');
      wx.openSetting({
        success: function (res) {
          console.log('设置打开成功', res.authSetting);
        },
        fail: function (err) {
          console.log('设置打开失败', err);
        }
      });
    },
    async getLocation() {
      try {
        const res = await this.$getCityId(); // 获取获取位置的状态
        console.log(res);
        this.locCity = res.cityName;
        this.locCityId = res.cityId;
        this.notAllowedLoc = false;
        !this.noAutoSel && this.checkLocation();
      } catch (error) {
        this.notAllowedLoc = true;
        console.log(error);
      }
    },
    init() {
      this.defaultCityId && (this.checkedCity = this.defaultCityId);
      this.getLocation();
    },
    checkLocation() {
      const curId = this.locCityId;
      const list = this.options;
      let find = false;
      list.forEach((item) => {
        if (+item.city_id === +curId) {
          find = true;
        }
      });
      this.find = find;
      if (find) {
        // 定位城市包含在服务城市中
        if (!this.checkedCity) {
          // 并且没有默认值
          this.checkedCity = this.locCityId;
          this.$emit('change', {
            city_id: this.locCityId,
            city_name: this.locCity,
            noClose: true
          });
        }
      }
    },
    hideServiceTimeSelect() {
      this.$emit('visible', false);
    },
    chooseCity(data) {
      this.optionsSearch = [];
      this.$emit('change', data);
      setTimeout(() => {
        this.checkedCity = data.city_id;
      }, 350);
    }
  }
};
</script>

<style scoped lang="less">
@px: 2rpx;
.date__dialog {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  z-index: 99999;
  transform: translateZ(11 * @px);
  .gray-text {
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #aaabb3;
    letter-spacing: 0;
    font-weight: 400;
  }
  .purple-text {
    font-family: PingFangSC-Medium;
    font-size: 13 * @px;
    color: #00ab84;
    letter-spacing: 0;
    font-weight: 500;
  }
  @keyframes popHeight {
    from {
      transform: translatey(100%);
    }
    to {
      transform: translatey(0);
    }
  }
  @keyframes popBackground {
    from {
      background: rgba(0, 0, 0, 0);
    }
    to {
      background: rgba(0, 0, 0, 0.5);
    }
  }
  @keyframes popHeightL {
    from {
      transform: translatey(0);
    }
    to {
      transform: translatey(100%);
    }
  }
  @keyframes popBackgroundL {
    from {
      background: rgba(0, 0, 0, 0.5);
    }
    to {
      background: rgba(0, 0, 0, 0);
    }
  }
  .ease-leave-b {
    animation: popBackgroundL 0.35s;
    animation-fill-mode: forwards;
  }
  .ease-leave {
    animation: popHeightL 0.35s;
    animation-fill-mode: forwards;
  }
  .find-city {
    width: calc(100vw - 60rpx);
    margin: 0 auto;
    background-color: #f2f2f2;
    height: 64rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    image {
      width: 14 * @px;
      height: 14 * @px;
      min-width: 14 * @px;
      margin-right: 10rpx;
    }

    input {
      width: 100%;
      border: none;
      background-color: transparent;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #bababa;
      letter-spacing: 0;
      font-weight: 400;
      flex: 1;
    }

    input::placeholder {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #bababa;
      font-weight: 400;
    }
  }
  .city-list {
    flex: 1;
    //display: flex;
    //flex-wrap: wrap;
    // justify-content: space-between;
    padding: 40rpx 30rpx 0;
    box-sizing: border-box;
    overflow-y: auto;
    .city-item {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20 * @px;
      .city-item-out {
        font-family: PingFangSC-Regular;
        font-size: 15 * @px;
        color: #030303;
        letter-spacing: 0;
        font-weight: 400;
      }
      .select {
        width: 24rpx;
        height: 24rpx;
      }
    }
    .city-blur {
      font-family: PingFangSC-Medium;
      // color: @text-color;
      color: #00ab84;
      background: #fff;
      font-weight: 500;
      border: 4rpx solid #00ab84;
    }
  }
  &--wrap {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 99;
    width: 100%;
    // min-height: 268 * 2rpx;
    max-height: 80vh;
    box-sizing: border-box;
    background: #fff;
    animation: popHeight 0.35s;
    animation-fill-mode: forwards;
    //border-radius: 15 * 2rpx 15 * 2rpx 0 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 40rpx;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
  &--mask {
    width: 100%;
    height: 100%;
    // background: rgba(0, 0, 0, 0.5);
    animation: popBackground 0.35s;
    animation-fill-mode: forwards;
  }
  .choose-city-text {
    padding-left: 30rpx;
    box-sizing: border-box;
    margin-top: 50rpx;
    margin-bottom: 15rpx;
  }
  .location-ctn {
    display: flex;
    align-items: center;
    padding-left: 30rpx;
    box-sizing: border-box;
    .location-text {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #222222;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      display: flex;
      align-items: center;
    }
    .purple-text {
      display: flex;
      align-items: center;
    }
    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 6rpx;
    }
  }
  .picker-toolbar {
    height: 52px;
    position: relative;
    .picker-title {
      display: flex;
      height: 104rpx;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Medium;
      font-size: 32rpx;
      color: #333;
      font-family: PingFangSC-Medium;
      text-align: center;
      flex-grow: 1;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
    .close-btn {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 40rpx;
      height: 40rpx;
      background: url('https://static.soyoung.com/sy-pre/56ro94fk2w7q-1650885000736.png')
        center/contain no-repeat;
    }
  }
}
.buttom-iphone-x {
  bottom: 40rpx;
}
@keyframes moveEnter {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}
@keyframes moveLeave {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}

.calendar-select-popup {
  width: 100%;
  height: 100%;
  border-top-left-radius: 0.16rem;
  border-top-right-radius: 0.16rem;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  background: #ffffff;
  transform: translateX(100%);
  border-radius: 15 * 2rpx 15 * 2rpx 0 0;
  &.enter {
    animation: moveEnter 0.5s;
    animation-fill-mode: forwards;
  }
  &.leave {
    animation: moveLeave 0.5s;
    animation-fill-mode: forwards;
  }
  .main-head {
    background: #f6f9f9;
    height: 90rpx;
    display: flex;
    li {
      width: 107rpx;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 30rpx;
      color: #555555;
      text-align: center;
      font-weight: 400;
    }
  }
  .picker-toolbar {
    height: 104rpx;
    position: relative;
    .picker-title {
      font-size: 32rpx;
      color: #222;
      font-family: PingFangSC-Medium;
      line-height: 104rpx;
      height: 104rpx;
      text-align: center;
      flex-grow: 1;
    }
    .close-btn {
      position: absolute;
      right: 32rpx;
      top: 24rpx;
      width: 40rpx;
      height: 40rpx;
      background: url('https://static.soyoung.com/sy-pre/19e0lndlqfm4n-1595855400700.png')
        center/contain no-repeat;
    }
    .cancel-icon {
      position: absolute;
      top: 0;
      left: 0;
      padding: 32rpx;
      width: 18rpx;
      height: 36rpx;
      object-fit: contain;
    }
  }
}
.date-list-wrap {
  z-index: 99;
  height: 90rpx;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
