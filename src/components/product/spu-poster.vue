<template>
  <div>
    <div
      class="poster-mask"
      :class="aniFadeIn ? 'poster-mask-show' : ''"
      :style="{
        display: aniVisible ? 'block' : 'none'
      }"
      @transitionend="ontransitionendMask"
    ></div>
    <div
      class="poster-wrap"
      :class="aniFadeIn ? 'poster-wrap-show' : ''"
      :style="{
        display: aniVisible ? 'block' : 'none'
      }"
    >
      <div class="canvas-wrap">
        <img :src="url" show-menu-by-longpress />
        <div class="close" @click="$emit('update:visible', false)"></div>
      </div>
      <div class="footer">
        <button class="save" @click="onSave">保存海报</button>
        <!--        <button class="share" @click="onSave">发送好友</button>-->
      </div>
    </div>
    <canvas
      type="2d"
      class="canvas"
      id="myCanvas"
      :style="{
        width: width * scale + 'px',
        height: height * scale + 'px'
      }"
    ></canvas>
  </div>
</template>
<script>
let ctx;
let canvas;
const dpr = 3;
const width = 280 * dpr;
const height = 529 * dpr;
const fix = (n) => n * dpr;
const scale = 1 / dpr;
// 全局缓存加载的图片资源，不load两次
const catched = new Map();
export default {
  props: {
    visible: Boolean,
    params: {
      type: Object,
      default: () => ({
        title: '',
        cover: '',
        code: ''
      })
    }
  },
  data() {
    return {
      aniVisible: false,
      aniFadeIn: false,
      url: '',
      width,
      height,
      scale,
      queue: [],
      resourceUrls: [],
      backgroundImage:
        'https://static.soyoung.com/sy-pre/1dqnaq6hs8j9c-1717398600621.png'
    };
  },
  watch: {
    visible(value) {
      if (value) {
        this.$nextTick().then(() => {
          this.aniVisible = true;
          setTimeout(() => {
            this.aniFadeIn = true;
          }, 50);
        });
      } else {
        this.aniFadeIn = false;
      }
    },
    params: {
      handler: function (params) {
        const { title, cover, code, subtitle } = params;
        if (title && cover && code && subtitle) {
          this.createPoster(params);
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  methods: {
    ontransitionendMask() {
      if (!this.aniFadeIn) {
        this.aniVisible = false;
      }
    },
    onSave() {
      this.$emit(
        'reportStat',
        'sy_wxtuan_op_product_info:share_img_save_click'
      );
      uni.saveImageToPhotosAlbum({
        filePath: this.url,
        success: () => {
          uni.showToast({
            title: '保存成功,赶快去分享吧!',
            icon: 'none'
          });
          this.$emit('update:visible', false);
        },
        fail(err) {
          const msg = Number(err.errno) === 103 ? '没有权限' : '';
          uni.showToast({
            title: '保存失败!' + msg,
            icon: 'none'
          });
        }
      });
    },
    createPoster({ title, cover, code, subtitle }) {
      const promise = new Promise((resolve) => {
        /**
         * 在draw之前调用，向绘制队列添加操作，如果存在图片url,也会发起一个不阻塞的图片fetch promise
         * 下面的顺序决定了绘制的顺序: 数组累加器 + promise调用链实现
         */
        this.resetCanvas();
        // this.drawBgImage(this.backgroundImage);
        this.drawPanel({
          radius: 0,
          width: 280,
          height: 529,
          x: 0,
          y: 0
          // ？？？
          // x: 15,
          // y: 50
        });
        this.loadCoverImage(cover);
        this.drawTitle(title);
        // TODO 缺少复标题
        this.drawSubtitle(subtitle);
        this.drawCode(code);
        this.drawTip(title);
        this.draw(resolve);
      });
      promise.then((url) => {
        this.url = url;
      });
      this.$emit('afterPosterCreated', promise);
    },
    async draw(resolve) {
      this.queue.push(function scaleCanvas() {
        ctx.scale(this.scale, this.scale);
        uni.canvasToTempFilePath({
          canvas,
          x: 0,
          y: 0,
          width,
          height,
          fileType: 'png',
          quality: 1,
          destWidth: width,
          destHeight: height,
          success: (res) => {
            console.log('图片生成：', res.tempFilePath);
            resolve(res.tempFilePath);
            // this.$uploadImg({
            //   url: '/xinyang/posts/addPic',
            //   filePath: res.tempFilePath,
            //   name: 'imgFile'
            // }).then((res) => {
            //   if (res.errorCode === 0) {
            //     console.log({
            //       title: this.params.title,
            //       path: this.params.path,
            //       imageUrl: res.responseData.u
            //     });
            //   }
            // });
          }
        });
      });
      // 所有的方法依赖ctx初始化
      await this.getCtxPromise();
      this.resourceUrls.forEach((url) => this.loadResource(url));
      console.log(
        '绘制操作队列：',
        this.queue.map((func) => func.name)
      );
      this.queue.reduce((prev, cur) => {
        return prev.then((...args) => {
          ctx.save();
          let isPromise = cur.apply(this, args);
          if (!isPromise) {
            isPromise = Promise.resolve(isPromise);
          }
          return isPromise.then((result) => {
            ctx.restore();
            return result;
          });
        });
      }, Promise.resolve());
    },
    drawTip() {
      this.queue.push(function drawTip() {
        // const width = fix(121);
        // const height = fix(16);
        const x = fix(71);
        const y = fix(480);
        const padding = fix(2);
        const fontStyle = `400 ${fix(12)}px PingFangSC-Regular`;
        // ctx.strokeRect(x, y, width, height);
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillStyle = '#8C8C8C';
        ctx.font = fontStyle;
        ctx.fillText('长按识别二维码查看商品', x, y + padding);
      });
    },
    resetCanvas() {
      this.queue = [];
      this.queue.unshift(function resetCanvas() {
        ctx.clearRect(0, 0, width, height);
      });
    },
    getCtxPromise() {
      return this.queryNode('#myCanvas').then((res) => {
        if (!res) {
          return Promise.reject('没有查询到canvas');
        }
        const [{ node }] = res;
        canvas = node;
        canvas.width = width;
        canvas.height = height;
        ctx = canvas.getContext('2d');
        ctx.strokeStyle = '#000';
        ctx.strokeRect(0, 0, width, height);
      });
    },
    drawSubtitle(title) {
      this.queue.push(function drawLine() {
        const x = fix(15); // 横线的起始 x 坐标
        const y = fix(128); // 横线的起始 y 坐标
        const width = fix(250); // 横线的宽度
        const height = fix(1); // 横线的高度

        // 设置线条颜色
        ctx.fillStyle = '#030303';

        // 绘制横线
        ctx.fillRect(x, y, width, height);
      });

      this.queue.push(function drawTitle() {
        const width = fix(250);
        const height = fix(17);
        const x = fix(15);
        const y = fix(132.5);
        const padding = fix(3);
        const fontStyle = `400 ${fix(12)}px PingFangSC-Regular`;

        const wrapText = (text, maxWidth) => {
          const fonts = text.split('');
          let subStr = '';
          const rows = [];
          ctx.font = fontStyle;
          while (fonts.length) {
            const cur = fonts.shift();
            if (ctx.measureText(subStr + cur).width > maxWidth) {
              // rows.push(subStr);
              subStr += '...';
              fonts.length = 0;
            } else {
              subStr += cur;
            }
          }
          rows.push(subStr);
          // if (rows.length < 1) {
          //   rows.push(subStr);
          // } else if (ellipsis) {
          //   let str = rows.pop();
          //   str = str.replace(/\S{1}$/g, '...');
          //   rows.push(str);
          // }
          return rows;
        };
        const rows = wrapText(title, width, 1);
        rows.forEach((text, index) => {
          // ctx.strokeRect(x, y, width, height * 2);
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          ctx.fillStyle = '#333';
          ctx.font = fontStyle;
          ctx.fillText(text, x, y + padding + height * index);
        });
      });

      this.queue.push(function drawLine() {
        const x = fix(15); // 横线的起始 x 坐标
        const y = fix(154); // 横线的起始 y 坐标
        const width = fix(250); // 横线的宽度
        const height = fix(1); // 横线的高度

        // 设置线条颜色
        ctx.fillStyle = '#030303';

        // 绘制横线
        ctx.fillRect(x, y, width, height);
      });
    },
    drawTitle(title) {
      this.queue.push(function drawTitle() {
        const width = fix(240);
        const height = fix(36);
        const x = fix(15);
        const y = fix(86);
        const padding = fix(3);
        const fontStyle = `600 ${fix(25)}px PingFangSC-Semibold`;

        const wrapText = (text, maxWidth) => {
          const fonts = text.split('');
          let subStr = '';
          const rows = [];
          ctx.font = fontStyle;
          while (fonts.length) {
            const cur = fonts.shift();
            console.log('cur', cur);
            if (ctx.measureText(subStr + cur).width > maxWidth) {
              // rows.push(subStr);
              subStr += '...';
              fonts.length = 0;
            } else {
              subStr += cur;
            }
          }
          rows.push(subStr);
          // if (rows.length < limit) {
          //   rows.push(subStr);
          // } else if (ellipsis) {
          //   let str = rows.pop();
          //   str = str.replace(/\S{1}$/g, '...');
          //   rows.push(str);
          // }
          return rows;
        };
        const rows = wrapText(title, width, 1);
        rows.forEach((text, index) => {
          // ctx.strokeRect(x, y, width, height * 2);
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          ctx.fillStyle = '#030303';
          ctx.font = fontStyle;
          ctx.fillText(text, x, y + padding + height * index);
        });
      });
    },
    drawPanel({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      this.queue.push(function drawPanel() {
        this.roundRect({ radius, x, y, width, height, backgroundColor });
      });
    },
    drawBgImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawBgImage() {
        const image = await this.loadResource(url);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          0,
          0,
          width,
          height
        );
      });
    },
    loadCoverImage(url) {
      this.queue.push(async function loadCoverImage() {
        const image = await this.loadResource(
          'https://static.soyoung.com/sy-pre/20240923-154704-1727075400629.png'
        );
        const dx = fix(15);
        const dy = fix(13);
        const dWidth = fix(60);
        const dHeight = fix(43);
        this.clipRect({ radius: 0, width: 60, height: 43, x: 15, y: 13 });
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function loadCoverImage() {
        const image = await this.loadResource(url);
        const dx = fix(15);
        const dy = fix(165);
        const dWidth = fix(250);
        const dHeight = fix(195);
        this.clipRect({ radius: 0, width: 250, height: 195, x: 15, y: 165 });
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    drawCode(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawCode() {
        const image = await this.loadResource(url, true);
        const dx = fix(87);
        const dy = fix(370);
        const dWidth = fix(100);
        const dHeight = fix(100);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    clipRect({ radius, x, y, width, height }) {
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height));
      ctx.lineTo(fix(x), fix(y + height));
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.clip();
    },
    roundRect({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      ctx.fillStyle = backgroundColor;
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height - radius));
      ctx.arcTo(
        fix(x + width),
        fix(y + height),
        fix(x + width - 2 * radius),
        fix(y + height),
        fix(radius)
      );
      ctx.lineTo(fix(x + radius), fix(y + height));
      ctx.arcTo(
        fix(x),
        fix(y + height),
        fix(x),
        fix(y + height - radius),
        fix(radius)
      );
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.fill();
    },
    loadResource(url, allFailedReturnResolve = false) {
      const fetch = (url, tryTimes = 5) =>
        new Promise((resolve, reject) => {
          const image = canvas.createImage();
          image.onload = () => resolve(image);
          image.onerror = (error) => {
            console.log(`load image error :try:${tryTimes} !!!`);
            if (tryTimes-- > 0) {
              fetch(
                url.replace(/\?t=\d+/g, '') + '?t=' + new Date().getTime(),
                tryTimes
              );
            } else {
              allFailedReturnResolve ? resolve(null) : reject(error);
            }
          };
          image.src = url;
        });
      // return promise<image> or undefined
      if (catched.has(url)) {
        const target = catched.get(url);
        target.catch(() => catched.delete(url));
      } else {
        catched.set(url, fetch(url));
      }
      return catched.get(url);
    },
    queryNode(selector, times = 10) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(selector)
          .fields({ node: true })
          .exec((res) => {
            if (res?.length > 0) {
              resolve(res);
            } else {
              if (times--) {
                setTimeout(() => {
                  resolve(this.queryNode(selector, times));
                }, 50);
              } else {
                uni.$log(
                  '[poster]没有查询到节点！',
                  this.params.title,
                  'error'
                );
                resolve([]);
              }
            }
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.poster-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background: rgba(0, 0, 0, 0.6);
  transition: opacity 0.3s;
  opacity: 0;
}
.poster-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10001;
  transition: transform 0.3s;
  transform: translateY(100%);
}
.canvas-wrap {
  position: absolute;
  top: 45%;
  left: 50%;
  width: 280 * 2rpx;
  height: 529 * 2rpx;
  transform: translate(-50%, -50%);
  z-index: 10;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.close {
  position: absolute;
  top: 0;
  right: 0;
  height: 80rpx;
  width: 80rpx;
  background: url(https://static.soyoung.com/sy-design/1azvpt7asrrzj1728646295362.png)
    no-repeat center center transparent;
  background-size: 40rpx 40rpx;
  z-index: 2;
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #ffffff;
  //border-radius: 30rpx 30rpx 0 0;
  padding-bottom: calc(
    20rpx + constant(safe-area-inset-bottom)
  ); //兼容 IOS<11.2
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  padding-left: 50rpx;
  padding-right: 50rpx;
  padding-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;

  button {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 42 * 2rpx;
    background-color: transparent;
    font-family: PingFangSC-Medium;
    font-size: 26rpx;
    text-align: center;
    font-weight: 500;
    border: none;
    border-radius: 0;

    &:after {
      border: none;
    }
  }

  .save {
    background-color: #ffffff;
    border: 2rpx solid #333333;
    color: #333333;
  }

  .share {
    background-color: #333333;
    color: #ffffff;
  }
}
.poster-mask-show {
  opacity: 1;
}
.poster-wrap-show {
  transform: translateY(-0%);
}
</style>
<style lang="less" scoped>
.canvas {
  position: fixed;
  top: -10000rpx;
  left: -10000rpx;
}
</style>
