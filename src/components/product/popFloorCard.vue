<template>
  <div class="floorCard">
    <div class="cardLeft">
      <div class="top">
        <div class="avatarList">
          <img
            v-for="item in avatarList"
            :key="item"
            :src="item"
            alt=""
            class="avatar"
          />
        </div>
        <div class="nameList">
          {{ userNameList.join('、') }}
        </div>
      </div>
      <div class="timeBox">
        <div class="left">
          还差<span style="color: red">{{ cardData.remain_num }}人</span>拼成
        </div>
        <div class="right">剩余{{ countDown }}</div>
      </div>
    </div>
    <div class="btnBox" @click="goPin">去拼单</div>
  </div>
</template>

<script>
export default {
  props: {
    cardData: {
      type: Object,
      required: true
    },
    activityId: {
      type: Number,
      required: true
    },
    skuId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      avatarList: [],
      userNameList: [],
      intervalId: null,
      remainingTime: this.cardData.remain_time * 1000
    };
  },
  computed: {
    countDown() {
      const time = this.remainingTime;
      let hours = Math.floor(time / (60 * 60 * 1000));
      let minutes = Math.floor((time / (60 * 1000)) % 60);
      let seconds = Math.floor((time / 1000) % 60);
      let milliseconds = Math.floor(time % 1000) / 100;
      hours < 10 ? (hours = `0${hours}`) : hours;
      minutes < 10 ? (minutes = `0${minutes}`) : minutes;
      seconds < 10 ? (seconds = `0${seconds}`) : seconds;
      return `${hours}:${minutes}:${seconds}:${milliseconds}`;
    }
  },
  methods: {
    formatData() {
      const userList = this.cardData.user_list.slice(0, 3);
      userList.forEach((item) => {
        this.avatarList.push(item.avatar);
        this.userNameList.push(item.nickname);
      });
    },
    goPin() {
      uni.navigateTo({
        url: `/packageActivity/pintuan?sku_id=${this.skuId}&activity_id=${this.activityId}&group_id=${this.cardData.group_id}`
      });
    }
  },
  mounted() {
    this.formatData();
    this.intervalId = setInterval(() => {
      if (this.remainingTime > 0) {
        this.remainingTime -= 100;
      } else {
        clearInterval(this.intervalId);
      }
    }, 100);
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  }
};
</script>

<style scoped lang="scss">
.floorCard {
  //padding: 30rpx 0;
  display: flex;
  align-items: flex-start;
  .cardLeft {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-right: 24rpx;
    //max-width: 365rpx;
    .top {
      display: flex;
      align-items: center;
    }
  }
}
.avatarList {
  display: flex;
  align-items: center;
  .avatar {
    width: 70rpx;
    height: 70rpx;
    margin-left: -40rpx;
    border-radius: 50%;
    border: 4rpx solid #fff;
    &:first-child {
      margin-left: 0;
    }
  }
}
.nameList {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 12rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #333333;
}
.timeBox {
  display: flex;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  line-height: 34rpx;
  margin-top: 20rpx;
  .left {
    color: #333333;
  }
  .right {
    margin-left: 16rpx;
    font-family: Outfit-Regular;
    font-size: 12px;
    color: #8c8c8c;
    letter-spacing: 0;
    font-weight: 400;
  }
}
.btnBox {
  width: 128rpx;
  height: 56rpx;
  background: #333333;
  line-height: 56rpx;
  text-align: center;
  font-family: PingFangSC-Medium;
  font-size: 13px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  margin-left: 20rpx;
  margin-top: 8rpx;
}
</style>
