<template>
  <image
    mode="widthFix"
    :style="{
      width: width,
      height: height
    }"
    :src="svgData"
  ></image>
</template>
<script>
import { Base64 } from './base64.js';
const base64 = new Base64();

export default {
  name: 'Svg',
  props: {
    color: {
      type: String,
      default: '#000000'
    },
    width: {
      type: String,
      default: '20px'
    },
    height: {
      type: String,
      default: '20px'
    },
    originData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      svgData: ''
    };
  },
  mounted() {
    this.getSvgFile(this.originData, this.color);
  },
  methods: {
    getSvgFile(originData, color) {
      let newFile = this.changeColor(originData, color);
      let svgBase64File = base64.encode(newFile);
      this.svgData = 'data:image/svg+xml;base64,' + svgBase64File;
    },
    changeColor(sourceFile, color) {
      let newSvg;
      if (/fill=".*?"/.test(sourceFile)) {
        newSvg = sourceFile.replace(/fill=".*?"/g, `fill="${color}"`); // SVG有默认色
      } else {
        newSvg = sourceFile.replace(/<svg /g, `<svg fill="${color}" `); // 无默认色
      }
      return newSvg;
    }
  }
};
</script>
<style lang="less"></style>
