import { mapGetters, mapState } from 'vuex';

export default {
  data() {
    return {
      mixin_login_doing: false
    };
  },
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  methods: {
    // 登录
    async keepSession(afterLoginCallback) {
      // 如果没有登录，先去登录，然后判断是否是 新用户
      if (!this.isLogin) {
        // 校验登录
        this.mixin_login_doing = true;
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        this.mixin_login_doing = false;
        if (!isAuth) return false;
        await afterLoginCallback?.();
      }
      return true;
    }
  }
};
