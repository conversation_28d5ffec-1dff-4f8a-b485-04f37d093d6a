import config from '@/config';

async function requestPayment(data) {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: 'wxpay',
      timeStamp: data.timeStamp,
      appId: data.appId,
      nonceStr: data.nonceStr,
      package: data.package,
      signType: data.signType,
      paySign: data.paySign,
      success: resolve,
      fail: reject
    });
  });
}

export default {
  methods: {
    async syPayment(oid, order_id) {
      const { errorCode, errorMsg, responseData } = await this.$request({
        host: config.payBaseApi,
        url: '/mp/payment/orderinfo',
        data: {
          order_id: oid
        }
      })
        .then((res) => res.data)
        .catch((err) => {
          return {
            errorCode: -100,
            errorMsg: err,
            responseData: null
          };
        });
      if (errorCode !== 0) {
        return Promise.reject(errorMsg);
      }
      // 如果totalPrice == 0 ，跳转支付成功
      /**
       *
       *  expire_countdown: "28800"
          order_desc: ""
          order_id: "12904877"
          order_type: "1501"
          paid_price: "0"
          pay_attr: [{pay_type: "3", name: "微信支付", desc: "亿万用户的选择，更快更安全", tips: "", recommend: "0", selected: "0",…}]
          0: {pay_type: "3", name: "微信支付", desc: "亿万用户的选择，更快更安全", tips: "", recommend: "0", selected: "0",…}
          desc: "亿万用户的选择，更快更安全"
          name: "微信支付"
          pay_type: "3"
          recommend: "0"
          selected: "0"
          sub_attr: {}
          tips: ""
          pay_status: "0" 1 已支付 0 未支付 3 已退款
          title: "支付订单预约金"
          topay_price: "901900"
          total_price: "901900"
       *
       *
       */
      const { topay_price } = responseData;
      if (Number(topay_price) === 0) {
        return {
          order_id,
          topay_price
        };
      }
      // payinfo
      {
        const { errorCode, errorMsg, responseData } = await this.$request({
          host: config.payBaseApi,
          url: '/mp/payment/payinfo',
          data: {
            order_id: oid,
            pay_type: 3, // 3 为微信支付
            topay_price
          }
        })
          .then((res) => res.data)
          .catch((err) => {
            return {
              errorCode: -100,
              errorMsg: err,
              responseData: null
            };
          });
        if (errorCode !== 0) {
          return Promise.reject(errorMsg);
        }
        try {
          const { errMsg } = await requestPayment(responseData.weixin_attr);
          // {errMsg: "requestPayment:ok"}
          return errMsg === 'requestPayment:ok'
            ? {
                order_id,
                topay_price
              }
            : Promise.reject(errMsg);
        } catch (error) {
          // {errMsg: "requestPayment:fail cancel"}
          return Promise.reject(error.errMsg);
        }
      }
    }
  }
};
