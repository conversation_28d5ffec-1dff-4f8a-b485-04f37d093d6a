<template>
  <div class="sku__wrap">
    <div class="sku-card" @click="on2sku">
      <img
        class="sku-card-img"
        v-if="skuInfo"
        :src="skuInfo.img_cover || skuInfo.img_url"
        alt=""
      />
      <div class="sku-card-info">
        <p class="sku-card-title">
          <rich-text class="sku-card-rich" :nodes="skuInfo.title"></rich-text>
          <!-- <span class="sku-card-total">x{{ skuInfo.num || 1 }}</span> -->
        </p>
        <p class="sku-card-num">
          <!-- <span>{{
            skuInfo.property_select_path || skuInfo.property_str || ''
          }}</span> -->
          <span>x{{ skuInfo.num || 1 }}</span>
          <span class="sku-card-price" v-if="skuInfo.price_online"
            >¥{{ skuInfo.price_online }}
            <!-- <template v-if="skuInfo.times && skuInfo.is_hide_times_card != 2">
              /{{ skuInfo.times || '' }}次
            </template> -->
          </span>
        </p>
      </div>
    </div>
    <!-- 赠品商品列表-->
    <div
      class="gift-sku-list"
      v-if="skuInfo.gift_list && skuInfo.gift_list.length > 0"
    >
      <div
        class="gift-sku-card"
        v-for="(item, index) in skuInfo.gift_list"
        :key="index"
      >
        <div class="sku-img-wrap">
          <img
            v-if="item"
            :src="item.img_cover || item.cover_img"
            mode="aspectFill"
            class="sku-img"
          />
        </div>
        <div class="box">
          <div class="line-1">
            <div class="title">
              <rich-text :nodes="item.title"></rich-text>
            </div>
            <div class="num">x{{ item.num || 1 }}</div>
          </div>
          <div class="line-2">
            <img
              v-if="!is_hide_price"
              src="https://static.soyoung.com/sy-pre/2hca4x4bkq4bk-1663765800685.png"
              class="zengpin"
            />
            <span class="price"> ¥0 </span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="order__coupon"
      v-if="skuInfo.code_count > 0"
      @click="$emit('selectCoupon', skuInfo)"
    >
      <p>红包立减</p>
      <div v-if="skuInfo.code_id">
        -￥{{ skuInfo.total_discount_money || '' }}<i></i>
      </div>
      <div v-else>
        {{ skuInfo.code_count || 0 }}个红包可用
        <i></i>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    skuInfo: Object,
    is_hide_price: {
      type: Number,
      default: 0
    },
    qrcode_id: String,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    on2sku() {
      if (this.disabled) return;
      const { sku_id, group_id, activity_id } = this.skuInfo;
      uni.navigateTo({
        url: `/pages/product?id=${sku_id}&group_id=${group_id}&activity_id=${activity_id}`,
        success: function () {
          console.log('跳转成功');
        },
        fail: function (err) {
          console.error('跳转失败', err);
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.sku__wrap {
  box-sizing: border-box;
  margin: 0 10rpx;
  padding: 15 * 2rpx 10 * 2rpx;
  background: #fff;
  border-radius: 8px;
}
.sku-card {
  display: flex;
  position: relative;
  flex-wrap: wrap;
  .sku-card-img {
    width: 90 * 2rpx;
    height: 70 * 2rpx;
    background-color: #dedede;
  }
  .sku-card-info {
    flex: 1;
    margin-left: 20rpx;
    font-size: 26rpx;
  }
  .sku-card-rich {
    display: -webkit-box;
    // width: 424rpx;
    max-height: 80rpx;
    line-height: 40rpx;
    font-size: 26rpx;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    font-weight: 500;
  }
  .sku-card-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
    height: 80rpx;
  }
  .sku-card-num {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #555;
    font-size: 24rpx;
    font-weight: 400;
  }
  .sku-card-price {
    font-size: 26rpx;
    font-weight: 500;
    color: #222;
  }
}
.gift-sku-list {
  padding-top: 30rpx;
  box-sizing: border-box;
  background: #fff;
  .gift-sku-card {
    display: flex;
    flex-direction: row;
    background: #f8f8f8;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    // margin-bottom: 10rpx;
    // &:last-child {
    //   margin-bottom: 0;
    // }

    .sku-img-wrap {
      height: 136rpx;
      width: 182rpx;
      position: relative;
      .sku-img {
        display: inline-block;
        height: 136rpx;
        width: 182rpx;
      }
    }

    .box {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 20rpx;
      .line-1 {
        display: flex;
        justify-content: space-between;
        height: 72rpx;
        .title {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          word-break: break-all;
          text-overflow: ellipsis;
          width: 192 * 2rpx;
          height: 80rpx;
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #777777;
          line-height: 20px;
          font-weight: 400;
        }
        .num {
          width: 13 * 2rpx;
          height: 18 * 2rpx;
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #777777;
          font-weight: 400;
          text-align: right;
        }
      }

      .line-2 {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        height: 36rpx;
        text-align: right;
        font-family: PingFangSC-Regular;
        color: #808080;
        margin-top: 10rpx;
        .zengpin {
          border-radius: 2px;
          overflow: hidden;
          display: inline-block;
          width: 56rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
        .price {
          text-align: right;
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #777777;
          text-align: right;
          font-weight: 400;
        }
      }
    }
  }
  &.disabled {
    filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    -webkit-filter: grayscale(1);
    .box {
      .title,
      .num,
      .price {
        color: #aaabb3;
      }
    }
  }
}
.order__coupon {
  position: relative;
  box-sizing: border-box;
  margin-top: 30rpx;
  padding: 50rpx 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 345 * 2rpx;
    border-top: 1rpx solid #f0f0f0;
  }
  p {
    font-size: 14 * 2rpx;
    font-weight: 500;
  }
  div {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 26rpx;
    color: @text-color;
    font-weight: 400;
    i {
      margin-left: 10rpx;
      height: 10 * 2rpx;
      width: 8 * 2rpx;
      background: url(https://static.soyoung.com/sy-pre/more-1661422200663.png)
        no-repeat center center transparent;
      background-size: contain;
    }
  }
}
</style>
