<template>
  <div class="agreement-cus">
    <div class="agreement">
      <img
        :src="checked ? is_select_icon : n_is_select_icon"
        class="select-img"
        @click.stop="() => $emit('update:checked', !this.checked)"
      />
      <div class="text">
        {{ descInfo }}
        <span
          class="em"
          v-for="(item, index) in list"
          :key="index"
          @click.stop="goAgreement(item)"
          >{{ item.name }}</span
        >
      </div>
    </div>
    <Modal title="提示" v-model="visible" width="300px">
      <template #body>
        <div class="agreement-confirm">
          购买商品，请先阅读并同意<span
            v-for="(item, index) in agreement_list"
            :key="index"
            >{{ item.name }}</span
          >
        </div>
      </template>
      <template #footer>
        <div class="agreement-confirm-btns">
          <div class="btn" @click="() => $emit('update:visible', false)">
            我再想想
          </div>
          <div class="btn confirm" @click="onConfrimAgreement">同意</div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import Modal from '@/components/popup';
import { apiUserAgreement, apiSignAgreement } from '@/api/order';
import { throttle } from 'lodash-es';

export default {
  props: {
    checked: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Modal
  },
  data() {
    return {
      list: [],
      descInfo: '',
      is_select_icon:
        'https://static.soyoung.com/sy-design/m6nemdnykrbc1726026826775.png',
      n_is_select_icon:
        'https://static.soyoung.com/sy-design/8k1ijrc526id1726026826754.png'
    };
  },
  mounted() {
    this.goAgreement = throttle((item) => this.$toH5(item.file), 1500, {
      trailing: false
    });
    this.fetchUserAgreement();
  },
  methods: {
    async fetchUserAgreement() {
      const data = await apiUserAgreement();
      this.list = data?.agreement_list || [];
      this.descInfo = data?.desc || '';
    },
    async onConfrimAgreement() {
      const res = await apiSignAgreement(0);
      this.$emit('update:checked', res);
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="less" scoped>
.agreement {
  display: flex;
  flex-direction: row;
  font-size: 12 * 2rpx;
  letter-spacing: 0;
  line-height: 18 * 2rpx;
  margin-top: 20 * 2rpx;
  padding: 0 10 * 2rpx;
  color: #8c8c8c;
  font-weight: 400;
  .em {
    color: #222;
    display: inline;
  }
  .select-img {
    display: inline-block;
    width: 24rpx;
    height: 24rpx;
    margin-top: 7rpx;
    margin-right: 10rpx;
    overflow: hidden;
    cursor: pointer;
  }
  .text {
    flex: 1;
  }
}
.agreement-confirm {
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}
.agreement-confirm-btns {
  text-align: center;
  margin-top: 25 * 2rpx;
  .btn {
    box-sizing: border-box;
    display: inline-block;
    padding: 0px;
    width: 118 * 2rpx;
    // height: 30 * 2rpx;
    font-size: 14 * 2rpx;
    border: 0.5px solid @border-color;
    border-radius: 20px;
    color: @text-color;
    margin-right: 18 * 2rpx;
    line-height: 30 * 2rpx;
    &:last-child {
      margin-right: 0px;
    }
    &.confirm {
      background-color: @border-color;
      color: #fff;
    }
  }
}
</style>
