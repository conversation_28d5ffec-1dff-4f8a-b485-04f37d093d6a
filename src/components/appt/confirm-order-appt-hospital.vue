<template>
  <div
    class="appt-card-wrap"
    :class="{
      sketch: firstLoad && loading
    }"
  >
    <div class="sketch-mask"></div>
    <div class="appt-card">
      <!-- 机构标题行 -->
      <div class="title-wrap">
        <div class="title">
          <div class="title-txt">
            选择意向门店 <span class="required">*</span>
          </div>
        </div>
        <p
          class="city"
          :class="{
            enabled: canChooseCity
          }"
          @click="onOpenCityBox"
        >
          {{ cityName }}
        </p>
      </div>
      <!-- 如果存在机构 -->
      <block v-if="hasHospital">
        <scroll-view
          :class="['hospital-list', multiple]"
          :scroll-x="true"
          :show-scrollbar="false"
          :scroll-left="hospitalScrollLeft"
        >
          <div
            class="hospital-item"
            v-for="item in hospitalList"
            :key="item.hospital_id"
            :class="{
              gray: item.surplus_num === 0,
              active: selectHospitalId === item.hospital_id
            }"
            :data-id="item.hospital_id"
            @click="onHospitalClick"
          >
            <!-- 机构名称 -->
            <div class="hospital-name">
              <div
                class="hospital-name-txt"
                :class="{ 'avoid-logo': item.hospital_self }"
              >
                {{ item.hospital_name }}
              </div>
              <Icon
                v-if="Number(item.hospital_tag_type) === 1"
                class="hospital-self"
              ></Icon>
              <HqIcon
                v-else-if="Number(item.hospital_tag_type) === 2"
                class="hospital-self"
              ></HqIcon>
            </div>
            <!-- 卡片最下面一行 -->
            <div class="hospital-bottom">
              <div class="address">{{ item.hospital_addr }}</div>
              <div class="distance">
                {{ item.distance_str || item.city_name || '' }}
              </div>
            </div>
            <!-- 定位的小图标 -->
            <div class="location" @click.stop="toMap(item)"></div>
            <!-- 选中的图标 -->
            <img
              v-if="
                selectHospitalId === item.hospital_id ||
                hospitalList.length == 1
              "
              class="sel-icon"
              src="https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1726299976851.png"
            />
            <div class="cashback-amount" v-if="item.cashback">
              {{ item.cashback }}
            </div>
          </div>
        </scroll-view>
        <!-- 机构库存标题行 -->
        <slot></slot>
        <div class="title-wrap">
          <div class="title">
            <div class="title-txt">
              意向到店时间
              <span class="required" v-if="forceReserve">*</span>
            </div>
          </div>
        </div>
        <!-- 所选机构库存 -->
        <div class="hospital-stock">
          <div class="hospital-stock-icon">
            <span>1.提交预约</span>
            <image
              src="https://static.soyoung.com/sy-design/2hca4x4bkq4bk1726299976855.png"
            ></image>
            <span>2.平台确认</span>
            <image
              src="https://static.soyoung.com/sy-design/2hca4x4bkq4bk1726299976855.png"
            ></image>
            <span>3.预约生效</span>
          </div>
          <TimePicker
            :options="pickerOptions"
            @clickDate="onDateChange"
            @calenderIconClick="onCalenderIconClick"
            :enabledConfirmOrderStyle="true"
          />
        </div>
      </block>
      <!-- 如果没有机构  -->
      <div class="hospital-empty" v-else>
        <img src="https://static.soyoung.com/sy-pre/empty-1683202200726.png" />
        <p>当前城市暂无服务门店，请切换城市</p>
        <div class="switch-city" @click="onOpenCityBox">切换城市</div>
      </div>
    </div>
    <ChooseCity
      :defaultCityId="city_id"
      :visible="chooseCityVisible"
      :cityList="cityList"
      @visible="chooseCityVisible = false"
      @city-change="onSelectCity"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex';
import {
  apiHospitalList,
  apiHospitalCalendar,
  getCityInfoByCityId
} from './api';
import ChooseCity from '@/components/chooseCity/index.vue';
import Icon from '@/components/icon/Self.vue';
import HqIcon from '@/components/icon/hq.vue';
import TimePicker from '@/components/appointNew/components/AppointmentTimerPicker/index.vue';
export default {
  components: { ChooseCity, Icon, TimePicker, HqIcon },
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    cityList: {
      type: Array,
      default: () => []
    },
    forceReserve: {
      type: Boolean,
      default: false
    },
    sku_id: {
      type: Number
    },
    city_id: {
      type: Number
    },
    ingredients_id: {
      type: Number
    },
    select_hospital_id: {
      type: Number
    },
    total_amount: {
      type: Number,
      default: 0
    },
    price_online: {
      type: Number,
      default: 0
    },
    reduce_list: {
      type: String,
      default: '[]'
    }
  },
  data() {
    return {
      chooseCityVisible: false,
      hospitalList: [], // 机构列表
      selectHospitalId: 0,
      pickerOptions: [],
      hospitalScrollLeft: 0,
      firstLoad: true,
      cityName: ''
    };
  },
  watch: {
    city_id: {
      handler(val) {
        if (!val) return;
        console.log('city_id change', val);
        this.$emit('update:loading', true);
        this.hospitalScrollLeft = -1;
        this.getHospitalList()
          .then(() => this.$nextTick())
          .then(() => {
            this.hospitalScrollLeft = 0;
            this.$emit('update:loading', false);
            this.firstLoad = false;
          });
        this.getCityName();
      },
      immediate: true
    },
    ingredients_id: {
      handler() {
        this.$emit('update:loading', true);
        this.hospitalScrollLeft = -1;
        this.getHospitalList()
          .then(() => this.$nextTick())
          .then(() => {
            this.hospitalScrollLeft = 0;
            this.$emit('update:loading', false);
            this.firstLoad = false;
          });
        this.getCityName();
      },
      immediate: true
    },
    price_online: {
      handler() {
        this.$emit('update:loading', true);
        this.hospitalScrollLeft = -1;
        this.getHospitalList()
          .then(() => this.$nextTick())
          .then(() => {
            this.hospitalScrollLeft = 0;
            this.$emit('update:loading', false);
            this.firstLoad = false;
          });
        this.getCityName();
      },
      immediate: true
    },
    reduce_list: {
      handler() {
        this.$emit('update:loading', true);
        this.hospitalScrollLeft = -1;
        this.getHospitalList()
          .then(() => this.$nextTick())
          .then(() => {
            this.hospitalScrollLeft = 0;
            this.$emit('update:loading', false);
            this.firstLoad = false;
          });
        this.getCityName();
      },
      immediate: true
    },
    total_amount: {
      handler() {
        this.$emit('update:loading', true);
        this.hospitalScrollLeft = -1;
        this.getHospitalList()
          .then(() => this.$nextTick())
          .then(() => {
            this.hospitalScrollLeft = 0;
            this.$emit('update:loading', false);
            this.firstLoad = false;
          });
        this.getCityName();
      },
      immediate: true
    },
    chooseCityVisible(val) {
      this.$emit('visibleChange', val);
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    multiple() {
      return this.hospitalList.length > 1 ? 'multiple' : '';
    },
    hasHospital() {
      return this.hospitalList?.length > 0;
    },
    // 是否可以选择城市
    canChooseCity() {
      return Array.isArray(this.cityList) && this.cityList.length > 1;
    }
  },
  methods: {
    async getCityName() {
      this.cityName = await getCityInfoByCityId(this.city_id);
    },
    // 日历 icon 点击
    onCalenderIconClick() {
      this.reportWrapper(
        'sy_wxtuan_tuan_order_confirm_page:switch_date_icon_click'
      );
    },
    // 获取机构列表
    async getHospitalList() {
      const responseData = await apiHospitalList({
        select_hospital_id: this.select_hospital_id,
        ingredients_id: this.ingredients_id, // 原料id
        city_id: this.city_id,
        sku_id: this.sku_id,
        total_amount: this.total_amount,
        price_online: this.price_online,
        reduce_list: this.reduce_list,
        sort: 0
      });
      if (!responseData) {
        this.clearState();
        return;
      }
      this.hospitalList = responseData;
      // 设置默认的机构：第一个机构
      if (this.hasHospital) {
        const { hospital_id } = this.hospitalList[0];
        this.selectHospitalId = hospital_id;
        await this.fetchHospitalCalendar();
      } else {
        this.clearState();
      }
    },
    // 获取机构库存列表
    // >>>???? 是否需要传递 group_buy_id
    // apiCalendarForTime
    async fetchHospitalCalendar() {
      console.log('fetchHospitalCalendar', this.total_amount);
      const responseData = await apiHospitalCalendar({
        ingredients_id: this.ingredients_id,
        hospital_id: this.selectHospitalId,
        city_id: this.city_id,
        sku_id: this.sku_id,
        total_amount: this.total_amount,
        price_online: this.price_online,
        reduce_list: this.reduce_list
      });
      if (!responseData) {
        this.clearState();
        return;
      }
      this.pickerOptions = responseData;
    },
    clearState() {
      this.selectHospitalId = 0;
      this.hospitalList = [];
      this.pickerOptions = [];
    },
    // 点击了日历之后
    onDateChange(stock) {
      console.log('onDateChange', stock);
      this.$emit('change', {
        ...stock,
        hospital_id: this.selectHospitalId
      });
      this.reportWrapper('sy_wxtuan_tuan_order_confirm_page:switch_time_click');
    },
    // 机构的选择
    async onHospitalClick(event) {
      console.log('onHospitalClick', event);
      const { id } = event.currentTarget.dataset;
      if (this.selectHospitalId === +id) return;
      this.selectHospitalId = +id;
      this.$emit('update:loading', true);
      await this.fetchHospitalCalendar();
      this.$emit('update:loading', false);
      this.reportWrapper(
        'sy_wxtuan_tuan_order_confirm_page:switch_hospital_click'
      );
    },
    // 打开城市选择窗口
    onOpenCityBox() {
      this.chooseCityVisible = this.canChooseCity;
    },
    reportWrapper(info) {
      this.$reportData({
        info,
        ext: {
          product_id: this.sku_id,
          city_id: this.city_id,
          hospital_id: this.selectHospitalId
        }
      });
    },
    // 去机构地图
    toMap(data) {
      uni.navigateTo({
        url: `/packageHospital/hospital-map?hospital_id=${data.hospital_id}`
      });
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:institution_address_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: '',
          status: 1,
          type: 2,
          city_id: this.city_id,
          hospital_id: data.hospital_id
        }
      });
    },
    // 选择服务机构所在的城市
    async onSelectCity(city_id) {
      if (!city_id) return;
      this.chooseCityVisible = false;
      this.$emit('update:city_id', city_id);
      await this.$nextTick();
      this.reportWrapper('sy_wxtuan_tuan_order_confirm_page:switch_city_click');
    }
  }
};
</script>
<style lang="less" scoped>
.appt-card-wrap {
  position: relative;
  background-color: #ffffff;
  &.sketch {
    position: relative;
    height: 356 * 2rpx + 60rpx;
    margin-right: 10rpx;
    padding-right: 0;
    overflow: hidden;
    .sketch-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 365 * 2rpx;
      height: 356 * 2rpx + 60rpx;
      background: url(https://static.soyoung.com/sy-pre/sketch-1708603800711.jpeg)
        no-repeat center center #fff;
      background-size: 365 * 2rpx 356 * 2rpx;
      z-index: 12;
    }
  }
}
.appt-card {
  box-sizing: border-box;
  padding: 50rpx 0 40rpx;
  .title-wrap {
    box-sizing: border-box;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    height: 40rpx;
    font-weight: 500;
    color: #030303;
    .title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-size: 28rpx;
      font-weight: 500;
      .title-txt {
        white-space: nowrap;
        max-width: 540rpx;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .required {
        margin-left: 4rpx;
        color: @text-color;
      }
    }
    .city {
      font-size: 24rpx;
      font-weight: 400;
      white-space: nowrap;
      color: #333333;
      &::after {
        content: '';
        display: none;
        margin-left: 10rpx;
        width: 16rpx;
        height: 20rpx;
        background: url(https://static.soyoung.com/sy-pre/arrow-1660273800731.png)
          no-repeat center center transparent;
        background-size: contain;
      }
      &.enabled::after {
        display: inline-block;
      }
    }
  }
  .appt-wrap {
    margin-right: -20rpx;
    margin-left: -20rpx;
    padding-top: 30rpx;
    overflow: hidden;
  }
}
.hospital-list {
  box-sizing: border-box;
  width: calc(100vw - 60rpx);
  padding-top: 30rpx;
  padding-bottom: 40rpx;
  white-space: nowrap;
  .hospital-item {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: 146rpx;
    width: 315 * 2rpx;
    padding: 30rpx;
    background: #f2f2f2;
    &:first-child {
      margin-left: 30rpx;
    }
    &:last-child {
      margin-right: 30rpx;
    }
    &.gray {
      color: #999999;
      border: 2px solid #333333;
      .right {
        color: #999999;
        .bar {
          background: #eaeaea;
        }
      }
    }
    .location {
      position: absolute;
      right: 30rpx;
      top: 32rpx;
      width: 36rpx;
      height: 36rpx;
      background-image: url(https://static.soyoung.com/sy-design/3gcimprsb1n2m1726299976862.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .cashback-amount {
      position: absolute;
      top: 0;
      left: 0;
      background: #89dc65;
      font-family: PingFangSC-Regular;
      font-size: 18rpx;
      color: #030303;
      font-weight: 400;
      height: 24rpx;
      line-height: 24rpx;
      padding: 0rpx 8rpx;
      box-sizing: border-box;
    }
    .hospital-name {
      max-width: 444rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      margin-bottom: 10rpx;
      color: #333333;
      font-weight: 500;
      display: flex;
      align-items: center;
      .hospital-name-txt {
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 520rpx;
        white-space: nowrap;
      }
      .avoid-logo {
        max-width: 380rpx;
      }
      .hospital-self {
        margin-left: 10rpx;
      }
    }
    .hospital-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #8c8c8c;
      font-size: 24rpx;
      font-weight: 400;
      overflow: hidden;
      .address {
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .distance {
        flex-shrink: 0;
      }
    }
    &.active {
      position: relative;
      background: #ffffff;
      border: 4rpx solid #333333;
      .hospital-bottom {
        color: #8c8c8c;
      }
      .location {
        background-image: url(https://static.soyoung.com/sy-design/3gcimprsb1n2m1726299976862.png);
      }
      .sel-icon {
        width: 36rpx;
        height: 26rpx;
        position: absolute;
        right: 0;
        bottom: -1px;
      }
    }
  }
  &.multiple {
    .hospital-item {
      width: 275 * 2rpx;
      margin-right: 20rpx;
      padding: 30rpx;
      &:last-child {
        margin-right: 20rpx;
      }
      .hospital-name {
        .hospital-name-txt {
          max-width: 440rpx;
        }
        .avoid-logo {
          max-width: 340rpx;
        }
      }
    }
  }
}
.hospital-empty {
  padding: 20rpx 0 40rpx;
  img {
    display: block;
    margin: 20rpx auto;
    height: 59 * 2rpx;
    width: 57 * 2rpx;
  }
  p {
    line-height: 40rpx;
    font-size: 28rpx;
    color: #030303;
    text-align: center;
    font-weight: 400;
  }
  .switch-city {
    display: flex;
    margin-top: 30rpx;
    text-align: center;
    font-weight: 400;
    background: #333333;
    width: 186rpx;
    height: 76rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
  }
}
.hospital-stock {
  padding-top: 30rpx;
  &-icon {
    height: 52rpx;
    width: 630rpx;
    display: flex;
    margin: 0 auto 30rpx;
    background-color: #ebfbdc;
    align-items: center;
    justify-content: center;
    span {
      font-family: OutFit-Regular;
      font-size: 28rpx;
      color: #030303;
      font-weight: 500;
    }
    image {
      display: block;
      width: 20rpx;
      height: 16rpx;
      margin: 0 30rpx;
    }
  }
}
.cashback-amount {
  position: absolute;
  top: 0;
  left: 0;
  background: #89dc65;
  font-family: PingFangSC-Regular;
  font-size: 18rpx;
  color: #030303;
  font-weight: 400;
  height: 24rpx;
  line-height: 24rpx;
  padding: 0rpx 8rpx;
  box-sizing: border-box;
}
</style>
