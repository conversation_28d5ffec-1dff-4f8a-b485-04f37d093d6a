import Vue from 'vue';

/**
 * 城市列表
 * @param {Object} param
 * @returns {Promise<Array>} 城市列表
 */
export async function apiChainCityList(
  // 用户购买的skuId,一般是顶级的商品SKU
  sku_id,
  // 用户想约的原料id,组套商品传要约的原料的skuId
  ingredients_id
) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/chainCityList',
    data: {
      sku_id,
      ingredients_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData) {
    return responseData;
  } else {
    return [];
  }
}

/**
 * 机构列表
 * @param {Object} param
 * @returns {Promise<Array>} 城市列表
 */
export async function apiHospitalList({
  ingredients_id, // 原料id
  city_id = 0,
  sku_id,
  total_amount,
  price_online,
  reduce_list,
  sort = 0 // 排序 0默认排序 1距离排序  2阈值排序
}) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/hospitalList',
    data: {
      ingredients_id,
      city_id,
      sku_id,
      sort,
      total_amount,
      price_online,
      reduce_list
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData?.list) {
    return responseData.list;
  } else {
    return null;
  }
}

/**
 * 获取机构日历列表
 * @param {Object} param
 * @returns {Promise<Array>} 城市列表
 */
export async function apiHospitalCalendar({
  ingredients_id,
  hospital_id,
  city_id = 0,
  sku_id,
  total_amount,
  price_online,
  reduce_list
}) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getCalendar',
    data: {
      ingredients_id,
      hospital_id,
      city_id,
      sku_id,
      total_amount,
      price_online,
      reduce_list
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData?.list) {
    return responseData.list;
  } else {
    return [];
  }
}

/**
 * 根据城市id获取城市名称
 * @param {Object} param
 * @returns {Promise<Array>} 城市列表
 */
export async function getCityInfoByCityId(city_id) {
  const { errorCode, responseData } = await Vue.$request({
    url: '/syGroupBuy/chain/reservation/getCityInfoByCityId',
    data: {
      city_id: city_id
    }
  })
    .then((res) => res.data)
    .catch((error) => {
      return {
        errorCode: -100,
        errorMsg: error || '网络错误,稍后再试',
        responseData: null
      };
    });
  if ((errorCode === 200 || errorCode === 0) && responseData?.city_name) {
    return responseData.city_name;
  } else {
    return '';
  }
}
