<template>
  <div class="loading" v-if="visible">
    <div class="spinner__box">
      <img
        class="spinner"
        src="https://static.soyoung.com/sy-pre/20240927-163903-1727424600628.gif"
      />
    </div>
  </div>
</template>
<script>
export default {
  props: {
    visible: Boolean
  },
  data() {
    return {};
  }
};
</script>
<style lang="less" scoped>
.loading {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  transform: translateZ(99999px);
  z-index: 999999;
}

.spinner {
  margin-top: 2px;
  width: 100%;
  height: 100%;
  &__box {
    height: 100rpx;
    width: 100rpx;
    background: rgba(238, 238, 238, 0.96);
    border-radius: 50%;
    box-sizing: border-box;
  }
}
</style>
