<template>
  <view class="loadmore">
    <view v-if="opts.total && opts.more == 1" class="loadmore-load">
      <text class="loadmore-loadicon" />
      <text>- 加载更多 -</text>
    </view>
    <view v-if="opts.total && opts.more == 0" class="loadmore-none">
      <text v-show="!opts.hidden"> 没有更多了哦 </text>
    </view>
    <view v-if="!opts.total" class="empty" :style="opts.emptyStyle">
      <div class="center">
        <img
          v-if="opts.imgHeight && opts.imgWidth"
          :style="
            'height:' + opts.imgHeight + 'rpx;width:' + opts.imgWidth + 'rpx;'
          "
          :src="
            opts.imgSrc ||
            'https://mstatic.soyoung.com/m/static/img/weixin/empty.png'
          "
        />
        <img
          v-else
          :src="
            opts.imgSrc ||
            'https://mstatic.soyoung.com/m/static/img/weixin/empty.png'
          "
        />
        <p>{{ opts.emptyText }}</p>
      </div>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    opts: {
      type: Object,
      default: () => {}
    }
  }
};
</script>

<style lang="less" scoped>
@keyframes load3 {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loadmore {
  padding: 20rpx 0;

  .loadmore-loadicon {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 50%;
      height: 50%;
      background: #aaabb3;
      border-radius: 100% 0 0;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      background: #f6f9f9;
      width: 90%;
      height: 90%;
      border-radius: 50%;
      margin: auto;
      bottom: 0;
      right: 0;
    }

    margin-right: 10rpx;
    font-size: 24rpx;
    // text-indent: -9999em;
    width: 30rpx;
    height: 30rpx;
    border-radius: 50%;
    background: #aaabb3;
    background: linear-gradient(
      to right,
      #aaabb3 10%,
      rgba(255, 255, 255, 0) 42%
    );
    position: relative;
    -webkit-animation: load3 0.5s infinite linear;
    animation: load3 0.5s infinite linear;
  }

  .loadmore-load {
    color: #aaabb3;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 2;
    font-size: 24rpx;
  }

  .loadmore-none {
    text-align: center;
    font-size: 24rpx;
    line-height: 2;
    color: #aaabb3;
  }

  .empty {
    position: relative;
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
    .center {
      img {
        display: block;
        margin: 0 auto 15rpx;
        width: 150rpx;
        height: 150rpx;
      }
      p {
        font-size: 28rpx;
        color: #999999;
        text-align: center;
        font-weight: 400;
      }
    }
  }
}
</style>
