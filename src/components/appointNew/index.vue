<template>
  <div class="modal-mask" v-if="visible">
    <div @click="closeDialog(2)" class="mask"></div>
    <div class="pannel" :class="{ 'confirm-pannel': !order_id }">
      <div class="close" @click="closeDialog"></div>
      <div class="title" :class="{ 'title-t': false }">
        <div class="title-h1">
          <div
            class="title-h1-l"
            :style="{
              'max-width': `calc(100% - ${oldData.month ? '100' : '56'}rpx)`
            }"
          >
            {{ titleStr }}
          </div>
          <div v-if="oldData.month" class="title-h1-r">改约</div>
        </div>
        <div @click="titleToUrl" class="title-h2">
          <img
            class="img1"
            mode="widthFix"
            src="https://static.soyoung.com/sy-design/380r1h9tko8mu1727167736435.png"
          />
          <span>为保障最佳治疗效果，请参考服务间隔进行预约</span>
          <img
            class="img2"
            src="https://static.soyoung.com/sy-design/kgmpik5wigpe1727167736430.png"
          />
        </div>
        <div class="tab-list">
          <TabList
            :defaultTab="curTabInfo"
            :tabList="tabList"
            @change="tabChange"
            @cityClick="tabCityClick"
            :cityName="curCityData.city_name"
          ></TabList>
        </div>
      </div>
      <div class="modals" v-if="curCityData.city_id">
        <TimeModel
          v-if="curTabInfo.value == 1"
          ref="timeModel"
          :othersBlur="othersBlur"
          :request_time="request_time"
          :skeletonReady.sync="skeletonReady"
          :baseInfo="baseInfo"
          :oldData="oldData"
          :lastMulData="lastMulData"
          :dateBtmTxtUrl="dateBtmTxtUrl"
          :city_id="curCityData.city_id"
          :city_name="curCityData.city_name"
          :cur_city_data="curCityData"
          :order_id="order_id"
          :sku_id="sku_id"
          :ingredients_id="ingredients_id"
          :order_app_id="order_app_id"
          :top_order_id="top_order_id"
          :order_hospital_id="order_hospital_id"
          :noMatchReserveCity="noMatchReserveCity"
          @setData="setModeData"
          @setTTop="setTTop"
        ></TimeModel>
        <HospitalModel
          v-if="curTabInfo.value == 2"
          ref="hospitalModel"
          :othersBlur="othersBlur"
          :request_time="request_time"
          :skeletonReady.sync="skeletonReady"
          :baseInfo="baseInfo"
          :oldData="oldData"
          :lastMulData="lastMulData"
          :dateBtmTxtUrl="dateBtmTxtUrl"
          :city_id="curCityData.city_id"
          :city_name="curCityData.city_name"
          :order_id="order_id"
          :fnlDefaultMode="fnlDefaultMode"
          :cur_city_data="curCityData"
          :sku_id="sku_id"
          :top_order_id="top_order_id"
          :ingredients_id="ingredients_id"
          :order_app_id="order_app_id"
          :order_hospital_id="order_hospital_id"
          @calendarVisible="calendarVisible"
          @setData="setModeData"
          @setTTop="setTTop"
        ></HospitalModel>
      </div>
      <!-- v-if="!hasYueT && initBtnShow" -->
      <div class="btm-commit">
        <div v-if="contTxtShow && !calendarVis" class="warn-text">
          <div class="warn-text-left">
            <img
              class="warn-text-icon"
              src="https://static.soyoung.com/sy-design/3of651hl3ckg81727167736495.png"
            />
            <div class="warn-text-middle">
              <!-- 强制预约的文案 这里永远不会展示了，因为确认订单页不用这个组件了-->
              <div v-if="oldData.month">
                已约:{{ (appointTime || '').replace(/-/g, '/')
                }}{{ oldMonthDou ? ',' : '' }}
                <!-- 返现文案 -->
                <!-- <span v-if="order_id && oldData.month && oldData.back_money > 0"
                  >按时到店返￥{{ oldData.back_money }}</span
                > -->
              </div>
              <div
                v-if="appointHospital"
                :class="{
                  'warn-text-middle-hos-long': !(
                    order_id &&
                    baseInfo.is_show_cancel_btn &&
                    oldData.month
                  )
                }"
                class="warn-text-middle-hos"
              >
                {{ appointHospital }}
              </div>
            </div>
          </div>
          <div
            @click="cancelAppoint"
            v-if="order_id && baseInfo.is_show_cancel_btn && oldData.month"
            class="cancel-appoint-btn"
          >
            取消预约
          </div>
        </div>
        <div v-else-if="reserveChanged && !calendarVis">
          <ApptWranText
            :isShowSubText="false"
            type="warning"
            title="预约将在平台确认后生效，请以最终确认结果为准"
          />
        </div>
        <div class="mul-commit-ctn">
          <!-- 未使用状态 -->
          <div v-if="!order_id" class="mul-commit-left">
            <div class="slot-wrap">
              <slot></slot>
            </div>
            <!-- <span class="price" v-if="!order_id">￥7999</span>
            <span class="price-reduce" v-if="!order_id">共减￥550</span> -->
          </div>
          <div
            v-if="order_id"
            @click="confirmCommitDate"
            :class="{
              'commit-btn-change': order_id,
              'btn-disabled': commitDisabled
            }"
            class="commit-btn"
          >
            <div class="commit-btn-in">
              <span
                :class="{ 'two-line': order_id && !isMul && oldData.month }"
                >{{ oldData.month ? '修改预约' : '提交预约' }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <ChooseCity
        :visible="chooseCityDia"
        :defaultCityId="curCityData.city_id"
        @visible="changeVisible"
        @city-change="cityChange"
        :options="cityOptions"
      ></ChooseCity>
      <!-- 改约原因填写 -->
      <ReasonPop
        :title="cancelAppointment ? '取消原因' : '改约原因'"
        @commitReason="commitReason"
        @visible="rePopChange"
        :visible="rePop"
      ></ReasonPop>
      <Skeleton v-if="!skeletonReady"></Skeleton>
      <root-portal>
        <div v-if="toastText" class="def-toast">{{ toastText }}</div>
      </root-portal>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import {
  // checkForceRes,
  // reserveMode,
  // apiCityListWithoutHos,
  // checkReserveStatus,
  // toCancelReserve,
  // checkCommodityCycleTxt,
  // apiCheckRes,
  getReserveBaseInfo,
  apiChainReservationChainCityList,
  apiChainReservationCancelReserve,
  getCityInfoByCityId
} from '@/api/reservation.js';
// tab 列表组件
import TabList from '@/components/appointNew/components/modelTab.vue';
// 按时间预约模块
import TimeModel from '@/components/appointNew/timeModel.vue';
// 按机构预约模块
import HospitalModel from '@/components/appointNew/hospitalModel.vue';
// 选择服务城市模块
import ChooseCity from '@/components/appointNew/components/chooseCity.vue';
// 改约提示
// import PopReserve from '@/components/popup/popReserve.vue';
// 改约原因填写
import ReasonPop from '@/components/appointNew/components/reasonPop.vue';
import Skeleton from '@/components/appointNew/components/skeleton.vue';
import ApptWranText from '@/components/order/appt-warn-text.vue';

import SubscribeMixins from '@/mixins/subscribe';
// 按医生预约模块
// import DoctorModel from '@/components/appointNew/doctorModel.vue';
export default {
  components: {
    TabList,
    TimeModel,
    HospitalModel,
    ChooseCity,
    // PopReserve,
    ReasonPop,
    Skeleton,
    ApptWranText
    // DoctorModel,
  },
  mixins: [SubscribeMixins],
  props: {
    visible: {
      /**
       * @desc 是否可见
       */
      type: Boolean,
      default: false
    },
    reserve_id: {
      /**
       * @desc 修改/取消时的预约id
       */
      type: Number,
      default: 0
    },
    order_id: {
      /**
       * @desc 20240604-预约弹窗的订单id，非主订单id，是拆单后的order_id
       */
      type: String,
      default: ''
    },
    order_app_id: {
      /**
       * @desc 当前订单下的appid,传递后会过滤后续接口返回数据。上游接口有返回，有就传
       */
      type: Number,
      default: 0
    },
    order_hospital_id: {
      /**
       * @desc 当前订单下的hospital_id,传递后会过滤后续接口返回数据。上游接口有返回，有就传
       */
      type: Number,
      default: 0
    },
    city_id: {
      /**
       * @desc 打开的弹窗的时候右边的城市id
       */
      type: Number,
      default: 0
    },
    sku_id: {
      /**
       * @desc sku_id,
       */
      type: Number,
      default: 0
    },
    ingredients_id: {
      /**
       * @desc 同sku_id,大部分时候
       */
      type: Number,
      default: 0
    },
    top_order_id: {
      /**
       * @desc 大部分时候和order_id一样。也可能是主订单id。上游接口有返回，有啥传啥
       */
      type: String,
      default: ''
    }
  },
  watch: {
    skeletonReady: {
      handler() {
        if (this.skeletonReady) {
          this.pageExpose();
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    contTxtShow() {
      // 如果 this.oldData.month 为false，后面的判断也都是false呀，没看懂...
      // return (
      //   (this.forceReserveInfo?.power_txt &&
      //     this.forceReserveInfo?.power_reserve &&
      //     !this.order_id) ||
      //   this.oldData.month ||
      //   (this.order_id && this.oldData.month && this.oldData?.back_money > 0) ||
      //   (this.order_id && !this.isMul && this.oldData.month)
      // );
      // !this.order_id 是确认订单页的场景，不存在这个场景了，所以删除了没有副作用
      return !this.reserveChanged && this.oldData.month;
    },
    oldMonthDou() {
      return (
        this.order_id && this.oldData.month && this.oldData?.back_money > 0
      );
    }
  },
  created() {
    // 正常预约场景
    // if (this.order_id) {
    //   // 获取能否预约的信息
    //   this.getCanReserve();
    //   // 确认订单页预约
    // } else {
    this.getBaseInfo();
    // }
  },
  mounted() {
    this.stayStart = new Date().valueOf();
    console.log('this.stayStart', this.stayStart);
  },
  data() {
    return {
      tTop: false, // 是否离开顶部
      cancelAppointment: false,
      calendarVis: false,
      oldTimeBlur: false,
      lastMulData: {},
      // 改约数据
      changeInfo: {
        changeTime: '',
        changeHospital: ''
      },
      skeletonReady: false, // 初始化是否完成，控制骨架图是否显示
      toastText: '',
      isMul: false,
      // calendarParamsChuan: {},
      request_time: 0,
      dateBtmTxtUrl: {}, // 日期下提示文案
      isCommitting: false, // 提交中防暴力点击
      fnlDefaultMode: {}, // 最终计算的默认模式
      commitDisabled: true,
      commitData: {
        date: '',
        start: '',
        end: ''
      },
      rePop: false, // 改约原因弹窗
      reason: {}, // 改约原因
      oldFan: 0, // 旧的返现金额
      newFan: 0, // 新的返现金额
      oldTime: '', // 之前预约的日期
      popupStatus: false, // 弹窗状态
      appointTime: '', // 已预约时间，从子组件传来
      appointHospital: '', // 已预约机构，从子组件传来
      forceReserveInfo: {}, // 强制预约的文案和信息
      // lesTimes: 3, // 剩余预约次数
      titleStr: '', // 弹窗标题
      baseInfo: {}, // 所有的基本信息
      selData: {}, // 已整合数据
      oldData: {}, // 之前预约的旧数据
      // canReserve: 1, // 是否支持预约
      chooseCityDia: false, // 选择城市窗口
      curCityData: {}, // 当前选中的城市信息
      curTabInfo: {}, // 选中的tab信息
      othersBlur: false, // 除了from，别的是否一致
      tabList: [
        { name: '按时间', value: 1 },
        { name: '按机构', value: 2 }
      ],
      defaultMode: {
        name: '按时间',
        value: 1
      },
      cityOptions: [], // 服务城市列表
      reserveChanged: false, // 预约改变了
      noMatchReserveCity: false
    };
  },
  methods: {
    rePopChange() {
      this.rePop = false;
    },
    popupStatusChange() {
      this.popupStatus = false;
    },
    // confirmCommitSkip() {
    //   if (this.btnText === '预约并支付' || this.btnText === '预约并申请') {
    //     this.closeDialog(3);
    //   } else {
    //     this.closeDialog();
    //   }
    // },
    // 上报停留时间
    countStayTime() {
      const endStay = new Date().valueOf();
      const time = (endStay - this.stayStart) / 1000;
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:confirm_subscribe_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          stay_time: time
        }
      });
    },
    // 页面曝光埋点
    pageExpose() {
      const ext = {
        uid: this.userInfo.uid,
        product_id: this.sku_id,
        order_id: this.order_id,
        status: this.oldData.month ? 2 : 1,
        city_id: this.curCityData?.city_id,
        hospital_id: this.selData?.hospital?.hospital_id || '',
        type: this.curTabInfo.value
      };
      this.$reportData({
        info: 'sy_m_tuan_subscribe_pop:subscribe_exposure',
        ext
      });
    },
    // 跳转间隔页
    titleToUrl() {
      this.$toH5('https://m.soyoung.com/tmwap22419');
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:treatment_interval_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: this.curTabInfo.value
        }
      });
    },
    checkLoc() {
      console.log('checkLoc');
      if (this.curTabInfo.value === 1) {
        this.$refs?.timeModel?.checkLoc?.();
      } else if (this.curTabInfo.value === 2) {
        this.$refs?.hospitalModel?.checkLoc?.();
      }
    },
    setTTop(tTop) {
      this.tTop = tTop;
    },
    // 是否支持预约
    // async getCanReserve() {
    //   const data = await apiCheckRes({
    //     order_id: this.order_id
    //   });
    //   const canReserve = data?.can_reserve;
    //   if (+canReserve !== 1) {
    //     this.canReserve = 0;
    //   } else {
    //     this.getBaseInfo();
    //   }
    // },
    calendarVisible(val) {
      this.calendarVis = val;
    },
    // async checkForceReserve() {
    //   const obj = {
    //     city_id: this.curCityData.city_id,
    //     package_id: this.package_id,
    //     pid: this.pid,
    //     spu_id: this.spu_id
    //   };
    //   this.forceReserveInfo = await checkForceRes(obj);
    // },
    // 关闭窗口
    closeDialog(type = 1) {
      // type 1 直接点 X 关闭按钮；type 2 点击空白处；type 3 点击跳过
      if (type === 3) {
        // 确认订单页跳过
        this.$emit('skip', false);
      } else {
        this.$emit('update:visible', false);
      }
      // this.showBar();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:close_pop_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1
        }
      });
    },
    // 显示底部bar
    // showBar() {
    // setTimeout(() => {
    //   uni.showTabBar({
    //     animation: false
    //   });
    // }, 50);
    // },
    // 请求tab列表
    // async getTabList() {
    //   const data = await reserveMode({
    //     pid: this.pid
    //   });
    //   if (data) {
    //     this.tabList = data?.reserve_mode || [];
    //     this.defaultMode = data?.default_reserve_mode || {};
    //     this.setDefaultMode();
    //   }
    // },
    // 获取服务城市列表
    async getCityList() {
      // const reqData = {
      //   order_id: this.order_id,
      //   city_id: this.city_id, // 默认锚定的city_id
      //   package_id: this.package_id,
      //   pid: this.pid
      // };
      let data;
      // 有缓存就用缓存
      // if (this.appointData?.cityListCache?.length) {
      //   data = this.appointData?.cityListCache;
      // } else {

      // data = await apiCityListWithoutHos(reqData);
      // 接口替换
      // todo 需要替换接口
      data = await apiChainReservationChainCityList({
        sku_id: this.sku_id,
        ingredients_id: this.ingredients_id,
        order_app_id: this.order_app_id,
        order_hospital_id: this.order_hospital_id,
        top_order_id: this.top_order_id,
        order_id: this.order_id
      });
      // }
      if (data.length === 0) {
        uni.showToast({
          title: '当前商品暂不支持预约，请联系客服',
          icon: 'none'
        });
      }
      this.cityOptions = data;
      // this.setInitCity();
      // if (this.curCityData.city_id) return;
      if (this.city_id && this.cityOptions) {
        // 有传下来的id
        const cityObj = this.cityOptions.find(
          (item) => item.city_id === this.city_id
        );
        if (cityObj) {
          this.curCityData = cityObj;
        } else if (this.cityOptions[0]) {
          /**
           * 上游传的city_id和 接口返回的，没有匹配的城市的时候，给提示
           * @type {boolean}
           */
          this.noMatchReserveCity = true;
          try {
            let city_name = '';

            const respCityData = await getCityInfoByCityId(this.city_id).catch(
              (e) => {
                console.log('预约弹窗-查询城市异常-getCityInfoByCityId', e);
              }
            );
            if (
              respCityData.errorCode === 200 ||
              respCityData.errorCode === 0
            ) {
              city_name = respCityData.responseData.city_name;
            }
            this.curCityData = {
              city_id: this.city_id,
              city_name: city_name
            };
          } catch (e) {
            console.log('预约弹窗-查询城市异常', e);
          }
        }
      } else if (this.cityOptions[0]) {
        this.curCityData = this.cityOptions[0];
      }
    },
    // setInitCity() {
    //   // TODO 改约：如果存在上次预约的数据，需要把上次的预约的城市带过来
    //   if (!this.curCityData.city_id) {
    //     if (this.city_id && this.cityOptions) {
    //       // 有传下来的id
    //       const cityObj = this.cityOptions.find(
    //         (item) => item.city_id === this.city_id
    //       );
    //       if (cityObj) {
    //         this.curCityData = cityObj;
    //       }
    //     } else {
    //       this.curCityData = this.cityOptions[0];
    //     }
    //   }
    // 注释掉，肯定有订单ID，之前在确认订单页 的场景 没有了
    // !this.order_id && this.checkForceReserve();
    // console.log('当前城市', this.city_id, this.curCityData);
    // },
    // async initBtmText(data) {
    //   if (
    //     data.date &&
    //     data?.date?.first_un &&
    //     (!data?.time?.from || this.oldTimeBlur)
    //   ) {
    //     const obj = {
    //       pid: this.pid,
    //       reserve_id: this.oldData.reserve_id || '',
    //       package_id: this.package_id,
    //       request_time: data?.date?.first_un?.slice?.(0, 10) || ''
    //     };
    //     const dataRes = await checkCommodityCycleTxt(obj);
    //     if (dataRes) {
    //       this.dateBtmTxtUrl = dataRes;
    //     }
    //   }
    // },
    // 设置数据
    setModeData(data) {
      this.selData = data;
      // 改约弹窗需要的参数
      this.newFan = data?.time?.back_money;
      // 改约弹窗参数
      this.changeInfo.changeTime =
        data.date?.month + '月' + data.date?.day + '日 ' + data.time?.from;
      this.changeInfo.changeHospital =
        data.hospital?.hospital_name || data.time?.hospital_name;
      this.currentDate = `${data.date?.year}-${data.date?.month}-${data.date?.day}`;
      // 提交用到的数据
      this.commitData.date = data.date?.first_un?.slice(0, 10);
      this.commitData.start_time =
        this.commitData.date + ' ' + data?.time?.from;
      this.commitData.end_time = this.commitData.date + ' ' + data?.time?.to;
      // 按钮状态
      this.checkBtnStatus();
      // 日期下边的周期间隔提示文案
      // this.initBtmText(data);
      // 检测是否进行了数据编辑
      this.reserveChanged = this.checkIsValidEdit(data);
      // console.log('是否进行了编辑：', this.reserveChanged);
    },
    checkIsValidEdit(data) {
      // console.log('是否进行了编辑入参：', data);
      const { date, hospital, time, doctor } = data;
      // 如果没有选择数据，不是一次有效的编辑
      if (time === undefined || Object.keys(time).length === 0) return false;
      const oldData = this.oldData;
      console.log('已经预约的数据', JSON.stringify(oldData));
      // 如果没有老预约数据
      if (!oldData.month) {
        return Boolean(
          date.year && hospital?.hospital_id && time.from && time.to
        );
      }
      // 如果有老预约数据
      if (
        +date.year === +oldData.year &&
        +date.month === +oldData.month &&
        +date.day === +oldData.day &&
        time.from === oldData.from &&
        time.to === oldData.to
      ) {
        if (+this.curTabInfo.value === 3) {
          // 如果是医生
          return doctor?.doctor_id !== +oldData.doctor_id;
        } else {
          // 查看是否变更了机构
          return hospital?.hospital_id !== +oldData.hospital_id;
        }
      } else {
        return true;
      }
    },
    // 点击城市，打开服务城市弹窗
    tabCityClick() {
      this.chooseCityDia = true;
    },
    // tab blur改变
    tabChange(item) {
      this.curTabInfo = item;
      // this.calendarParamsChuan = {}; // 切换tab清除确认订单页带过来的数据
      this.selData = {};
      this.checkBtnStatus();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_mode_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: this.curTabInfo.value
        }
      });
    },
    // 服务城市弹窗状态改变
    changeVisible(el) {
      this.chooseCityDia = el;
      // TODO 需@李和宁确认
      // this.clearStatus();
    },
    // 服务城市改变
    cityChange(data) {
      setTimeout(() => {
        this.chooseCityDia = false;
        this.curCityData = data;
        if (this.noMatchReserveCity) {
          this.noMatchReserveCity = false;
        }

        this.$emit('cityChange', data);
        // !this.order_id && this.checkForceReserve();
        this.$reportData({
          info: 'sy_wxtuan_or_subscribe_pop:switch_city_click',
          ext: {
            uid: this.userInfo.uid,
            product_id: this.sku_id,
            order_id: this.order_id,
            status: this.oldData.month ? 2 : 1,
            city_id: data.city_id,
            type: this.curTabInfo.value
          }
        });
      });
    },
    // 获取预约基本信息
    async getBaseInfo() {
      // const reqData = { order_id: this.order_id };
      // let data;
      // const baseInfoCa = this?.appointData?.baseInfoCache || {};
      // 有缓存就用缓存
      // if (Object.keys(baseInfoCa)?.length) {
      // data = this.appointData?.baseInfoCache;
      // } else {
      const data = await getReserveBaseInfo({
        order_id: this.order_id,
        reserve_id: this.reserve_id,
        ingredients_id: this.ingredients_id
      });
      // }
      if (data) {
        // 修改次数
        // const change_cnt = +data.change_cnt;
        // 剩余修改次数
        // this.lesTimes = 3 - change_cnt < 0 ? 0 : 3 - change_cnt;
        // 所有信息
        this.baseInfo = data;
        // 之前预约的数据
        const oldData = data.old_reservation_time || {};
        // 之前的一单多约
        if (this.baseInfo?.reservation_info?.length) {
          this.isMul = true;
          this.setMulOldData();
        } else {
          this.oldData = oldData;
        }
        // this.oldData.reserve_mode = 3;
        this.setReservedInfo();
        // 弹窗标题
        this.titleStr = this.baseInfo.commodity_alias_name;
      }
      this.getCityList();
      this.setDefaultMode();
      // 取消预约曝光埋点
      this.reportCancelExpose();
    },
    reportCancelExpose() {
      if (
        this.order_id &&
        this.baseInfo?.is_show_cancel_btn &&
        this.oldData?.month
      ) {
        this.$reportData({
          info: 'sy_wxtuan_tuan_subscribe_pop:cancel_reserve_exposure',
          ext: {
            order_id: this.order_id,
            uid: this.userInfo.uid
          }
        });
      }
    },
    // 设置次卡一单多约数据
    setMulOldData() {
      const reservationInfo = this.baseInfo?.reservation_info;
      let request_time = 0;
      if (this.reserve_id) {
        // 修改预约
        const obj = reservationInfo.find((item, index) => {
          if (item.reserve_id === this.reserve_id) {
            request_time = index + 1;
            return true;
          }
        });
        this.oldData = obj;
      } else {
        // 新预约
        for (let i = 0; i < reservationInfo.length; i++) {
          const item = reservationInfo[i];
          if (!item.reserve_id) {
            request_time = i + 1;
            break;
          }
        }
        this.oldData = {};
        // 有前一次预约时，保存前一次数据
        if (request_time > 1) {
          this.lastMulData = reservationInfo[request_time - 2];
        }
      }
      this.request_time = request_time;
    },
    // 设置已预约信息
    setReservedInfo() {
      if (this.oldData.month) {
        const date = `${this.oldData.year}-${this.oldData.month}-${this.oldData.day}`;
        const time = this.oldData?.from?.slice?.(0, 5);
        this.appointTime = date + ' ' + time;
        this.oldTime = date;
        this.appointHospital = this.oldData.hospital_name;
      }
    },
    // 设置默认预约模式
    setDefaultMode() {
      let modeObj = { value: 1 };
      // let findFlag = 0;
      // 已预约数据
      if (this.oldData.reserve_mode) {
        // this.oldData.mode = 2;
        modeObj = { value: this.oldData.reserve_mode };
        // 确认订单页已约数据
        // } else if (this.reserveResult?.commitData?.reserve_mode) {
        //   modeObj = {
        //     value: this.reserveResult?.commitData?.reserve_mode
        //   };
        // 次卡上次约的数据
      } else if (this.lastMulData?.reserve_mode) {
        modeObj = {
          value: this.lastMulData?.reserve_mode
        };
        // 默认数据
      } else if (this.defaultMode?.value) {
        modeObj = this.defaultMode;
      }
      // this.tabList.forEach((item) => {
      //   if (+item.value === +modeObj.value) {
      //     findFlag = 1;
      //   }
      // });
      // let fCurTab = {};
      // if (findFlag) {
      //   fCurTab = modeObj;
      // } else {
      //   fCurTab = this.tabList[0];
      // }
      // 最终计算的默认tab
      this.fnlDefaultMode = modeObj;
      // 只有时间模式下，才会传用户点击的数据
      // if (+fCurTab.value === 1) {
      //   this.calendarParamsChuan = this.calendarParams;
      // }
      this.curTabInfo = modeObj;
    },
    // 修改预约、核销返现、守约返现的提示确认
    // diaConfirm() {
    //   // 剩余修改次数大于0时，弹预约原因弹窗
    //   if (this.lesTimes > 0) {
    //     this.rePop = true;
    //   } else {
    //     this.$bridge({
    //       url: `/packageAccount/consult?qzychannel=50`
    //     });
    //   }
    // },
    // 提交改约原因
    commitReason(data) {
      this.reason = data;
      if (!this.cancelAppointment) {
        // 填写修改原因后，直接提交预约
        this.commitDate();
      } else {
        // 填写修改原因后，直接取消预约
        this.commitCancelDate();
      }
    },
    addReson(data) {
      // 改约原因
      if (this.reason.primary) {
        data.reason += this.reason.primary;
        data.reason_type = this.reason.reasonType;
      }
      if (this.reason.secondary) {
        data.reason += ',' + this.reason.secondary;
      }
      if (this.reason.description) {
        data.reason += ',' + this.reason.description;
      }
      if (this.oldData.reserve_id) {
        data.reserve_id = this.oldData.reserve_id;
      }
    },
    // 取消预约
    cancelAppoint() {
      if (this.oldData.back_money <= 0) {
        this.cancelAppointment = true;
        this.rePop = true;
        this.$reportData({
          info: 'sy_wxtuan_tuan_subscribe_pop:cancel_reserve_click',
          ext: {
            order_id: this.order_id,
            uid: this.userInfo.uid
          }
        });
      } else {
        uni.showModal({
          title: '',
          content: '取消预约后，将无法参与返现活动',
          success: (res) => {
            if (res.confirm) {
              this.cancelAppointment = true;
              this.rePop = true;
              this.$reportData({
                info: 'sy_wxtuan_tuan_subscribe_pop:cancel_reserve_click',
                ext: {
                  order_id: this.order_id,
                  uid: this.userInfo.uid
                }
              });
            }
          }
        });
      }
    },
    jiaC() {
      if (!this.order_id) {
        return;
      }
      this.$bridge({
        url: `/packageAccount/consult?qzychannel=50`
      });
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:contact_consultant_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1
        }
      });
    },
    // 点击确认时间后，进行层层校验
    async confirmCommitDate() {
      // 剩余次数为0,或不支持预约 去联系客服
      // if (this.lesTimes === 0 && !this.isMul && this.oldData.month) {
      //   // this.jiaC();
      //   uni.showToast({
      //     title: '修改次数已用完，如有疑问，可咨询客服',
      //     icon: 'none'
      //   });
      //   return;
      // }
      // else if (!this.canReserve) {
      //   return;
      // }
      this.cancelAppointment = false;
      if (!this.selData?.time?.from) {
        uni.showToast({
          title: '请选择时间段再提交',
          icon: 'none'
        });
        return;
      }
      if (this.commitDisabled) {
        uni.showToast({
          title: '所选时间与之前预约时间相同',
          icon: 'none'
        });
        return;
      }
      // 改约情况下
      if (this.oldData.month > 0 && this.order_id) {
        this.rePop = true;
        // 初次预约情况下，直接提交
      } else {
        this.commitDate();
      }
      // if (this.oldData.month > 0 && this.order_id && !this.isMul) {
      //   this.popupStatus = true;
      // } else {
      //   // 当前需要弹窗
      //   if (this.isMul && this?.oldData?.is_obey_agreement) {
      //     this.popupStatus = true;
      //     return;
      //   }
      // if (+this.oldData.reserve_status === 1) {

      // this.rePop = true;
      // } else {
      //   this.commitDate();
      // }
      // }
    },
    // 校验按钮状态
    checkBtnStatus() {
      const val = this.selData;
      const oldData = this.oldData;
      // 判断除了时间切片外别的是否一致,每次改变都要计算
      if (
        +val?.date?.year === +oldData.year &&
        +val?.date?.month === +oldData.month &&
        +val?.date?.day === +oldData.day
        // +this.curCityData?.city_id === +oldData.city_id &&
      ) {
        if (
          +this.curTabInfo.value !== 3 &&
          +val.hospital?.hospital_id === +oldData.hospital_id
        ) {
          this.othersBlur = true;
        } else if (
          +this.curTabInfo.value === 3 &&
          val.doctor?.doctor_id === +oldData.doctor_id
        ) {
          this.othersBlur = true;
        } else {
          this.othersBlur = false;
        }
      } else {
        this.othersBlur = false;
      }
      // 机构、日期、时间都全了，doctor时也要选中了，才不置灰
      if (val?.date?.year && val?.time?.from) {
        if (val?.hospital?.hospital_id) {
          this.commitDisabled = false;
        } else if (val?.doctor) {
          if (val?.doctor?.doctor_id) {
            this.commitDisabled = false;
          } else {
            this.commitDisabled = true;
            return;
          }
        } else {
          this.commitDisabled = false;
        }
      } else {
        this.commitDisabled = true;
        return;
      }
      // 判断有没有和之前预约的时间一样
      if (
        +val.date.year === +oldData.year &&
        +val.date.month === +oldData.month &&
        +val.date.day === +oldData.day &&
        val.time.from === oldData.from &&
        val.time.to === oldData.to
        // +this.curCityData?.city_id === +oldData.city_id &&
      ) {
        // 选到了之前预约的日期
        if (
          +this.curTabInfo.value !== 3 &&
          +val.hospital?.hospital_id === +oldData.hospital_id
        ) {
          this.commitDisabled = true;
          this.oldTimeBlur = true;
        } else if (
          +this.curTabInfo.value === 3 &&
          val.doctor?.doctor_id === +oldData.doctor_id
        ) {
          this.oldTimeBlur = true;
          this.commitDisabled = true;
        } else {
          this.commitDisabled = false;
          this.oldTimeBlur = false;
        }
      } else {
        this.oldTimeBlur = false;
        this.commitDisabled = false;
      }
    },
    // 取消预约提交
    async commitCancelDate() {
      const obj = {
        order_id: this.order_id,
        reservation_id: this.reserve_id,
        reason: ''
      };
      this.addReson(obj);
      this.isCommitting = true;
      // todo 需要替换接口
      // const res = await toCancelReserve(obj);
      const res = await apiChainReservationCancelReserve({
        ...obj,
        sku_id: this.sku_id,
        ingredients_id: this.ingredients_id
      });

      this.isCommitting = false;
      if (res?.data?.errorCode === 200 || res?.data?.errorCode === 0) {
        this.$emit('success', {});
        this.$emit('onSuccessSubscribe', '取消预约成功~');
      } else {
        uni.showToast({
          title: res.errorMsg,
          icon: 'none'
        });
      }
    },
    async commitDate() {
      if (this.isCommitting) {
        // 防止暴力点击
        return;
      }
      let data;
      let url;
      // 之前没约过
      if (!this.oldData.month) {
        data = {
          reserve_from: 20,
          order_id: this.order_id,
          hospital_id: this.selData?.hospital?.hospital_id || '',
          date: this.selData.date.first_un,
          start_time: this.commitData.start_time,
          end_time: this.commitData.end_time,
          sku_id: this.sku_id,
          ingredients_id: this.ingredients_id,
          top_order_id: this.top_order_id
        };
        // todo 需要替换接口
        // 之前没约过
        // url = '/syGroupBuy/reservation/submitReservationV2';
        url = '/syGroupBuy/chain/reservation/reserveSubmit';
      } else {
        data = {
          reserve_from: 20,
          order_id: this.order_id,
          hospital_id: this.selData?.hospital?.hospital_id || '',
          new_date: this.selData.date.first_un,
          start_time: this.commitData.start_time,
          end_time: this.commitData.end_time,
          reason: '',
          reason_type: '',
          sku_id: this.sku_id,
          ingredients_id: this.ingredients_id,
          top_order_id: this.top_order_id
        };
        this.addReson(data);
        // todo 需要替换接口
        // url = '/syGroupBuy/reservation/editReservationV2';
        url = '/syGroupBuy/chain/reservation/reserveSubmit';
      }
      // 预约模式需要提交
      data.reserve_mode = this.curTabInfo.value;
      // 医生id有的情况下提交
      if (this.selData?.doctor?.doctor_id) {
        data.doctor_id = this.selData.doctor.doctor_id;
        data.hospital_id = this.selData?.time?.hospital_id;
      }
      if (this.reserve_id) {
        data.reserve_id = this.reserve_id;
      }
      if (this.request_time) {
        data.request_times = this.request_time;
      }
      // 计算停留时间
      // 确认订单页直接提交数据，不请求接口
      // if (!this.order_id) {
      //   // 是否是强制预约的标识需要提交给后端
      //   data.power_reserve = this.forceReserveInfo?.power_reserve || 0;
      //   this.successSkipTo(data);
      //   return;
      // }
      this.isCommitting = true; // 正在提交
      try {
        const res = await this.$request({
          url,
          data
        });
        this.isCommitting = false; // 正在提交
        if (res?.data?.errorCode === 200 || res?.data?.errorCode === 0) {
          this.countStayTime();
          // uni.showToast({
          //   title:
          //     res?.data?.responseData?.submit_success_txt || '预约提交成功',
          //   icon: 'none'
          // });
          this.clearDialog();
          await this.createGroupBySub(
            [
              'ZIM86tjmejzz-8G1WB-SE5sp3JBBaFwhrRToYXersg8',
              'VXiI_w92txkVKnxDc3BLFBuJIHRnAD2DZPpe_wxKQAY'
              // 'VXiI_w92txkVKnxDc3BLFBuJIHRnAD2DZPpe_wx'
            ],
            [res?.data?.responseData?.id]
          );
          this.$emit('success', { commitData: { ...data }, ...this.selData });
          this.$emit(
            'onSuccessSubscribe',
            res?.data?.responseData?.submit_success_txt || '预约提交成功'
          );
        } else if (res.data.errorMsg) {
          this.setToast(res.data.errorMsg);
        }
      } catch (err) {
        this.isCommitting = false; // 正在提交
        console.log(err);
      }
    },
    // 清空状态
    clearDialog() {
      this.popupStatus = false;
      this.rePop = false;
    },
    setToast(text, timeout = 2500) {
      clearTimeout(this.tstTimtout);
      this.toastText = text;
      this.tstTimtout = setTimeout(() => {
        this.toastText = '';
      }, timeout);
    }
    // 确认订单页提交数据
    // async successSkipTo(data, noCheck) {
    //   if (noCheck) {
    //     this.$emit('success', {
    //       commitData: { ...data },
    //       ...this.selData,
    //       forceReserveInfo: this.forceReserveInfo
    //     });
    //     this.countStayTime();
    //     return;
    //   }
    //   // 提交前做参数校验
    //   const res = await checkReserveStatus({
    //     ...data
    //   });
    //   const { errorCode, errorMsg } = res || {};
    //   if ((errorCode === 200 || errorCode === 0)) {
    //     this.$emit('success', {
    //       commitData: { ...data },
    //       ...this.selData,
    //       forceReserveInfo: this.forceReserveInfo
    //     });
    //     this.countStayTime();
    //   } else if (errorMsg) {
    //     this.setToast(errorMsg);
    //   } else {
    //     this.setToast('预约时间失败，请重试');
    //   }
    // }
  }
};
</script>

<style lang="less" scoped>
@bgColorF8: #f8f8f8;
@fontColor03: #030303;
@fontColor9A8E87: #9a8e87;

.flex-align-center() {
  display: flex;
  align-items: center;
}
.def-toast {
  position: fixed;
  z-index: 999999999;
  max-width: 50vw;
  top: 50%;
  left: 50%;
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  transform: translate3d(-50%, -50%, 999999px);
  padding: 30rpx;
  text-align: center;
  background: rgb(76, 76, 76);
  color: white;
  border-radius: 20rpx;
  white-space: wrap;
}
.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: @fontColor03;
  font-weight: 500;
  font-size: 32rpx;
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fade-in 0.3s ease-in-out forwards;
}

.pannel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 85vh;
  // border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  background-color: @bgColorF8;
  animation: fly-bottom-in 0.2s linear forwards;
  .btm-commit {
    position: fixed;
    width: 100%;
    left: 0px;
    bottom: 0px;
    z-index: 999;
    background-color: #fff;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
    .back-cash-text {
      display: flex;
      align-items: center;
      background-image: url(https://static.soyoung.com/sy-pre/u9xr0dj8nnex-1677485400713.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #333;
      font-weight: 400;
      width: 100%;
      box-sizing: border-box;
      // margin: 0 2rpx;
      height: 92rpx;
      .back-cash-icon {
        display: flex;
        align-items: center;
        height: 48rpx;
        margin-left: 29rpx;
        margin-right: 16rpx;
        padding-right: 12rpx;
        border-radius: 48rpx 48rpx 48rpx 0;
        background: @border-color;
        color: white;
        font-family: PingFangSC-Semibold;
        font-size: 24rpx;
        color: #ffffff;
        font-weight: 600;
        img {
          width: 32rpx;
          height: 28rpx;
          margin-right: 4rpx;
          margin-left: 12rpx;
        }
      }
      .back-cash-text-r {
        display: flex;
        align-items: center;
        .mark-red {
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #f85d2d;
        }
        .fan-text-ctn {
          height: 42rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: @text-color;
          font-weight: 400;
          padding: 0 8rpx;
          margin-right: 12rpx;
        }
      }
    }
    .warn-text {
      width: 100%;
      background: #ebfbdc;
      // height: 88rpx;
      padding: 10rpx 0;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: @text-color;
      letter-spacing: 0;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .warn-text-mul {
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        letter-spacing: 0;
        text-align: justify;
        line-height: 36rpx;
        font-weight: 400;
        margin-right: 30rpx;
        .warn-text-mul-resd {
          color: #222;
          margin-left: 30rpx;
        }
      }
      .warn-text-icon {
        margin-left: 24rpx;
        width: 28rpx;
        min-width: 28rpx;
        height: 30rpx;
        margin-right: 18rpx;
      }
      .cancel-appoint-btn {
        width: 164rpx;
        margin-right: 30rpx;
        min-width: 136rpx;
        height: 60rpx;
        border: 1px solid @border-color;
        white-space: nowrap;
        //border-radius: 34rpx;
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        color: @text-color;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .warn-text-left {
        display: flex;
        align-items: center;
      }
      .warn-text-middle {
        font-family: PingFangSC-Medium;
        font-size: 26rpx;
        color: #030303;
        font-weight: 500;
        .warn-text-middle-hos {
          width: 500rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #646464;
          font-weight: 400;
        }
        .warn-text-middle-hos-long {
          width: 650rpx;
        }
      }
    }
    .commit-btn {
      width: 690rpx;
      height: 88rpx;
      box-sizing: border-box;
      background: @border-color;
      color: #fff;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      letter-spacing: 0;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      .commit-btn-in {
        display: flex;
        flex-direction: column;
        align-items: center;
        .two-line {
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
        }
        .line-second {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
        }
      }
    }
    .commit-btn-change {
      margin: 0 auto;
    }
    .confirm-cmt-btn {
      background-color: #fff;
      color: @text-color;
      width: 520rpx;
      box-sizing: border-box;
      border-radius: 44rpx;
      overflow: hidden;
      .stay-appoint {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-right: 0;
        box-sizing: border-box;
        border-radius: 44rpx 0 0 44rpx;
        width: 50%;
        height: 100%;
        background: #e2f8f1;
        .line-1 {
          font-family: PingFangSC-Medium;
          font-size: 30rpx;
          color: @text-color;
          letter-spacing: 0;
          text-align: center;
          line-height: 28rpx;
          font-weight: 500;
        }
        .line-2 {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: @text-color;
          letter-spacing: 0;
          text-align: center;
          line-height: 20rpx;
          font-weight: 400;
          margin-top: 10rpx;
        }
      }
      .to-appoint {
        width: 50%;
        height: 100%;
        font-family: PingFangSC-Medium;
        font-size: 30rpx;
        box-sizing: border-box;
        background: @border-color;
        color: #fff;
        letter-spacing: 0;
        text-align: center;
        line-height: 28rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .btn-disabled.confirm-cmt-btn {
      background-color: #fff;
      color: @text-color;
      .to-appoint {
        opacity: 0.3;
        background: @border-color;
      }
    }
    .taocan-commit-ctn {
      .mul-commit-left-ctn {
        display: flex;
        padding: 0 44rpx;
        .mul-commit-left {
          width: 116rpx;
        }
        .mul-commit-left2 {
          img {
            width: 50rpx;
            height: 50rpx;
            padding: 4rpx 0;
          }
        }
      }
    }
    .slot-wrap {
      margin-left: 30rpx;
      font-family: none;
    }
    .mul-commit-ctn {
      display: flex;
      height: 108rpx;
      box-sizing: border-box;
      align-items: center;
      background: #ffffff;
      .mul-commit-left {
        width: 250rpx;
        display: flex;
        align-items: center;
        font-size: 20rpx;
        color: @fontColor03;
        letter-spacing: 0;
        .mul-left-jiac {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 auto;
          // padding-left: 30rpx;
        }
        img {
          width: 56rpx;
          height: 56rpx;
        }
        .price {
          font-family: PingFangSC-Medium;
          font-size: 36rpx;
          color: @fontColor03;
          letter-spacing: 0;
          font-weight: 500;
        }
        .price-reduce {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #777777;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
      .commit-btn {
        margin-right: 30rpx;
      }
      .commit-btn1 {
        margin-left: 30rpx;
      }
    }
    .btn-disabled {
      background: #dedede;
      color: white;
    }
  }
}
.confirm-pannel {
  height: 95vh;
}
.title {
  padding: 0 30rpx;
  background-color: @bgColorF8;
  .title-h1 {
    font-family: PingFangSC-Medium;
    font-size: 32rpx;
    color: @fontColor03;
    font-weight: 500;
    padding-top: 40rpx;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    .title-h1-l {
      transition: all 0.2s;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .title-h1-r {
      margin-left: 10rpx;
      background: #89dc65;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: #030303;
      letter-spacing: 0;
      font-weight: 400;
      width: 50rpx;
      height: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .title-h2 {
    transition: all 0.2s;
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: @fontColor9A8E87;
    letter-spacing: 0;
    height: 42rpx;
    opacity: 1;
    line-height: 42rpx;
    font-weight: 400;
    display: flex;
    align-items: center;
    margin-top: 14rpx;
    .img1 {
      width: 22rpx;
      // height: 24rpx;
      margin-right: 8rpx;
    }
    .img2 {
      width: 12rpx;
      height: 16rpx;
      margin-left: 6rpx;
      object-fit: contain;
    }
  }
  .tab-list {
    margin-top: 30rpx;
  }
}
.title-t {
  // transition: all 0.2s;
  .title-h1 {
    // transition: all 0.2s;
    padding-top: 30rpx;
    .title-h1-l {
      transition: all 0.2s;
      font-family: PingFangSC-Medium;
      font-size: 32rpx;
      color: @fontColor03;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
  // .title-h2 {
  //   transition: all 0.2s;
  //   opacity: 0;
  //   height: 0rpx;
  //   margin: 0rpx;
  //   overflow: hidden;
  // }
}
.modals {
  width: 100%;
  height: calc(100vh - 308rpx);
  overflow-y: hidden;
}
.close {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 38rpx;
  background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1727167736758.png)
    no-repeat center / 100%;
  z-index: 9;
}
.mask-tiao {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
  position: absolute;
  right: 30rpx;
  top: 30rpx;
}
</style>
<style>
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fly-bottom-in {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
</style>
