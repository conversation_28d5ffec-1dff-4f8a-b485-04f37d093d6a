<template>
  <div class="doctor-modal">
    <div class="doctor-list">
      <div class="doc-num">
        <span>全部医生</span>
        <span class="doc-num-txt">(共{{ doctorList.length || 0 }}位)</span>
      </div>
    </div>
    <scroll-view
      @scroll="hosScroll"
      @scrolltoupper="scrolltoupper"
      :scroll-x="false"
      :scroll-y="true"
      :show-scrollbar="false"
      class="doc-list"
    >
      <div v-if="doctorList.length" class="doctor-ctn">
        <DoctorCard
          v-for="item in doctorList"
          :key="item.doctor_id"
          :itemData="item"
          @hosClick="doctorClick(item)"
        ></DoctorCard>
      </div>
      <div v-else-if="loading" class="loading">加载中...</div>
      <div class="empty-ctn" v-else>
        <Empty
          :reportData="{
            uid: userInfo.uid,
            pid: pid,
            order_id: order_id,
            month: oldData.month
          }"
          :remain="emptyRemain"
        ></Empty>
      </div>
      <div class="doc-list-btm">
        <!-- <img
          src="https://static.soyoung.com/sy-pre/20240606-175421-1717675800638.png"
        /> -->
      </div>
    </scroll-view>
    <div class="modal-mask" v-if="visible">
      <div class="pannel" @click.stop>
        <DoctorModelDetail
          :oldData="oldData"
          :baseInfo="baseInfo"
          :request_time="request_time"
          :lastMulData="lastMulData"
          :othersBlur="othersBlur"
          :reserveResult="reserveResult"
          :dateBtmTxtUrl="dateBtmTxtUrl"
          :order_id="order_id"
          :doctor_list="doctorList"
          :doctor_id="doctor_id"
          :city_id="city_id"
          :pid="pid"
          :card_num="card_num"
          :clickDoctorId="curDoctor.doctor_id"
          :package_id="package_id"
          @calendarVisible="(val) => $emit('calendarVisible', val)"
          @setTTop="(el) => $emit('setTTop', el)"
          @setSkelReady="setSkelReady"
          @change="selDataChange"
          @close="diaClose"
        ></DoctorModelDetail>
      </div>
    </div>
  </div>
</template>

<script>
import { calendarForDoctor } from '@/api/reservation.js';
import DoctorCard from '@/components/appointNew/components/doctorListItem.vue';
import DoctorModelDetail from '@/components/appointNew/components/doctorModelIndex.vue';
import Empty from '@/components/appointNew/components/empty.vue';
import { mapState } from 'vuex';
export default {
  props: {
    pid: {
      type: Number,
      default: 0
    },
    card_num: {
      type: Number,
      default: 0
    },
    request_time: {
      type: Number,
      default: 0
    },
    skeletonReady: {
      type: Boolean,
      default: false
    },
    othersBlur: {
      type: Boolean,
      default: false
    },
    // 确认订单页，用户已预约的日期
    reserveResult: {
      type: Object,
      default: () => {}
    },
    oldData: {
      type: Object,
      default: () => {}
    },
    fnlDefaultMode: {
      // 默认的预约模式
      type: Object,
      default: () => {}
    },
    baseInfo: {
      type: Object,
      default: () => {}
    },
    lastMulData: {
      type: Object,
      default: () => {}
    },
    dateBtmTxtUrl: {
      type: Object,
      default: () => {}
    },
    package_id: {
      type: Number,
      default: 0
    },
    order_id: {
      type: String,
      default: ''
    },
    city_id: {
      type: Number,
      default: 0
    }
  },
  components: { DoctorCard, Empty, DoctorModelDetail },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  watch: {
    // 城市改变，重新请求数据
    city_id() {
      this.initdoctorList();
      // 初始化日期数据
    }
  },
  created() {
    this.initdoctorList('init');
  },
  data() {
    return {
      loading: false,
      visible: false, // 按机构预约弹窗
      emptyRemain: '当前商品暂不支持按医生预约，请切换预约方式',
      curDoctor: {}, // 当前点击的机构
      doctorList: []
    };
  },
  methods: {
    scrolltoupper() {
      this.$emit('setTTop', false);
    },
    hosScroll(e) {
      const top = e?.detail?.scrollTop;
      // 再往下走
      if (top - this.lastTop > 0) {
        if (top > 5) {
          this.$emit('setTTop', true);
        }
      }
      this.lastTop = top;
    },
    diaClose() {
      this.visible = false;
      this.$reportData({
        info: 'y_wxtuan_or_subscribe_pop:switch_views_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.pid,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 1
        }
      });
    },
    async initdoctorList() {
      this.loading = true;
      this.doctorList = [];
      const reqData = {
        order_id: this.order_id,
        city_id: this.city_id,
        pid: this.pid,
        package_id: this.package_id
      };
      const res = await calendarForDoctor(reqData);
      this.loading = false;
      if (res) {
        this.doctorList = res || [];
        this.spliceDoctor(this.doctorList);
        if (
          (this.oldData.doctor_id ||
            (+this.fnlDefaultMode?.value === 3 && !this.order_id) || // 确认订单页初始化默认锚定到此模式时
            this.lastMulData.doctor_id) &&
          this.doctorList?.length &&
          !this.skeletonReady
        ) {
          this.visible = true;
        } else {
          this.setSkelReady();
        }
      } else {
        this.setSkelReady();
      }
    },
    setSkelReady() {
      this.$emit('update:skeletonReady', true);
    },
    spliceDoctor(docList = []) {
      if (!docList?.length) return;
      const id =
        this.oldData.doctor_id ||
        this.reserveResult?.doctor?.doctor_id ||
        this.lastMulData.doctor_id;
      if (id) {
        let index = 0;
        docList.forEach((item, idx) => {
          if (+item.doctor_id === +id) {
            index = idx;
          }
        });
        if (index === 0) return;
        const newArr = docList.splice(index, 1);
        docList.unshift(newArr[0]);
      }
    },
    // 组件内的数据改变
    selDataChange(data) {
      // 数据改变，向上触发数据改变
      this.$emit('setData', data);
    },
    // 点击某个医生
    doctorClick(data) {
      this.curDoctor = data;
      this.visible = true;
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_doctor_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.pid,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 3,
          city_id: this.city_id,
          doctor_id: data.doctor_id
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.doctor-modal {
  background: #f8f8f8;
  position: relative;
  .doctor-list {
    width: 100%;
    height: 140rpx;
    background-image: linear-gradient(
      180deg,
      #ffffff 38%,
      rgba(255, 255, 255, 0) 74%
    );
    border-radius: 10px 10px 0 0;
  }
  .doc-num {
    padding: 30rpx 30rpx 0 30rpx;
    display: flex;
    justify-content: space-between;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #222222;
    font-weight: 400;
    .doc-num-txt {
      font-size: 24rpx;
      color: #777;
    }
  }
  .doc-list {
    padding: 0rpx 30rpx 0;
    height: calc(85vh - 250rpx);
    margin-top: -50rpx;
    overflow-y: auto;
    box-sizing: border-box;
    .doc-list-btm {
      height: 350rpx;
      padding-top: 30rpx;
      img {
        display: block;
        margin: 0 auto;
        width: 234rpx;
        height: 132rpx;
      }
    }
    .empty-ctn {
      height: 422rpx;
      padding-top: 50rpx;
      background-color: white;
    }
    .loading {
      text-align: center;
      line-height: 416rpx;
      height: 416rpx;
      border-radius: 20rpx;
      background-color: #fff;
      color: #aaabb3;
    }
  }
}
</style>
<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: absolute;
  top: 0rpx;
  left: 0;
  width: 100vw;
  overflow: hidden;
  z-index: 1;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(1px);
}

.pannel {
  width: 100%;
  border-radius: 30rpx 30rpx 0 0;
  background-color: #f8f8f8;
  animation: fly-bottom-in 0.2s linear forwards;
}
@keyframes fly-bottom-in {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}
</style>
