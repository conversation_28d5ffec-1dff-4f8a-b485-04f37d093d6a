<template>
  <root-portal>
    <div
      class="custom-popup"
      :style="yourStyle"
      v-show="visible"
      @click="onMaskClick"
    >
      <div class="warp" @click.stop>
        <div class="header">预约取消通知</div>
        <div class="body">
          <div class="notify">
            很抱歉地通知您:
            <div style="text-indent: 2em">
              您的<span class="em" v-show="cancelApptCnt > 1">
                {{ cancelApptCnt }}条</span
              >预约因机构原因无法正常接待，很抱歉取消您的预约，给您带来的不便敬请谅解~
            </div>
          </div>
          <div class="list">
            <div class="card" v-for="(card, index) in list" :key="index">
              <div class="row">
                <div class="col">预约时间：</div>
                <div class="col">{{ card.reserve_date }}</div>
              </div>
              <div class="row">
                <div class="col">预约项目：</div>
                <div class="col">{{ card.item_product }}</div>
              </div>
              <div class="row">
                <div class="col">预约机构：</div>
                <div class="col">{{ card.hospital }}</div>
              </div>
            </div>
          </div>
          <div class="more" v-if="cancelApptCnt > 2">滑动查看更多</div>
        </div>
        <div class="footer">
          <div class="btns">
            <div class="btn" @click="close">暂不预约</div>
            <div class="btn confrim" @click="go2order">重新预约</div>
          </div>
        </div>
      </div>
    </div>
  </root-portal>
</template>

<script>
import { getPopupWindow, confirmPopupWindow } from '@/api/reservation.js';
import { mapState, mapGetters } from 'vuex';
export default {
  props: {
    yourStyle: String
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin', 'hasWxAvatar']),
    cancelApptCnt() {
      return this.list.length || 0;
    }
  },
  data() {
    return {
      visible: false,
      list: []
    };
  },
  created() {
    this.unwatcher = null;
  },
  beforeDestroy() {
    this.unwatcher = null;
  },
  onPageShow() {
    this.visible = false;
    if (this.isLogin) {
      this.fetchData();
    } else {
      this.unwatcher?.();
      this.unwatcher = this.$watch('isLogin', (value) => {
        if (value) {
          this.fetchData();
        }
      });
      this.$emit('next');
    }
  },
  onPageHide() {
    this.unwatcher?.();
    this.visible = false;
  },
  methods: {
    async fetchData() {
      const { responseData } = await getPopupWindow();
      if (
        !responseData ||
        !responseData.list ||
        responseData.list.length === 0
      ) {
        this.visible = false;
        this.$emit('next');
        return;
      }
      this.list = responseData.list;
      // this.list.length = 1;
      await this.$nextTick();
      this.visible = true;
    },
    async confirmAppCancel() {
      return confirmPopupWindow({
        reserve_ids: this.list.map(({ id }) => id).join(',')
      });
    },
    close() {
      this.visible = false;
      this.confirmAppCancel();
      this.$emit('next');
    },
    async go2order() {
      if (this.jumping) return;
      this.jumping = true;
      await this.confirmAppCancel();
      const currentPages = getCurrentPages();
      const currentPage = currentPages[currentPages.length - 1];
      switch (this.cancelApptCnt) {
        case 1:
          {
            if (/packageOrder\/order-detail/g.test(currentPage.route)) {
              const [{ order_id }] = this.list;
              wx.redirectTo({
                url: `/packageOrder/order-detail?orderId=${order_id}&isShowYuyue=1`
              });
            } else {
              const [{ order_id }] = this.list;
              this.$bridge({
                url: `/packageOrder/order-detail?orderId=${order_id}&isShowYuyue=1`
              });
            }
          }
          break;
        default:
          {
            if (!/pages\/order-list/g.test(currentPage.route)) {
              uni.switchTab({
                url: '/pages/order-list'
              });
            } else {
              this.close();
            }
          }
          break;
      }
      this.jumping = false;
    },
    onMaskClick() {
      // this.visible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.custom-popup {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9999999;
  top: 0px;
  left: 0px;
  background-color: rgba(0, 0, 0, 0.6);
  transform: translateZ(999px);
  text-align: center;
  .warp {
    box-sizing: border-box;
    background-color: #fff;
    width: 300 * 2rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 25 * 2rpx 20 * 2rpx;
    border-radius: 15px;
    .header {
      position: relative;
      font-size: 18 * 2rpx;
      color: #333333;
      text-align: center;
      font-weight: 500;
      margin-bottom: 10 * 2rpx;
      .close {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        right: 0rpx;
        top: 4rpx;
        background: url(https://static.soyoung.com/sy-pre/close-1661847000665.png)
          no-repeat center / 100%;
        z-index: 2;
      }
    }
    .body {
      font-size: 15 * 2rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      .notify {
        margin-bottom: 30rpx;
        font-size: 28rpx;
        color: #333333;
        letter-spacing: -0.4px;
        text-align: justify;
        line-height: 48rpx;
        font-weight: 400;
        .em {
          font-weight: bold;
        }
      }
      .list {
        max-height: 224 * 2rpx;
        overflow-y: auto;
        font-size: 26rpx;
        color: #777777;
        line-height: 24px;
        font-weight: 400;
        .card {
          margin-bottom: 30rpx;
          box-sizing: border-box;
          padding: 20rpx 16rpx;
          display: table;
          width: 100%;
          min-height: 184rpx;
          background: #f8f8f8;
          border-radius: 8rpx;
          &:last-child {
            margin-bottom: 0;
          }
          .row {
            display: table-row;
          }
          .col {
            display: table-cell;
            &:first-child {
              width: 140rpx;
            }
            &:last-child {
              text-align: left;
            }
          }
        }
      }
      .more {
        padding-top: 20rpx;
        font-size: 26rpx;
        color: #777777;
        text-align: center;
        height: 48rpx;
        line-height: 48rpx;
        font-weight: 400;
      }
    }
    .btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15 * 2rpx;
      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 68rpx;
        width: 240rpx;
        border: 0.5px solid @border-color;
        border-radius: 20px;
        color: @text-color;

        &.confrim {
          background-color: @border-color;
          color: #fff;
        }
      }
    }
  }
}
</style>
