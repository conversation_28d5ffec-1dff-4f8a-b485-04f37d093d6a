<template>
  <div
    class="cash-back-notice"
    @click="handleClick"
    :style="{ background: bgColor }"
  >
    <image
      class="notice-image"
      mode="widthFix"
      src="https://static.soyoung.com/sy-pre/20250523-124858-1747973400676.png"
      alt=""
    />
    <div class="notice-text">
      <span class="notice-text-content">{{ notice }}</span>
      <span class="notice-icon-box" v-if="noticeUrl && showNotice">
        <image
          class="notice-icon"
          mode="widthFix"
          src="https://static.soyoung.com/sy-pre/20250520-154527-1747725000641.png"
          alt=""
        />
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    notice: {
      type: String,
      default: ''
    },
    showNotice: {
      type: Boolean,
      default: true
    },
    noticeUrl: {
      type: String,
      default: 'https://m.soyoung.com/tmwap27174'
    },
    bgColor: {
      type: String,
      default: '#F8F8F8'
    }
  },
  methods: {
    handleJump(link) {
      if (link.indexOf('.com') > -1 || link.indexOf('http') > -1) {
        this.$toH5(link);
      } else {
        this.$bridge({
          url: link
        });
      }
    },
    handleClick() {
      if (this.noticeUrl) {
        this.handleJump(this.noticeUrl);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.cash-back-notice {
  display: flex;
  align-items: flex-start;
  padding: 13rpx 10rpx;
  background: #f8f8f8;
  line-height: 26rpx;
  .notice-image {
    width: 30rpx;
    height: 30rpx;
    margin-right: 10rpx;
    margin-top: -2rpx;
    flex-shrink: 0;
  }
  .notice-text-content {
    display: inline;
    line-height: 30rpx;
  }
  .notice-icon-box {
    display: inline-flex;
    width: 30rpx;
    height: 30rpx;
    line-height: 30rpx;
    margin-left: 4rpx;
    align-items: center;
    vertical-align: -2rpx;
    justify-content: center;
  }
  .notice-icon {
    width: 22rpx;
    height: 22rpx;
  }
  .notice-text {
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    line-height: 30rpx;
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
}
</style>
