<template>
  <div class="doctor-item" @click="hosClick(itemData)">
    <div class="doctor-left">
      <img :src="itemData.icon" />
    </div>
    <div class="doctor-right">
      <div class="line-t">
        <span>{{ itemData.doctor_name }}</span>
        <div v-if="itemData.job" class="zhicheng">
          <img
            src="https://static.soyoung.com/sy-pre/1clxo4ar2xjzp-1683198600747.png"
          />
          <span>{{ itemData.job }}</span>
        </div>
        <div v-if="itemData.doctor_tag == 2" class="zhicheng daka">
          <img
            src="https://static.soyoung.com/sy-pre/2eph3qr81jfb6-1683288600748.png"
          />
          <span>大咖医生</span>
        </div>
      </div>
      <div class="line-h">
        <div class="line-h-l">
          <span v-if="itemData.hos_info.length > 1"
            >服务{{ itemData.hos_info.length }}家机构 &nbsp;|&nbsp;</span
          >
          <span v-for="(item, index) in itemData.hos_info" :key="index"
            >{{ item.hospital_name
            }}{{ index + 1 === itemData.hos_info.length ? '' : ',' }}</span
          >
        </div>
        <div v-if="itemData.hos_info.length === 1">
          {{ itemData.hos_info[0].distance_str }}
        </div>
      </div>
      <div class="line-y">
        <span v-if="itemData.work_experience"
          >{{ itemData.work_experience }}年从业经验</span
        >
        <span v-else>从业经验丰富</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    city_id: {
      type: Number,
      default: 0
    },
    itemData: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {},
  data() {
    return {};
  },
  methods: {
    hosClick(data) {
      this.$emit('hosClick', data);
    }
  }
};
</script>

<style lang="less" scoped>
.doctor-item {
  padding: 30rpx;
  display: flex;
  flex-wrap: nowrap;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  .doctor-left {
    margin-right: 20rpx;
    img {
      width: 82rpx;
      height: 82rpx;
      border-radius: 50% 50% 0 50%;
    }
  }
  .doctor-right {
    flex: 1;
    .line-t {
      font-family: PingFangSC-Medium;
      font-size: 28rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
      display: flex;
      align-items: center;
      & > img {
        width: 114rpx;
        height: 32rpx;
        margin-left: 10rpx;
      }
      .zhicheng {
        height: 32rpx;
        position: relative;
        padding-left: 18rpx;
        font-family: PingFangSC-Medium;
        font-size: 20rpx;
        border-radius: 0 4rpx 4rpx 0;
        color: white;
        display: flex;
        align-items: center;
        padding-right: 6rpx;
        margin-left: 26rpx;
        background-image: linear-gradient(
          134deg,
          #b79b91 0%,
          #c8b1a7 49%,
          #c0a69c 100%
        );
        border-radius: 0 4rpx 4rpx 0;
        img {
          width: 32rpx;
          height: 32rpx;
          position: absolute;
          top: 0;
          left: -16rpx;
        }
      }
      .daka {
        background-image: none;
        background-color: @border-color;
        padding-left: 6rpx;
        border-radius: 4rpx;
        margin-left: 10rpx;
        img {
          position: static;
        }
      }
    }
    .line-h {
      font-family: PingFangSC-Regular;
      font-size: 22rpx;
      color: #777777;
      letter-spacing: 0;
      font-weight: 400;
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      margin-top: 10rpx;
      .line-h-l {
        max-width: 440rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .line-y {
      font-family: PingFangSC-Regular;
      font-size: 22rpx;
      color: #777777;
      letter-spacing: 0;
      font-weight: 400;
      margin-top: 10rpx;
    }
  }
}
</style>
