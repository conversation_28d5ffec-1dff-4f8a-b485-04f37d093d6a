<template>
  <div
    class="calendar-select-popup"
    :class="{
      'confirm-commit': isConfirmPage,
      enter: calendarVisible,
      leave: !calendarVisible && !firstStatus,
      'auto-height': type == 'hos'
    }"
  >
    <div class="calendar-select-popup-mask"></div>
    <div class="picker-toolbar">
      <img
        src="https://static.soyoung.com/sy-design/3cj8rc3ipek931726715721972.png"
        class="cancel-icon"
        @click="tCalendarHandel"
      />
      <div class="picker-title">选择到店时间</div>
    </div>
    <ul class="main-head">
      <!-- :class="{ 'red': item === '六' || item === '日' }" -->
      <li v-for="(item, index) in calendarHeader" :key="index">
        <img
          v-if="xyDay && item === '二'"
          src="https://static.soyoung.com/sy-design/1a7irk98co8nq1748339561896.png"
        />
        <img
          v-if="xyDay && item === '四'"
          src="https://static.soyoung.com/sy-design/1dejxywlyb2fd1748339561900.png"
        />
        {{ item }}
      </li>
    </ul>
    <!-- <p class="tip" :class="{'buttom-iphone-x': isIphoneX}">已开放未来30天内的时间可预约</p> -->
    <div class="calendar-select-popup-cont">
      <div class="top-height"></div>
      <div :class="{ 'l-btm': lBtm }" class="calendar">
        <!-- 每个月 start  -->
        <div class="calendar__item" v-for="(data, v) in nextOptions" :key="v">
          <div
            class="year-month next-sty"
            v-if="Object.keys(nextOptions).length"
          >
            {{ getYear(data) }}年{{ getMonth(data) }}月
          </div>
          <div class="contain-day">
            <template v-for="(item, index) in data">
              <div :class="['item-day', { 'no-item': !item.day }]" :key="index">
                <div
                  class="item-info"
                  @click="selectedDate(item)"
                  :class="{
                    'selected-date': selectDateFun(item),
                    'yx-day':
                      xyDay &&
                      (item.week == 2 || item.week == 4) &&
                      !(item.status === 0 || item.remain_num <= 0),
                    'border':
                      item.day && !(item.status === 0 || item.remain_num <= 0)
                  }"
                >
                  <img
                    class="yx-day-icon"
                    v-if="
                      xyDay &&
                      (item.week == 2 || item.week == 4) &&
                      !(item.status === 0 || item.remain_num <= 0)
                    "
                    src="https://static.soyoung.com/sy-design/4s7mfddmmsga1748339561930.png"
                  />
                  <div
                    class="item-date"
                    v-if="item.day"
                    :class="{
                      'selected-text': selectDateFun(item),
                      gray: item.status === 0 || item.remain_num <= 0
                    }"
                  >
                    {{
                      currentDay === Number(item.day) &&
                      currentMonth === Number(item.month)
                        ? '今天'
                        : currentDay + 1 === Number(item.day) &&
                          currentMonth === Number(item.month)
                        ? '明天'
                        : Number(item.day) || ''
                    }}
                  </div>
                  <div class="item-date-fan" v-if="item.day && item.cashback">
                    {{ item.cashback }}
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <!-- 每个月 end    -->
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';

export default {
  name: 'calendar',
  props: {
    calendarVisible: { type: Boolean },
    lBtm: { type: Boolean, default: false },
    firstStatus: { type: Number },
    nextOptions: { type: Array },
    activeDateTab: { type: Object },
    type: { type: String }
  },
  computed: {
    ...mapState({
      xyDay: (state) => state.reserve.yxDay
    }),
    isConfirmPage() {
      return (
        getCurrentPages()[getCurrentPages().length - 1]?.route ===
        'packageOrder/confirmOrder'
      );
    }
  },
  data() {
    return {
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      currentDay: new Date().getDate(),
      calendarHeader: ['一', '二', '三', '四', '五', '六', '日'],
      next_calendar_list: [],
      currentMonthList: 0 // 当前月份的显示长度
    };
  },
  watch: {
    nextOptions: {
      handler(val) {
        console.log(val, 'nextOptions');
      },
      immediate: true
    }
  },
  methods: {
    getYear(arr) {
      return arr.find((i) => {
        return i.year;
      }).year;
    },
    getMonth(arr) {
      return arr.find((i) => {
        return i.month;
      }).month;
    },
    selectedDate(date) {
      const length = Object.keys(date).length;
      length && this.$parent.selectDate(date);
      length && this.$parent.watchClientRect();
      this.$parent.calendarHandel();
    },
    tCalendarHandel() {
      this.$parent.calendarHandel();
    },
    selectDateFun(item) {
      const that = this;
      if (this.activeDateTab) {
        return (
          item.month === that.activeDateTab.month &&
          item.day === that.activeDateTab.day
        );
      } else return false;
    }
  }
};
</script>
<style scoped lang="less">
@keyframes moveEnter {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0%);
  }
}
@keyframes moveLeave {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(100%);
  }
}
.calendar-select-popup {
  position: fixed;
  left: 0rpx;
  bottom: 0rpx;
  height: 85vh;
  overflow-y: hidden;
  z-index: 999999999;
  width: 100%;
  transform: translateX(100%) translateZ(0);
  &.enter {
    animation: moveEnter 0.5s;
    animation-fill-mode: forwards;
  }
  &.leave {
    animation: moveLeave 0.5s;
    animation-fill-mode: forwards;
  }
  &.auto-height {
    height: calc(85vh - 296rpx);
  }
  .main-head {
    background: #f8f8f8;
    height: 39 * 2rpx;
    display: flex;
    box-sizing: border-box;
    padding: 0 20rpx;
    li {
      width: 107rpx;
      height: 100%;
      display: flex;
      // align-items: center;
      padding-top: 28rpx;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      line-height: 36rpx;
      color: #555555;
      text-align: center;
      font-weight: 400;
      position: relative;
      box-sizing: border-box;
      img {
        position: absolute;
        width: 88rpx;
        height: 28rpx;
        left: 50%;
        transform: translateX(-50%);
        top: 0;
      }
    }
    .red {
      color: #f85d2d;
    }
  }
  .picker-toolbar {
    height: 104rpx;
    background-color: #fff;
    position: relative;
    .picker-title {
      font-size: 17 * 2rpx;
      color: #030303;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      line-height: 104rpx;
      height: 104rpx;
      text-align: center;
      flex-grow: 1;
    }
    .close-btn {
      position: absolute;
      right: 32rpx;
      top: 24rpx;
      width: 40rpx;
      height: 40rpx;
      background: url('https://static.soyoung.com/sy-pre/19e0lndlqfm4n-1595855400700.png')
        center/contain no-repeat;
    }
    .cancel-icon {
      position: absolute;
      top: 15rpx;
      left: 5rpx;
      width: 80rpx;
      height: 80rpx;
    }
  }
}
.calendar-select-popup-mask {
  display: none;
}
.confirm-commit {
  height: 96vh;
  background: transparent;
  .calendar-select-popup-mask {
    display: block;
    background: rgba(0, 0, 0, 0.5);
    height: 12vh;
  }
}
.calendar-select-popup-cont {
  overflow-y: auto;
  height: calc(85vh - 184rpx);
  box-sizing: border-box;
  background-color: #ffffff;
  .top-height {
    width: 100%;
    height: 20rpx;
  }
}
.app-calendar-sty {
  background: #ebf9f6 !important;
}
.app-text-color {
  color: #00af84 !important;
}
.calendar {
  box-sizing: border-box;
  padding-bottom: 180rpx;
  //overflow: scroll;
  //scroll-behavior: smooth;
  //-webkit-overflow-scrolling: touch;
  // &__item {
  //   &:first-child {
  //     .next-sty {
  //       .border(0 0 1rpx 0);
  //     }
  //   }
  // }

  .main-head {
    width: 107rpx;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Regular;
    font-size: 30rpx;
    color: #555555;
    text-align: center;
    font-weight: 400;
    background: #f6f9f9;
  }
  ul {
    background: #f6f9f9;
    height: 90rpx;
    display: flex;
    li {
      width: 107rpx;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 30rpx;
      color: #555555;
      text-align: center;
      font-weight: 400;
    }
  }
  .year-month {
    position: sticky;
    top: 0;
    height: 102rpx;
    font-family: PingFangSC-Medium;
    font-size: 30rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    line-height: 102rpx;
    background-color: #fff;
    transform: translate3d(0, 0, 0);
    z-index: 10;
  }
  .contain-day {
    display: flex;
    flex-wrap: wrap;
    padding: 0 30rpx;
    flex-direction: row;
    gap: 12rpx;
    // justify-content: space-between;
  }
  .item-day {
    width: 44 * 2rpx;
    //margin-bottom: 20rpx;
    display: flex;
    justify-content: center;
    //margin-left: 14rpx;
    &.no-item {
      .item-info {
        background-color: transparent;
      }
    }
    .item-info {
      width: 44 * 2rpx;
      height: 53 * 2rpx;
      padding-top: 20rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      position: relative;
      position: relative;
      background: #f8f8f8;
      .yx-day-icon {
        position: absolute;
        width: 22rpx;
        height: 24rpx;
        left: -2rpx;
        top: -2rpx;
      }
      &.selected-date {
        background: #373a36;
        .item-date-fan {
          color: #fff;
        }
      }
    }
    .item-date {
      width: 80rpx;
      font-family: OutFit-Regular;
      font-size: 14 * 2rpx;
      color: #030303;
      text-align: center;
      line-height: 30rpx;
      font-weight: 500;
      &.gray {
        color: #aaabb3;
      }
      &.selected-text {
        color: #fff;
      }
    }
    .yx-day {
      //background: #e2f8f1;
      //border: 2rpx solid #e7e4fa;
      .item-date {
        color: #030303;
        &.gray {
          color: #aaabb3;
        }
        &.selected-text {
          color: #fff;
        }
      }
      .item-date-fan {
        // color: #030303;
      }
    }
    .item-date-fan {
      font-family: PingFangSC-Regular;
      font-size: 18rpx;
      color: #f85d2d;
      letter-spacing: 0;
      text-align: center;
      line-height: 20rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      margin-top: 6rpx;
      .m-txt {
        margin: 0 -4rpx;
      }
    }
    .item-text {
      margin-top: 10rpx;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #aaabb3;
      letter-spacing: 0;
      text-align: center;
      line-height: 22rpx;
      font-weight: 400;
    }
    .can-use {
      color: #222;
    }
  }
  .item-day:nth-child(7n + 1) {
    margin-left: 0;
  }
}
.l-btm {
  padding-bottom: 650rpx;
}
.border(@width) {
  position: relative;
  &::before {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    border: 1rpx solid #f0f0f0;
    border-width: @width;
    transform-origin: 0 0;
    transform: scale(0.5);
  }
}
</style>
