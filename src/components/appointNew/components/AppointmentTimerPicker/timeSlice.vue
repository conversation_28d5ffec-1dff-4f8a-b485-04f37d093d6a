<template>
  <div>
    <!-- 时间切片 -->
    <div
      class="time-item"
      v-for="(time, index) in timeList"
      :key="index"
      :class="{
        widthSm1: small1,
        active:
          value.from == time.from &&
          value.to == time.to &&
          hosDataFocus.hospital_id == hosData.hospital_id,
        disabled: time.remain_num == 0 || curDate.status == 0
      }"
      @click="selectTime(time)"
    >
      <div class="time-text">
        <span>
          {{ time.from.slice(0, 5) }}
        </span>
        <span
          v-if="
            time.from === oldTimeFrom &&
            time.to === oldTimeTo &&
            matchOldDate &&
            hosIdOld == hosData.hospital_id
          "
          class="time-text-man"
          >(已约)</span
        >
        <!-- <span v-else-if="time.remain_num === 0" class="time-text-man">{{
          time.remain_num === 0 ? '(约满)' : ''
        }}</span> -->
        <span v-else class="time-text-man">{{
          time.remain_num === 1
            ? '(仅剩1)'
            : time.remain_num === 0
            ? '(约满)'
            : ''
        }}</span>
      </div>
      <!-- <div class="time-store">剩余: {{ time.remain_num }}</div> -->
      <!-- <div v-if="time.back_money > 0" class="time-status">
        返￥{{ time.back_money }}
      </div> -->
      <div class="time-cash-back" v-if="time.cashback">
        {{ time.cashback }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    othersBlur: {
      type: Boolean,
      default: false
    },
    matchOldDate: {
      type: Boolean,
      default: false
    },
    timeList: { type: Array, default: () => [] }, // 提示文案
    value: { type: Object, default: () => {} }, // 当前选中
    curDate: { type: Object, default: () => {} }, // 当前选中日期
    hosDataFocus: { type: Object, default: () => {} }, // 当前选中机构
    hosData: { type: Object, default: () => {} }, // 当前机构数据
    small1: { type: Boolean, default: () => false }, //
    hosIdOld: { type: Number, default: 0 }, //
    oldTimeFrom: { type: String, default: '' }, //
    oldTimeTo: { type: String, default: '' } //
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    selectTime(time) {
      if (+time.remain_num === 0 || +this.curDate.status === 0) return;
      this.$emit('sliceTime', { time, hosData: this.hosData });
    }
  }
};
</script>

<style scoped lang="less">
.time-item {
  position: relative;
  width: 140.5rpx;
  height: 82rpx;
  background: #f2f2f2;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-bottom: 16rpx;
  box-sizing: border-box;
  //border-radius: 12rpx;
  box-sizing: border-box;
  &:not(:nth-child(4n)) {
    margin-right: 16rpx;
  }
  .time-text {
    font-size: 24rpx;
    color: @text-color;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Outfit-Regular;
    .time-text-man {
      font-size: 18rpx;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      margin-left: 4rpx;
    }
  }
  .time-status {
    position: absolute;
    left: 0rpx;
    top: 0rpx;
    display: flex;
    align-items: center;
    height: 24rpx;
    font-size: 18rpx;
    color: #fff;
    background: #e2aa60;
    border-radius: 12rpx 0 12rpx 0;
    border: 1rpx solid #fff;
    padding: 0rpx 6rpx;
    box-sizing: border-box;
    font-weight: 500;
  }
  .time-cash-back {
    position: absolute;
    left: 0rpx;
    top: 0rpx;
    display: flex;
    align-items: center;
    height: 24rpx;
    font-size: 18rpx;
    color: #333333;
    background: #89dc65;
    padding: 0rpx 6rpx;
    box-sizing: border-box;
    font-weight: 400;
  }
  &.disabled {
    background: #f2f2f2;
    .time-text,
    .time-store {
      color: #bababa;
    }
  }
  &.active {
    background: @border-color;
    .time-text,
    .time-store {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      color: #fff;
    }
  }
}
.widthSm1 {
  width: 144rpx;
}
</style>
