<template>
  <div class="date__dialog">
    <div class="date__dialog--wrap">
      <div
        :class="{
          'custome-confirm-order-style': enabledConfirmOrderStyle
        }"
      >
        <div
          v-if="options && options.length"
          class="date-list-wrap"
          :class="{ 'date-list-wrap-white': !noTime }"
        >
          <scroll-view
            :show-scrollbar="false"
            scroll-x="true"
            :scroll-left="scrollLeft"
            scroll-with-animation="true"
            class="date-list"
            id="scrollWrap"
          >
            <div
              class="date-item"
              v-for="(date, index) in options"
              :key="date.tpl_id"
              :class="{
                'date-item-notm': noTime,
                active:
                  activeDateTab &&
                  activeDateTab.tpl_id === date.tpl_id &&
                  !calendarDisable,
                'disabled-style':
                  date.status == 0 || (date.status == 1 && date.remain_num <= 0)
              }"
              @click="selectDate(date, index)"
            >
              <!-- <div class="date-fan" v-if="date && date.back_money != 0">
                返￥{{ date.back_money }}
              </div> -->
              <div class="date-cash-back" v-if="date.cashback">
                {{ date.cashback }}
              </div>
              <div class="date-item-ctn">
                <div class="date-text1">{{ `${date.month}/${date.day}` }}</div>
                <div class="date-text">
                  {{
                    currentDay === Number(date.day) &&
                    currentMonth === Number(date.month)
                      ? '今日'
                      : currentDay + 1 === Number(date.day) &&
                        currentMonth === Number(date.month)
                      ? '明日'
                      : date.week_str
                  }}
                </div>
              </div>
              <!-- 所有日期的地方，全部去掉周二“优享日”的标识 from dengyun -->
              <img
                class="sy-day"
                v-if="
                  xyDay &&
                  date.week == 2 &&
                  !(
                    date.status == 0 ||
                    (date.status == 1 && date.remain_num <= 0)
                  )
                "
                src="https://static.soyoung.com/sy-design/1nmiv8x8zlvar1748339757330.png"
              />
              <img
                class="sy-day"
                v-if="
                  xyDay &&
                  date.week == 4 &&
                  !(
                    date.status == 0 ||
                    (date.status == 1 && date.remain_num <= 0)
                  )
                "
                src="https://static.soyoung.com/sy-pre/20250528-162247-1748419800634.png"
              />
            </div>
          </scroll-view>
          <div v-if="options.length" class="date-info" @click="calendarHandel">
            <img
              @click="() => $emit('calenderIconClick')"
              src="https://static.soyoung.com/sy-design/1yubgvsyhbgj61726299976516.png"
              class="calender-icon"
            />
            <p @click="() => $emit('calenderIconClick')">日历</p>
          </div>
        </div>
        <div v-if="!noTime && options.length" class="white-t"></div>
        <div v-if="options.length" class="date-btm-txt">
          <DateBtmTxt
            v-if="dateBtmTxtUrl.describe"
            :textUrl="dateBtmTxtUrl"
          ></DateBtmTxt>
        </div>
        <div v-if="!noTime" class="time-list">
          <!-- 时间切片的loading -->
          <div
            class="no-available-phone"
            v-if="
              (((activeDateTab && activeDateTab.status == 0) ||
                (activeDateTab && activeDateTab.remain_num <= 0)) &&
                !othersBlur) ||
              !options.length ||
              loading
            "
          >
            <div class="loading-txt" v-if="loading">加载中...</div>
            <template v-else>
              <div
                :class="[
                  'empty-ctn',
                  { 'consult-to-order-box': !enabledConfirmOrderStyle }
                ]"
              >
                <Empty
                  :reportData="{
                    uid: userInfo.uid,
                    sku_id: sku_id,
                    order_id: order_id,
                    month: oldTimeFrom
                  }"
                  :remain="emptyRemain"
                ></Empty>
                <ConsultToOrder
                  v-if="!enabledConfirmOrderStyle"
                  :add_c_btn="activeDateTab.add_c_btn"
                ></ConsultToOrder>
              </div>
            </template>
          </div>
          <template v-else>
            <div class="time-ctn-out">
              <div class="time-ctn">
                <!-- 时间切片 -->
                <div
                  class="time-item"
                  v-for="(time, index) in currentDateAvailableTimeList"
                  :key="index"
                  :class="{
                    active:
                      JSON.stringify(value.time) === JSON.stringify(time) &&
                      activeDateTab.first_un === value.date.first_un,
                    disabled: time.remain_num === 0 || activeDateTab.status == 0
                  }"
                  @click="selectTime(time)"
                >
                  <div class="time-text">
                    <span>
                      {{ time.from.slice(0, 5) }}
                    </span>
                    <span
                      v-if="time.from === oldTimeFrom && othersBlur"
                      class="time-text-man"
                      >(已约)</span
                    >
                    <span v-else class="time-text-man">{{
                      time.remain_num === 1
                        ? '(仅剩1)'
                        : time.remain_num === 0
                        ? '(约满)'
                        : ''
                    }}</span>
                  </div>
                  <!-- <div v-if="time.back_money > 0" class="time-status">
                    返￥{{ time.back_money }}
                  </div> -->
                  <div v-if="time.cashback" class="time-status-cashback">
                    {{ time.cashback }}
                  </div>
                </div>
              </div>
              <div
                v-if="value.time && value.time.hospital_id"
                class="hospital-ctn"
              >
                <div @click="toMap(value.time)" class="time-slice-hospital">
                  <div class="time-slice-hospital-l">
                    服务门店：{{ value.time.hospital_name }}
                  </div>
                  <img
                    src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1726299976862.png"
                  />
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <Calendar
        v-if="!calendarDisable"
        :lBtm="lBtm"
        :firstStatus="firstStatus"
        :calendarVisible="calendarVisible"
        :nextOptions="nextOptions"
        :activeDateTab="activeDateTab"
        :type="type"
      ></Calendar>
    </div>
  </div>
</template>

<script>
import DateBtmTxt from '@/components/appointNew/components/dateBtmTxt.vue';
import Empty from '@/components/appointNew/components/empty.vue';
import { mapState } from 'vuex';
import Calendar from './calendar.vue';
// import {WeixinUrl} from '@/api/ug'
// import iphoneXButton from '@/components/iphoneXButton.vue'
import ConsultToOrder from '@/components/appointNew/components/consult-to-order.vue';
export default {
  name: 'ck-appointment-timer-picker',
  props: {
    type: {
      type: String
    },
    order_id: {
      type: String || Number
    },
    othersBlur: {
      type: Boolean,
      default: false
    },
    enabledConfirmOrderStyle: {
      type: Boolean,
      default: false
    },
    noTime: { type: Boolean, default: false },
    lBtm: { type: Boolean, default: false },
    infoText: { type: Object, default: () => {} }, // 提示文案
    initDate: { type: String, default: '' },
    hospitalId: { type: Number, default: 0 },
    sku_id: { type: Number, default: 0 },
    loading: { type: Boolean, default: false },
    calendarDisable: { type: Boolean, default: false },
    calenderDialog: { type: Boolean, default: true },
    initTime: { type: String, default: '' },
    wxcode: { type: String, default: '' },
    visible: { type: Boolean, default: false },
    oldTimeFrom: { type: String, default: '' }, // 当前选中
    dateBtmTxtUrl: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      subNumber: '',
      activeDateTab: {},
      activeDateTabIndex: 0,
      // options: [],
      value: {
        date: null,
        time: null
      },
      calendarVisible: false, // 日历组件
      currentOptions: [], // 当月日历数据
      nextOptions: [], // 下月日历数据
      currentMonth: new Date().getMonth() + 1,
      currentDay: new Date().getDate(),
      scrollLeft: 0,
      firstStatus: true,
      stop_init: false // options 改变时不触发init
    };
  },
  components: { Calendar, Empty, DateBtmTxt, ConsultToOrder },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo,
      xyDay: (state) => state.reserve.yxDay
    }),
    currentDateAvailableTimeList() {
      if (this.activeDateTab) {
        return this.activeDateTab.time_details || [];
      } else {
        return [];
      }
    },
    completeValue() {
      if (this.value.date && this.value.time) {
        return `${this.value.date.year}/${this.value.date.month}/${this.value.date.day} ${this.value.time.time_slot}`;
      } else {
        return false;
      }
    },
    emptyRemain() {
      if (this.activeDateTab.status === 0) {
        return '当前日期暂无可预约时段，请切换日期预约';
      } else if (this.activeDateTab.remain_num <= 0) {
        return '当前日期暂无可预约时段，请切换日期预约';
      } else {
        if (this.type === 'hos') {
          return '当前城市暂未开放可预约门店，请先切换城市';
        } else if (this.type === 'doc') {
          return '当前暂无可选医生，请切换预约方式';
        }
        return '当前暂无时间可约';
      }
    }
  },
  watch: {
    options: {
      deep: true,
      handler() {
        !this.stop_init && this.init();
        setTimeout(() => {
          this.$emit('update:loading', false);
        }, 100);
      }
    },
    calendarVisible(val) {
      this.$emit('calendarVisible', val);
    },
    // loading: {
    //   handler() {
    //     if (!this.loading) {
    //       this.init();
    //     }
    //   }
    // },
    visible: {
      deep: true,
      handler(val) {
        if (val) {
          this.watchClientRect();
        } else {
          this.firstStatus = true;
        }
      }
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      clearTimeout(this.deTimeout);
      this.deTimeout = setTimeout(() => {
        this.value.date = null;
        this.value.time = null;
        if (this.initDate && this.options.length) {
          const current = this.options.filter(
            (i) =>
              new Date(`${i.year}/${i.month}/${i.day}`) -
                new Date(this.initDate.replace(/-/g, '/')) ===
              0
          )[0];
          if (current) {
            this.value.time =
              current &&
              current.time_details &&
              current.time_details.filter((i) => {
                if (i.from === this.initTime) {
                  return true;
                }
              })[0];
            this.activeDateTab = current;
            this.value.date = current;
            // if (this.value.date && this.value.time) {
            //   this.$emit('submit', this.value)
            // }
            this.watchClientRect();
          } else {
            const current = this.options.filter(
              (n) => n.status > 0 && n.remain_num > 0
            )[0];
            this.activeDateTab = current || this.options[0];
            this.value.date = this.activeDateTab;
            this.value.time = {};
            this.watchClientRect();
          }
        } else if (this.options[0]) {
          const current = this.options.filter(
            (n) => n.status > 0 && n.remain_num > 0
          )[0];
          this.activeDateTab = current || this.options[0];
          this.value.date = this.activeDateTab;
          this.value.time = {};
          this.watchClientRect();
        }
        this.nextOptions = [];

        // this.options.sort((a, b) => {
        //   return +a.tpl_id - +b.tpl_id;
        // });
        console.log(this.options, 'this.options');
        this.options.forEach((n) => {
          if (Array.isArray(this.nextOptions[`${n.year}-${n.month}`])) {
            this.nextOptions[`${n.year}-${n.month}`].push(n);
          } else {
            let nReduce = n.week - 1;
            if (nReduce < 0) {
              nReduce = 6;
            }
            this.nextOptions[`${n.year}-${n.month}`] = [
              ...Array(Number(nReduce)).fill({}),
              n
            ];
          }
        });
        console.log(this.nextOptions, 'this.options-next');
        const arrPinker = Object.values(this.nextOptions);
        console.log(arrPinker, 'this.options-next-arrPinker');
        // arrPinker.sort((a, b) => {
        //   const aNumItem = a.find((i) => {
        //     return i.year;
        //   });
        //   const bNumItem = b.find((i) => {
        //     return i.year;
        //   });
        //   const bNum =
        //     bNumItem.year +
        //     (bNumItem.month.length === 1
        //       ? '0' + bNumItem.month
        //       : bNumItem.month);
        //   const aNum =
        //     aNumItem.year +
        //     (aNumItem.month.length === 1
        //       ? '0' + aNumItem.month
        //       : aNumItem.month);
        //   return +aNum - +bNum;
        // });
        this.nextOptions = arrPinker;
        this.submit(true);
        // if (this.activeDateTab.is_subscribe) {
        //   this.getSubNumber(this.activeDateTab);
        // }
      }, 100);
    },
    toMap(data) {
      console.log('toMap', data);
      uni.navigateTo({
        url: `/packageHospital/hospital-map?hospital_id=${data.hospital_id}`
      });
      this.$emit('reportClick', data.hospital_id);
    },
    selectDate(date, index) {
      this.activeDateTab = date;
      this.activeDateTabIndex = index;
      this.value.date = date;
      this.value.time = {};
      this.submit();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_date_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldTimeFrom ? 2 : 1,
          type: this.type === 'hos' ? 2 : this.type === 'doc' ? 3 : 1
        }
      });
    },
    async getSubNumber(date) {
      this.subNumber = '';
      try {
        const res = await this.$request({
          url: '/groupBuy/order/GetSubscribeNum',
          data: {
            hospital_id: this.hospitalId,
            sku_id: this.sku_id,
            subscribe_date: date.first_un.slice(0, 10)
          }
        });
        if (
          res.data?.responseData?.subscribe_num ||
          res.data?.responseData?.subscribe_num === 0
        ) {
          this.subNumber = res.data?.responseData?.subscribe_num;
          this.$forceUpdate();
        }
      } catch (err) {
        console.log(err);
      }
    },
    selectTime(time) {
      if (+time.remain_num === 0 || +this.activeDateTab.status === 0) return;
      this.value.time = time;
      this.value.date = this.activeDateTab;
      this.submit();
    },
    submit(init) {
      if (!this.value.date) {
        this.value.date = this.activeDateTab;
      }
      this.value.init = init || false;
      this.$emit('clickDate', this.value);
      this.$emit('submit', this.value);
    },
    // hideServiceTimeSelect() {
    //   this.$emit('visible', false)
    //   // 若没有选中则清空
    //   !this.initDate && (this.value.time = null)
    // },
    calendarHandel() {
      if (this.calendarDisable) {
        this.$emit('clickDate', { init: false });
        return;
      }
      if (!this.calenderDialog) {
        this.$emit('calenderClick');
      } else {
        this.calendarVisible = !this.calendarVisible;
        this.firstStatus = false;
      }
      this.calendarVisible &&
        this.$reportData({
          info: 'sy_wxtuan_or_subscribe_pop:switch_date_icon_click',
          ext: {
            uid: this.userInfo.uid,
            product_id: this.sku_id,
            order_id: this.order_id,
            status: this.oldTimeFrom ? 2 : 1
          }
        });
    },
    watchClientRect() {
      // 每次打开时判断一下当前选中的日期是否在屏幕内
      setTimeout(async () => {
        let dateIndex = this.options.findIndex(
          (n) => JSON.stringify(n) === JSON.stringify(this.activeDateTab)
        );
        if (dateIndex >= 0) {
          // dateIndex = dateIndex + 1;
          const dateItem = await this.getBoundingClientRect(
            '.date-item',
            'selectAll'
          );
          const dateItemChild = dateItem[dateIndex];
          // console.log(1111, dateItem);
          if (dateItemChild && dateItem[0]) {
            this.scrollLeft = dateItemChild.left - dateItem[0].left;
            // dateIndex >= 1
            //   ? dateItemChild.left - dateItem[1].left
            //   : dateItemChild.left;
          }
        }
      }, 150);
    },
    getBoundingClientRect(str, type = 'select') {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this);
        query[type](str)
          .boundingClientRect((data) => {
            resolve(data);
          })
          .exec();
      });
    },
    async toPhone() {
      this.$bridge({
        url: `/packageAccount/consult?qzychannel=50`
      });
      this.$emit(
        'toReportData',
        'sy_wxtuan_or_subscribe_pop:contact_consultant_click'
      );
      // if (navigator.userAgent.match(/miniprogram/i) || window.__wxjs_environment === 'miniprogram') {
      //   window.wx.miniProgram.navigateTo({url: '/packageAccount/consult?qzychannel=5'})
      // } else {
      //   const obj = {
      //     applet: 2,
      //     original_url: 'packageAccount/consult?qzychannel=5'
      //   }
      //   const res = await WeixinUrl(obj)
      //   if (res.status === 200 && res.data.url_link) {
      //     window.location.href = res.data.url_link
      //   }
      // }
    },
    // 订阅可预约提醒
    onClickRemindere() {
      // 预约提醒订阅-点击埋点
      this.$reportData({
        info: 'sy_wxtuan_tuan_subscribe_pop:subscribe_remind_button_click',
        ext: {}
      });
      if (
        this.options[this.activeDateTabIndex] &&
        !this.options[this.activeDateTabIndex].is_subscribe
      ) {
        this.stop_init = true; // 禁止触发init方法

        // 发送订阅请求
        this.$emit('yuyueReminderSubscribe', {
          date:
            this.activeDateTab.year +
            '-' +
            this.formatNumber(this.activeDateTab.month) +
            '-' +
            this.formatNumber(this.activeDateTab.day),
          callback: () => {
            // 更新缓存数据的订阅状态
            this.options[this.activeDateTabIndex].is_subscribe = 1;
            this.activeDateTab = this.options[this.activeDateTabIndex];
            this.$forceUpdate();
            this.getSubNumber(this.activeDateTab);
            setTimeout(() => {
              this.stop_init = false;
            }, 100);
          }
        });
      }
    },
    formatNumber(str) {
      if (str.length === 1) {
        return '0' + str;
      } else {
        return str;
      }
    }
  }
};
</script>

<style scoped lang="less">
.date__dialog--wrap {
  position: relative;
  // overflow-x: hidden;
  // display: flex;
}
.tip {
  background: #fff9e0;
  height: 70rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #b8793f;
  font-weight: 400;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  line-height: 70rpx;
}
.date__dialog {
  width: 100%;
  box-sizing: border-box;
  &--wrap {
    width: 100%;
    box-sizing: border-box;
    // padding-bottom: 40rpx;
    animation: popHeight 0.5s;
    animation-fill-mode: forwards;
  }
}
.buttom-iphone-x {
  bottom: 40rpx;
}

.date-list-wrap {
  z-index: 9;
  height: 152rpx;
  overflow: hidden;
  margin: 0rpx;
  position: relative;
  display: flex;
  border: 0;
  justify-content: space-between;
}
.date-list-wrap-white {
  background-color: #fff;
  // padding-left: 30rpx;
}
.white-t {
  height: 30rpx;
  background-color: #fff;
}
.bac-img {
  position: absolute;
  width: 24rpx;
  height: 110rpx;
  right: 110rpx;
  top: -10rpx;
  z-index: 9999;
}
.date-info {
  background: #fff;
  width: 108rpx;
  height: 100%;
  position: absolute;
  top: 0;
  z-index: 1;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .calender-icon {
    width: 30rpx;
    height: 28rpx;
    margin-bottom: 6rpx;
  }
  p {
    font-family: PingFangSC-Medium;
    font-size: 24rpx;
    color: #222222;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
}

.date-list {
  height: 76 * 2rpx;
  white-space: nowrap;
  overflow: hidden;
  // -webkit-overflow-scrolling: touch;
  position: relative;
  padding-right: 100rpx;
  width: 100%;
  box-sizing: border-box;
  // margin-left: 10rpx;
  &::-webkit-scrollbar {
    display: none;
  }
  .date-item {
    display: inline-block;
    position: relative;
    width: 58 * 2rpx;
    height: 76 * 2rpx;
    box-sizing: border-box;
    background: #f2f2f2;
    margin-left: 16rpx;
    .sy-day {
      position: absolute;
      width: 100%;
      height: 30rpx;
      left: 0;
      bottom: 0;
    }
    .date-fan {
      display: inline-block;
      height: 14 * 2rpx;
      line-height: 14 * 2rpx;
      background: #e2f8f1;
      border-radius: 12rpx 0 12rpx 0;
      font-family: PingFangSC-regular;
      font-size: 18rpx;
      color: @text-color;
      letter-spacing: 0;
      // font-weight: 500;
      padding: 0rpx 10rpx;
      position: absolute;
      left: 0rpx;
      top: 0rpx;
      box-sizing: border-box;
    }
    .date-cash-back {
      display: inline-block;
      height: 12 * 2rpx;
      line-height: 12 * 2rpx;
      background: #89dc65;
      font-family: PingFangSC-regular;
      font-size: 18rpx;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      padding: 0rpx 10rpx;
      position: absolute;
      left: 0rpx;
      top: 0rpx;
      right: 0rpx;
      box-sizing: border-box;
    }
    .date-item-ctn {
      position: relative;
      box-sizing: border-box;
      color: @text-color;
      .date-text {
        font-family: PingFangSC-Medium;
        font-size: 22rpx;
        color: #222222;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
      }
      .date-text1 {
        font-family: Outfit-Regular;
        margin-top: 20 * 2rpx;
        margin-bottom: 10rpx;
        font-size: 22rpx;
        color: #222222;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
      .date-status {
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #222222;
        letter-spacing: 0.56px;
        text-align: center;
        font-weight: 500;
      }
    }
    &.disabled-style {
      background-color: #f2f2f2;
      .date-text,
      .date-text1,
      .date-status {
        color: #bababa;
      }
    }
    &.active {
      color: #fff;
      background: #373a36;
      .date-fan {
        background-color: @border-color;
        color: white;
      }
      .date-text,
      .date-text1,
      .date-status {
        color: #fff;
      }
    }
  }
  .date-item:nth-child(1) {
    margin-left: 30rpx;
  }
  .date-item-notm:nth-child(1) {
    margin-left: 20rpx;
  }
  .app-date-sty {
    &.active {
      &::after {
        background: @border-color !important;
      }
    }
  }
}
.date-btm-txt {
  margin: 0 30rpx;
}
.time-list {
  padding: 30rpx;
  min-height: 276rpx;
  margin-top: 10rpx;
  overflow: scroll;
  position: relative;
  .hospital-ctn {
    box-sizing: border-box;
    margin: 0 30rpx;
    .time-slice-hospital {
      width: 100%;
      height: 88rpx;
      background: #f8f8f8;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: @text-color;
      font-weight: 400;
      border-radius: 12rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      .time-slice-hospital-l {
        width: 620rpx;
      }
      img {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
  .time-ctn-out {
    background-color: #f2f2f2;
    .time-ctn {
      background-color: #fff;
      padding: 30rpx;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      column-gap: 16rpx;
      row-gap: 16rpx;
      .time-item {
        position: relative;
        height: 82rpx;
        background: #f2f2f2;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        box-sizing: border-box;
        .time-text {
          font-size: 24rpx;
          color: #222222;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          font-family: OutFit-Regular;
          .time-text-man {
            font-size: 18rpx;
            text-align: center;
            font-weight: 400;
            font-family: PingFangSC-Regular;
          }
        }
        .time-status {
          position: absolute;
          left: 0rpx;
          top: 0rpx;
          display: flex;
          align-items: center;
          height: 24rpx;
          font-size: 18rpx;
          color: #fff;
          background: #e2aa60;
          border: 1rpx solid #fff;
          padding: 0rpx 6rpx;
          box-sizing: border-box;
          font-weight: 500;
        }
        .time-status-cashback {
          position: absolute;
          left: 0rpx;
          top: 0rpx;
          display: flex;
          align-items: center;
          height: 24rpx;
          font-size: 18rpx;
          color: #222222;
          background: #89dc65;
          padding: 0rpx 6rpx;
          box-sizing: border-box;
          font-weight: 500;
        }
        &.disabled {
          background: #f2f2f2;
          .time-text,
          .time-store {
            color: #bababa;
          }
        }
        &.active {
          background: @border-color;
          .time-text,
          .time-store {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            color: #fff;
          }
        }
      }
    }
  }
  .app-img {
    &.active {
      &::after {
        content: '';
        background: url('https://static.soyoung.com/sy-pre/t724211kf9he-1622445000622.png')
          center/100% 100% no-repeat;
      }
    }
  }
  .no-available-phone {
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #aaabb3;
    line-height: 40rpx;
    font-weight: 400;
    text-align: center;
    .empty-ctn {
      padding-top: 50rpx;
      background-color: #fff;
    }
    .consult-to-order-box {
      padding-bottom: 50rpx;
    }
    .loading-txt {
      padding-top: 130rpx;
    }
    .time-loading {
      display: block;
      background-image: url('https://static.soyoung.com/sy-pre/g0jq73i1rxuv-1717657800624.gif');
      background-repeat: no-repeat;
      background-size: 100%;
      width: 60rpx;
      height: 60rpx;
    }
    .green-set {
      font-family: PingFangSC-Semibold;
      color: #00ab84;
      font-weight: 600;
    }
    .app-empty {
      color: #00ab84;
    }
    // 订阅可预约提醒
    .yuyue-reminder-btn {
      background: #ffffff;
      border: 1px solid @border-color;
      border-radius: 23 * 2rpx;
      width: 260 * 2rpx;
      height: 41 * 2rpx;
      line-height: 41 * 2rpx;
      text-align: center;
      margin-top: 32rpx;

      font-family: PingFangSC-Medium;
      font-size: 15px;
      color: #00ab84;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
      &.remindered {
        background: #ffffff;
        border: 1px solid #dedede;
        border-radius: 23px;
        font-family: PingFangSC-Medium;
        font-size: 15px;
        color: #999999;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        .waiting-info {
          font-family: PingFangSC-Regular;
          font-size: 15px;
          color: #999999;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
          display: inline-block;
          margin-left: 12rpx;
        }
      }
    }
  }
  .no-available {
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #777777;
    text-align: center;
    font-weight: 400;
    position: absolute;
    top: 50%;
    right: 50%;
    margin-right: -250rpx;
    margin-top: -225rpx;
    width: 500rpx;
    height: 450rpx;
    .empty-img {
      width: 500rpx;
      height: 400rpx;
      margin-bottom: 10rpx;
    }
  }
}
.submit__btn--wrap {
  position: absolute;
  right: 0;
  left: 0;
  bottom: 12rpx;
  .newTips {
    padding: 10rpx 30rpx 0 30rpx;
    font-size: 24rpx;
    font-family: PingFangSC-Regular;
    color: #555555;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 30rpx;
    .set-red {
      color: rgba(255, 64, 64, 1);
    }
    > img {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
      margin: 1rpx 10rpx 0 0;
    }
  }
}
.submit-btn {
  width: 690rpx;
  height: 88rpx;
  background: @border-color;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: 0 auto;
  color: #fff;
  font-family: PingFangSC-Medium;
  font-size: 34rpx;
  z-index: 99;
  border-radius: 22 * 2rpx;
  &.disabled {
    background: #dedede;
  }
  &.is-iphone-x {
    bottom: 80rpx;
  }
  .btn-text {
    font-family: PingFangSC-Medium;
    font-size: 34rpx;
    line-height: 40rpx;
  }
  .complete-value {
    font-size: 18rpx;
    line-height: 26rpx;
  }
}
.app-button-sty {
  background: #00af84 !important;
}
</style>

// 确认订单页使用这个样式的时候，特殊重写
<style lang="less" scoped>
.custome-confirm-order-style {
  .white-t {
    display: none;
  }
  .time-list {
    padding: 0;
    margin: 40rpx auto 0;
    box-sizing: border-box;
    min-height: initial;
    .time-ctn-out {
      box-sizing: border-box;
      background-color: #ffffff;
      .time-ctn {
        box-sizing: border-box;
        padding: 0 30rpx;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        column-gap: 16rpx;
        row-gap: 16rpx;
        .time-item {
          &.disabled {
            background: #f2f2f2;
            .time-text,
            .time-store {
              color: #bababa;
            }
          }
        }
      }
    }
  }
}
</style>
