<template>
  <div @click="toUrl" class="date-btm-txt">{{ textUrl.describe }}</div>
</template>

<script>
export default {
  props: {
    textUrl: Object,
    url: String
  },
  mounted() {},
  data() {
    return {};
  },
  methods: {
    toUrl() {
      this.textUrl.url && this.$toH5(this.textUrl.url);
    }
  }
};
</script>

<style lang="less" scoped>
.date-btm-txt {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #fff;
  font-family: PingFangSC-Regular;
  font-size: 22rpx;
  color: @text-color;
  letter-spacing: 0;
  text-align: justify;
  font-weight: 400;
  margin: 20rpx 0 20rpx;
}
</style>
