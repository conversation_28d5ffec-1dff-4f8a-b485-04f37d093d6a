<template>
  <div class="container" :class="{ 'confirm-ctn': !order_id }">
    <div class="doctor-title">
      <div @click="$emit('close')" class="t-left">
        <img
          src="https://static.soyoung.com/sy-pre/2k47i8q9a59jo-1681729800706.png"
        />
        <span> 查看全部医生 </span>
      </div>
      <span class="order-hos-gray">（共{{ doctorList.length || 0 }}位）</span>
    </div>
    <!-- 滚动条位置 -->
    <scroll-view
      @scrolltoupper="scrolltoupper"
      @scroll="hosScroll"
      :scroll-x="false"
      :scroll-y="true"
      class="btm-ctn"
      :show-scrollbar="false"
      :class="{ 'confirm-btm-ctn': !order_id }"
    >
      <!-- 选择适用门店 start  -->
      <div class="order__doctor" v-if="doctorList.length">
        <div :class="['order__doctor--list', multiple]">
          <div
            class="order__doctor--item"
            v-for="item in doctorList"
            :key="item.doctor_id"
            :class="{
              gray: item.surplus_num === 0,
              active:
                curDoctor.doctor_id === item.doctor_id || doctorList.length == 1
            }"
            @click="getDoctor(item)"
          >
            <div class="doctor-left">
              <img :src="item.icon" />
            </div>
            <div class="doctor-right">
              <div class="line-t">
                <span>{{ item.doctor_name }}</span>
                <div v-if="item.job" class="zhicheng">
                  <img
                    src="https://static.soyoung.com/sy-pre/1clxo4ar2xjzp-1683198600747.png"
                  />
                  <span>{{ item.job }}</span>
                </div>
                <div v-if="item.doctor_tag == 2" class="zhicheng daka">
                  <img
                    src="https://static.soyoung.com/sy-pre/2eph3qr81jfb6-1683288600748.png"
                  />
                  <span>大咖医生</span>
                </div>
              </div>
              <div class="line-h">
                <div class="line-h-l">
                  <span v-if="item.hos_info.length > 1"
                    >服务{{ item.hos_info.length }}家机构 &nbsp;|&nbsp;</span
                  >
                  <span v-for="(item, index) in item.hos_info" :key="index"
                    >{{ item.hospital_name
                    }}{{
                      item.hos_info.length > 1 &&
                      index + 1 !== item.hos_info.length
                        ? ','
                        : ''
                    }}</span
                  >
                </div>
                <div v-if="item.hos_info.length === 1">
                  {{ item.hos_info[0].distance_str }}
                </div>
              </div>
              <div class="line-y">
                <span v-if="item.work_experience"
                  >{{ item.work_experience }}年从业经验</span
                >
                <span v-else>从业经验丰富</span>
              </div>
            </div>
            <!-- 选中的图标 -->
            <img
              v-if="
                curDoctor.doctor_id === item.doctor_id || doctorList.length == 1
              "
              class="sel-icon"
              src="https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1727167737093.png"
            />
          </div>
        </div>
      </div>
      <div
        v-if="doctorList.length && pinkerOptions.length"
        class="service-title"
      >
        选择服务开始时间
      </div>
      <!-- 选择适用门店 end    -->
      <div class="time-pk-ctn">
        <TimePinker
          :lBtm="true"
          type="doc"
          :order_id="order_id"
          :oldTimeFrom="oldData.from"
          :othersBlur="othersBlur"
          :loading.sync="timeLoading"
          :dateBtmTxtUrl="dateBtmTxtUrl"
          :initDate="oldCurrentDate"
          :initTime="oldCurrentTime"
          :options="pinkerOptions"
          :doctorId="curDoctor.doctor_id"
          :pid="pid"
          @submit="selectTime"
          @reportClick="reportClick"
          @calendarVisible="(val) => $emit('calendarVisible', val)"
          @yuyueReminderSubscribe="onYuyueReminderSubscribe"
        ></TimePinker>
      </div>
    </scroll-view>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import {
  calendarForDoctor,
  calendarForDoctorDetail
} from '@/api/reservation.js';
import TimePinker from './AppointmentTimerPicker';
export default {
  components: {
    TimePinker
  },
  props: {
    dateBtmTxtUrl: {
      type: Object,
      default: () => {}
    },
    card_num: {
      type: Number,
      default: 0
    },
    // 确认订单页，用户已预约的日期
    reserveResult: {
      type: Object,
      default: () => {}
    },
    request_time: {
      type: Number,
      default: 0
    },
    othersBlur: {
      type: Boolean,
      default: false
    },
    forceReserveInfo: {
      type: Object
    },
    baseInfoCache: {
      type: Object
    },
    doctor_list: {
      type: Array
    },
    oldData: {
      type: Object,
      default: () => {}
    },
    baseInfo: {
      type: Object,
      default: () => {}
    },
    lastMulData: {
      type: Object,
      default: () => {}
    },
    // 点击的机构id
    clickDoctorId: {
      type: String || Number
    },
    order_id: {
      type: String || Number
    },
    pid: {
      type: String || Number
    },
    city_id: {
      type: String || Number
    },
    doctor_id: {
      type: String || Number
    },
    package_id: {
      type: String || Number,
      default: ''
    }
  },
  data() {
    return {
      timeLoading: false,
      oldCurrentDate: '', // 已预约的日期
      oldCurrentTime: '', // 已预约的时间
      curDoctor: {}, // 已选择的机构
      curDate: {}, // 已选择的日期
      curTime: {}, // 已选择的时间切片
      pinkerOptions: [], // 日期列表
      doctorList: [], // 机构列表
      toastText: ''
    };
  },
  watch: {
    // 城市改变，重新请求数据
    city_id() {
      this.clearStatus();
      this.getDoctorListFn();
      // 初始化日期数据
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    multiple() {
      return this.doctorList.length > 1 ? 'multiple' : '';
    }
  },
  created() {
    this.startStayTime = new Date().valueOf();
    this.setOldData();
    this.getDoctorListFn('init');
  },
  mounted() {},
  methods: {
    clearStatus() {
      this.oldCurrentDate = '';
      this.oldCurrentTime = '';
      this.curDoctor = {};
      this.curDate = {};
      this.curTime = {};
    },
    // 查看机构地址埋点
    reportClick(hospital_id) {
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:institution_address_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.pid,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 3,
          city_id: this.city_id,
          hospital_id
        }
      });
    },
    scrolltoupper() {
      this.$emit('setTTop', false);
    },
    hosScroll(e) {
      const top = e?.detail?.scrollTop;
      // 再往下走
      if (top - this.lastTop > 0) {
        if (top > 5) {
          this.$emit('setTTop', true);
        }
      }
      this.lastTop = top;
    },
    titleToUrl() {
      this.$toH5('https://m.soyoung.com/tmwap22419');
    },
    setOldData() {
      // 点击的其它医生，不还原旧数据
      if (
        this.clickDoctorId &&
        +this.clickDoctorId !== +this.oldData.hospital_id
      ) {
        return;
      }
      // 有之前的预约数据
      if (
        this.oldData.month &&
        (+this.oldData.reserve_mode === 3 || +this.oldData.reserve_mode === 0)
      ) {
        this.oldCurrentDate =
          this.oldData.year + '-' + this.oldData.month + '-' + this.oldData.day;
        this.oldCurrentTime = this.oldData.from;
      }
      if (this.reserveResult?.commitData?.reserve_mode) {
        const wSetData = this.reserveResult?.commitData;
        this.oldCurrentDate = wSetData.start_time?.slice?.(0, 10);
        this.oldCurrentTime = this.reserveResult?.time?.from;
      }
    },
    async getDoctorListFn(init) {
      this.timeLoading = true;
      const reqData = {
        order_id: this.order_id,
        city_id: this.city_id,
        doctor_id: this.doctor_id,
        package_id: this.package_id,
        pid: this.pid
      };
      let res;
      if (this.doctor_list?.length && init) {
        res = this.doctor_list;
      } else {
        res = await calendarForDoctor(reqData);
      }
      if (res) {
        const docList = res;
        this.doctorList = docList || [];
        this.spliceDoctor(this.doctorList);
        if (docList?.length) {
          this.curDoctor = docList[0];
          this.getDateListFn();
        } else {
          this.$emit('setSkelReady');
          this.pinkerOptions = [];
          this.timeLoading = false;
        }
      } else {
        this.$emit('setSkelReady');
        this.pinkerOptions = [];
        this.timeLoading = false;
      }
    },
    spliceDoctor(docList = []) {
      if (!docList?.length) return;
      const id =
        this.clickDoctorId ||
        this.oldData.doctor_id ||
        this.lastMulData.doctor_id;
      if (id) {
        let index = 0;
        docList.forEach((item, idx) => {
          if (+item.doctor_id === +id) {
            index = idx;
          }
        });
        const newArr = docList.splice(index, 1);
        docList.unshift(newArr[0]);
      }
    },
    getCurUrl() {
      const pages = getCurrentPages();
      return pages[pages.length - 1];
    },
    // 推算次卡锚定日期
    setIntervalAppointment(dateList = []) {
      const interval = +this.baseInfo.interval_day_num || 0; // 最小间隔
      const lastReserved = this.lastMulData; // 上次的时间
      let currentIndex; // 上次预约的索引
      for (let i = 0; i < dateList.length; i++) {
        const item = dateList[i];
        if (
          +item.year === +lastReserved.year &&
          +item.month === +lastReserved.month &&
          +item.day === +lastReserved.day
        ) {
          currentIndex = i;
        }
        if (
          i > currentIndex + interval &&
          +item.status !== 0 &&
          +item.remain_num > 0
        ) {
          // 在间隔日期之后
          this.oldCurrentDate = `${item.year}-${item.month}-${item.day}`;
          break;
        }
      }
    },
    async getDateListFn() {
      this.timeLoading = true;
      const obj = {
        order_id: this.order_id,
        city_id: this.city_id,
        card_num: this.card_num,
        package_id: this.package_id,
        pid: this.pid,
        doctor_id: this.curDoctor.doctor_id,
        hos_list: ''
      };
      (this.curDoctor?.hos_info || []).forEach((item, index) => {
        if (index === 0) {
          obj.hos_list += item.hospital_id;
        } else {
          obj.hos_list += `,${item.hospital_id}`;
        }
      });
      if (this.request_time) {
        obj.request_times = this.request_time;
      }
      const res = await calendarForDoctorDetail(obj);
      this.$emit('setSkelReady');
      setTimeout(() => {
        this.timeLoading = false;
      }, 200);
      if (res) {
        this.setIntervalAppointment(res || []);
        this.pinkerOptions = res || [];
      } else {
        this.pinkerOptions = [];
        this.timeLoading = false;
      }
    },
    selectTime(val) {
      this.curDate = val.date;
      this.curTime = val.time || {};
      this.submitData();
    },
    submitData() {
      this.$emit('change', {
        doctor: this.curDoctor,
        date: this.curDate,
        time: this.curTime
      });
    },
    // 点击选择了医生
    getDoctor(item) {
      if (item.doctor_id === this.curDoctor.doctor_id) {
        return;
      }
      this.curDoctor = item;
      this.curDate = {};
      this.curTime = {};
      this.oldCurrentDate = '';
      this.oldCurrentTime = '';
      // this.submitData();
      this.getDateListFn();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_doctor_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.pid,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 3,
          city_id: this.city_id,
          doctor_id: item.doctor_id
        }
      });
    },
    queryToObject(query) {
      if (!query || typeof query !== 'string') return;
      const Obj = {};
      const queryArr = query.split('&');
      queryArr.forEach((item) => {
        const temp = item.split('=');
        const key = temp[0];
        const val = temp[1];
        Obj[key] = val;
      });
      return Obj;
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  color: #222;
  overflow: hidden;
  height: calc(85vh - 304rpx);
  position: relative;
  width: 100%;
  box-sizing: border-box;
  .service-title {
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: #222222;
    background-color: #fff;
    font-weight: 500;
    padding-left: 30rpx;
    padding-bottom: 30rpx;
  }
  .time-pk-ctn {
    background: #f8f8f8;
    // padding-left: 30rpx;
    padding-bottom: 240rpx;
  }
  .btm-ctn {
    height: calc(85vh - 440rpx);
    overflow-y: auto;
  }
  .confirm-btm-ctn {
    height: calc(85vh - 340rpx);
  }
  .doctor-title {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 28rpx;
    color: #222222;
    background-color: #fff;
    padding: 30rpx;
    padding-bottom: 10rpx;
    padding-right: 19rpx;
    display: flex;
    justify-content: space-between;
    .t-left {
      display: flex;
      align-items: center;
      img {
        width: 36rpx;
        height: 36rpx;
      }
    }
    .order-hos-gray {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #777777;
      font-weight: 400;
    }
    .order-change-city {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #222222;
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
      display: flex;
      align-items: center;
      img {
        width: 20rpx;
        height: 20rpx;
        margin-left: 8rpx;
        margin-top: 2rpx;
      }
    }
  }
  .order {
    &__doctor {
      padding: 28rpx 0 0rpx;
      background-color: #fff;
      &--title {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 28rpx;
        color: #222222;
        padding: 0 30rpx;
        display: flex;
        justify-content: space-between;
        .t-left {
          display: flex;
          align-items: center;
          img {
            width: 36rpx;
            height: 36rpx;
          }
        }
        .order-hos-gray {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #777777;
          font-weight: 400;
        }
        .order-change-city {
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #222222;
          letter-spacing: 0;
          text-align: right;
          font-weight: 400;
          display: flex;
          align-items: center;
          img {
            width: 20rpx;
            height: 20rpx;
            margin-left: 8rpx;
            margin-top: 2rpx;
          }
        }
      }
      &--list {
        // padding-top: 30rpx;
        padding-left: 30rpx;
        padding-bottom: 40rpx;
        box-sizing: border-box;
        overflow-x: auto;
        white-space: nowrap;
        .order__doctor--item {
          position: relative;
          display: inline-flex;
          flex-wrap: nowrap;
          width: calc(100% - 30rpx);
          margin-right: 30rpx;
          padding: 30rpx;
          justify-content: space-between;
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          background: #f8f8f8;
          box-sizing: border-box;
          border-radius: 20rpx;
          .doctor-left {
            margin-right: 20rpx;
            img {
              width: 82rpx;
              height: 82rpx;
              border-radius: 50% 50% 0 50%;
            }
          }
          .doctor-right {
            flex: 1;
            .line-t {
              font-family: PingFangSC-Medium;
              font-size: 28rpx;
              color: #333333;
              letter-spacing: 0;
              font-weight: 500;
              display: flex;
              align-items: center;
              .zhicheng {
                height: 32rpx;
                position: relative;
                padding-left: 18rpx;
                font-family: PingFangSC-Medium;
                font-size: 20rpx;
                border-radius: 0 4rpx 4rpx 0;
                color: white;
                display: flex;
                align-items: center;
                padding-right: 6rpx;
                margin-left: 26rpx;
                background-image: linear-gradient(
                  134deg,
                  #b79b91 0%,
                  #c8b1a7 49%,
                  #c0a69c 100%
                );
                border-radius: 0 4rpx 4rpx 0;
                img {
                  width: 32rpx;
                  height: 32rpx;
                  position: absolute;
                  top: 0;
                  left: -16rpx;
                }
              }
              .daka {
                background-image: none;
                background-color: @border-color;
                padding-left: 6rpx;
                border-radius: 4rpx;
                margin-left: 10rpx;
                img {
                  position: static;
                }
              }
              & > img {
                width: 114rpx;
                height: 32rpx;
                margin-left: 10rpx;
              }
            }
            .line-h {
              font-family: PingFangSC-Regular;
              font-size: 22rpx;
              color: #777777;
              letter-spacing: 0;
              font-weight: 400;
              display: flex;
              flex-wrap: nowrap;
              justify-content: space-between;
              margin-top: 10rpx;
              .line-h-l {
                max-width: 440rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
            .line-y {
              font-family: PingFangSC-Regular;
              font-size: 22rpx;
              color: #777777;
              letter-spacing: 0;
              font-weight: 400;
              margin-top: 10rpx;
            }
          }
          &.active {
            position: relative;
            background: #e2f8f1;
            .doctor-right {
              .line-t,
              .line-h,
              .line-y {
                color: @text-color !important;
              }
            }
            .sel-icon {
              width: 36rpx;
              height: 20rpx;
              position: absolute;
              right: 0;
              bottom: 0;
            }
          }
          &.reserved {
            border: 2rpx solid #f8f8f8;
            background: #f8f8f8;
          }
        }
      }
      .multiple {
        .order__doctor--item {
          width: 265 * 2rpx;
          margin-right: 20rpx;
          padding: 30rpx;
          .doctor-right {
            .line-h {
              .line-h-l {
                max-width: 300rpx;
              }
            }
          }
        }
      }
    }
  }
}
.confirm-ctn {
  height: calc(85vh - 100rpx);
}
</style>
