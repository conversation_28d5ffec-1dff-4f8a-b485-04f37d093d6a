<template>
  <div v-if="visibleT" class="date__dialog">
    <div
      class="date__dialog--wrap"
      :class="{ 'ease-leave': !visible, 'pkg_list_up': ifpkgListUp }"
    >
      <div class="picker-toolbar">
        <div class="close-btn" @click="$emit('close')"></div>
        <div class="picker-title">{{ title || '预约服务' }}</div>
        <div class="picker-title-2">
          <div class="picker-title-2-l">全部项目</div>
          <div class="picker-title-2-r">
            <span
              >已使用 <span class="txt-bl">{{ usedNum || 0 }}</span
              >次/</span
            >
            <span>剩余{{ remainNum }}次</span>
          </div>
        </div>
      </div>
      <div class="picker-content">
        <div @click="toUrl" class="picker-title-link">
          <span>
            为保障最佳效果，疗程类项目请按推荐顺序预约和治疗，查看每次服务间隔<img
              src="https://static.soyoung.com/sy-pre/20b9j9z2noqkc-1678263000790.png"
            />
          </span>
        </div>
        <!-- 已预约列表 -->
        <div v-if="reserve.length" class="content-list-1">
          <div class="content-l-title">
            <div class="content-l-title-b"></div>
            <div>待到店</div>
          </div>
          <div class="list-1-c">
            <div
              v-for="(item, index) in reserve"
              :key="index"
              class="list-1-item"
            >
              <div class="list-1-item-title">
                <div class="list-1-item-title-l">
                  第{{ item.times }}次 <span class="l-h2">已预约</span>
                </div>
                <div @click="toReserve(item)" class="list-1-item-btn">
                  修改预约
                </div>
              </div>
              <div class="list-1-item-content">
                <div class="content-item">
                  预约项目：<span>{{ item.title }}</span>
                </div>
                <div class="content-item">
                  预约时间：<span>{{ item.reserve_time }}</span>
                </div>
                <div class="content-item">
                  预约机构：<span>{{ item.hospital_name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 待预约列表 -->
        <div v-if="pending_reserve.length" class="content-list-2">
          <div class="content-l-title">
            <div class="content-l-title-b"></div>
            <div>待预约</div>
          </div>
          <div class="list-1-c">
            <div
              v-for="(item, index) in pending_reserve"
              :key="index"
              class="list-1-item"
            >
              <div
                class="list-1-item-title"
                :class="{ 'item-title-blur': index === 0 }"
              >
                <div class="list-1-item-title-l">
                  第{{ item.times }}次 <span class="l-h2">待预约</span>
                </div>
                <span
                  v-if="
                    (index !== 0 && !(index !== 0 && isPackageSeq != 1)) ||
                    (index === 0 &&
                      ((!reserve.length && isPackageSeq != 1) ||
                        isPackageSeq == 1))
                  "
                >
                  <div @click="toReserve(item)" class="list-1-item-btn">
                    {{ index === 0 ? '立即' : '提前' }}预约
                  </div>
                </span>
              </div>
              <div class="list-1-item-content">
                <div class="content-item">
                  预约项目：<span>{{ item.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 已完成列表 -->
        <div v-if="write_off.length" class="content-list-3">
          <div class="content-l-title">
            <div class="content-l-title-b"></div>
            <div>已使用</div>
          </div>
          <div class="list-1-c">
            <div
              v-for="(item, index) in write_off"
              :key="index"
              class="list-1-item"
            >
              <div class="list-1-item-title">
                <div class="list-1-item-title-l">
                  第{{ item.times }}次 <span class="l-h2">已使用</span>
                </div>
                <div class="list-1-item-btn">已使用</div>
              </div>
              <div class="list-1-item-content">
                <div class="content-item">
                  预约项目：<span>{{ item.title }}</span>
                </div>
                <div class="content-item">
                  预约时间：<span>{{ item.reserve_time }}</span>
                </div>
                <div class="content-item">
                  预约机构：<span>{{ item.hospital_name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="height: 60rpx"></div>
      </div>
    </div>
    <div
      class="date__dialog--mask"
      @click="$emit('close')"
      :class="{ 'ease-leave-b': !visible }"
    ></div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { packageReserveList } from '@/api/reservation.js';
// import RemindDialog from '@/components/appointNew/components/remindDialog.vue';
export default {
  name: 'ck-appointment',
  props: {
    visible: { type: Boolean, default: false },
    pkgListUp: {
      type: Boolean
    },
    order_id: {
      type: String || Number
    }
  },
  watch: {
    visible: {
      handler() {
        if (this.visible) {
          this.visibleT = true;
          this.initData();
        } else {
          setTimeout(() => {
            this.visibleT = false;
          }, 350);
        }
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  data() {
    return {
      listInit: false,
      ifpkgListUp: false,
      remDialog: false,
      curItem: {},
      curIdx: '',
      usedNum: '',
      remainNum: '',
      visibleT: false,
      clkItem: {},
      isPackageSeq: '', // 乱序预约开关是否打开
      firstPenddingReserve: {},
      pending_reserveS: [],
      pending_reserve: [], // 待预约
      reserve: [], // 已预约
      write_off: [] // 已完成
    };
  },
  created() {
    this.ifpkgListUp = this.pkgListUp;
  },
  methods: {
    toUrl() {
      this.$toH5('https://m.soyoung.com/tmwap22419');
    },
    // 立即预约或提前预约
    toReserve(item) {
      // console.log(item);
      this.$emit('reservePopUp', item);
      // } else {
      //   // 提示弹窗
      //   this.remDialog = true;
      //   this.clkItem = item;
      // }
    },
    async initData() {
      const res = await packageReserveList({
        order_id: this.order_id
      });
      if (res) {
        const listObj = res?.reserve_list;
        this.isPackageSeq = res?.is_package_seq;
        this.usedNum = res?.package_num?.used_num || '';
        this.remainNum = res?.package_num?.remain_num || '';
        this.pending_reserve = listObj?.pending_reserve || [];
        this.firstPenddingReserve = this.pending_reserve[0] || {};
        this.reserve = listObj?.reserve || [];
        this.write_off = listObj?.write_off || [];
      }
    }
  }
};
</script>

<style scoped lang="less">
.tip {
  background: #fff9e0;
  height: 70rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #b8793f;
  font-weight: 400;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  line-height: 70rpx;
}
.date__dialog {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  z-index: 999;
  @keyframes popHeight {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  @keyframes popBackground {
    from {
      background: rgba(0, 0, 0, 0);
    }
    to {
      background: rgba(0, 0, 0, 0.5);
    }
  }
  @keyframes popHeightL {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }
  @keyframes popBackgroundL {
    from {
      background: rgba(0, 0, 0, 0.5);
    }
    to {
      background: rgba(0, 0, 0, 0);
    }
  }
  .ease-leave-b {
    animation: popBackgroundL 0.35s;
    animation-fill-mode: forwards;
  }
  .ease-leave {
    animation: popHeightL 0.35s;
    animation-fill-mode: forwards;
  }
  &--wrap {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 99;
    width: 100%;
    height: 85vh;
    overflow: hidden;
    box-sizing: border-box;
    background: #fff;
    animation: popHeight 0.3s;
    animation-fill-mode: forwards;
    border-radius: 15 * 2rpx 15 * 2rpx 0 0;
    .picker-toolbar {
      height: 150rpx;
      position: relative;
      background-image: url(https://static.soyoung.com/sy-pre/1cb0p1p2zk6al-1678705800838.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      padding: 0 30rpx 10rpx;

      .picker-title {
        display: flex;
        height: 104rpx;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        color: #222222;
        font-family: PingFangSC-Medium;
        text-align: center;
        flex-grow: 1;
      }
      .picker-title-2 {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        .picker-title-2-l {
          font-family: PingFangSC-Medium;
          font-size: 32rpx;
          color: #222222;
          font-weight: 500;
        }
        .picker-title-2-r {
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #222222;
          font-weight: 400;
          .txt-bl {
            color: @text-color;
          }
        }
      }
      .close-btn {
        position: absolute;
        right: 20rpx;
        top: 56rpx;
        transform: translateY(-50%);
        width: 40rpx;
        height: 40rpx;
        background: url('https://static.soyoung.com/sy-pre/56ro94fk2w7q-1650885000736.png')
          center/contain no-repeat;
      }
    }
    .picker-content {
      height: calc(85vh - 150rpx);
      overflow-y: auto;
      .picker-title-link {
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        height: 110rpx;
        padding: 5rpx 30rpx 0;
        color: #555555;
        background: url('https://static.soyoung.com/sy-pre/1cb0onnz2q4a2-1678705800838.png')
          center no-repeat;
        background-size: 100% 100%;
        letter-spacing: 0;
        line-height: 42rpx;
        font-weight: 400;
        border-bottom: 10rpx solid #f8f8f8;
        img {
          width: 24rpx;
          height: 24rpx;
          transform: translate(0, 4rpx);
        }
      }
      .content-list-1,
      .content-list-2,
      .content-list-3 {
        padding: 30rpx 30rpx 0;
        .content-l-title {
          display: flex;
          align-items: center;
          font-family: PingFangSC-Medium;
          font-size: 32rpx;
          color: #222222;
          font-weight: 500;
          .content-l-title-b {
            width: 8rpx;
            height: 28rpx;
            background: @text-color;
            margin-right: 12rpx;
          }
        }
        .list-1-c {
          .list-1-item {
            margin-top: 20rpx;
            padding-bottom: 30rpx;
            border-bottom: 2rpx solid #e7e7e7;
            .list-1-item-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 16rpx;
              .list-1-item-title-l {
                font-family: PingFangSC-Semibold;
                font-size: 30rpx;
                color: #333333;
                line-height: 48rpx;
                font-weight: 600;
                .l-h2 {
                  font-family: PingFangSC-Regular;
                  color: #222222;
                  font-weight: 400;
                  margin-left: 10rpx;
                }
              }
              .list-1-item-btn {
                width: 164rpx;
                height: 64rpx;
                border: 2rpx solid #dedede;
                border-radius: 34rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: PingFangSC-Regular;
                font-size: 26rpx;
                color: #222222;
                letter-spacing: 0;
                text-align: center;
                font-weight: 400;
              }
            }
            .list-1-item-content {
              font-family: PingFangSC-Regular;
              font-size: 28rpx;
              color: #333333;
              line-height: 50rpx;
              font-weight: 400;
              span {
                font-family: PingFangSC-Regular;
                font-size: 28rpx;
                color: #999999;
                line-height: 50rpx;
                font-weight: 400;
              }
              .content-item {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-bottom: 10rpx;
              }
            }
          }
          .list-1-item:last-child {
            border: 0;
          }
        }
      }
      .content-list-1,
      .content-list-2 {
        border-bottom: 10rpx solid #f8f8f8;
        .list-1-c {
          .list-1-item {
            .item-title-blur {
              .list-1-item-title-l {
                color: @text-color;
                span {
                  font-family: PingFangSC-Semibold;
                  font-weight: 600;
                  color: @text-color;
                }
              }
              .list-1-item-btn {
                background: @border-color;
                color: white;
                border: 0;
              }
            }
          }
        }
      }
      .content-list-3 {
        .list-1-c {
          .list-1-item {
            .list-1-item-title {
              .list-1-item-title-l {
                opacity: 0.4;
              }
              .list-1-item-btn {
                background: #e5e5e5;
                color: white;
              }
            }
            .list-1-item-content {
              opacity: 0.4;
            }
          }
        }
      }
    }
  }
  .pkg_list_up {
    animation: none;
  }
  &--mask {
    width: 100%;
    height: 100%;
    animation: popBackground 0.35s;
    animation-fill-mode: forwards;
  }
}
.buttom-iphone-x {
  bottom: 40rpx;
}

.submit__btn--wrap {
  position: absolute;
  right: 0;
  left: 0;
  bottom: 12rpx;
  .newTips {
    padding: 10rpx 30rpx 0 30rpx;
    font-size: 24rpx;
    font-family: PingFangSC-Regular;
    color: #555555;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 30rpx;
    .set-red {
      color: rgba(255, 64, 64, 1);
    }
    > img {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
      margin: 1rpx 10rpx 0 0;
    }
  }
}
.submit-btn {
  width: 690rpx;
  height: 88rpx;
  background: @border-color;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: 0 auto;
  color: #fff;
  font-family: PingFangSC-Medium;
  font-size: 34px;
  z-index: 99;
  border-radius: 22 * 2rpx;
  &.disabled {
    background: #dedede;
  }
  &.is-iphone-x {
    bottom: 80rpx;
  }
  .btn-text {
    font-family: PingFangSC-Medium;
    font-size: 34rpx;
    line-height: 40rpx;
  }
  .complete-value {
    font-size: 18rpx;
    line-height: 26rpx;
  }
}
.app-button-sty {
  background: @border-color !important;
}
</style>
