<template>
  <root-portal>
    <div v-if="visibleT" class="date__dialog">
      <div class="date__dialog--wrap" :class="{ 'ease-leave': !visible }">
        <div class="picker-toolbar">
          <div class="picker-title">{{ title }}</div>
          <template>
            <div class="close-btn" @click="hideServiceTimeSelect(false)"></div>
          </template>
        </div>
        <div class="picker-content">
          <div
            v-for="(item, index) in reasonList"
            :key="index"
            class="radio-item"
          >
            <div @click="checkRadio(item, index)" class="radio-item-ctn">
              <div class="radio-item-left">{{ item.Primary }}</div>
              <div class="radio-item-right">
                <div class="choose">
                  <div
                    class="choose-box"
                    v-if="curRadio === item.Primary"
                  ></div>
                </div>
              </div>
            </div>
            <div
              v-if="
                curRadio === item.Primary &&
                (item.secondaryList || curRadio === '其他')
              "
              class="radio-item-btm"
            >
              <div v-if="item.directions" class="radio-item-btm-title">
                {{ item.directions }}
              </div>
              <div v-if="'其他' !== curRadio" class="radio-item-btm-c">
                <div
                  v-for="(item1, index1) in item.secondaryList"
                  @click="checkRadio2(index, item1, index1)"
                  :key="index1"
                  class="radio-item-btm-item"
                  :class="{
                    'radio-item-btm-item-b':
                      item1 == secondary['secondary' + index].curSecondary
                  }"
                >
                  {{ item1 }}
                </div>
                <div
                  @click="checkRadio2(index, '其他', index1)"
                  v-if="item.other == 1"
                  class="radio-item-btm-item"
                  :class="{
                    'radio-item-btm-item-b':
                      '其他' == secondary['secondary' + index].curSecondary
                  }"
                >
                  其他
                </div>
              </div>
              <div
                v-if="
                  '其他' == secondary['secondary' + index].curSecondary ||
                  '其他' == curRadio
                "
                class="radio-item-btm-texta"
              >
                <textarea
                  maxlength="100"
                  v-model="secondary['secondary' + index].direc"
                  placeholder="请填写具体原因..."
                  name="btm-textarea"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="pinker-btn">
          <div
            class="commit-btn"
            @click="commitReason"
            :class="{ 'can-commit': canCommit }"
          >
            提交
          </div>
        </div>
      </div>
      <div
        class="date__dialog--mask"
        :class="{ 'ease-leave-b': !visible }"
        @click="hideServiceTimeSelect(false)"
      ></div>
    </div>
  </root-portal>
</template>

<script>
import { getReasonList } from '@/api/reservation.js';
export default {
  name: 'ck-appointment',
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: '改约原因' }
  },
  watch: {
    visible: {
      handler() {
        if (this.visible) {
          this.visibleT = true;
          this.init();
          // wx.hideTabBar({
          //   animation: false
          // });
        } else {
          setTimeout(() => {
            this.visibleT = false;
            // wx.showTabBar({
            //   animation: false
            // });
          }, 350);
        }
      }
    }
  },
  data() {
    return {
      curRadio: '',
      reasonType: '',
      curRadioIdx: '',
      visibleT: false,
      curSecondaryIdx: '',
      secondary: {},
      reasonList: []
    };
  },
  computed: {
    canCommit() {
      if (
        (this.curRadio && !this.reasonList[this.curRadioIdx]?.Secondary) ||
        this.reasonList[this.curRadioIdx]?.Secondary === '其他'
      ) {
        if (this.curRadio !== '其他') {
          return true;
        } else {
          if (this.secondary['secondary' + this.curRadioIdx].direc.trim()) {
            return true;
          }
        }
      } else if (
        this.curRadio &&
        this.reasonList[this.curRadioIdx]?.Secondary
      ) {
        if (this.secondary['secondary' + this.curRadioIdx].curSecondary) {
          if (
            this.secondary['secondary' + this.curRadioIdx].curSecondary !==
            '其他'
          ) {
            return true;
          } else {
            if (this.secondary['secondary' + this.curRadioIdx].direc.trim()) {
              return true;
            }
          }
        }
      }
      return false;
    }
  },
  methods: {
    init() {
      this.curRadio = '';
      this.reasonType = '';
      this.curRadioIdx = '';
      this.curSecondaryIdx = '';
      this.secondary = {};
      this.reasonList = [];
      this.getReasons();
    },
    checkRadio(item, index) {
      this.curRadio = item.Primary;
      this.reasonType = item.reason_type || '';
      this.curRadioIdx = index;
      if (!this.secondary['secondary' + index]) {
        this.$set(this.secondary, 'secondary' + index, {
          curSecondary: '',
          direc: ''
        });
      }
    },
    hideServiceTimeSelect() {
      this.$emit('visible', false);
    },
    commitReason() {
      if (!this.canCommit) {
        return;
      }
      const data = {
        primary: this.curRadio,
        reasonType: this.reasonType,
        secondary: this.secondary['secondary' + this.curRadioIdx].curSecondary,
        description: this.secondary['secondary' + this.curRadioIdx].direc || ''
      };
      this.$emit('commitReason', data);
    },
    checkRadio2(index, item1, index1) {
      this.secondary['secondary' + index].curSecondary = item1;
      this.curSecondaryIdx = index1;
      if (item1 !== '其他') {
        this.secondary['secondary' + index].direc = '';
      }
    },
    async getReasons() {
      const res = await getReasonList({});
      if (res) {
        this.reasonList = res;
        this.reasonList.forEach((item) => {
          if (item.Secondary) {
            item.secondaryList = item.Secondary.split(',');
          }
        });
      }
    }
  }
};
</script>

<style scoped lang="less">
.tip {
  background: #fff9e0;
  height: 70rpx;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  color: #b8793f;
  font-weight: 400;
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  line-height: 70rpx;
}
.date__dialog {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  transform: translateZ(1000px);
  z-index: 99999999;
  @keyframes popHeight {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
  @keyframes popBackground {
    from {
      background: rgba(0, 0, 0, 0);
    }
    to {
      background: rgba(0, 0, 0, 0.5);
    }
  }
  @keyframes popHeightL {
    from {
      transform: translatey(0);
    }
    to {
      transform: translatey(100%);
    }
  }
  @keyframes popBackgroundL {
    from {
      background: rgba(0, 0, 0, 0.5);
    }
    to {
      background: rgba(0, 0, 0, 0);
    }
  }
  .ease-leave-b {
    animation: popBackgroundL 0.35s;
    animation-fill-mode: forwards;
  }
  .ease-leave {
    animation: popHeightL 0.35s;
    animation-fill-mode: forwards;
  }
  &--wrap {
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 99;
    width: 100%;
    height: 85vh;
    overflow: hidden;
    box-sizing: border-box;
    background: #fff;
    animation: popHeight 0.3s;
    animation-fill-mode: forwards;
    .pinker-btn {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 180rpx;
      display: flex;
      justify-content: center;
      box-sizing: border-box;
      padding-top: 12rpx;
      .commit-btn {
        width: 670rpx;
        height: 88rpx;
        background: #dedede;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 26rpx;
        font-weight: 500;
      }
      .can-commit {
        background: @border-color;
      }
    }
    .picker-toolbar {
      height: 104rpx;
      position: relative;
      .picker-title {
        display: flex;
        height: 104rpx;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        color: #222222;
        font-family: PingFangSC-Medium;
        text-align: center;
        flex-grow: 1;
      }
      .skip-btn {
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%);
        font-family: PingFangSC-Regular;
        font-size: 28rpx;
        color: #777777;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
      }
      .close-btn {
        position: absolute;
        right: 20rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 40rpx;
        height: 40rpx;
        background: url('https://static.soyoung.com/sy-pre/56ro94fk2w7q-1650885000736.png')
          center/contain no-repeat;
      }
    }
    .picker-content {
      height: calc(85vh - 300rpx);
      overflow-y: auto;
      padding: 0rpx 50rpx 0;
      max-height: 1080rpx;
      overflow-y: scroll;
      .radio-item {
        border-top: 1px solid #f2f2f2;
        .radio-item-ctn {
          height: 100rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .radio-item-left {
            font-family: PingFangSC-Regular;
            font-size: 28rpx;
            color: #222222;
            font-weight: 400;
          }
          .radio-item-right {
            .choose {
              width: 32rpx;
              height: 32rpx;
              border: 1px solid #333333;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .choose-box {
              width: 26rpx;
              height: 26rpx;
              background: #333333;
            }
          }
        }
        .radio-item-btm {
          margin-bottom: 30rpx;
          width: 100%;
          box-sizing: border-box;
          .radio-item-btm-c {
            display: flex;
            flex-wrap: wrap;
            .radio-item-btm-item {
              padding: 20rpx 40rpx;
              display: flex;
              align-items: center;
              font-family: PingFangSC-Regular;
              font-size: 26rpx;
              color: #333333;
              letter-spacing: 0;
              font-weight: 400;
              margin-right: 30rpx;
              margin-bottom: 30rpx;
              background-color: #f2f2f2;
              text-align: center;
              box-sizing: border-box;
              height: 76rpx;
              border: 4rpx solid #f2f2f2;
            }
            .radio-item-btm-item-b {
              color: @text-color;
              border: 4rpx solid @border-color;
              background-color: #ffffff;
              position: relative;
              &::after {
                content: '';
                display: block;
                position: absolute;
                width: 38rpx;
                height: 28rpx;
                background-image: url('https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1727148176604.png');
                background-size: contain;
                position: absolute;
                right: -1px;
                bottom: -1px;
              }
            }
          }
          .radio-item-btm-texta {
            padding-bottom: 30rpx;
            textarea {
              background: #f2f2f2;
              width: 100%;
              height: 160rpx;
              box-sizing: border-box;
              padding: 30rpx 20rpx;
              font-family: PingFangSC-Regular;
              font-size: 26rpx;
              color: #030303;
              letter-spacing: 0;
              line-height: 40rpx;
              font-weight: 400;
            }
            input::-webkit-input-placeholder,
            textarea::-webkit-input-placeholder {
              font-family: PingFangSC-Regular;
              font-size: 26rpx;
              color: #bababa;
              letter-spacing: 0;
              line-height: 40rpx;
              font-weight: 400;
            }
          }
          .radio-item-btm-title {
            font-family: PingFangSC-Regular;
            font-size: 24rpx;
            color: #646464;
            letter-spacing: 0;
            font-weight: 400;
            margin-bottom: 30rpx;
          }
        }
      }
    }
  }
  &--mask {
    width: 100%;
    height: 100%;
    animation: popBackground 0.35s;
    animation-fill-mode: forwards;
  }
}
.buttom-iphone-x {
  bottom: 40rpx;
}

.submit__btn--wrap {
  position: absolute;
  right: 0;
  left: 0;
  bottom: 12rpx;
  .newTips {
    padding: 10rpx 30rpx 0 30rpx;
    font-size: 24rpx;
    font-family: PingFangSC-Regular;
    color: #555555;
    letter-spacing: 0;
    font-weight: 400;
    margin-bottom: 30rpx;
    .set-red {
      color: rgba(255, 64, 64, 1);
    }
    > img {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
      margin: 1rpx 10rpx 0 0;
    }
  }
}
.submit-btn {
  width: 690rpx;
  height: 88rpx;
  background: @border-color;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: 0 auto;
  color: #fff;
  font-family: PingFangSC-Medium;
  font-size: 34px;
  z-index: 99;
  border-radius: 22 * 2rpx;
  &.disabled {
    background: #dedede;
  }
  &.is-iphone-x {
    bottom: 80rpx;
  }
  .btn-text {
    font-family: PingFangSC-Medium;
    font-size: 34rpx;
    line-height: 40rpx;
  }
  .complete-value {
    font-size: 18rpx;
    line-height: 26rpx;
  }
}
.app-button-sty {
  background: #00af84 !important;
}
</style>
