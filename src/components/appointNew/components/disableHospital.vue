<template>
  <div v-if="hospitalListDisabled.length" class="hospital-disable-list">
    <div class="line-text">
      <div class="line-t"></div>
      <div class="line-m">以下机构预约时段已约满或未开放</div>
      <div class="line-t"></div>
    </div>
    <div
      v-for="item in hospitalListDisabled"
      :key="item.hospital_id"
      class="hospital-ctn"
    >
      <HospitalCard
        :noTimeSlice="true"
        :lesNum="contLesNum(item.time_details)"
        :itemData="item.hos_detail || item"
        :add_c_btn="item.add_c_btn"
      ></HospitalCard>
    </div>
  </div>
</template>

<script>
import HospitalCard from '@/components/appointNew/components/hospitalListItem.vue';
export default {
  components: { HospitalCard },
  props: {
    hospitalListDisabled: Array
  },
  mounted() {},
  data() {
    return {};
  },
  methods: {
    contLesNum(list = []) {
      let num = 0;
      console.log(list);
      list?.forEach((item) => {
        num += item.remain_num;
      });
      return num;
    }
  }
};
</script>

<style lang="less" scoped>
.hospital-disable-list {
  .line-text {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #777777;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    margin: 30rpx 0;
    .line-m {
      color: #bababa;
      margin: 0rpx 20rpx;
    }
    .line-t {
      width: 28rpx;
      height: 2rpx;
      background-color: #dedede;
    }
  }
  .hospital-ctn {
    background-color: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
  }
}
</style>
