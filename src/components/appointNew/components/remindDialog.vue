<template>
  <div>
    <customPopup title="预约提示" v-model="popupStatus1" width="280px">
      <template #body>
        <div class="dia-body">
          <!-- <div @click="close" class="close-btn"></div> -->
          <div>
            为保证最佳治疗效果，建议您按推荐顺序预约
            <span class="cl-bl">「{{ suggestReserve }}」</span>。
          </div>
          <div>
            若提前预约「{{
              clickReserve
            }}」，治疗顺序会按实际预约顺序调整，是否确认提前预约？
          </div>
        </div>
      </template>
      <template #footer>
        <div class="dia-btns">
          <div class="btn normal" @click="close">取消</div>
          <div class="btn" @click="confirm">确定</div>
        </div>
      </template>
    </customPopup>
  </div>
</template>

<script>
import customPopup from '@/components/popup';

export default {
  props: {
    popupStatus: Boolean,
    failTime: String,
    suggestReserve: String,
    clickReserve: String
  },
  components: { customPopup },
  data() {
    return {
      popupStatus1: false
    };
  },
  watch: {
    popupStatus: {
      handler() {
        this.popupStatus1 = this.popupStatus;
      },
      immediate: true
    }
  },
  methods: {
    close() {
      this.$emit('close');
    },
    confirm() {
      this.$emit('confirm');
    }
  }
};
</script>

<style lang="less" scoped>
.close-btn {
  position: absolute;
  right: 20rpx;
  top: 30rpx;
  width: 40rpx;
  height: 40rpx;
  background: url('https://static.soyoung.com/sy-pre/56ro94fk2w7q-1650885000736.png')
    center/contain no-repeat;
}
.dia-body {
  text-align: left;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #333333;
  letter-spacing: 0;
  line-height: 48rpx;
  font-weight: 400;
  .cl-bl {
    color: @text-color;
  }
}
.set-p {
  color: @text-color;
}
.dia-btns {
  margin-top: 30rpx;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  letter-spacing: 0;
  font-weight: 400;
  display: flex;
  .btn {
    width: 208rpx;
    height: 68rpx;
    margin: 0 auto 30rpx;
    border: 1rpx solid @border-color;
    border-radius: 40rpx;
    background: @border-color;
    color: #fff;
    line-height: 68rpx;
  }
  .normal {
    background: #fff;
    color: #222;
    border-color: #dedede;
  }
}
</style>
