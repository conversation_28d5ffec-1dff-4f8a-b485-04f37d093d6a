<template>
  <div class="container" :class="{ 'confirm-ctn': !order_id }">
    <div class="hospital-title" v-if="hospitalList.length">
      <div @click="$emit('close')" class="t-left">
        <img
          src="https://static.soyoung.com/sy-pre/2k47i8q9a59jo-1681729800706.png"
        />
        <span> 查看全部门店 </span>
      </div>
      <span class="order-hos-gray">（共{{ hospitalList.length || 0 }}家）</span>
    </div>
    <!-- 滚动条位置 -->
    <scroll-view
      @scroll="hosScroll"
      @scrolltoupper="scrolltoupper"
      :scroll-x="false"
      :scroll-y="true"
      class="btm-ctn"
      :show-scrollbar="false"
      :class="{ 'confirm-btm-ctn': !order_id }"
    >
      <!-- 选择适用门店 start  -->
      <div class="order__hospital" v-if="hospitalList.length">
        <div :class="['order__hospital--list', multiple]">
          <div
            class="order__hospital--item"
            v-for="item in hospitalList"
            :key="item.hospital_id"
            :class="{
              gray: item.surplus_num === 0,
              active:
                curHospital.hospital_id === item.hospital_id ||
                hospitalList.length == 1
            }"
            @click="getHosptial(item)"
          >
            <div class="cashback-status" v-if="item.cashback">
              {{ item.cashback }}
            </div>
            <div class="hospitalName">
              <div
                class="hospital-name-txt"
                :class="{ 'hospital-name-stxt': item.hospital_self }"
              >
                {{ item.hospital_name }}
              </div>
              <Icon
                v-if="Number(item.hospital_tag_type) === 1"
                class="hospital-self"
              ></Icon>
              <HqIcon
                v-else-if="Number(item.hospital_tag_type) === 2"
                class="hospital-self"
              ></HqIcon>
            </div>
            <div class="address">
              <div class="addressName">
                {{ item.hospital_addr }}
              </div>
            </div>
            <div @click.stop="toMap(item)" class="distance">
              <div class="dis-img"></div>
              <div class="dis-txt">
                {{ item.distance_str || item.city_name || '' }}
              </div>
            </div>
            <!-- 选中的图标 -->
            <img
              v-if="
                curHospital.hospital_id === item.hospital_id ||
                hospitalList.length == 1
              "
              class="sel-icon"
              src="https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1727167737093.png"
            />
          </div>
        </div>
      </div>
      <div
        v-if="hospitalList.length && pinkerOptions.length"
        class="service-title"
      >
        选择服务开始时间
      </div>
      <!-- 选择适用门店 end    -->
      <div class="time-pk-ctn">
        <TimePinker
          type="hos"
          :lBtm="true"
          :order_id="order_id"
          :othersBlur="othersBlur"
          :oldTimeFrom="oldData.from"
          :loading.sync="timeLoading"
          :dateBtmTxtUrl="dateBtmTxtUrl"
          @submit="selectTime"
          :initDate="oldCurrentDate"
          :initTime="oldCurrentTime"
          :options="pinkerOptions"
          :hospitalId="curHospital.hospital_id"
          :sku_id="sku_id"
          :curHospital="curHospital"
          @calendarVisible="(val) => $emit('calendarVisible', val)"
          @yuyueReminderSubscribe="onYuyueReminderSubscribe"
        ></TimePinker>
      </div>
    </scroll-view>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import {
  apiChainReservationHospitalList,
  getCalendar
} from '@/api/reservation.js';

import Icon from '@/components/icon/Self.vue';
import HqIcon from '@/components/icon/hq.vue';
import TimePinker from './AppointmentTimerPicker';
export default {
  components: {
    Icon,
    HqIcon,
    TimePinker
  },
  props: {
    dateBtmTxtUrl: {
      type: Object,
      default: () => {}
    },
    // 确认订单页，用户已预约的日期
    reserveResult: {
      type: Object,
      default: () => {}
    },
    // 预约次数
    request_time: {
      type: Number,
      default: 0
    },
    othersBlur: {
      type: Boolean,
      default: false
    },
    forceReserveInfo: {
      type: Object
    },
    oldData: {
      type: Object,
      default: () => {}
    },
    baseInfo: {
      type: Object,
      default: () => {}
    },
    lastMulData: {
      type: Object,
      default: () => {}
    },
    hospital_list: {
      type: Array,
      default: () => []
    },
    baseInfoCache: {
      type: Object
    },
    order_id: {
      type: String || Number
    },
    // 点击的机构id
    clickHospitalId: {
      type: String || Number
    },
    sku_id: {
      type: String || Number
    },
    ingredients_id: {
      type: String || Number
    },
    city_id: {
      type: String || Number
    },
    hospital_id: {
      type: String || Number
    },
    order_app_id: {
      type: Number,
      default: 0
    },
    order_hospital_id: {
      type: Number,
      default: 0
    },
    top_order_id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      oldTimeBlur: false,
      timeLoading: false,
      oldCurrentDate: '', // 已预约的日期
      oldCurrentTime: '', // 已预约的时间
      curHospital: {}, // 已选择的机构
      curDate: {}, // 已选择的日期
      curTime: {}, // 已选择的时间切片
      pinkerOptions: [], // 日期列表
      hospitalList: [], // 机构列表
      toastText: ''
    };
  },
  watch: {
    selectHospitalId: {
      handler() {
        this.selectHospitalId && this.getLesTimeRefTxt();
      },
      immediate: true
    },
    // 城市改变，重新请求数据
    city_id() {
      this.clearStatus();
      this.getHospitalListFn();
      // 初始化日期数据
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    multiple() {
      return this.hospitalList.length > 1 ? 'multiple' : '';
    }
  },
  created() {
    this.setOldData();
    this.getHospitalListFn('init');
  },
  mounted() {},
  methods: {
    toMap(data) {
      console.log('toMap', data);
      uni.navigateTo({
        url: `/packageHospital/hospital-map?hospital_id=${data.hospital_id}`
      });
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:institution_address_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 2,
          city_id: this.city_id,
          hospital_id: data.hospital_id
        }
      });
    },
    clearStatus() {
      this.oldCurrentDate = '';
      this.oldCurrentTime = '';
      this.curHospital = {};
      this.curDate = {};
      this.curTime = {};
    },
    scrolltoupper() {
      this.$emit('setTTop', false);
    },
    hosScroll(e) {
      const top = e?.detail?.scrollTop;
      // 再往下走
      if (top - this.lastTop > 0) {
        if (top > 5) {
          this.$emit('setTTop', true);
        }
      }
      this.lastTop = top;
    },
    setOldData() {
      // 点击的其它机构，不还原旧数据
      if (
        this.clickHospitalId &&
        +this.clickHospitalId !== +this.oldData.hospital_id
      ) {
        return;
      }
      // 有之前的预约数据
      if (
        this.oldData.month &&
        (+this.oldData.reserve_mode === 2 ||
          +this.oldData.reserve_mode === 0 ||
          +this.oldData.reserve_mode === 1)
      ) {
        this.oldCurrentDate =
          this.oldData.year + '-' + this.oldData.month + '-' + this.oldData.day;
        this.oldCurrentTime = this.oldData.from;
      }
      // 确认订单页的旧数据
      if (this.reserveResult?.commitData?.reserve_mode) {
        const wSetData = this.reserveResult?.commitData;
        this.oldCurrentDate = wSetData.start_time?.slice?.(0, 10);
        this.oldCurrentTime = this.reserveResult?.time?.from;
      }
    },
    titleToUrl() {
      this.$toH5('https://m.soyoung.com/tmwap22419');
    },
    async getHospitalListFn(init) {
      this.timeLoading = true;
      const reqData = {
        order_id: this.order_id,
        city_id: this.city_id,
        hospital_id: this.hospital_id,
        sku_id: this.sku_id,
        ingredients_id: this.ingredients_id,
        select_city_id: this.city_id,
        order_hospital_id: this.order_hospital_id,
        order_app_id: this.order_app_id,
        top_order_id: this.top_order_id
      };
      let res;
      if (this.hospital_list.length && init) {
        res = {
          list: this.hospital_list
        };
      } else {
        res = await apiChainReservationHospitalList(reqData);
      }
      if (res) {
        const hosList = res?.list || [];
        this.hospitalList = hosList;
        this.spliceHolpital(this.hospitalList);
        if (hosList.length) {
          this.curHospital = hosList[0];
          this.getDateListFn();
        } else {
          this.timeLoading = false;
          this.$emit('setSkelReady');
          this.pinkerOptions = [];
        }
      } else {
        this.timeLoading = false;
        this.$emit('setSkelReady');
        this.pinkerOptions = [];
      }
    },
    spliceHolpital(hosList = []) {
      if (!hosList?.length) return;
      const id =
        this.clickHospitalId ||
        this.oldData.hospital_id ||
        this.lastMulData.hospital_id;
      if (id) {
        let index = 0;
        hosList.forEach((item, idx) => {
          if (+item.hospital_id === +id) {
            index = idx;
          }
        });
        const newArr = hosList.splice(index, 1);
        hosList.unshift(newArr[0]);
      }
    },
    getCurUrl() {
      const pages = getCurrentPages();
      return pages[pages.length - 1];
    },
    // 推算次卡锚定日期
    setIntervalAppointment(dateList = []) {
      const interval = +this.baseInfo.interval_day_num || 0; // 最小间隔
      const lastReserved = this.lastMulData; // 上次的时间
      let currentIndex; // 上次预约的索引
      for (let i = 0; i < dateList.length; i++) {
        const item = dateList[i];
        if (
          +item.year === +lastReserved.year &&
          +item.month === +lastReserved.month &&
          +item.day === +lastReserved.day
        ) {
          currentIndex = i;
        }
        if (
          i > currentIndex + interval &&
          +item.status !== 0 &&
          +item.remain_num > 0
        ) {
          // 在间隔日期之后
          this.oldCurrentDate = `${item.year}-${item.month}-${item.day}`;
          break;
        }
      }
    },
    async getDateListFn() {
      this.timeLoading = true;
      const obj = {
        order_id: this.order_id,
        city_id: this.city_id,
        top_order_id: this.top_order_id,
        hospital_id: this.curHospital.hospital_id,
        sku_id: this.sku_id,
        ingredients_id: this.ingredients_id
      };
      if (this.request_time) {
        obj.request_times = this.request_time;
      }
      // 改约 todo

      // if(){
      //   // 改约的时候加
      //   obj.order_id = this.order_id;
      //   obj.reserve_id = this.reserve_id;
      //
      // }

      const res = await getCalendar(obj);
      this.$emit('setSkelReady');
      setTimeout(() => {
        this.timeLoading = false;
      }, 200);
      if (res) {
        this.setIntervalAppointment(res.list || []);
        this.pinkerOptions = res.list || [];
      } else {
        this.pinkerOptions = [];
        this.timeLoading = false;
      }
    },
    selectTime(val) {
      this.curDate = val.date;
      this.curTime = val.time || {};
      this.submitData();
    },
    submitData() {
      this.$emit('change', {
        hospital: this.curHospital,
        date: this.curDate,
        time: this.curTime
      });
    },
    // 点击选择了机构
    getHosptial(item) {
      if (item.hospital_id === this.curHospital.hospital_id) {
        return;
      }
      this.curHospital = item;
      this.curDate = {};
      this.curTime = {};
      this.oldCurrentDate = '';
      this.oldCurrentTime = '';
      // this.submitData();
      this.getDateListFn();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_hospital_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 2,
          city_id: this.city_id,
          hospital_id: item.hospital_id
        }
      });
    },
    queryToObject(query) {
      if (!query || typeof query !== 'string') return;
      const Obj = {};
      const queryArr = query.split('&');
      queryArr.forEach((item) => {
        const temp = item.split('=');
        const key = temp[0];
        const val = temp[1];
        Obj[key] = val;
      });
      return Obj;
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  color: #222;
  height: calc(85vh - 304rpx);
  overflow: hidden;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  .service-title {
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: #222222;
    font-weight: 500;
    padding-left: 30rpx;
    padding-bottom: 30rpx;
    background-color: #fff;
  }
  .time-pk-ctn {
    background: #f8f8f8;
    // padding-left: 30rpx;
    padding-bottom: 240rpx;
  }
  .btm-ctn {
    height: calc(85vh - 440rpx);
    overflow-y: auto;
  }
  .confirm-btm-ctn {
    height: calc(85vh - 340rpx);
  }
  .hospital-title {
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 28rpx;
    color: #222222;
    background-color: #fff;
    padding: 30rpx;
    padding-bottom: 10rpx;
    padding-right: 19rpx;
    display: flex;
    justify-content: space-between;
    .t-left {
      display: flex;
      align-items: center;
      img {
        width: 36rpx;
        height: 36rpx;
      }
    }
    .order-hos-gray {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #777777;
      font-weight: 400;
    }
    .order-change-city {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #222222;
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
      display: flex;
      align-items: center;
      img {
        width: 20rpx;
        height: 20rpx;
        margin-left: 8rpx;
        margin-top: 2rpx;
      }
    }
  }
  .order {
    &__hospital {
      background-color: #fff;
      &--list {
        padding-top: 30rpx;
        padding-left: 30rpx;
        padding-bottom: 40rpx;
        box-sizing: border-box;
        overflow-x: auto;
        white-space: nowrap;
        .order__hospital--item {
          position: relative;
          display: inline-flex;
          flex-wrap: wrap;
          width: calc(100% - 30rpx);
          margin-right: 30rpx;
          padding: 30rpx;
          justify-content: space-between;
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          background: #f8f8f8;
          box-sizing: border-box;
          .cashback-status {
            height: 28rpx;
            line-height: 28rpx;
            background: #89dc65;
            font-family: PingFangSC-regular;
            font-size: 18rpx;
            color: #333333;
            letter-spacing: 0;
            text-align: center;
            padding: 0rpx 10rpx;
            position: absolute;
            left: 0rpx;
            top: 0rpx;
          }
          //border-radius: 20rpx;
          &.gray {
            color: #999999;
            .right {
              color: #999999;
              .bar {
                background: #eaeaea;
              }
            }
          }
          .distance {
            position: absolute;
            top: 82rpx;
            right: 30rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 22rpx;
            line-height: 22rpx;
            color: #777;
            font-weight: 400;
            .dis-txt {
              height: 22rpx;
            }
            .dis-img {
              position: absolute;
              right: 0rpx;
              bottom: 36rpx;
              width: 40rpx;
              height: 40rpx;
              background-image: url('https://static.soyoung.com/sy-pre/<EMAIL>');
              background-repeat: no-repeat;
              background-size: 100% 100%;
            }
          }
          .hospitalName {
            max-width: 424rpx;
            font-family: PingFangSC-Medium;
            font-size: 28rpx;
            line-height: 40rpx;
            margin-bottom: 10rpx;
            color: #333333;
            font-weight: 500;
            display: flex;
            align-items: center;
            .hospital-name-txt {
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 610rpx;
              white-space: nowrap;
            }
            .hospital-name-stxt {
              max-width: 434rpx;
            }
            .hospital-self {
              margin-left: 10rpx;
            }
          }
          .address {
            font-family: PingFangSC-Regular;
            display: flex;
            width: 100%;
            color: #777777;
            font-size: 22rpx;
            font-weight: 400;
            .addressName {
              max-width: 360rpx;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .right {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            color: @text-color;
            font-size: 20rpx;
            font-weight: 400;
            .bar {
              margin-bottom: 8rpx;
              width: 100rpx;
              height: 20rpx;
              background: #d9ece8;
              border-radius: 10rpx;
              .inbar {
                border-radius: 10rpx;
                height: 20rpx;
                background: @border-color;
              }
            }
          }
          &.active {
            position: relative;
            background: #fff;
            border: 4rpx solid #333;
            box-sizing: border-box;
            .hospitalName,
            .address,
            .right {
              color: @text-color !important;
            }
            .distance {
              color: @text-color !important;
              .dis-img {
                background-image: url('https://static.soyoung.com/sy-pre/<EMAIL>');
              }
            }
            .sel-icon {
              width: 36rpx;
              height: 20rpx;
              position: absolute;
              right: 0;
              bottom: -1px;
            }
          }
          &.reserved {
            border: 2rpx solid #f8f8f8;
            background: #f8f8f8;
          }
        }
        .order__hospital_empty {
          height: 130rpx;
        }
      }
      .multiple {
        .order__hospital--item {
          width: 265 * 2rpx;
          margin-right: 20rpx;
          padding: 30rpx;
          .cashback-status {
            height: 28rpx;
            line-height: 28rpx;
            background: #89dc65;
            font-family: PingFangSC-regular;
            font-size: 18rpx;
            color: #333333;
            letter-spacing: 0;
            text-align: center;
            padding: 0rpx 10rpx;
            position: absolute;
            left: 0rpx;
            top: 0rpx;
          }
          .hospitalName {
            .hospital-name-txt {
              max-width: 430rpx;
            }
            .hospital-name-stxt {
              max-width: 280rpx;
            }
          }
          .address {
            .addressName {
              font-size: 20rpx;
              // max-width: 252rpx;
            }
          }
        }
        .order__hospital_empty {
          height: 130rpx;
        }
      }
    }
  }
}
.confirm-ctn {
  height: calc(85vh - 100rpx);
}
</style>
