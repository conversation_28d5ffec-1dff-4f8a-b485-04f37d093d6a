<template>
  <div class="empty">
    <img
      src="https://static.soyoung.com/sy-design/2k8b1fjb5gw6x1727167736564.png"
      alt=""
    />
    <div class="line-1">{{ remain }}</div>
  </div>
</template>

<script>
export default {
  props: {
    remain: String,
    reportData: Object
  },
  mounted() {},
  data() {
    return {};
  },
  methods: {
    async jiaC() {
      this.$bridge({
        url: `/packageAccount/consult?qzychannel=50`
      });
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:contact_consultant_click',
        ext: {
          uid: this.reportData.uid,
          product_id: this.reportData.sku_id,
          order_id: this.reportData.order_id,
          status: this.reportData.month ? 2 : 1
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.empty {
  width: 100%;
  background-color: #fff;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  line-height: 40rpx;
  color: #999999;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  border-radius: 20rpx;
  img {
    width: 70rpx;
    height: 68rpx;
    margin-bottom: 40rpx;
  }
  .line-1 {
    margin: 0 auto;
    width: 600rpx;
    color: #030303;
    padding-bottom: 50rpx;
  }
  .to-c {
    color: @text-color;
  }
}
</style>
