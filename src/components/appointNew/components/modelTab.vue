<template>
  <div class="model-tab">
    <div class="tab-left anchor">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        @click="tabClick(item, index)"
        class="tab-item anchor-btn"
        :class="{ 'tab-blur': defaultTab.value == item.value }"
      >
        <span>{{ item.name }}</span>
      </div>
      <div
        class="anchor-slide"
        :style="{
          transform: `translateX(${slideLeft}px)`
        }"
      ></div>
    </div>
    <div @click="$emit('cityClick')" class="tab-right">
      <span>{{ cityName }}</span>
      <img
        src="https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    cityName: {
      type: String,
      default: ''
    },
    tabList: {
      type: Array,
      default: () => []
    },
    defaultTab: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tabBlurIdx: 0,
      slideLeft: 0,
      slideVisible: false
    };
  },
  async mounted() {
    // 计算默认tab的索引
    if (this.defaultTab.value) {
      this.tabList.forEach((item, index) => {
        if (item.value === this.defaultTab.value) {
          this.tabBlurIdx = index;
        }
      });
    }
    const res = await this.$queryNodes('.anchor .anchor-btn');
    this.$watch(
      'tabBlurIdx',
      function (key) {
        const { left, width } = res[key];
        this.slideLeft = left + width / 2;
        this.slideVisible = true;
      },
      { immediate: true }
    );
  },
  methods: {
    tabClick(item, index) {
      this.tabBlurIdx = index;
      this.$emit('change', item);
    }
  }
};
</script>

<style lang="less" scoped>
@fontColor03: #030303;

.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.model-tab {
  height: 60rpx;
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #646464;
  letter-spacing: 0;
  font-weight: 400;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  .tab-left {
    display: flex;
    position: relative;
    .tab-item {
      margin-right: 40rpx;
      position: relative;
    }
    .tab-blur {
      color: @fontColor03;
      font-weight: 600;
      font-family: PingFangSC-Semibold;
    }
    //.anchor-slide {
    //  position: absolute;
    //  bottom: -12rpx;
    //  left: -52rpx;
    //  height: 12rpx;
    //  width: 44rpx;
    //  background: url(https://static.soyoung.com/sy-design/2g0b0yvllsm7i1717128497403.png)
    //    no-repeat center center transparent;
    //  background-size: contain;
    //  transition: transform 0.2s;
    //}
    .anchor-slide {
      position: absolute;
      bottom: -20rpx;
      left: -47rpx;
      height: 4rpx;
      width: 40rpx;
      border-bottom: 4rpx solid @fontColor03;
      transition: transform 0.2s;
      box-sizing: border-box;
    }
  }
  .tab-right {
    display: flex;
    align-items: center;
    transform: translateY(4rpx);
    font-family: PingFangSC-Regular;
    font-size: 26rpx;
    color: @fontColor03;
    font-weight: 400;
    img {
      width: 14rpx;
      height: 18rpx;
      margin-left: 6rpx;
    }
  }
}
</style>
