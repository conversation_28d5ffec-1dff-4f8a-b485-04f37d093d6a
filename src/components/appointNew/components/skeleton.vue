<template>
  <div class="skeleton">
    <div class="sk-close"></div>
    <div class="sk-title"></div>
    <div class="sk-title2"></div>
    <div class="sk-tab">
      <div class="sk-tab-le">
        <div class="sk-tab-itm"></div>
        <div class="sk-tab-itm"></div>
      </div>
      <div class="sk-tab-ri"></div>
    </div>
    <div class="sk-date-list">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div class="kuan"></div>
    </div>
    <div class="sk-hos sk-hos1">
      <div class="line-1">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-2">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-3"></div>
      <div class="sk-slice">
        <div class="sl-item"></div>
        <div class="sl-item"></div>
        <div class="sl-item"></div>
        <div class="sl-item"></div>
        <div class="sl-item"></div>
        <div class="sl-item"></div>
        <div class="sl-item"></div>
        <div class="sl-item"></div>
      </div>
    </div>
    <div class="sk-hos">
      <div class="line-1">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-2">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-3"></div>
    </div>
    <div class="sk-hos">
      <div class="line-1">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-2">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-3"></div>
    </div>
    <div class="sk-hos">
      <div class="line-1">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-2">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-3"></div>
    </div>
    <div class="sk-hos">
      <div class="line-1">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-2">
        <div class="le"></div>
        <div class="ri"></div>
      </div>
      <div class="line-3"></div>
    </div>
    <SkeletonLoading></SkeletonLoading>
  </div>
</template>

<script>
// 骨架图的loading动效
import SkeletonLoading from '@/components/skeletonLoading';
export default {
  components: { SkeletonLoading },
  mounted() {},
  data() {
    return {};
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.skeleton {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999;
  transform: translateZ(999px);
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  .sk-tab {
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
    padding-left: 30rpx;
    .sk-tab-le {
      display: flex;
      .sk-tab-itm {
        width: 84rpx;
        height: 42rpx;
        background-color: #fff;
        margin-right: 40rpx;
      }
    }
    .sk-tab-ri {
      width: 100rpx;
      height: 42rpx;
      margin-right: 30rpx;
      background-color: #fff;
    }
  }
  .sk-date-list {
    margin-top: 32rpx;
    padding: 40rpx 30rpx 0;
    width: 100%;
    height: 284rpx;
    display: flex;
    background-image: linear-gradient(
      180deg,
      #ffffff 38%,
      rgba(255, 255, 255, 0) 74%
    );
    div {
      width: 120rpx;
      height: 132rpx;
      background-color: #f8f8f8;
      margin-right: 16rpx;
    }
    .kuan {
      width: 142rpx;
      margin-right: 0;
    }
  }
  .sk-hos {
    margin: 0 30rpx;
    background-color: #fff;
    box-sizing: border-box;
    padding: 30rpx;
    margin-bottom: 22rpx;
    .sk-slice {
      margin-top: 30rpx;
      .sl-item {
        width: 146rpx;
        height: 76rpx;
        background: #f8f8f8;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-bottom: 10rpx;
        box-sizing: border-box;
        box-sizing: border-box;
        &:not(:nth-child(4n)) {
          margin-right: 16rpx;
        }
      }
    }
    .line-1,
    .line-2 {
      display: flex;
      justify-content: space-between;
    }
    .line-1 {
      .le {
        width: 350rpx;
        height: 40rpx;
        background-color: #f8f8f8;
      }
      .ri {
        width: 34rpx;
        height: 34rpx;
        background-color: #f8f8f8;
      }
    }
    .line-2 {
      margin-top: 10rpx;
      .le {
        width: 428rpx;
        height: 32rpx;
        background-color: #f8f8f8;
      }
      .ri {
        width: 66rpx;
        height: 32rpx;
        background-color: #f8f8f8;
      }
    }
    .line-3 {
      width: 152rpx;
      height: 32rpx;
      background-color: #f8f8f8;
      margin-top: 10rpx;
    }
  }
  .sk-hos1 {
    margin-top: -110rpx;
    padding-bottom: 14rpx;
  }
  .sk-close {
    position: absolute;
    right: 20rpx;
    top: 40rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: #fff;
  }
  .sk-title {
    width: 380rpx;
    height: 42rpx;
    background-color: #fff;
    margin-top: 38rpx;
    margin-left: 30rpx;
  }
  .sk-title2 {
    margin-left: 30rpx;
    width: 600rpx;
    height: 40rpx;
    margin-top: 20rpx;
    background-color: #fff;
  }
}
</style>
