<template>
  <div class="sort-tab">
    <div
      :class="{ blur: curVal === item.value }"
      v-for="(item, index) in list"
      @click="tabChange(item)"
      :key="index"
    >
      {{ item.name }}
    </div>
    <SettingPoup
      width="70"
      confirmBtnText="去设置"
      title="需要获取您的位置"
      :value="modalVisible"
      :showCancel="true"
      @confirm="onModalConfirm"
      @cancel="modalVisible = false"
    >
      <template #body>
        <div class="rule-wrap">
          请前往新氧青春小程序-设置-位置信息，开启地理位置授权
        </div>
      </template>
    </SettingPoup>
    <div v-if="toastText" class="def-toast">{{ toastText }}</div>
  </div>
</template>

<script>
import SettingPoup from '@/components/popup';
export default {
  props: {
    city_id: {
      type: Number
    },
    city_name: {
      type: String,
      default: ''
    }
  },
  components: { SettingPoup },
  data() {
    return {
      toastText: '',
      curVal: 0,
      itemData: {},
      modalVisible: false,
      notAllowedLoc: false,
      list: [
        {
          name: '推荐',
          value: 0
        },
        {
          name: '距离优先',
          value: 1
        },
        {
          name: '库存优先',
          value: 2
        }
      ]
    };
  },
  methods: {
    // 回到当前页面的校验
    async checkLocation() {
      try {
        // 没有开启弹窗定位时，不进行校验
        if (!this.modalVisible) {
          return;
        }
        // 获取授权，或定位城市成功
        const res = await this.$getCityId(); // 获取获取位置的状态
        this.modalVisible = false;
        if (+res.cityId === +this.city_id) {
          this.curVal = this.itemData.value;
          this.$emit('sorTabChange', this.itemData);
        } else {
          this.setToast('很抱歉，您当前不在该城市，无法筛选距离最近的机构');
        }
      } catch (error) {
        console.log(error);
      }
    },
    setToast(text, timeout = 2500) {
      clearTimeout(this.tstTimtout);
      this.toastText = text;
      this.tstTimtout = setTimeout(() => {
        this.toastText = '';
      }, timeout);
    },
    async tabChange(item) {
      this.itemData = item;
      if (item.value === 1) {
        // 获取定位城市
        try {
          // 获取授权，或定位城市成功
          this.curVal = item.value;
          this.$emit('sorTabChange', { loading: true });
          const res = await this.$getCityId(); // 获取获取位置的状态
          if (+res.cityId === +this.city_id) {
            this.$emit('sorTabChange', item);
          } else {
            // 不在一个城市时，显示推荐顺序
            this.$emit('sorTabChange', this.list[0]);
            this.setToast(
              `您当前定位的“${res.cityName}”与“${this.city_name}”距离较远，无法推荐距离最近的机构`
            );
          }
        } catch (error) {
          // 获取失败，没有开权限
          this.modalVisible = true;
        }
      } else {
        this.curVal = item.value;
        this.$emit('sorTabChange', item);
      }
      console.log(item);
    },
    onModalConfirm() {
      wx.openSetting();
    }
  }
};
</script>

<style lang="less" scoped>
.sort-tab {
  font-size: 26rpx;
  color: #777777;
  letter-spacing: 0;
  font-weight: 400;
  display: flex;
  div {
    margin-right: 40rpx;
  }
  .def-toast {
    position: fixed;
    z-index: 999999999;
    max-width: 50vw;
    top: 50%;
    left: 50%;
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    transform: translate3d(-50%, -50%, 999999px);
    padding: 30rpx;
    text-align: center;
    background: rgb(76, 76, 76);
    color: white;
    border-radius: 20rpx;
    white-space: wrap;
  }
  .blur {
    color: #333333;
    font-weight: 500;
  }
  .rule-wrap {
    font-size: 26rpx;
    font-weight: 400;
    overflow-y: auto;
    color: #222222;
    text-align: center;
  }
}
</style>
