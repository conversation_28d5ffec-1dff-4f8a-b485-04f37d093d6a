<template>
  <div
    class="order__hospital--item"
    :class="{
      'white-bg': !noBackground,
      'no-time-slice': noTimeSlice,
      'item-blur': blur,
      // 'bg-status-4': itemData.hospital_status_toc == 4,
      // 'bg-status-5': itemData.hospital_status_toc == 5,
      'disable-font': noTimeSlice
    }"
    @click="hosClick(itemData)"
  >
    <div class="cashback-status" v-if="itemData.cashback">
      {{ itemData.cashback }}
    </div>
    <div class="hospitalName">
      <span
        class="hos-title"
        :class="{
          'hos-title-l':
            !itemData.last_service &&
            !itemData.uid_verify_num &&
            !itemData.hospital_self,
          'hos-title-li':
            !itemData.hospital_self &&
            (itemData.last_service || itemData.uid_verify_num),
          'hos-title-i':
            itemData.hospital_self &&
            !itemData.last_service &&
            !itemData.uid_verify_num,
          'hos-title-lli':
            itemData.hospital_self &&
            (itemData.last_service || itemData.uid_verify_num)
        }"
        >{{ itemData.hospital_name }}</span
      >
      <Icon
        v-if="Number(itemData.hospital_tag_type) === 1"
        class="icon-ctn"
        :gray="noTimeSlice"
      ></Icon>
      <HqIcon
        v-else-if="Number(itemData.hospital_tag_type) === 2"
        class="icon-ctn"
        :gray="noTimeSlice"
      ></HqIcon>
      <span v-if="itemData.last_service" class="last-reserve">上次服务</span>
      <span
        v-if="!itemData.last_service && itemData.uid_verify_num"
        class="last-reserve last-reserve-num"
        >服务过{{ itemData.uid_verify_num }}次</span
      >
      <!-- <span v-if="most_reduce" class="most-reduce">返现最高</span> -->
    </div>
    <div class="address">
      <div class="addressName">
        {{ itemData.hospital_addr || '' }}
      </div>
    </div>
    <!-- 未开放和已约满状态下不显示剩余库存 -->
    <div
      v-if="
        lesNum &&
        itemData.hospital_status_toc != 4 &&
        itemData.hospital_status_toc != 5
      "
      class="add-top"
    >
      剩余可约库存：{{ lesNum }}
    </div>
    <!-- 立即抢占 -->
    <div
      class="add-c-btn"
      v-if="add_c_btn && (add_c_btn.url || add_c_btn.desc)"
    >
      <div class="desc" v-if="add_c_btn.desc">{{ add_c_btn.desc }}</div>
      <div
        class="title"
        v-if="add_c_btn.url"
        @click="onClickAddCBtn(add_c_btn)"
      >
        {{ add_c_btn.title }}
      </div>
    </div>
    <div @click.stop="toMap(itemData)" class="distance">
      <div class="dis-img" :class="{ 'dis-img-blur': blur }"></div>
      <div class="dis-txt">
        {{ itemData.distance_str || itemData.city_name || '' }}
      </div>
    </div>

    <div
      v-if="
        itemData.hospital_status_toc &&
        [4, 5].includes(itemData.hospital_status_toc)
      "
      class="hospital_status_toc_45"
    >
      {{ hospital_status_toc_map[itemData.hospital_status_toc].text }}
    </div>
  </div>
</template>

<script>
import Icon from '@/components/icon/Self.vue';
import HqIcon from '@/components/icon/hq.vue';
export default {
  components: { Icon, HqIcon },
  props: {
    city_id: {
      type: Number,
      default: 0
    },
    blur: {
      type: Boolean,
      default: false
    },
    noBackground: {
      type: Boolean,
      default: false
    },
    noTimeSlice: {
      type: Boolean,
      default: false
    },
    most_reduce: {
      type: Boolean,
      default: false
    },
    lesNum: {
      type: Number,
      default: 0
    },
    itemData: {
      type: Object,
      default: () => {}
    },
    add_c_btn: {
      type: Object,
      default: () => {}
    }
  },
  mounted() {},
  data() {
    return {
      hospital_status_toc_map: {
        4: {
          text: '未开放'
        },
        5: {
          text: '已约满'
        }
      }
    };
  },
  methods: {
    hosClick(data) {
      this.$emit('hosClick', data);
    },
    toMap(data) {
      console.log('toMap', data);
      uni.navigateTo({
        url: `/packageHospital/hospital-map?hospital_id=${data.hospital_id}`
      });
      this.$emit('reportClick', data.hospital_id);
    },
    onClickAddCBtn({ url }) {
      if (url.indexOf('http') === 0) {
        this.$toH5(url);
      } else {
        this.$bridge({
          url
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.bg-status-4 {
  background-image: url(https://static.soyoung.com/sy-pre/1qmonlkryg9ck-1690445400775.png);
}
.bg-status-5 {
  background-image: url(https://static.soyoung.com/sy-pre/f39gpqzqjnbp-1690445400775.png);
}
.bg-status-4,
.bg-status-5 {
  background-repeat: no-repeat;
  background-size: 84rpx 72rpx;
  background-position: right bottom;
}

.hospital_status_toc_45 {
  font-family: PingFangSC-Regular;
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 400;
  background: #bababa;
  display: flex;
  width: 68rpx;
  height: 28rpx;
  justify-content: center;
  align-items: center;
  margin-top: 6rpx;
}

.order__hospital--item {
  margin-bottom: 20rpx;
  position: relative;
  display: inline-flex;
  flex-wrap: wrap;
  width: calc(100%);
  padding: 30rpx;
  justify-content: space-between;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  box-sizing: border-box;
  .cashback-status {
    height: 28rpx;
    line-height: 28rpx;
    background: #89dc65;
    font-family: PingFangSC-regular;
    font-size: 18rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    padding: 0rpx 10rpx;
    position: absolute;
    left: 0rpx;
    top: 0rpx;
  }
  .distance {
    position: absolute;
    top: 84rpx;
    right: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 22rpx;
    line-height: 22rpx;
    color: #777;
    font-weight: 400;
    .dis-txt {
      height: 22rpx;
    }
    .dis-img {
      position: absolute;
      right: 0rpx;
      bottom: 38rpx;
      width: 40rpx;
      height: 40rpx;
      background-image: url('https://static.soyoung.com/sy-pre/<EMAIL>');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    .dis-img-blur {
      background-image: url('https://static.soyoung.com/sy-pre/<EMAIL>');
    }
  }
  .hospitalName {
    max-width: 470rpx;
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-bottom: 10rpx;
    color: #333333;
    font-weight: 500;
    display: flex;
    align-items: center;
    .icon-ctn {
      display: flex;
      margin-left: 10rpx;
    }
    .hos-title {
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 470rpx;
      white-space: nowrap;
    }
    .hos-title-l {
      max-width: 576rpx;
    }
    .hos-title-li {
      max-width: 470rpx;
    }
    .hos-title-i {
      max-width: 430rpx;
    }
    .hos-title-lli {
      max-width: 327rpx;
    }
    > img {
      width: 106rpx;
      height: 28rpx;
      margin-left: 24rpx;
    }
    .most-reduce {
      display: inline-block;
      width: 92rpx;
      height: 36rpx;
      background-color: rgb(254, 238, 234);
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: #f85d2d;
      font-weight: 500;
      margin-left: 10rpx;
    }
    .last-reserve {
      display: inline-block;
      white-space: nowrap;
      padding: 0 8rpx;
      height: 36rpx;
      background-color: #e2f8f1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: @text-color;
      font-weight: 500;
      margin-left: 10rpx;
      border-radius: 6rpx;
    }
    .last-reserve-num {
      border-radius: 4rpx;
    }
  }
  .address {
    font-family: PingFangSC-Regular;
    display: flex;
    width: 100%;
    color: #777777;
    font-size: 22rpx;
    font-weight: 400;
    .addressName {
      max-width: 542rpx;
      min-height: 22rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: PingFangSC-Regular;
      font-size: 22rpx;
      color: #8c8c8c;
      font-weight: 400;
    }
  }
  .add-c-btn {
    margin-top: 10rpx;
    display: flex;
    flex-direction: row;
    color: @border-color;
    font-family: PingFangSC-Medium;
    font-size: 22rpx;
    color: @text-color;
    letter-spacing: 0;
    line-height: 16px;
    font-weight: 500;
    .desc {
      margin-right: 10rpx;
    }
    .title {
      text-decoration: underline;
      color: #030303;
    }
  }
  .add-top {
    margin-top: 10rpx;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    letter-spacing: 0;
    font-weight: 400;
    color: #8c8c8c;
    font-weight: 400;
  }
}
.disable-font {
  .hospitalName {
    color: #777777;
  }

  .dis-img {
    background-image: url('https://static.soyoung.com/sy-pre/<EMAIL>') !important;
  }
  .desc,
  .dis-txt {
    color: #8c8c8c;
  }

  .title {
    color: #8c8c8c;
  }
}
.no-time-slice {
  margin-bottom: 0;
}
.item-blur {
  color: @text-color;
  .distance {
    color: @text-color;
  }
  .hospitalName {
    color: @text-color;
  }
  .address {
    color: @text-color;
  }
  .add-top {
    //color: @text-color;
  }
}
.white-bg {
  background-color: #fff;
}
</style>
