<template>
  <div class="consult-to-order" v-if="showBtn" @click="onClick">
    <!-- 联系客服预约 -->
    <img
      class="icon"
      src="https://static.soyoung.com/sy-design/qiyeweixin1727167736561.png"
    />
    <span>{{ add_c_btn.top_btn_title || '联系客服' }}</span>
  </div>
</template>
<script>
export default {
  props: {
    mode: {
      type: String,
      default: ''
    },
    add_c_btn: {
      type: Object,
      default: () => {}
    },
    hospitalListDisabled: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {
    showBtn() {
      let flag = false;
      if (this.add_c_btn && this.add_c_btn.url) {
        if (this.mode === 'time') {
          // 按时间预约模式
          const result = this.hospitalListDisabled.find((item) => {
            return item.add_c_btn && item.add_c_btn.url;
          });
          if (result) {
            flag = true;
          }
        } else {
          flag = true;
        }
      }
      return flag;
    }
  },
  methods: {
    onClick() {
      const url = this.add_c_btn.url;
      if (url.indexOf('http') === 0) {
        this.$toH5(url);
      } else {
        this.$bridge({
          url
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.consult-to-order {
  border: 3rpx solid @border-color;
  width: 252rpx;
  height: 38 * 2rpx;
  box-sizing: border-box;
  text-align: center;
  color: @border-color;
  background: #fff;
  margin: 0 auto;
  cursor: pointer;
  font-family: PingFangSC-Medium;
  font-size: 24rpx;
  color: @text-color;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .icon {
    display: inline-block;
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }
}
</style>
