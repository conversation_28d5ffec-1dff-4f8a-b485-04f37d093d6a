<template>
  <div class="time-modal">
    <div v-if="pinkerOptions.length" class="time-list">
      <div class="time-ls-ctn">
        <TimePinker
          @submit="selectTime"
          :noTime="true"
          :pid="pid"
          :order_id="order_id"
          :oldTimeFrom="oldData.from"
          :initDate="initDate"
          :options="pinkerOptions"
        ></TimePinker>
      </div>
      <div v-if="dateBtmTxtUrl.describe" class="date-btm-txt">
        <DateBtmTxt :textUrl="dateBtmTxtUrl"></DateBtmTxt>
      </div>
      <div class="sort-tab">
        <HosSortTabs
          ref="hosSortTab"
          :city_id="city_id"
          :city_name="city_name"
          @sorTabChange="sorTabChange"
        ></HosSortTabs>
      </div>
    </div>
    <!-- 下方滚动条位置 -->
    <scroll-view
      @scroll="hosScroll"
      @scrolltoupper="scrolltoupper"
      :scroll-x="false"
      :scroll-y="true"
      :scroll-top="scrollTop"
      :show-scrollbar="false"
      class="hos-list"
      :class="{ 'confirm-hos-list': !order_id }"
    >
      <div
        v-if="
          !loading &&
          !noMatchReserveCity &&
          hospitalList.length &&
          ((curDate.date.status != 0 && curDate.date.remain_num > 0) ||
            othersBlur)
        "
      >
        <div
          v-for="item in hospitalList"
          :key="item.hospital_id"
          class="hospital-ctn"
          :class="{
            'hospital-ctn-hos-card-ctn':
              curHospital.hospital_id == item.hos_detail.hospital_id
          }"
        >
          <div
            :class="{
              'hos-card-ctn':
                curHospital.hospital_id == item.hos_detail.hospital_id
            }"
          >
            <HospitalCard
              :blur="curHospital.hospital_id == item.hos_detail.hospital_id"
              :noBackground="true"
              :lesNum="contLesNum(item.time_details)"
              :itemData="item.hos_detail"
              @reportClick="reportClick"
            ></HospitalCard>
            <!-- @hosClick="hospitalChange" -->
          </div>
          <!-- v-if="curHospital.hospital_id == item.hos_detail.hospital_id" -->
          <div class="time-slice-ctn">
            <TileSlice
              :small1="true"
              :matchOldDate="matchOldDate"
              :hosDataFocus="curHospital"
              :hosIdOld="oldData.hospital_id"
              :hosData="item.hos_detail"
              :curDate="curDate.date"
              :oldTimeFrom="oldData.from"
              :oldTimeTo="oldData.to"
              :othersBlur="othersBlur"
              :value="curSliceTime"
              @sliceTime="sliceTime"
              :timeList="item.time_details"
            ></TileSlice>
          </div>
        </div>
      </div>
      <div v-else-if="loading" class="loading">加载中...</div>
      <div v-else :class="['empty-ctn', { 'consult-to-order-box': true }]">
        <Empty
          :reportData="{
            uid: userInfo.uid,
            pid: pid,
            order_id: order_id,
            month: oldData.month
          }"
          :remain="emptyRemain"
        ></Empty>
        <ConsultToOrder
          :add_c_btn="cur_city_data.add_c_btn"
          :mode="'time'"
          :hospitalListDisabled="hospitalListDisabled"
        ></ConsultToOrder>
      </div>
      <HospitalDisabled
        :hospitalListDisabled="hospitalListDisabled"
        v-if="hospitalListDisabled.length && !loading"
      >
      </HospitalDisabled>
      <div class="hos-list-btm">
        <!-- <img
          src="https://static.soyoung.com/sy-pre/20240606-175421-1717675800638.png"
        /> -->
      </div>
      <div class="hospital-ls-ctn"></div>
    </scroll-view>
  </div>
</template>

<script>
import {
  apiChainReservationReserveCalendarForTime,
  apiChainReservationReserveCalendarForTimeDetail
  // calendarForTime,
  // calendarForTimeDetail
} from '@/api/reservation.js';
import TimePinker from '@/components/appointNew/components/AppointmentTimerPicker';
import TileSlice from '@/components/appointNew/components/AppointmentTimerPicker/timeSlice.vue';
import ConsultToOrder from '@/components/appointNew/components/consult-to-order.vue';
import DateBtmTxt from '@/components/appointNew/components/dateBtmTxt.vue';
import HospitalDisabled from '@/components/appointNew/components/disableHospital.vue';
import Empty from '@/components/appointNew/components/empty.vue';
import HosSortTabs from '@/components/appointNew/components/hosSortTabs.vue';
import HospitalCard from '@/components/appointNew/components/hospitalListItem.vue';
import { mapState } from 'vuex';
export default {
  props: {
    pid: {
      type: Number,
      default: 0
    },
    request_time: {
      type: Number,
      default: 0
    },
    skeletonReady: {
      type: Boolean,
      default: false
    },
    othersBlur: {
      type: Boolean,
      default: false
    },
    // 确认订单页，用户点击的日期
    calendarParams: {
      type: Object,
      default: () => {}
    },
    // 确认订单页，用户已预约的日期
    reserveResult: {
      type: Object,
      default: () => {}
    },
    oldData: {
      type: Object,
      default: () => {}
    },
    baseInfo: {
      type: Object,
      default: () => {}
    },
    lastMulData: {
      type: Object,
      default: () => {}
    },
    dateBtmTxtUrl: {
      type: Object,
      default: () => {}
    },
    package_id: {
      type: Number,
      default: 0
    },
    order_id: {
      type: String,
      default: ''
    },
    city_id: {
      type: Number,
      default: 0
    },
    city_name: {
      type: String,
      default: ''
    },
    card_num: {
      type: Number,
      default: 0
    },
    cur_city_data: {
      type: Object,
      default: () => {}
    },
    sku_id: {
      type: Number
    },
    ingredients_id: {
      type: Number
    },
    order_app_id: {
      type: Number,
      default: 0
    },
    top_order_id: {
      type: String,
      default: ''
    },
    order_hospital_id: {
      type: Number,
      default: 0
    },
    noMatchReserveCity: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    emptyRemain() {
      if (this.noMatchReserveCity) {
        return '当前城市暂未开放可预约门店，请先切换城市';
      }
      return '当前日期暂无可预约时段，请切换日期预约';
    },
    matchOldDate() {
      return (
        Number(this.oldData?.month) === Number(this.curDate?.date?.month) &&
        Number(this.oldData?.year) === Number(this.curDate?.date?.year) &&
        Number(this.oldData?.day) === Number(this.curDate?.date?.day)
      );
    }
  },
  components: {
    HospitalCard,
    TimePinker,
    TileSlice,
    DateBtmTxt,
    Empty,
    HosSortTabs,
    HospitalDisabled,
    ConsultToOrder
  },
  created() {
    // 还原之前已预约数据
    this.setOldData();
    // 初始化日期数据
    this.initDateList('init');
  },
  data() {
    return {
      sort: 0,
      scrollTop: 0,
      initRdy: false, // 记录初始化的状态
      loading: false, // 加载中状态
      initDate: '', // 已选日期
      curDate: {}, // 选择的日期数据
      curSliceTime: {}, // 选择的时间切片数据
      curHospital: {}, // 选择的机构数据
      pinkerOptions: [],
      hospitalList: [],
      hospitalListDisabled: []
    };
  },
  watch: {
    // 城市改变，重新请求数据
    city_id() {
      this.clearStatus();
      // 初始化日期数据
      this.initDateList();
    }
  },
  methods: {
    checkLoc() {
      this.$refs?.hosSortTab?.checkLocation?.();
    },
    // 查看机构地址埋点
    reportClick(hospital_id) {
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:institution_address_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 1,
          city_id: this.city_id,
          hospital_id
        }
      });
    },
    clearStatus() {
      this.initDate = '';
      this.curDate = {};
      this.curSliceTime = {};
      this.curHospital = {};
      this.pinkerOptions = [];
    },
    contLesNum(list = []) {
      let num = 0;
      list?.forEach((item) => {
        num += item.remain_num;
      });
      return num;
    },
    scrolltoupper() {
      this.$emit('setTTop', false);
    },
    hosScroll(e) {
      const top = e?.detail?.scrollTop;
      // 再往下走
      if (top - this.lastTop > 0) {
        if (top > 5) {
          this.$emit('setTTop', true);
        }
      }
      this.lastTop = top;
    },
    setOldData() {
      // 有之前的预约数据
      if (
        this.oldData.month &&
        (+this.oldData.reserve_mode === 1 ||
          +this.oldData.reserve_mode === 0 ||
          +this.oldData.reserve_mode === 2)
      ) {
        this.initDate =
          this.oldData.year + '-' + this.oldData.month + '-' + this.oldData.day;
      }
      // 确认订单页点击的数据还原
      if (this.calendarParams?.date?.month) {
        const pDate = this.calendarParams?.date;
        this.initDate = pDate.year + '-' + pDate.month + '-' + pDate.day;
      }
      // 确认订单页选的数据还原
      if (+this.reserveResult?.commitData?.reserve_mode === 1) {
        this.curDate = this.reserveResult?.date;
        this.curSliceTime = this.reserveResult?.time;
        this.curHospital = this.reserveResult?.hospital;
        this.initDate = this.reserveResult?.date?.first_un?.slice(0, 10);
      }
    },
    // 推算次卡锚定日期
    setIntervalAppointment(dateList = []) {
      const interval = +this.baseInfo.interval_day_num || 0; // 最小间隔
      const lastReserved = this.lastMulData; // 上次的时间
      let currentIndex; // 上次预约的索引
      for (let i = 0; i < dateList.length; i++) {
        const item = dateList[i];
        if (
          +item.year === +lastReserved.year &&
          +item.month === +lastReserved.month &&
          +item.day === +lastReserved.day
        ) {
          currentIndex = i;
        }
        if (
          i > currentIndex + interval &&
          +item.status !== 0 &&
          +item.remain_num > 0
        ) {
          // 在间隔日期之后
          this.initDate = `${item.year}-${item.month}-${item.day}`;
          break;
        }
      }
    },
    // 选中机构改变
    hospitalChange(item) {
      this.initRdy && (this.curSliceTime = {});
      this.curHospital = item;
      // this.submitData();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_hospital_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 1,
          hospital_id: item.hospital_id
        }
      });
    },
    // 触发选择日期
    selectTime(data) {
      this.curDate = data;
      this.initRdy && (this.curSliceTime = {});
      this.initHospitalDetail();
    },
    // 选择时间切片
    sliceTime(info) {
      const data = info.time;
      const item = info.hosData;
      this.hospitalChange(item);
      this.curSliceTime = data;
      this.submitData();
    },
    // 提交数据
    submitData() {
      console.log(this.curDate.date);
      this.$emit('setData', {
        date: this.curDate.date,
        hospital: this.curHospital,
        time: this.curSliceTime
      });
    },
    sorTabChange(data) {
      if (data.loading) {
        this.setLoading(true);
        return;
      }
      this.sort = data.value;
      this.initHospitalDetail();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:sort_tab_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 1,
          tab_type: data.value + 1
        }
      });
    },
    // 去请求机构列表，和机构对应的时间切片
    async initHospitalDetail() {
      this.setLoading(true);
      const obj = {
        sort: this.sort,
        order_id: this.order_id,
        package_id: this.package_id,
        card_num: this.card_num,
        hos_list: this.curDate.date?.hos_info?.join?.(',') || '',
        pid: this.pid,
        city_id: this.city_id,
        date_str: this.curDate?.date?.first_un?.slice?.(0, 10) || '',
        time_status: this.curDate?.date?.status
      };
      if (this.request_time) {
        obj.request_times = this.request_time;
      }
      // const res = await calendarForTimeDetail(obj);
      console.log(12345);
      let res = null;

      // todo 需要替换接口
      const calendarForTimeDetailResponseData =
        await apiChainReservationReserveCalendarForTimeDetail({
          ingredients_id: this.ingredients_id,
          hospital_id: this.curDate.date?.hospital_id_list?.join?.(',') || '',
          // uid: this.userInfo.uid,
          sku_id: this.sku_id,
          city_id: this.city_id,
          date_str: this.curDate?.date?.first_un?.slice?.(0, 10) || '',
          order_id: this.order_id,
          reserve_id: this.oldData?.reserve_id,
          total_amount: 0
        });

      if (
        calendarForTimeDetailResponseData &&
        Array.isArray(calendarForTimeDetailResponseData.list)
      ) {
        res = calendarForTimeDetailResponseData.list;
      }

      this.$emit('update:skeletonReady', true);
      this.setLoading(false);
      this.initRdy = true;
      if (res) {
        const thisHospitalList = [];
        const btmHosList = [];

        // 原逻辑不动
        (res || []).forEach((item) => {
          if (item.top_yn !== 1) {
            if (item.hos_detail.hospital_status_toc === 5) {
              btmHosList.unshift(item);
            } else if (item.hos_detail.hospital_status_toc === 4) {
              btmHosList.push(item);
            } else {
              thisHospitalList.push(item);
            }
          }
        });

        // 需要将res中top_yn = 1的数据筛选出来
        const topYn1 = res.filter((item) => {
          return item.top_yn === 1;
        });
        if (topYn1.length > 0) {
          topYn1.forEach((item) => {
            btmHosList.unshift(item);
            if (
              item.hos_detail.hospital_status_toc !== 5 &&
              item.hos_detail.hospital_status_toc !== 4
            ) {
              thisHospitalList.push(item);
            }
          });
        }

        console.log(thisHospitalList, '获取到新的list数据');
        this.hospitalList = thisHospitalList;
        this.hospitalListDisabled = btmHosList;
        // 之前选了机构
        if (this.hospitalList.length) {
          this.spliceHolpital(this.hospitalList);
          this.curHospital = this.hospitalList?.[0]?.hos_detail;
          console.log('inithospital', this.curDate.init, this.oldData);
          if (this.curDate.init && +this.oldData.city_id === +this.city_id) {
            this.setOldTimeSlice(this.hospitalList[0]);
          }
        } else {
          /**
           *   date: this.curDate.date,
           *   hospital: this.curHospital,
           *   time: this.curSliceTime
           */
          this.curHospital = {};
          this.curSliceTime = {};
        }
        this.submitData();
        this.scrollTop = 1;
        this.$nextTick(() => {
          this.scrollTop = 0;
        });
      }
    },
    setOldTimeSlice(curHospitalData) {
      if (this.oldData.from && curHospitalData?.time_details) {
        const arr = curHospitalData.time_details.filter((item) => {
          return item.from === this.oldData.from;
        });
        this.curSliceTime = arr[0];
      }
    },
    spliceHolpital(hosList) {
      if (
        (this.oldData.hospital_id && +this.oldData.reserve_mode === 1) ||
        this.lastMulData.hospital_id ||
        this.reserveResult?.hospital?.hospital_id
      ) {
        let index = 0;
        hosList.forEach((item, idx) => {
          if (
            +item?.hos_detail?.hospital_id === +this.oldData.hospital_id ||
            +item?.hos_detail?.hospital_id === +this.lastMulData.hospital_id ||
            +item?.hos_detail?.hospital_id ===
              +this.reserveResult?.hospital?.hospital_id
          ) {
            index = idx;
          }
        });
        const newArr = hosList.splice(index, 1);
        hosList.unshift(newArr[0]);
      }
    },
    setLoading(ele) {
      this.loading = ele;
      ele && (this.hospitalList = []);
    },
    // 获取日期列表
    async initDateList(init) {
      if (!this.city_id) return;
      this.setLoading(true);
      const obj = {
        order_id: this.order_id,
        package_id: this.package_id,
        card_num: this.card_num,
        pid: this.pid,
        city_id: this.city_id
      };
      if (this.request_time) {
        obj.request_times = this.request_time;
      }
      // todo 需要替换接口
      // const res = await calendarForTime(obj);
      const res = await apiChainReservationReserveCalendarForTime({
        ingredients_id: this.ingredients_id,
        sku_id: this.sku_id,
        top_order_id: this.top_order_id,
        // ingredients_type: 1,
        // market_product_id: 1,
        city_id: this.city_id,
        order_id: this.order_id,
        reserve_id: this.oldData?.reserve_id, // 改约时
        // uid: this.userInfo.uid,
        order_hospital_id: this.order_hospital_id,
        order_app_id: this.order_app_id,
        total_amount: 0
      });

      if (res) {
        init && this.setIntervalAppointment(res?.list || []);
        this.pinkerOptions = res?.list || [];
        if (!this.pinkerOptions?.length) {
          this.$emit('update:skeletonReady', true);
          this.setLoading(false);
        }
      } else {
        this.$emit('update:skeletonReady', true);
        this.setLoading(false);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.time-modal {
  background: #f8f8f8;
  .loading {
    text-align: center;
    line-height: 400rpx;
    height: 400rpx;
    // border-radius: 20rpx;
    // background-color: #fff;
    color: #aaabb3;
  }
  .sort-tab {
    padding: 30rpx 0 0rpx 30rpx;
  }
  .time-list {
    width: 100%;
    // height: 188rpx;
    box-sizing: border-box;
    padding-top: 40rpx;
    background-image: linear-gradient(
      180deg,
      #ffffff 38%,
      rgba(255, 255, 255, 0) 74%
    );
    border-radius: 10px 10px 0 0;
    // .time-ls-ctn {
    // margin-left: 30rpx;
    // }
    .date-btm-txt {
      margin: 0 30rpx;
      margin-bottom: -20rpx;
    }
  }
  .hos-list {
    padding: 20rpx 30rpx 0;
    box-sizing: border-box;
    height: calc(100vh - 558rpx);
    .hospital-ls-ctn {
      height: 200rpx;
    }
    // overflow-y: auto;
    .hospital-ctn {
      background-color: #ffffff;
      //border-radius: 20rpx;
      background: #ffffff;
      box-sizing: border-box;
      margin-bottom: 20rpx;
      border: 1px solid #fff;
      .time-slice-ctn {
        padding: 0 30rpx 20rpx;
        margin-top: -20rpx;
      }
    }
    .hospital-ctn-hos-card-ctn {
      border: 1px solid #030303;
    }
    .empty-ctn {
      padding-top: 50rpx;
      background-color: white;
    }
    .consult-to-order-box {
      padding-bottom: 50rpx;
    }
    .hos-list-btm {
      height: 350rpx;
      padding-top: 30rpx;
      img {
        display: block;
        margin: 0 auto;
        width: 234rpx;
        height: 132rpx;
      }
    }
  }
  .confirm-hos-list {
    height: calc(100vh - 428rpx);
  }
}
</style>
