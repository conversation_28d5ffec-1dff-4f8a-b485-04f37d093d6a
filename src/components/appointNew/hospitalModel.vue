<template>
  <div class="hospital-modal">
    <div v-show="!visible" class="hospital-list">
      <div class="hos-num">
        <span
          ><HosSortTabs
            ref="hosSortTab"
            :city_id="city_id"
            :city_name="city_name"
            @sorTabChange="sorTabChange"
          ></HosSortTabs
        ></span>
        <span class="hos-num-txt">(共{{ hospitalList.length || 0 }}家)</span>
      </div>
    </div>
    <scroll-view
      @scroll="hosScroll"
      @scrolltoupper="scrolltoupper"
      :scroll-x="false"
      :scroll-y="true"
      :show-scrollbar="false"
      v-if="!visible"
      class="hos-list"
    >
      <div v-if="hospitalList.length" class="hospital-ctn">
        <HospitalCard
          @reportClick="reportClick"
          v-for="item in hospitalList"
          :key="item.hospital_id"
          :itemData="item"
          @hosClick="hospitalClick(item)"
        ></HospitalCard>
      </div>
      <div v-else-if="loading" class="loading">加载中...</div>
      <div v-else :class="['empty-ctn', { 'consult-to-order-box': showBtn }]">
        <Empty
          :reportData="{
            uid: userInfo.uid,
            sku_id: sku_id,
            order_id: order_id,
            month: oldData.month
          }"
          :remain="emptyRemain"
        ></Empty>
        <div
          class="consult-to-order"
          v-if="showBtn"
          @click="onClickAppointment"
        >
          <!-- 联系客服预约 -->
          <img
            class="icon"
            src="https://static.soyoung.com/sy-design/qiyeweixin1727167736561.png"
          />
          <span>{{ cur_city_data.add_c_btn.top_btn_title || '联系客服' }}</span>
        </div>
      </div>
      <HospitalDisabled
        :hospitalListDisabled="hospitalListDisabled"
        v-if="hospitalListDisabled.length && !loading"
      >
      </HospitalDisabled>
      <div class="hos-list-btm">
        <!-- <img
          src="https://static.soyoung.com/sy-pre/20240606-175421-1717675800638.png"
        /> -->
      </div>
    </scroll-view>
    <div class="modal-mask" v-if="visible">
      <div class="pannel" @click.stop>
        <HospitalModelDetail
          :oldData="oldData"
          :baseInfo="baseInfo"
          :request_time="request_time"
          :lastMulData="lastMulData"
          :othersBlur="othersBlur"
          :reserveResult="reserveResult"
          :dateBtmTxtUrl="dateBtmTxtUrl"
          :order_id="order_id"
          :hospital_id="hospital_id"
          :city_id="city_id"
          :sku_id="sku_id"
          :top_order_id="top_order_id"
          :ingredients_id="ingredients_id"
          :order_app_id="order_app_id"
          :order_hospital_id="order_hospital_id"
          :clickHospitalId="curHospital.hospital_id"
          :hospital_list="hospitalList"
          @calendarVisible="(val) => $emit('calendarVisible', val)"
          @setTTop="(el) => $emit('setTTop', el)"
          @setSkelReady="setSkelReady"
          @change="selDataChange"
          @close="diaClose"
        ></HospitalModelDetail>
      </div>
    </div>
  </div>
</template>

<script>
import { apiChainReservationHospitalList } from '@/api/reservation.js';
import HospitalDisabled from '@/components/appointNew/components/disableHospital.vue';
import Empty from '@/components/appointNew/components/empty.vue';
import HosSortTabs from '@/components/appointNew/components/hosSortTabs.vue';
import HospitalCard from '@/components/appointNew/components/hospitalListItem.vue';
import HospitalModelDetail from '@/components/appointNew/components/hospitalModelIndex.vue';
import { mapState } from 'vuex';
export default {
  props: {
    request_time: {
      type: Number,
      default: 0
    },
    skeletonReady: {
      type: Boolean,
      default: false
    },
    othersBlur: {
      type: Boolean,
      default: false
    },
    // 确认订单页，用户已预约的日期
    reserveResult: {
      type: Object,
      default: () => {}
    },
    oldData: {
      type: Object,
      default: () => {}
    },
    fnlDefaultMode: {
      // 默认的预约模式
      type: Object,
      default: () => {}
    },
    baseInfo: {
      type: Object,
      default: () => {}
    },
    lastMulData: {
      type: Object,
      default: () => {}
    },
    dateBtmTxtUrl: {
      type: Object,
      default: () => {}
    },
    order_id: {
      type: String,
      default: ''
    },
    city_id: {
      type: Number,
      default: 0
    },
    city_name: {
      type: String,
      default: ''
    },
    cur_city_data: {
      type: Object,
      default: () => {}
    },
    sku_id: {
      type: Number
    },
    ingredients_id: {
      type: Number
    },
    order_app_id: {
      type: Number,
      default: 0
    },
    order_hospital_id: {
      type: Number,
      default: 0
    },
    top_order_id: {
      type: String,
      default: ''
    }
  },
  components: {
    HospitalCard,
    Empty,
    HospitalModelDetail,
    HosSortTabs,
    HospitalDisabled
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  watch: {
    // 城市改变，重新请求数据
    city_id() {
      this.initHospitalList();
      // 初始化日期数据
    }
  },
  created() {
    this.initHospitalList('init');
  },
  data() {
    return {
      showBtn: false,
      sort: 0,
      loading: false,
      visible: true, // 按机构预约弹窗
      emptyRemain: '当前日期暂无可预约时段，请切换日期预约',
      curHospital: {}, // 当前点击的机构
      hospitalList: [],
      hospitalListDisabled: []
    };
  },
  methods: {
    checkLoc() {
      this.$refs?.hosSortTab?.checkLocation?.();
    },
    scrolltoupper() {
      this.$emit('setTTop', false);
    },
    hosScroll(e) {
      const top = e?.detail?.scrollTop;
      // 再往下走
      if (top - this.lastTop > 0) {
        if (top > 5) {
          this.$emit('setTTop', true);
        }
      }
      this.lastTop = top;
    },
    // 查看机构地址埋点
    reportClick(hospital_id) {
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:institution_address_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 2,
          city_id: this.city_id,
          hospital_id
        }
      });
    },
    diaClose() {
      this.visible = false;
      this.$reportData({
        info: 'y_wxtuan_or_subscribe_pop:switch_views_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 2
        }
      });
    },
    // 按照距离优先、库存优先排序
    sorTabChange(data) {
      if (data.loading) {
        this.loading = true;
        this.hospitalList = [];
        return;
      }
      this.sort = data.value;
      this.initHospitalList();
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:sort_tab_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 2,
          tab_type: data.value + 1
        }
      });
    },
    async initHospitalList() {
      this.loading = true;
      this.hospitalList = [];

      const obj = {
        sku_id: this.sku_id,
        sort: this.sort,
        ingredients_id: this.ingredients_id,
        select_city_id: this.city_id,
        city_id: this.city_id,
        order_hospital_id: this.order_hospital_id,
        order_app_id: this.order_app_id,
        top_order_id: this.top_order_id,
        order_id: this.order_id,
        total_amount: 0
      };

      const res = await apiChainReservationHospitalList(obj);

      this.loading = false;
      if (res) {
        // const hosList = orderHos.responseData.hospital_list
        // const hosList = res?.list;
        // this.hospitalList = hosList || [];
        const thisHospitalList = [];
        const btmHosList = [];

        // 原逻辑不动
        (res?.list || []).forEach((item) => {
          if (item.top_yn !== 1) {
            if (item.hospital_status_toc === 5) {
              btmHosList.push(item);
            } else if (item.hospital_status_toc === 4) {
              btmHosList.push(item);
            } else {
              thisHospitalList.push(item);
            }
          }
        });

        // 需要将res中top_yn = 1的数据筛选出来
        const topYn1 = res?.list.filter((item) => {
          return item.top_yn === 1;
        });
        if (topYn1.length > 0) {
          topYn1.forEach((item) => {
            btmHosList.unshift(item);
            if (
              item.hospital_status_toc !== 5 &&
              item.hospital_status_toc !== 4
            ) {
              thisHospitalList.push(item);
            }
          });
        }

        this.hospitalList = thisHospitalList;
        this.hospitalListDisabled = btmHosList;
        this.spliceHolpital(this.hospitalList);

        // 是否展示预约按钮
        this.showBtn = false;
        console.log(this.cur_city_data, 'cur_city_data');
        if (this.cur_city_data.add_c_btn && this.cur_city_data.add_c_btn.url) {
          const result = this.hospitalListDisabled.find((item) => {
            return item.add_c_btn && item.add_c_btn.url;
          });
          if (result) {
            this.showBtn = true;
          }
        }

        // 只有初始化时，才打开详情页
        if (
          (this.oldData.hospital_id || // 后置预约
            (+this.fnlDefaultMode?.value === 2 && !this.order_id) || // 确认订单页初始化默认锚定到此模式时
            this.lastMulData.hospital_id) && // 后置预约一单多约
          this.hospitalList?.length &&
          !this.skeletonReady
        ) {
          this.visible = true;
        } else {
          this.setSkelReady();
        }
      } else {
        this.setSkelReady();
      }
    },
    onClickAppointment() {
      const url = this.cur_city_data.add_c_btn.url;
      if (url.indexOf('http') === 0) {
        this.$toH5(url);
      } else {
        this.$bridge({
          url
        });
      }
    },
    setSkelReady() {
      this.$emit('update:skeletonReady', true);
    },
    spliceHolpital(hosList = []) {
      if (!hosList?.length) return;
      const id =
        this.oldData.hospital_id ||
        this.reserveResult?.hospital?.hospital_id ||
        this.lastMulData.hospital_id;
      if (id) {
        let index = 0;
        hosList.forEach((item, idx) => {
          if (+item.hospital_id === +id) {
            index = idx;
          }
        });
        if (index === 0) return;
        const newArr = hosList.splice(index, 1);
        hosList.unshift(newArr[0]);
      }
    },
    // 组件内的数据改变
    selDataChange(data) {
      // 数据改变，向上触发数据改变
      this.$emit('setData', data);
    },
    // 点击某个机构
    hospitalClick(data) {
      this.curHospital = data;
      this.visible = true;
      this.$reportData({
        info: 'sy_wxtuan_or_subscribe_pop:switch_hospital_click',
        ext: {
          uid: this.userInfo.uid,
          product_id: this.sku_id,
          order_id: this.order_id,
          status: this.oldData.month ? 2 : 1,
          type: 2,
          city_id: this.city_id,
          hospital_id: data.hospital_id
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.hospital-modal {
  background: #f8f8f8;
  position: relative;
  .hospital-list {
    width: 100%;
    height: 140rpx;
    background-image: linear-gradient(
      180deg,
      #ffffff 38%,
      rgba(255, 255, 255, 0) 74%
    );
    border-radius: 10px 10px 0 0;
  }
  .hos-num {
    padding: 30rpx 30rpx 0 30rpx;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #222222;
    font-weight: 400;
    display: flex;
    justify-content: space-between;
    .hos-num-txt {
      font-size: 24rpx;
      color: #777;
    }
  }
  .hos-list {
    padding: 0rpx 30rpx 0;
    height: calc(85vh - 250rpx);
    margin-top: -50rpx;
    overflow-y: auto;
    box-sizing: border-box;
    .hos-list-btm {
      height: 350rpx;
      padding-top: 30rpx;
      img {
        display: block;
        margin: 0 auto;
        width: 234rpx;
        height: 132rpx;
      }
    }
    .consult-to-order-box {
      padding-bottom: 50rpx;
    }
    .empty-ctn {
      height: 345rpx;
      padding-top: 50rpx;
      background-color: white;
      .consult-to-order {
        border: 1px solid @border-color;
        width: 252rpx;
        height: 38 * 2rpx;
        box-sizing: border-box;
        text-align: center;
        color: @border-color;
        background: #fff;
        margin: 0 auto;
        cursor: pointer;
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        color: @text-color;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        .icon {
          display: inline-block;
          width: 40rpx;
          height: 40rpx;
          margin-right: 10rpx;
        }
      }
    }
    .loading {
      text-align: center;
      line-height: 400rpx;
      height: 400rpx;
      // border-radius: 20rpx;
      // background-color: #fff;
      color: #aaabb3;
    }
  }
}
</style>
<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: absolute;
  top: 0rpx;
  left: 0;
  width: 100vw;
  overflow: hidden;
  z-index: 1;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(1px);
}

.pannel {
  width: 100%;
  border-radius: 30rpx 30rpx 0 0;
  background-color: #f8f8f8;
  animation: fly-bottom-in 0.2s linear forwards;
}
@keyframes fly-bottom-in {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}
</style>
