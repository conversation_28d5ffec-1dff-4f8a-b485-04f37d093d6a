<template>
  <div class="password-to-special-wrap" v-if="is_show_special_entry_modal">
    <div class="modal-box">
      <div class="title">请输入神秘口令</div>
      <div class="body">
        <input
          v-model="special_password"
          class="special-password"
          :class="{ 'active': special_password.length }"
          placeholder="请输入口令"
        />
        <div class="error-tip" v-show="is_show_password_error">
          {{ password_error || '口令错误，请输入正确口令' }}
        </div>
      </div>
      <div class="footer">
        <div class="btn" @click="cancelSpecialEntry">取消</div>
        <div class="btn active" @click="submitSpecialEntry">马上进入</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    is_show_special_entry_modal: {
      type: Boolean,
      default: false
    },
    material_id: {
      type: String,
      default: ''
    },
    material_type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      special_password: '', // 神秘口令
      password_error: '', // 错误提示
      is_show_password_error: false // 显示口令错误提示
    };
  },
  methods: {
    async submitSpecialEntry() {
      console.log(this.special_password);
      if (this.special_password === '') {
        uni.showToast({
          title: '请先输入口令',
          icon: 'none'
        });
        return;
      }
      const { errorCode, errorMsg, responseData } = await this.$request({
        url: '/groupBuy/User/GetH5JumpUrl',
        data: {
          code: this.special_password
        }
      })
        .then((res) => res.data)
        .catch((err) => ({
          errorCode: -100,
          errorMsg: err
        }));
      if (+errorCode === 200 || +errorCode === 0) {
        if (this.material_id && this.material_type) {
          // 如果携带了商品ID和商品类型，则认为页面来源于商详页的分享，输入口令后进入商详页
          this.$bridge({
            url: `/pages/product?material_id=${this.material_id}&material_type=${this.material_type}&share_page=home`
          });
        } else {
          if (responseData.jump_url.indexOf('.com') > -1) {
            const url =
              '/pages/h5?share_page=home&url=' +
              encodeURIComponent(responseData.jump_url);
            this.$bridge({
              url: url
            });
          } else {
            const jump_url =
              responseData.jump_url +
              (responseData.jump_url.indexOf('?') > -1
                ? '&share_page=home'
                : '?share_page=home');
            this.$bridge({
              url: jump_url
            });
          }
        }
        this.cancelSpecialEntry();
      } else {
        this.password_error = errorMsg;
        this.is_show_password_error = true;
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    cancelSpecialEntry() {
      this.special_password = '';
      this.password_error = '';
      this.is_show_password_error = false;
      this.$emit('closeModal');
    }
  }
};
</script>
<style lang="less" scoped>
.password-to-special-wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999999;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  .modal-box {
    width: 300px;
    background: #ffffff;
    border-radius: 10px;
    .title {
      width: 300px;
      height: 52px;
      line-height: 52px;
      background-image: linear-gradient(180deg, #dcd9ec 0%, #ffffff 100%);
      border-radius: 10px 10px 0 0;
      text-align: center;
    }
    .body {
      margin-bottom: 20px;
      padding: 0 15px;
      .special-password {
        width: 270px;
        height: 42px;
        line-height: 42px;
        padding: 0 15px;
        box-sizing: border-box;
        background: #f6f5fb;
        border: 1px solid rgba(219, 217, 229, 1);
        border-radius: 4px;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
        &.active {
          font-family: PingFangSC-Semibold;
          font-size: 18px;
          color: #222222;
          letter-spacing: 0;
          line-height: 24px;
          font-weight: 600;
        }
      }
      .error-tip {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #f85d2d;
        letter-spacing: 0;
        font-weight: 400;
        margin-top: 5px;
      }
    }
    .footer {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      .btn {
        width: 104px;
        height: 34px;
        line-height: 34px;
        border: 1px solid rgba(222, 222, 222, 1);
        border-radius: 17px;
        text-align: center;
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #222222;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        &.active {
          background: @border-color;
          font-family: PingFangSC-Medium;
          font-size: 13px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          margin-left: 18px;
        }
      }
    }
  }
}
</style>
