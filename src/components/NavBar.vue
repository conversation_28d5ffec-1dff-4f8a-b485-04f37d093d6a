<template>
  <div class="navbar-box" :style="{ background, height: navBottom + 2 + 'px' }">
    <div
      class="navbar"
      :style="{
        top: navTop + 'px',
        height: navHeight + 'px'
      }"
    >
      <div
        class="capsule"
        v-if="showBack || showHome"
        :style="{
          height: navHeight + 'px',
          width: (showBack && showHome ? navWidth : navWidth / 2) + 'px',
          left: navRight + 'px',
          'border-radius': navHeight / 2 + 'px'
        }"
      >
        <div class="back" v-if="showBack" @click="goBack">
          <i class="icon-left-arrow"></i>
        </div>
        <div class="line" v-if="showBack && showHome"></div>
        <div class="home-icon-wr" v-if="showHome" @click="goHome">
          <i class="homeicon"></i>
        </div>
      </div>
      <slot></slot>
      <span class="title">{{ title || '' }}</span>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 背景色，默认透明
    background: {
      type: String,
      default: 'transparent'
    },
    // 是否显示返回，默认显示
    hasBack: {
      type: Boolean,
      default: true
    },
    // 是否显示回首页，默认不显示
    // （设置显示后只有当前页面是一级页面时才会显示，如分享后打开的页面）
    hasHome: {
      type: Boolean,
      default: false
    },
    // 是否一直显示回首页,默认不显示
    // （无论当前页面是不是一级页面都显示）
    hasFixedHome: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      navTop: 0,
      navHeight: 0,
      navBottom: 0,
      navWidth: 0,
      navRight: 0,
      isFirst: false
    };
  },
  computed: {
    ...mapState({
      systemInfo: (state) => state.global.systemInfo
    }),
    showBack() {
      return this.hasBack && !this.isFirst;
    },
    showHome() {
      return this.hasFixedHome || (this.hasHome && this.isFirst);
    }
  },
  created() {
    // 获取菜单按钮（右上角胶囊按钮）的布局位置信息，坐标信息以屏幕左上角为原点。
    const { top, height, width, bottom, right } =
      uni.getMenuButtonBoundingClientRect();
    this.navTop = top;
    this.navHeight = height;
    this.navBottom = bottom;
    this.navWidth = width;
    this.navRight = this.systemInfo.windowWidth - right;
    this.isFirst = getCurrentPages().length === 1;
  },
  mounted() {
    const that = this;
    const query = uni.createSelectorQuery().in(this);
    query
      .selectAll('.navbar-box')
      .boundingClientRect((rects) => {
        that.$emit('navbarMounted', rects[0]);
      })
      .exec();
  },
  methods: {
    goBack() {
      if (
        this.$scope.dataset?.eventOpts?.findIndex(
          ([name]) => name === '^onBack'
        ) > -1
      ) {
        this.$emit('onBack', (params) => {
          uni.navigateBack(params);
        });
      } else {
        uni.navigateBack();
      }
    },
    goHome() {
      uni.switchTab({
        url: '/pages/index'
      });
    }
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.navbar-box {
  position: fixed;
  top: 0;
  width: 100%;
  box-sizing: border-box;
  z-index: 10000;
}
.navbar {
  position: absolute;
  .flex-center;
  width: 100%;
}

.capsule {
  .flex-center;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  background: rgba(255, 255, 255, 0.6);
  border: 0.5px solid rgba(151, 151, 151, 0.2);
}

.line {
  position: absolute;
  height: 37rpx;
  width: 1px;
  top: 50%;
  margin-top: -37/2rpx;
  left: 50%;
  margin-left: -1px;
  border-right: 0.5px solid rgba(0, 0, 0, 0.2);
}

.back {
  .flex-center;
  flex: 1;
  width: 50%;
  .icon-left-arrow {
    width: 22rpx;
    height: 36rpx;
    background: url('https://static.soyoung.com/sy-pre/back-1661847000665.png')
      no-repeat center center transparent;
    background-size: 100%;
  }
}
.home-icon-wr {
  .flex-center;
  flex: 1;
  width: 50%;
  .homeicon {
    width: 40rpx;
    height: 40rpx;
    background: url(https://static.soyoung.com/sy-pre/home-1661847000665.png)
      no-repeat center center transparent;
    background-size: 100%;
  }
}
.title {
  display: inline-block;
  font-size: 32rpx;
  font-weight: bold;
}
// &.isios {
//   .navbar {
//     text-align: center;
//     .title {
//       margin-left: 0rpx;
//       font-weight: bold;
//     }
//   }
// }
</style>
