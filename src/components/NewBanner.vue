<template>
  <div class="swiper-box">
    <div class="swiper-overflow" :style="{ height, width }">
      <image
        v-if="list.length === 1"
        :src="list[0].img_url"
        class="cover"
        :style="{ width }"
        mode="widthFix"
        show-menu-by-longpress="true"
        @click="handleClick(list[0], 0)"
      />
      <swiper
        v-else
        class="swiper"
        :style="{ height, width }"
        circular
        :interval="3000"
        :autoplay="true"
        @change="onchange"
      >
        <swiper-item
          v-for="(item, index) in list"
          :item-id="index"
          :key="index"
          @click="handleClick(item, index)"
        >
          <image
            :src="item.img_url"
            class="cover"
            :style="{ width }"
            mode="widthFix"
            show-menu-by-longpress="true"
          />
        </swiper-item>
      </swiper>
      <div class="banner-indicator" v-if="list.length > 1">
        <div
          v-for="(poster, index) in list"
          :key="index"
          :class="{
            indicator: true,
            active: activeIndex === index
          }"
        ></div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: () => '160rpx'
    },
    width: {
      type: String,
      default: () => '690rpx'
    }
  },
  data() {
    return {
      activeIndex: 0
    };
  },
  mounted() {
    this.$emit(
      'expose',
      {
        ...this.list[this.activeIndex],
        index: this.activeIndex
      },
      this.activeIndex
    );
  },
  methods: {
    handleClick(item, index) {
      this.$emit('click', {
        ...item,
        index
      });
      // 如果是 H5
      // if (/^http(s)?:\/\//g.test(item.jump_url)) {
      //   this.$bridge({
      //     url: '/pages/h5?url=' + encodeURIComponent(item.jump_url)
      //   });
      // } else {
      //   this.$bridge({
      //     url: item.jump_url
      //   });
      // }
    },
    onchange(event) {
      const { current } = event.detail;
      this.activeIndex = current;
      this.$emit(
        'expose',
        {
          ...this.list[this.activeIndex],
          index: this.activeIndex
        },
        this.activeIndex
      );
    }
  }
};
</script>
<style lang="less" scoped>
@swiper_height: 80 * 2rpx;
@swiper_width: 345 * 2rpx;
.swiper-box {
  box-sizing: border-box;
  background: #fff;
  .swiper-overflow {
    margin: 0 auto;
    width: @swiper_width;
    height: @swiper_height;
    overflow: hidden;
    position: relative;
  }
}
.swiper {
  width: @swiper_width;
  height: @swiper_height;
  .cover {
    width: @swiper_width;
    object-fit: contain;
  }
}
.banner-indicator {
  position: absolute;
  bottom: 20rpx;
  left: 30rpx;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  max-width: 120rpx;
  .indicator {
    flex: 1;
    height: 4rpx;
    width: 30rpx;
    opacity: 0.9;
    background-color: #ffffff;
    transition: background-color, width 0.3s ease-in-out;
    &.active {
      width: 30rpx;
      background-color: #030303;
    }
  }
}
</style>
