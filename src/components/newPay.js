import Vue from 'vue';
import config from '@/config';

async function requestPayment(data, subscribe) {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: 'wxpay',
      timeStamp: data.timeStamp,
      appId: data.appId,
      nonceStr: data.nonceStr,
      package: data.package,
      signType: data.signType,
      paySign: data.paySign,
      success: async (res) => {
        typeof subscribe === 'function' && (await subscribe());
        resolve(res);
      },
      fail: reject
    });
  });
}

export const subscribeTmpl = [
  '5v3EjUo21KQ8D1jjb7eBBDNOAeQuY7bg5pRpKxJSgp0',
  'c1w_2vGA0TdnYgZIsHO19v1jsyhQI5bMdyZSAZTb65U',
  'siVw2L2pYZVqo9P79iZLJKdh_yhP6cVUftprSvK0aX8'
];
/**
 * 新支付流程
 * @param {number} params.oid 订单id
 * @param {number} params.pay_type 3 为微信支付 27 分期支付
 * @param {number} params.yfbei_num 分期期数
 * @return {{ status: number, message: string }} status < 0 错误, status > 0 成功, 目前 status 错误状态只有 -1 状态，正常态 0, 1 取消支付, 2 分期支付需特殊处理
 * */
export default async function pay(params, subscribe) {
  return new Promise((resolve) => {
    (async function () {
      try {
        // 判断是否为0元订单
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const orderInfoResponse = await Vue.$request({
          url: '/mp/payment/orderinfo',
          host: config.payBaseApi,
          method: 'post',
          data: {
            order_id: params.oid
          }
        })
          .then((response) => response.data)
          .catch((error) => error);
        console.log('/mp/payment/orderinfo', orderInfoResponse);
        if (orderInfoResponse.errorCode !== 0) {
          uni.hideLoading();
          uni.showToast({
            title: orderInfoResponse.errorMsg,
            icon: 'none'
          });
          resolve({
            status: -1,
            message: orderInfoResponse.errorMsg
          });
          return;
        }
        if (Number(orderInfoResponse.responseData.total_price) === 0) {
          uni.hideLoading();
          typeof subscribe === 'function' && (await subscribe());
          resolve({
            status: 0,
            message: ''
          });
          return;
        }
        // 非0元订单
        const payInfoResponse = await Vue.$request({
          url: '/mp/payment/payinfo',
          host: config.payBaseApi,
          method: 'post',
          data: {
            order_id: params.oid,
            pay_type: params.pay_type, // 3 为微信支付 27 分期支付
            yfbei_num: params.yfbei_num,
            topay_price: Number(orderInfoResponse.responseData.total_price)
          }
        })
          .then((response) => response.data)
          .catch((error) => error);
        console.log('/mp/payment/payinfo', payInfoResponse.responseData);
        if (payInfoResponse.errorCode !== 0) {
          uni.hideLoading();
          uni.showToast({
            title: payInfoResponse.errorMsg,
            icon: 'none'
          });
          resolve({
            status: -1,
            message: payInfoResponse.errorMsg
          });
          return;
        }
        // 判断是否分期
        if (params.pay_type === 27) {
          uni.hideLoading();
          Vue.$bridge({
            url:
              '/pages/h5?url=' +
              encodeURIComponent(payInfoResponse.responseData.pay_url)
          });
          // uni.navigateTo({
          //   url: `/pages/h5?url=${encodeURIComponent(
          //     payInfoResponse.responseData.pay_url
          //   )}`
          // });
          resolve({
            status: 2,
            message: ''
          });
          return;
        }
        const { errMsg } = await requestPayment(
          payInfoResponse.responseData.weixin_attr,
          subscribe
        );
        if (errMsg === 'requestPayment:ok') {
          uni.showLoading({
            title: '支付中...',
            mask: true
          });
          setTimeout(() => {
            uni.hideLoading();
            resolve({
              status: 0,
              message: errMsg
            });
          }, 200);
        } else {
          uni.hideLoading();
          uni.showToast({
            title: errMsg,
            icon: 'none'
          });
          resolve({
            status: -1,
            message: errMsg
          });
        }
      } catch (error) {
        console.log(error);
        uni.hideLoading();
        // 兼容取消支付逻辑
        if (error.errMsg === 'requestPayment:fail cancel') {
          resolve({
            status: 1,
            message: error.errMsg
          });
          return;
        }
        uni.showToast({
          title: error.message || '支付失败',
          icon: 'none'
        });
        resolve({
          status: -1,
          message: error.errMsg
        });
      }
    })();
  });
}
