import React, { useState } from 'react'
import { Button } from '@tarojs/components'
import './index.scss'

interface LoadingButtonProps {
  children: React.ReactNode
  loading?: boolean
  disabled?: boolean
  type?: 'primary' | 'default' | 'warn'
  size?: 'default' | 'mini'
  onClick?: () => void | Promise<void>
  className?: string
}

const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  loading = false,
  disabled = false,
  type = 'primary',
  size = 'default',
  onClick,
  className = '',
}) => {
  const [internalLoading, setInternalLoading] = useState(false)

  const handleClick = async () => {
    if (disabled || loading || internalLoading) {
      return
    }

    if (onClick) {
      try {
        setInternalLoading(true)
        await onClick()
      } catch (error) {
        console.error('Button click error:', error)
      } finally {
        setInternalLoading(false)
      }
    }
  }

  const isLoading = loading || internalLoading

  return (
    <Button
      className={`loading-button ${className}`}
      type={type}
      size={size}
      disabled={disabled || isLoading}
      loading={isLoading}
      onClick={handleClick}
    >
      {children}
    </Button>
  )
}

export default LoadingButton
