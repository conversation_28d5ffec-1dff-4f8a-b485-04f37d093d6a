<template>
  <div class="tab-container-out">
    <div
      class="tab-container"
      :style="{
        top: navStatusBarHeight + 'px',
        position: setFixed ? 'fixed' : '',
        padding: padding || 0,
        'border-radius': borderRadius || ''
      }"
    >
      <!-- position: ceiling ? 'sticky' : '',
			position: ceiling ? '-webkit-sticky' : '', -->
      <div
        v-for="item in tabList"
        :key="item.value"
        @click="tabChange(item)"
        class="tab-item"
        :class="{ 'tab-blur': item.value == tabBlur }"
      >
        <span :style="{ 'font-size': fontSize || '36rpx' }">
          {{ item.label }}
        </span>
        <span v-if="item.value == tabBlur" class="tab-item-color"> </span>
        <img
          v-if="item.icon"
          class="tab-icon"
          src="https://static.soyoung.com/sy-pre/2sxpcss4m4ndr-1656501000695.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabBlur: 0,
      setFixed: false,
      navStatusBarHeight: 0
    };
  },
  props: {
    scrollTop: Number,
    top: Number,
    padding: String,
    borderRadius: String,
    fontSize: String,
    defineNavBar: Boolean,
    tabList: Array,
    ceiling: Boolean,
    noContCeil: Boolean,
    defaultValue: String || Number
  },
  watch: {
    scrollTop: {
      handler(newInfo) {
        if (newInfo + this.navStatusBarHeight >= this.top) {
          this.setFixed === false && (this.setFixed = true);
        } else {
          this.setFixed === true && (this.setFixed = false);
        }
      },
      immediate: true
    },
    setFixed: {
      handler(newInfo) {
        this.$emit('fixed', newInfo);
      },
      immediate: true
    }
  },
  methods: {
    tabChange(item) {
      this.tabBlur = item.value;
      this.$emit('tabChange', item);
    },
    setTopSim() {
      this.navStatusBarHeight = -8;
    },
    setTop() {
      const systemInfo = wx.getSystemInfoSync();
      // 状态栏高度
      const statusBarHeight = Number(systemInfo.statusBarHeight);
      const menu = wx.getMenuButtonBoundingClientRect();
      // 状态栏加导航栏高度
      let navStatusBarHeight = statusBarHeight + menu.height;
      if (!this.defineNavBar && menu) {
        navStatusBarHeight += (menu.top - statusBarHeight) * 2;
      }
      const plus = 0;
      // if (systemInfo.system.indexOf('iOS') > -1) {
      //   plus = 2
      // }
      console.log('navStatusBarHeight', navStatusBarHeight);
      this.navStatusBarHeight = navStatusBarHeight + plus;
      this.$emit('setNavHeight', this.navStatusBarHeight);
    }
  },
  mounted() {
    console.log(this.fontSize, 'this.fontSize');
    if (this.defaultValue) {
      this.tabBlur = this.defaultValue;
    } else {
      this.tabBlur = this.tabList[0].value;
    }
    if (this.ceiling && !this.noContCeil) {
      this.setTop();
    } else {
      this.setTopSim();
    }
  }
};
</script>

<style lang="less" scoped>
.tab-container-out {
  width: 100%;
  height: 84rpx;
}
.tab-container {
  width: 100%;
  display: flex;
  // position: fixed;
  top: 0px;
  justify-content: space-between;
  align-items: center;
  //  position: sticky;
  z-index: 9;
  box-sizing: border-box;
  font-family: PingFangSC-Medium;
  background: #fff;
  color: #777777;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
  height: 84rpx;
  .tab-item {
    position: relative;
    // margin-left: 30rpx;
    span {
      font-size: 36rpx;
      display: block;
    }
    .tab-item-color {
      margin-top: -17rpx;
      bottom: 4rpx;
      left: 0rpx;
      width: 100%;
      height: 12rpx;
      background-image: linear-gradient(
        90deg,
        rgba(0, 171, 132, 0) 0%,
        #00ab84 56%
      );
      border-radius: 6rpx;
    }
    .tab-icon {
      position: absolute;
      right: -36rpx;
      width: 28rpx;
      height: 28rpx;
      top: 12rpx;
    }
  }
  .tab-blur {
    margin-top: -4rpx;
    span {
      color: #333333;
    }
  }
}
</style>
