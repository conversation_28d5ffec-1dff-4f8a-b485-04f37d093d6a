<template>
  <div class="pintuan-box" v-if="groupInfo.group_id">
    <!-- <div
      class="status-img"
      :style="{
        'background-image': `url(${groupInfo.icon})`
      }"
    ></div> -->
    <div class="pintuan">
      <!-- 拼团失败 -->
      <image
        class="pintuan-icon"
        v-if="Number(groupInfo.status) === 2"
        src="https://static.soyoung.com/sy-pre/20240914-113010-1726283400654.png"
      ></image>
      <view
        v-else-if="Number(groupInfo.status) === 1"
        class="pintuan-icon-success"
      >
        <image
          src="https://static.soyoung.com/sy-design/kgmpik5wigpe1729076512010.png"
        ></image>
      </view>
      <h2>{{ groupInfo.title }}</h2>
      <div class="countdown" v-if="groupInfo.time_count_down">
        剩余
        <div class="countdown-time">
          {{ countDownMap.h }}：{{ countDownMap.m }}：{{ countDownMap.s }}
        </div>
        <!--        <div class="rect">{{ countDownMap.h }}</div>-->
        <!--        <div class="colon">:</div>-->
        <!--        <div class="rect">{{ countDownMap.m }}</div>-->
        <!--        <div class="colon">:</div>-->
        <!--        <div class="rect">{{ countDownMap.s }}</div>-->
        结束
      </div>
      <div
        class="user"
        :class="[
          `margin-${groupInfo.user_num >= 6 ? 'more' : groupInfo.user_num}`
        ]"
      >
        <template v-for="(user, index) in userList">
          <div :key="index" class="colon" v-if="user.colon === 1">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div
            v-else
            class="item"
            :key="index"
            :class="{
              'no-border': user.user_id === -1
            }"
          >
            <button
              v-if="user.share === 1"
              :style="{
                backgroundImage: `url(${user.avatar})`
              }"
              open-type="share"
              class="avatar"
              plain="true"
              @click="onavatarClick"
            />
            <div
              v-else
              :style="{
                backgroundImage: `url(${user.avatar})`
              }"
              class="avatar"
            ></div>
            <p class="name">{{ user.nickname }}</p>
          </div>
        </template>
      </div>
      <button
        open-type="share"
        class="share"
        v-if="showBtn"
        plain="true"
        @click="onInviteUser"
      >
        邀请好友马上组团
      </button>
      <div class="tip" v-if="groupInfo.failure_tip">
        {{ groupInfo.failure_tip }}
      </div>
    </div>
  </div>
</template>
<script>
import { beatFactory } from '@/common/beat.js';
import SubscribeMixins from '@/mixins/subscribe';
export default {
  mixins: [SubscribeMixins],
  props: {
    groupInfo: {
      type: Object,
      default: () => {}
    },
    shareBtnVisible: {
      type: Boolean,
      default: false
    },
    reportInfo: {
      type: Object,
      default: () => null
    }
  },
  onPageShow() {
    if (this.groupInfo.time_count_down) {
      this.beat?.run(1000);
    }
  },
  onPageHide() {
    if (this.groupInfo.time_count_down) {
      this.beat?.stop();
    }
  },
  data() {
    return {
      countDownMap: {
        h: 0,
        m: 0,
        s: 0
      },
      userList: []
    };
  },
  computed: {
    showBtn() {
      return this.shareBtnVisible && this.groupInfo.status === 0;
    }
  },
  watch: {
    showBtn: {
      handler(visible) {
        if (visible && this.reportInfo) {
          this.$reportData({
            info: 'sy_wxtuan_tuan_order_info:yaoqinghaoyou_exposure',
            ext: {
              product_id: this.reportInfo?.product_id,
              group_id: this.reportInfo?.group_id,
              order_id: this.reportInfo?.order_id,
              activity_id: this.reportInfo?.activity_id
            }
          });
        }
      },
      immediate: true
    },
    groupInfo: {
      handler(newInfo) {
        if (newInfo && newInfo.group_id) {
          console.log('watch groupInfo=--------');
          this.setBeatLoop();
          this.setInviteList();
        }
      },
      immediate: true
    }
  },
  methods: {
    onavatarClick() {
      // this.createGroupBySub(
      //   [
      //     'WVEq1xRbHUtILERdzzBl88FzQJU8wQA0ZdfdCE1dUKs',
      //     'eGnRAuGNgOmj8uFVNQqZbiaMMssCfKSiLeIjNZTTVS4',
      //     'E-3yPJELuSVKOUIkV8q0NY2L9J-LGsu_jEf5ZIVOSDU'
      //   ],
      //   [this.groupInfo.group_id]
      // );
      if (!this.reportInfo) return;
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:yaoqinghao_click',
        ext: {
          product_id: this.reportInfo?.product_id,
          group_id: this.reportInfo?.group_id,
          order_id: this.reportInfo?.order_id,
          activity_id: this.reportInfo?.activity_id
        }
      });
    },
    onInviteUser() {
      // this.createGroupBySub(
      //   [
      //     'WVEq1xRbHUtILERdzzBl88FzQJU8wQA0ZdfdCE1dUKs',
      //     'eGnRAuGNgOmj8uFVNQqZbiaMMssCfKSiLeIjNZTTVS4',
      //     'E-3yPJELuSVKOUIkV8q0NY2L9J-LGsu_jEf5ZIVOSDU'
      //   ],
      //   [this.groupInfo.group_id]
      // );
      if (!this.reportInfo) return;
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_info:yaoqinghaoyou_click',
        ext: {
          product_id: this.reportInfo?.product_id,
          group_id: this.reportInfo?.group_id,
          order_id: this.reportInfo?.order_id,
          activity_id: this.reportInfo?.activity_id
        }
      });
    },
    setInviteList() {
      const {
        join_num: joinNum,
        user_num: userNum,
        user_list: userList = [],
        status
      } = this.groupInfo;
      let needInvite = +userNum - joinNum;
      if (needInvite && this.reportInfo) {
        this.$reportData({
          info: 'sy_wxtuan_tuan_order_info:yaoqing_exposure',
          ext: {
            product_id: this.reportInfo?.product_id,
            group_id: this.reportInfo?.group_id,
            order_id: this.reportInfo?.order_id,
            activity_id: this.reportInfo?.activity_id
          }
        });
      }
      if (this.groupInfo.status !== 1) {
        while (needInvite--) {
          console.log('userList', userList);
          userList.push(
            status === 0 ? this.inviteFactory() : this.failFactory()
          );
        }
      }
      if (+userNum >= 6) {
        const startList = userList.slice(0, 2);
        const endList = userList.slice(-2);
        this.userList = [...startList, { colon: 1 }, ...endList];
      } else {
        this.userList = [...userList];
      }
    },
    setBeatLoop() {
      if (this.beat) {
        this.beat.clear();
      } else {
        this.beat = beatFactory(this);
      }
      if (this.groupInfo.time_count_down === 0) {
        return;
      }
      let countdown = this.groupInfo.time_count_down;
      const cb = () => {
        this.countDownMap = this.fmt(countdown--);
        if (countdown <= 0) {
          this.beat.remove(cb);
          this.$emit('countdownEnd');
        }
      };
      this.beat.add(cb).run(1000);
    },
    fmt(time) {
      const hour = parseInt(time / 3600);
      time = time % 3600;
      const minute = parseInt(time / 60);
      time = time % 60;
      const second = parseInt(time);
      const fix = (b) => (b > 9 ? b : '0' + b);
      return {
        h: fix(hour),
        m: fix(minute),
        s: fix(second)
      };
    },
    inviteFactory() {
      return {
        avatar:
          'https://static.soyoung.com/sy-pre/20240927-101201-1727403000665.png',
        nickname: '待邀请',
        user_id: -1,
        share: 1
      };
    },
    failFactory() {
      return {
        avatar:
          'https://static.soyoung.com/sy-pre/20240927-101201-1727403000665.png',
        nickname: '邀请失败',
        user_id: -1
      };
    }
  }
};
</script>
<style lang="less" scoped>
@px: 2rpx;

.flex-align-center() {
  display: flex;
  align-items: center;
}
.flex-center() {
  .flex-align-center;
  justify-content: center;
}
.pintuan-box {
  position: relative;
  box-sizing: border-box;
  background-color: transparent;
  width: 100%;
  .status-img {
    position: absolute;
    top: 0;
    right: 0;
    height: 100rpx;
    width: 100rpx;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    // &.doing {
    //   background-image: url(https://static.soyoung.com/sy-pre/doing-1669097400698.png);
    // }
    // &.success {
    //   background-image: url(https://static.soyoung.com/sy-pre/success-1669097400698.png);
    // }
    // &.fail {
    //   background-image: url(https://static.soyoung.com/sy-pre/fail-1669097400698.png);
    // }
  }
  .pintuan {
    //padding: 1rpx 0;
    padding-top: 25 * @px;
    min-height: 180rpx;
    background-color: transparent;
    //background: url(https://static.soyoung.com/sy-pre/wg58kz839jws-1719389400659.png)
    //  no-repeat center top #ffffff;
    //background-size: 363 * 2rpx 130 * 2rpx;
    //border-radius: 16rpx;
    //background-position-y: 2rpx;
    //background-position-x: 2rpx;

    .pintuan-icon {
      width: 30 * @px;
      height: 30 * @px;
      min-width: 30 * @px;
      margin: 0 auto 10 * @px;
      display: block;
    }

    .pintuan-icon-success {
      width: 30 * @px;
      height: 30 * @px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fbbcff;
      border-radius: 50%;
      margin: 0 auto 10 * @px;

      image {
        width: 17 * @px;
        height: 17 * @px;
      }
    }

    h2 {
      position: relative;
      height: 44rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      text-align: center;
      font-family: OutFit-Regular;
    }
    .countdown {
      margin-top: 18rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24rpx;
      color: #8c8c8c;
      font-weight: 400;
      font-family: PingFangSC-Regular;

      .countdown-time {
        display: flex;
        align-items: center;
        padding: 0 6 * 2rpx;
        font-family: OutFit-Regular;
        font-size: 14 * 2rpx;
        color: #030303;
        letter-spacing: 0;
        font-weight: 500;
        text-decoration: underline;
      }

      .rect {
        box-sizing: border-box;
        padding: 0 5rpx;
        margin: 0 10rpx;
        height: 44rpx;
        min-width: 44rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba(248, 93, 45, 0.15);
        border-radius: 6rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #f85d2d;
      }
      .colon {
        font-size: 28rpx;
        font-weight: 400;
      }
    }
    .user {
      padding: 30 * 2rpx 0 20 * 2rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      .item {
        width: 80rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        margin-left: 15 * @px !important;
        margin-right: 15 * @px !important;
        &.no-border .avatar {
          border: none;
        }
        .avatar {
          box-sizing: border-box;
          display: block;
          margin: 0 auto 14rpx;
          height: 30 * @px;
          width: 30 * @px;
          overflow: hidden;
          background-repeat: no-repeat;
          background-size: 60rpx 60rpx;
          background-position: center;
          border-radius: 50%;
        }
        .name {
          width: 100%;
          box-sizing: border-box;
          padding: 0 5rpx;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-family: PingFangSC-Regular;
          font-size: 13 * @px;
          color: #8c8c8c;
          font-weight: 400;
        }
      }
      .colon {
        height: 80rpx;
        line-height: 80rpx;
        align-self: flex-start;
        text-align: center;
        color: #8c8c8c;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          display: block;
          width: 6px;
          height: 6px;
          background: #8c8c8c;
          border-radius: 3px;
          margin: 0 2px;
        }
      }
      &.margin-2 .item {
        margin: 0 85rpx;
        .name {
          width: 200rpx;
        }
      }
      &.margin-3 .item {
        margin: 0 64rpx;
        .name {
          width: 200rpx;
        }
      }
      &.margin-4 .item {
        margin: 0 35rpx;
        .name {
          width: 140rpx;
        }
      }
      &.margin-5 .item {
        margin: 0 24rpx;
        .name {
          width: 120rpx;
        }
      }
      &.margin-more .item {
        margin: 0 14rpx;
        .name {
          width: 106rpx;
        }
      }
    }
    .ell {
      margin: 0 15rpx;
      display: flex;
      justify-content: center;
      align-self: flex-start;
      align-items: center;
      height: 80rpx;
      font-size: 24rpx;
      color: #999999;
    }
    .share {
      margin-bottom: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 142.5 * 2rpx;
      font-size: 24rpx;
      color: #fff;
      font-weight: 500;
      border-radius: 0;
      background-color: #333;
      height: 37 * @px;

      &:after {
        border: none;
      }
    }
    .tip {
      height: 37 * 2rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      color: #8c8c8c;
      text-align: center;
      position: relative;

      &:before {
        content: '';
        width: 100%;
        border-top: 1 * 2rpx solid #f2f2f2;
        position: absolute;
        top: 0;
      }
    }
  }
}
</style>

<style>
@keyframes _group_component_breath_ {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(0.95, 0.95);
  }
  100% {
    transform: scale(1, 1);
  }
}
</style>
