<template>
  <div class="canvas-wrap">
    <canvas
      type="2d"
      id="myCanvas"
      :style="{
        width: width * scale + 'px',
        height: height * scale + 'px'
      }"
    ></canvas>
  </div>
</template>
<script>
let ctx;
let canvas;
const dpr = 1;
const width = 500 * dpr;
const height = 400 * dpr;
const fix = (n) => n * dpr;
const scale = 1 / dpr;
// 全局缓存加载的图片资源，不load两次
const catched = new Map();
import Vue from 'vue';
import { btoa } from '@/common/base64';
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    path: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => ({
        title: '',
        path: '',
        backgroundImage: '',
        imgCover: '',
        userNum: 0,
        priceGroup: 0,
        priceOnline: 0
      })
    }
  },
  data() {
    return {
      width,
      height,
      scale,
      queue: [],
      resourceUrls: []
    };
  },
  watch: {
    params(params) {
      const {
        title,
        path,
        backgroundImage,
        imgCover,
        userNum,
        priceGroup,
        priceOnline
      } = params;
      if (
        title &&
        path &&
        backgroundImage &&
        imgCover &&
        userNum > 0 &&
        priceGroup > 0 &&
        priceOnline > 0
      ) {
        this.createPoster(params);
      }
    }
  },
  methods: {
    /**
     * 分享信息生成
     * @param {number|string} param.group_id - 拼团id
     * @param {number|string} param.sku_id - 商品id
     * @param {number|string} [param.activity_id] - 活动id，非必传
     * @param {number|string} [param.path] - 分享id，非必传
     * @returns {Promise<{ title: string, path: string, imageUrl: string }>}
     * @desc path 优先级 param path > this.path > 默认拼接
     * */
    async shareInfo(param) {
      try {
        const timestampStart = new Date().getTime();
        const response = await Vue.$request({
          url: '/syChainTrade/wxapp/share/getGroupShareInfo',
          method: 'post',
          data: {
            sku_id: param.sku_id,
            group_id: param.group_id
          }
        });
        const timestampEnd = new Date().getTime();
        console.log(response);
        if (response.statusCode !== 200 && response.data.errorCode !== 0) {
          throw new Error(response.data.errorMsg);
        }
        const shareMetadata = response.data.responseData;
        return new Promise((resolve) => {
          const finalResult = setTimeout(() => {
            resolve({
              title: this.title,
              path: this.path,
              imageUrl:
                shareMetadata && shareMetadata.img_cover
                  ? shareMetadata.img_cover
                  : ''
            });
          }, 2800 - (timestampEnd - timestampStart));
          this.createPoster({
            backgroundImage: shareMetadata.background_image,
            imgCover:
              shareMetadata && shareMetadata.img_cover
                ? shareMetadata.img_cover
                : '',
            userNum: shareMetadata.user_num,
            priceGroup: shareMetadata.price_group,
            priceOnline: shareMetadata.price_online
          }).then((res) => {
            clearTimeout(finalResult);
            const base64Url = btoa(
              `/packageActivity/pintuan?sku_id=${param.sku_id}&activity_id=${param.activity_id}&group_id=${param.group_id}`
            );
            const shareConfig = Object.assign(
              {
                title: this.title,
                path:
                  param.path ||
                  this.path ||
                  `/pages/index?shining_link=` + base64Url.replace(/\//g, '_')
              },
              res
            );
            console.log('分享信息', shareConfig);
            resolve(shareConfig);
          });
        });
      } catch (error) {
        return new Promise((resolve) => {
          resolve({
            title: this.title,
            path: this.path
          });
        });
      }
    },
    createPoster({
      backgroundImage = '',
      imgCover = '',
      userNum = 0,
      priceGroup = 0,
      priceOnline = 0
    }) {
      const pro = new Promise((resolve) => {
        /**
         * 在draw之前调用，向绘制队列添加操作，如果存在图片url,也会发起一个不阻塞的图片fetch promise
         * 下面的顺序决定了绘制的顺序: 数组累加器 + promise调用链实现
         */
        this.resetCanvas();
        this.drawBgImage(backgroundImage);
        this.drawCard({
          radius: 14,
          width: 466,
          height: 258,
          x: 17,
          y: 117
        });
        this.drawText(userNum, priceGroup, priceOnline);
        this.drawButton();
        this.loadCoverImage(imgCover);
        this.draw(resolve);
      });
      this.$emit('afterPosterCreated', pro);
      return pro;
    },
    async draw(resolve) {
      this.queue.push(function scaleCanvas() {
        ctx.scale(this.scale, this.scale);
        uni.canvasToTempFilePath({
          canvas,
          x: 0,
          y: 0,
          width,
          height,
          fileType: 'jpg',
          quality: 1,
          destWidth: width,
          destHeight: height,
          success: (res) => {
            console.log('图片生成：', res.tempFilePath);
            resolve({
              imageUrl: res.tempFilePath
            });
            // this.$uploadImg({
            //   url: '/xinyang/posts/addPic',
            //   filePath: res.tempFilePath,
            //   name: 'imgFile'
            // }).then((res) => {
            //   if (res.errorCode === 0) {
            //     console.log({
            //       title: this.params.title,
            //       path: this.params.path,
            //       imageUrl: res.responseData.u
            //     });
            //   }
            // });
          }
        });
      });
      // 所有的方法依赖ctx初始化
      await this.getCtxPromise();
      this.resourceUrls.forEach((url) => this.loadResource(url));
      console.log(
        '绘制操作队列：',
        this.queue.map((func) => func.name)
      );
      this.queue.reduce((prev, cur) => {
        return prev.then((...args) => {
          ctx.save();
          let isPromise = cur.apply(this, args);
          if (!isPromise) {
            isPromise = Promise.resolve(isPromise);
          }
          return isPromise.then((result) => {
            ctx.restore();
            return result;
          });
        });
      }, Promise.resolve());
    },
    resetCanvas() {
      this.queue = [];
      this.queue.unshift(function resetCanvas() {
        ctx.clearRect(0, 0, width, height);
      });
    },
    getCtxPromise() {
      return this.queryNode('#myCanvas').then((res) => {
        if (!res) {
          return Promise.reject('没有查询到canvas');
        }
        const [{ node }] = res;
        canvas = node;
        canvas.width = width;
        canvas.height = height;
        ctx = canvas.getContext('2d');
        ctx.strokeStyle = '#000';
        ctx.strokeRect(0, 0, width, height);
      });
    },
    drawText(userNum, priceGroup, priceOnline) {
      this.queue.push(function drawText() {
        const width = fix(173);
        const height = fix(34);
        const x = fix(282);
        /**
         * 多维数组
         */
        const rows = [
          [
            {
              txt: `${userNum}人拼单价`,
              y: fix(145),
              font: `400 ${fix(32)}px PingFangSC-Regular`,
              color: '#F85D2D'
            }
          ],
          [
            {
              txt: `￥`,
              y: fix(190),
              font: `600 ${fix(20)}px PingFangSC-Regular`,
              color: '#FF4040'
            },
            {
              txt: `${priceGroup}`,
              y: fix(194),
              font: `600 ${fix(38)}px PingFangSC-Regular`,
              color: '#FF4040'
            }
          ],
          [
            {
              txt: `原价`,
              y: fix(240),
              font: `400 ${fix(28)}px PingFangSC-Regular`,
              color: '#999999'
            },
            {
              txt: `￥`,
              y: fix(240),
              font: `400 ${fix(20)}px PingFangSC-Regular`,
              color: '#999999'
            },
            {
              txt: `${priceOnline}`,
              y: fix(240),
              font: `400 ${fix(28)}px PingFangSC-Regular`,
              color: '#999999'
            }
          ]
        ];

        const measureWidth = (list) => {
          let length = 0;
          const array = list.map(({ txt, font }) => {
            ctx.font = font;
            const w = ctx.measureText(txt).width;
            length += w;
            return w;
          });
          return {
            array,
            length
          };
        };

        rows.forEach((row) => {
          const { array, length } = measureWidth(row);
          let start = x + (width - length) / 2;
          row.forEach((col, index) => {
            const { txt, y, font, color } = col;
            // ctx.strokeRect(start, y, array[index], height);
            ctx.textAlign = 'left';
            ctx.textBaseline = 'bottom';
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.fillText(txt, start, y + height);
            start += array[index];
          });
        });
      });
    },
    drawButton() {
      this.queue.push(function drawButton() {
        const x = 290;
        const y = 290;
        const width = 158;
        const height = 60;
        this.roundRect({
          backgroundColor: '#FF5C1D',
          radius: 10,
          x,
          y,
          width,
          height
        });
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#fff';
        ctx.font = `500 ${fix(30)}px PingFangSC-Regular`;
        ctx.fillText('立即抢购', fix(x + width / 2), fix(y + height / 2));
      });
    },
    drawCard({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      this.queue.push(function drawCard() {
        this.roundRect({ radius, x, y, width, height, backgroundColor });
      });
    },
    drawBgImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function drawBgImage() {
        const image = await this.loadResource(url);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          0,
          0,
          width,
          height
        );
      });
    },
    loadCoverImage(url) {
      if (!url) return;
      this.resourceUrls.push(url);
      this.queue.push(async function loadCoverImage() {
        const image = await this.loadResource(url);
        const dx = fix(43);
        const dy = fix(166);
        const dWidth = fix(215);
        const dHeight = fix(161);
        ctx.drawImage(
          image,
          0,
          0,
          image.width,
          image.height,
          dx,
          dy,
          dWidth,
          dHeight
        );
      });
    },
    roundRect({ radius, x, y, width, height, backgroundColor = '#fff' }) {
      ctx.fillStyle = backgroundColor;
      ctx.beginPath();
      ctx.moveTo(fix(x + radius), fix(y));
      ctx.lineTo(fix(x + width - 2 * radius), fix(y));
      ctx.arcTo(
        fix(x + width),
        fix(y),
        fix(x + width),
        fix(y + radius),
        fix(radius)
      );
      ctx.lineTo(fix(x + width), fix(y + height - radius));
      ctx.arcTo(
        fix(x + width),
        fix(y + height),
        fix(x + width - 2 * radius),
        fix(y + height),
        fix(radius)
      );
      ctx.lineTo(fix(x + radius), fix(y + height));
      ctx.arcTo(
        fix(x),
        fix(y + height),
        fix(x),
        fix(y + height - radius),
        fix(radius)
      );
      ctx.lineTo(fix(x), fix(y + radius));
      ctx.arcTo(fix(x), fix(y), fix(x + radius), fix(y), fix(radius));
      ctx.closePath();
      ctx.fill();
    },
    loadResource(url) {
      const fetch = (url, tryTimes = 5) =>
        new Promise((resolve, reject) => {
          const image = canvas.createImage();
          image.onload = () => resolve(image);
          image.onerror = (error) => {
            console.log(`load image error :try:${tryTimes} !!!`);
            if (tryTimes-- > 0) {
              fetch(
                url.replace(/\?t=\d+/g, '') + '?t=' + new Date().getTime(),
                tryTimes
              );
            } else {
              reject(error);
            }
          };
          image.src = url;
        });
      // return promise<image> or undefined
      if (catched.has(url)) {
        const target = catched.get(url);
        target.catch(() => catched.delete(url));
      } else {
        catched.set(url, fetch(url));
      }
      return catched.get(url);
    },
    queryNode(selector, times = 10) {
      return new Promise((resolve) => {
        this.createSelectorQuery()
          .select(selector)
          .fields({ node: true })
          .exec((res) => {
            if (res?.length > 0) {
              resolve(res);
            } else {
              if (times--) {
                setTimeout(() => {
                  resolve(this.queryNode(selector, times));
                }, 50);
              } else {
                uni.$log('[poster]没有查询到节点！', this.params.path, 'error');
                resolve([]);
              }
            }
          });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.canvas-wrap {
  position: absolute;
  transform: translate3d(-2000px, -2000px, 1px); // 单独来个图层
}
</style>
