<template>
  <root-portal>
    <div class="qrcode-modal" v-if="visible">
      <div class="popup-mask" @click="handleClose"></div>
      <div class="qrcode-container">
        <div class="qrcode-header">
          <div class="qrcode-title">会员码</div>
        </div>

        <div class="qrcode-content">
          <div class="qrcode-wrapper" :class="{ 'expired': isExpired }">
            <tki-qrcode
              ref="qrcode"
              :val="qrValue"
              :size="200"
              :background="background"
              :foreground="foreground"
              :pdground="pdground"
            ></tki-qrcode>

            <div class="expired-mask" v-if="isExpired">
              <div class="expired-text">二维码已失效</div>
              <div class="refresh-btn" @click="handleRefresh">刷新</div>
            </div>
          </div>

          <div class="qrcode-timer" v-if="!isExpired">
            有效期：{{ formatTime(remainingTime) }}
          </div>
        </div>
      </div>
    </div>
  </root-portal>
</template>

<script>
import tkiQrcode from 'tki-qrcode';

export default {
  name: 'VipQrCode',
  components: {
    tkiQrcode
  },
  props: {
    // 控制弹窗显示
    visible: {
      type: Boolean,
      default: false
    },
    // 二维码内容
    value: {
      type: String,
      default: ''
    },
    // 有效期（秒）
    validTime: {
      type: Number,
      default: 60
    }
  },
  data() {
    return {
      qrValue: '',
      remainingTime: 0,
      isExpired: false,
      timer: null,
      // 二维码样式配置
      background: '#ffffff', // 背景色
      foreground: '#000000', // 前景色
      pdground: '#000000' // 定位角颜色
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initQrCode();
      } else {
        this.clearTimer();
      }
    },
    value(newVal) {
      if (newVal && this.visible) {
        this.initQrCode();
      }
    }
  },
  methods: {
    // 初始化二维码
    initQrCode() {
      this.qrValue = this.value;
      this.isExpired = false;
      this.remainingTime = this.validTime;
      // this.startTimer();

      this.$nextTick(() => {
        if (this.$refs.qrcode) {
          this.$refs.qrcode._makeCode();
        }
      });
    },

    // 开始倒计时
    startTimer() {
      this.clearTimer();
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--;
        } else {
          this.isExpired = true;
          this.clearTimer();
        }
      }, 1000);
    },

    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 格式化时间
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
        .toString()
        .padStart(2, '0')}`;
    },

    // 关闭弹窗
    handleClose() {
      this.clearTimer();
      this.$emit('close');
    },

    // 刷新二维码
    handleRefresh() {
      this.$emit('refresh');
    }
  },
  beforeDestroy() {
    this.clearTimer();
  }
};
</script>

<style lang="less" scoped>
.qrcode-modal {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100vw;
  z-index: 10000;
  overflow: hidden;
  .popup-mask {
    width: 100%;
    height: 100%;
    background-color: #e0e2e8;
  }
}

.qrcode-container {
  width: 300px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.qrcode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.qrcode-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.qrcode-close {
  font-size: 22px;
  color: #999;
  cursor: pointer;
  line-height: 1;
}

.qrcode-content {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-wrapper {
  position: relative;
  margin-bottom: 20px;

  &.expired {
    opacity: 0.6;
  }
}

.expired-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.expired-text {
  font-size: 16px;
  color: #ff4d4f;
  margin-bottom: 15px;
}

.refresh-btn {
  padding: 6px 16px;
  background-color: #1890ff;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: #40a9ff;
  }
}

.qrcode-timer {
  font-size: 14px;
  color: #666;
}
</style>
