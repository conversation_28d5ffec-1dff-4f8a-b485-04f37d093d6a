<script>
// https://soyoung.feishu.cn/wiki/TDsGwgEfNiq1shkxQPvcbIoEnKg

export default {
  name: 'HomeCardComponent',
  props: {
    cardData: {
      type: Object,
      default: () => {},
      required: true
    }
  },
  data() {
    return {
      showData: {}
    };
  },
  methods: {
    goToPage(url) {
      if (url.indexOf('http') > -1) {
        this.$toH5(url);
      } else {
        this.$bridge({
          url
        });
      }
    },
    isNotEmpty(value) {
      return value !== null && value !== undefined && value !== '';
    },
    handleClickCard() {
      // uni.showToast({
      //   title: '点击整个卡片'
      // });

      if (this.cardData.card_key === 'check_out') {
        // https://m.soyoung.com/chain/questionnaire?channel_id=230&visit_id={visit_id}&tenant_id={tenant_id}
        return;
      }

      this.handleClickGuide();
      // this.$emit('clickCard', this.showData);
      this.$reportData({
        info: 'sy_wxtuan_tuan_home_new:service_card_click',
        ext: {
          title: this.cardData?.appointment?.subtitle || ''
        }
      });
    },
    handleClickGuide() {
      const url = this.showData.guide_obj.jump_url;

      // uni.showToast({
      //   title: `todo 查看指引,跳转页面: ${url}`
      // });

      this.goToPage(url);

      // this.$emit('clickGuide', this.showData);
    },
    handleClickScanReport() {
      const url = this.showData.scan_report_obj.jump_url;

      uni.showToast({
        title: `todo 查看皮肤检测报告, 跳转页面:${url}`
      });
      this.goToPage(url);
      // this.$emit('clickScanReport', this.showData);
    },
    handleClickToOtherPage(jump_url) {
      uni.showToast({
        title: `jump_url:${jump_url}`
      });
      this.goToPage(jump_url);
    },
    extractAmount(str) {
      const match = str.match(/￥(\d+)/);
      return match ? match[0] : null;
    },
    extractProjectCount(str) {
      const match = str.match(/共(\d+)个项目/);
      return match ? match[0] : null;
    }
  },
  watch: {
    cardData: {
      handler(val) {
        console.log('watch-cardData', val);
        const card_key = val['card_key'];
        this.showData = val[card_key];
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:service_card_exposure',
          ext: {
            title: val?.appointment?.subtitle || ''
          }
        });
      },
      deep: true,
      immediate: true
    }
  }
};
</script>

<template>
  <!-- card_key 区分card -->
  <view
    @click="handleClickCard"
    class="homecard"
    :data-homecard-key="cardData.card_key"
  >
    <view class="title">
      <view class="row gap-5">
        <img class="title-leftIcon" :src="showData.icon" />
        <view class="title-subtitle">{{ showData.title }}</view>
        <view class="title-divider" v-if="showData.subtitle"></view>
        <view class="title-title">{{ showData.subtitle }}</view>
      </view>
      <view @click.stop="handleClickGuide" class="row gap-5">
        <view class="title-right">{{
          showData.guide_obj ? showData.guide_obj.btn_txt : '服务指引'
        }}</view>
        <view class="title-right-arrow"></view>
      </view>
    </view>

    <view class="whiteBlock col gap-7">
      <view class="hospital-name"
        >{{ showData.hospital_obj.hospital_name }}
      </view>
      <!-- 白色区域 Begin -->

      <!-- 咨询 Begin -->
      <view
        v-if="['consultation'].includes(cardData.card_key)"
        class="col gap-7"
      >
        <view
          v-if="showData.add_on_items_obj && showData.add_on_items_obj.desc"
          class="row justContentBetween"
        >
          <view class="reserve-title">本次消费</view>
          <view class="row">
            <view class="consultation-title">
              {{ extractProjectCount(showData.add_on_items_obj.desc) }}
            </view>
            <view
              v-if="extractAmount(showData.add_on_items_obj.desc)"
              class="consultation-title-line"
            >
            </view>
            <view class="consultation-name">
              {{ extractAmount(showData.add_on_items_obj.desc) }}
            </view>
          </view>
        </view>
        <view class="row justContentBetween">
          <view class="consultation-title">咨询师</view>
          <view class="consultation-name"
            >{{ showData.consult_obj.name }}
          </view>
        </view>
      </view>
      <!-- 咨询 End -->

      <!-- 治疗 Begin -->
      <view v-if="['treatment'].includes(cardData.card_key)">
        <view class="row justContentBetween">
          <view class="reserve-title"
            >{{
              showData.notice_obj ? showData.notice_obj.content : '本次治疗项目'
            }}
          </view>
          <view
            v-if="
              showData.service_obj &&
              showData.service_obj.service_item_total > 2
            "
            class="reserve-title"
            >共{{ showData.service_obj.service_item_total }}项
          </view>
        </view>
      </view>
      <!-- 治疗 End-->

      <!-- 术后 Begin -->
      <view
        v-if="['care', 'check_out'].includes(cardData.card_key)"
        class="col gap-7"
      >
        <view v-if="showData.default_txt" class="care-box row reserve-title">
          {{ showData.default_txt }}
        </view>
        <view
          v-if="showData.is_show_review"
          class="care-box-review row justContentBetween"
        >
          <view class="care-box-left">
            <view class="care-box-title">
              <img
                class="care-box-icon"
                src="https://static.soyoung.com/sy-pre/care-1729059000636.png"
              />
              {{ showData.review_obj.title }}</view
            >
            <view class="care-box-subtitle"
              >{{ showData.review_obj.subtitle }}
            </view>
          </view>
          <view
            @click.stop="handleClickToOtherPage(showData.review_obj.jump_url)"
            class="care-btn"
            >{{ showData.review_obj.btn_txt }}
          </view>
        </view>
      </view>
      <!-- 术后 End -->

      <view
        v-if="
          ['appointment', 'check', 'consultation', 'treatment'].includes(
            cardData.card_key
          )
        "
        class="col gap-7"
      >
        <view
          class="row justContentBetween"
          v-if="showData.service_obj && cardData.card_key !== 'treatment'"
        >
          <view class="reserve-title"
            >{{ showData.service_obj.key_name }}
          </view>
          <view
            v-if="showData.service_obj.service_item_total > 2"
            class="reserve-title"
            >共{{ showData.service_obj.service_item_total }}项
          </view>
        </view>
        <view
          v-for="(serviceItem, serviceIndex) in showData.service_obj
            .service_item_list"
          class="reserve-item row justContentBetween"
          :key="serviceIndex"
        >
          <view class="row gap-5">
            <view class="circle"></view>
            <view class="reserve-obj-name"
              >{{
                serviceItem[
                  ['consultation', 'treatment'].includes(cardData.card_key)
                    ? 'treatment_obj'
                    : 'reserve_obj'
                ].name
              }}
            </view>
          </view>
          <view
            v-if="['consultation', 'treatment'].includes(cardData.card_key)"
            class="treatment-obj-quantity row"
          >
            <view class="treatment-obj-quantity-i">x</view>
            <view> {{ serviceItem.treatment_obj.quantity }}</view>
          </view>
          <view v-else class="reserve-obj-time">
            {{ serviceItem.reserve_obj.reserve_start_time_des }}
          </view>
        </view>

        <!-- 皮肤检测 Begin -->
        <view
          v-if="
            ['check'].includes(cardData.card_key) &&
            showData.has_scan_report &&
            showData.scan_report_obj
          "
          @click.stop="handleClickScanReport"
          class="scanReportItem row justContentBetween"
        >
          <view class="row">
            <img
              v-if="isNotEmpty(showData.scan_report_obj.icon)"
              :src="showData.scan_report_obj.icon"
              class="scan-report-icon"
            />
            <view class="scan-report-title">
              {{ showData.scan_report_obj.title }}
            </view>
          </view>
          <view class="scan-report-subtitle">
            {{ showData.scan_report_obj.subtitle }}
          </view>
        </view>
        <!-- 皮肤检测 End -->

        <!-- todo 记得改成2，超过2条展示 -->
        <view
          v-if="
            showData.service_obj && showData.service_obj.service_item_total > 2
          "
          class="row reserve-more"
          @click.stop="handleClickGuide"
        >
          查看全部项目及服务详情，请点击【查看指引】
        </view>
      </view>
      <!-- 白色区域 End -->
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;
@fontColorGreen: #a9ea6a;
@fontColor8c8c8c: #8c8c8c;
@fontColor030303: #030303;
@fontColorf2f2f2: #f2f2f2;

.homecard {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;

  .title {
    width: 100%;
    height: 38 * @px;
    background-color: @fontColorGreen;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 15 * @px;
    box-sizing: border-box;
  }

  .title-leftIcon {
    display: inline-block;
    width: 16 * @px;
    height: 16 * @px;
  }

  .title-subtitle,
  .title-title {
    font-size: 13 * @px;
    color: @fontColor030303;
    font-weight: 400;
    font-family: 'PingFangSC-Medium';
  }

  .title-divider {
    width: 1 * @px;
    height: 12 * @px;
    background-color: @fontColor030303;
  }

  .title-right {
    font-size: 12 * @px;
    color: @fontColor030303;
    font-weight: 400;
    font-family: 'PingFangSC-Regular';
  }

  .title-right-arrow {
    content: '';
    display: inline-block;
    height: 21rpx;
    width: 14rpx;
    background: url(https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png)
      no-repeat center center transparent;
    background-size: contain;
  }

  .whiteBlock {
    width: 100%;
    background-color: #fff;
    padding: 15 * @px;
    box-sizing: border-box;
  }

  .hospital-name {
    font-size: 12 * @px;
    font-family: 'PingFangSC-Medium';
    color: @fontColor030303;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .reserve-title,
  .consultation-title {
    font-size: 11 * @px;
    font-family: 'PingFangSC-Regular';
    color: @fontColor8c8c8c;
    font-weight: 400;
  }

  .consultation-title-line {
    width: 1 * @px;
    height: 11 * @px;
    background-color: @fontColor8c8c8c;
    margin: 0 5 * @px;
  }

  .consultation-name {
    font-family: PingFangSC-Medium;
    font-size: 12 * @px;
    color: @fontColor030303;
    letter-spacing: 0;
    font-weight: 500;
  }

  .circle {
    display: inline-block;
    width: 6 * @px;
    height: 6 * @px;
    background-color: @fontColorGreen;
    border-radius: 50%;
  }

  .reserve-obj-name {
    font-size: 12 * @px;
    font-family: 'PingFangSC-Medium';
    color: @fontColor030303;
    font-weight: 500;
    padding-right: 10 * 2rpx;
    box-sizing: border-box;
  }

  .reserve-obj-time,
  .treatment-obj-quantity {
    font-family: Outfit-Regular;
    font-size: 12 * @px;
    color: @fontColor8c8c8c;
    font-weight: 400;
  }

  .treatment-obj-quantity-i {
    font-family: Outfit-Regular;
    font-size: 8 * @px;
    color: @fontColor8c8c8c;
    font-weight: 400;
  }

  .reserve-more {
    .reserve-title;
  }

  .scanReportItem {
    background-color: @fontColorf2f2f2;
    height: 45 * @px;
    padding: 0 10 * @px;
    margin-top: 10 * @px;
    box-sizing: border-box;
  }

  .scan-report-icon {
    width: 13 * @px;
    height: 13 * @px;
    // background-color: @fontColorGreen;
  }

  .scan-report-title {
    margin-left: 6rpx;
    font-family: PingFangSC-Medium;
    font-size: 12 * @px;
    color: #345f22;
    letter-spacing: 0;
    font-weight: 500;
  }

  .scan-report-subtitle {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: @fontColor8c8c8c;
    font-weight: 400;
  }

  .care-box {
  }

  .care-box-review {
    box-sizing: border-box;
    height: 50 * @px;
    background-color: @fontColorf2f2f2;
    padding: 0 15 * @px;
  }

  .care-box-left {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 7 * @px;
  }

  .care-box-title {
    font-family: PingFangSC-Medium;
    font-size: 12 * @px;
    color: #345f22;
    font-weight: 500;
    .care-box-icon {
      margin-right: 4rpx;
      width: 20rpx;
      height: 20rpx;
    }
  }

  .care-box-subtitle {
    font-family: PingFangSC-Regular;
    font-size: 12 * @px;
    color: #646464;
    font-weight: 400;
  }

  .care-btn {
    border: 1 * @px solid #333333;
    width: 66 * @px;
    height: 30 * @px;
    font-family: PingFangSC-Medium;
    font-size: 12 * @px;
    color: #333333;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
  }
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.justContentBetween {
  justify-content: space-between;
}

.col {
  display: flex;
  flex-direction: column;
}

.gap-5 {
  gap: 5 * @px;
}

.gap-7 {
  gap: 7 * @px;
}
</style>
