<template>
  <div class="home-swiper">
    <div class="home-swiper-list" :style="swiperStyle">
      <div
        v-for="(item, index) in loopSwiperList"
        :key="index"
        class="home-swiper-list-item"
      >
        <span class="list-item__sp mr2">{{ item.classify_name }}</span>
        <!-- <span class="list-item__sp">累计治疗量</span> -->

        <!-- 数字滚动容器 -->
        <div class="number-container">
          <span
            v-for="(digit, idx) in digits(item.count)"
            :key="idx"
            :class="{
              'digit-wrapper': true,
              'digit-wrapper-douhao': digit === ','
            }"
          >
            <!-- 如果存在动画数据，则同时渲染旧值和新值 -->
            <template
              v-if="
                digitAnimations[item.classify_id] &&
                digitAnimations[item.classify_id].digits[idx] !== undefined
              "
            >
              <span
                class="digit old-digit"
                :style="{
                  transform: `translateY(${
                    digitAnimations[item.classify_id].digits[idx].offsetOld
                  }px)`,
                  transition: 'transform 0.5s ease-in-out'
                }"
              >
                {{ digitAnimations[item.classify_id].digits[idx].old }}
              </span>
              <span
                class="digit new-digit"
                :style="{
                  transform: `translateY(${
                    digitAnimations[item.classify_id].digits[idx].offsetNew
                  }px)`,
                  transition: 'transform 0.5s ease-in-out'
                }"
              >
                {{ digitAnimations[item.classify_id].digits[idx].new }}
              </span>
            </template>
            <!-- 否则直接渲染当前数字 -->
            <template v-else>
              <span class="digit">{{ digit }}</span>
            </template>
          </span>
        </div>

        <span class="list-item__sp mlf2">{{ item.uint }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      swiperList: [{ classify_id: 1, count: 0 }],
      currentIndex: 0,
      itemHeight: 18,
      transitionStyle: 'transform 0.5s ease-in-out',
      timer: null,
      pendingUpdate: null,
      digitAnimations: {}
    };
  },
  props: {
    list: {
      type: Object,
      default: () => ({})
    },
    sdata: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    loopSwiperList() {
      if (this.sdata.length > 0) {
        return [...this.swiperList, this.swiperList[0]];
      }
      if (this.sdata.length === 0 && this.list && this.list.classify_id) {
        return [this.list, this.list];
      }
      return [];
    },
    swiperStyle() {
      return `transform: translate3d(0, -${
        this.currentIndex * this.itemHeight
      }px, 0); transition: ${this.transitionStyle};`;
    }
  },
  watch: {
    list: {
      handler(newVal) {
        console.log(newVal, '*** newVal ***', newVal && newVal.classify_id);
        if (newVal && newVal.classify_id) {
          this.pendingUpdate = newVal;

          // 检查该 classify_id 是否在当前 swiperList 中存在
          const existInList = this.swiperList.some(
            (item) => item.classify_id === newVal.classify_id
          );

          // 如果不存在，则添加到 swiperList 中
          if (!existInList) {
            this.swiperList.push({ ...newVal });
            console.log('添加新项到 swiperList:', newVal.classify_id);
          }
        } else {
          this.pendingUpdate = null;
        }
      },
      deep: true,
      immediate: true
    },
    sdata: {
      handler(val) {
        this.swiperList = val;
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.startAutoScroll();
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    // 添加千分位格式化方法
    formatNumberWithCommas(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    // 根据 count 返回数字数组（无动画时）
    digits(val) {
      return this.formatNumberWithCommas(val).split('');
    },
    scrollNext() {
      this.currentIndex++;
      if (this.currentIndex >= this.swiperList.length) {
        setTimeout(() => {
          this.transitionStyle = 'none';
          this.currentIndex = 0;
          this.$nextTick(() => {
            setTimeout(() => {
              this.transitionStyle = 'transform 0.5s ease-in-out';
            }, 50);
          });
        }, 500);
      }
      setTimeout(() => {
        const visibleIndex = this.currentIndex % this.swiperList.length;
        const currentItem = this.loopSwiperList[visibleIndex];
        if (
          this.pendingUpdate &&
          this.pendingUpdate.classify_id === currentItem.classify_id
        ) {
          this.$emit('close');
          console.log(currentItem.count, '<<原值>>', this.pendingUpdate.count);
          // 调用动画方法，传入待累加的值
          this.animateDigits(
            currentItem,
            Math.abs(this.pendingUpdate.count - currentItem.count),
            visibleIndex
          );
        }
      }, 500);

      const Index = this.currentIndex % this.swiperList.length;
      const cItem = this.loopSwiperList[Index];
      this.$reportData({
        info: 'sy_chain_store_home_info:treatment_quantity_show_exposure',
        ext: {
          title: cItem.classify_name,
          content: cItem.count
        }
      });
    },
    animateDigits(item, addCount) {
      const oldCount = item.count;
      const newCount = oldCount + addCount;

      // 使用千分位格式化
      const formattedOldCount = this.formatNumberWithCommas(oldCount);
      const formattedNewCount = this.formatNumberWithCommas(newCount);

      // 计算旧值和新值的最大位数，确保数字长度一致
      const maxLen = Math.max(
        formattedOldCount.length,
        formattedNewCount.length
      );
      const oldDigits = formattedOldCount.padStart(maxLen, ' ').split('');
      const newDigits = formattedNewCount.padStart(maxLen, ' ').split('');

      // 构造每一位的动画数据
      const digits = oldDigits.map((oldDigit, idx) => {
        return {
          old: oldDigit,
          new: newDigits[idx],
          offsetOld: 0, // 旧值初始在原位
          offsetNew: 18 // 新值初始在下方
        };
      });
      this.$set(this.digitAnimations, item.classify_id, { digits });

      // 延迟 500ms 后同时启动旧值上移和新值滚入，避免中间出现空白
      setTimeout(() => {
        this.digitAnimations[item.classify_id].digits.forEach((digit) => {
          if (digit.old !== digit.new) {
            digit.offsetOld = -18; // 旧值上移
            digit.offsetNew = 0; // 新值同时上移到正常位置
          }
        });
        this.$set(this.digitAnimations, item.classify_id, {
          digits: [...this.digitAnimations[item.classify_id].digits]
        });
      }, 500);

      // 动画结束后（0.85s 后）更新真实数据，并清除动画状态
      setTimeout(() => {
        item.count = newCount;
        this.$delete(this.digitAnimations, item.classify_id);
      }, 850);
    },
    startAutoScroll() {
      this.timer = setInterval(() => {
        this.scrollNext();
      }, 4000);
    }
  }
};
</script>

<style lang="scss" scoped>
.home-swiper {
  overflow: hidden;
  height: 18px;
  position: relative;
}
.home-swiper-list {
  height: 100%;
  float: left;
}
.home-swiper-list-item {
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.list-item__sp {
  font-family: DFKingGothicSC16-Medium;
  font-size: 19rpx;
  color: #333333;
  text-align: right;
  font-weight: 500;
}
/* .number-container {
  display: flex;
  font-weight: bold;
  overflow: hidden;
  align-items: center;
} */
.digit-wrapper {
  position: relative;
  height: 18px;
  overflow: hidden;
  width: 12rpx;
}
.digit-wrapper-douhao {
  width: 8rpx;
}
.number-container {
  display: flex;
  font-weight: bold;
  overflow: hidden;
  align-items: center;
  line-height: 18px; /* 确保和 .home-swiper-list-item 高度一致 */
}

.digit {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%); /* 让数字垂直居中 */
  width: 100%;
  height: auto; /* 让高度适应内容 */
  text-align: center;
  //font-size: 28rpx; /* 适当降低字体大小匹配周围文本 */
  font-size: 19rpx;
  color: #ff6c0e;
  font-weight: 400;
  font-family: Outfit-Regular;
}

.old-digit,
.new-digit {
  top: 0;
}
.mlf2 {
  margin-left: 2px;
}
.mr2 {
  margin-right: 2px;
}
</style>
