<template>
  <div class="container" :class="{ skeleton }">
    <div
      class="item"
      v-for="(item, index) in showList"
      :key="index"
      @click="handleClick"
      :style="item.background"
      :data-index="index"
      :class="{
        grown: isOdd && index === length - 1
      }"
    ></div>
  </div>
</template>
<script>
import { throttle } from 'lodash-es';

export default {
  props: {
    skeleton: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  computed: {
    showList() {
      if (this.skeleton) {
        return new Array(4).fill({
          background: 'background: #fff'
        });
      }
      return this.list.map((item) => {
        return {
          ...item,
          background: 'background-image:  url(' + item.material_url + ')'
        };
      });
    },
    length() {
      return this.list.length;
    },
    isOdd() {
      return this.length % 2 === 1;
    }
  },
  methods: {
    handleClick: throttle(
      async function (e) {
        if (this.skeleton) return;
        const { index } = e.currentTarget.dataset;
        const { jump_url, jump_type, program_id } = this.list[index];
        this.$emit('jump', {
          type: jump_type,
          url: jump_url,
          appId: program_id
        });
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:tofu_click',
          ext: {
            serial_num: index + 1,
            url: jump_url
          }
        });
      },
      1000,
      { trailing: false }
    ),
    createReportObserver() {
      const ob = this.createIntersectionObserver({
        thresholds: [0, 1],
        observeAll: true
      });
      ob.relativeToViewport({
        left: 0,
        top: 0,
        bottom: 0,
        right: 0
      });
      ob.observe('.item', (res) => {
        if (res.intersectionRatio === 1) {
          const index = res.dataset.index;
          const b = this.list[index];
          this.$reportData({
            info: 'sy_wxtuan_tuan_home_new:tofu_exposure',
            ext: {
              serial_num: index + 1,
              url: b.jump_url
            }
          });
        }
      });
      this.$once('hook:beforeDestroy', () => {
        ob.disconnect();
      });
    }
  },
  mounted() {
    const unwatch = this.$watch(
      'list',
      function () {
        setTimeout(() => {
          this.createReportObserver();
          unwatch();
        }, 100);
      },
      { deep: true }
    );
  }
};
</script>
<style lang="less" scoped>
.container {
  margin: 0 auto 30rpx;
  width: 345 * 2rpx;
  display: grid;
  // grid-template-rows: repeat(auto-fill, 160rpx);
  grid-template-columns: repeat(2, 345rpx);
  column-gap: 0;
  row-gap: 0;
  .item {
    background-origin: 0 0;
    background-position-y: center;
    background-position-x: right;
    background-size: cover;
    background-repeat: no-repeat;
    height: 160rpx;
    width: 345rpx;
    &:nth-child(2n) {
      background-position-x: left;
    }
  }
  .grown {
    grid-column-start: 1;
    grid-column-end: 3;
  }
}
@skeletonColor: #f2f2f2;
.skeleton .item {
  height: 140rpx;
  margin-bottom: 20rpx;
  width: 325rpx;
  background-color: #fff;
}
</style>
