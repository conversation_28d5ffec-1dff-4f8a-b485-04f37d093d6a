<template>
  <div class="video-box">
    <video
      :controls="false"
      objectFit="contain"
      class="video"
      referrer-policy="no-referrer"
      x5-video-player-type="h5"
      :enable-progress-gesture="false"
      :loop="false"
      :autoplay="false"
      :src="src"
      id="videoElem"
      :muted="muted"
      @play="handlePlay"
      @pause="handlePause"
      @ended="handleEnded"
    ></video>
    <div
      class="play"
      :class="{
        pause: playing
      }"
      @click.stop="togglePlay"
    ></div>
    <div
      class="sound"
      :class="{
        muted: muted
      }"
      @click.stop="toggleMuted"
    ></div>
  </div>
</template>
<script>
export default {
  props: {
    src: {
      type: String,
      default: ''
    },
    poster: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      playing: false,
      muted: true
    };
  },
  mounted() {
    this.context = uni.createVideoContext('videoElem', this);
    this.createObserver();
  },
  methods: {
    handlePlay() {
      this.playing = true;
      console.log('play');
      this.$emit('play');
    },
    handlePause() {
      this.playing = false;
      console.log('pause');
      this.$emit('pause');
    },
    handleEnded() {
      this.playing = false;
      console.log('ended');
      this.$emit('ended');
    },
    togglePlay() {
      if (this.playing) {
        this.context.pause();
      } else {
        this.context.play();
      }
    },
    toggleMuted() {
      this.muted = !this.muted;
    },
    createObserver() {
      const ob = this.createIntersectionObserver({
        thresholds: [0, 1],
        observeAll: true
      });
      ob.relativeToViewport({
        left: 0,
        top: 0,
        bottom: 0,
        right: 0
      });
      ob.observe('.video', (res) => {
        if (res.intersectionRatio > 0) {
          if (this.playing) return;
          this.context.play();
        } else {
          if (!this.playing) return;
          this.context.pause();
        }
      });
      this.$once('hook:beforeDestroy', () => {
        ob.disconnect();
      });
    }
  }
};
</script>
<style lang="less" scoped>
@swiperHeight: 1000rpx;
.video-box {
  width: 100vw;
  height: @swiperHeight;
}
.video {
  width: 100vw;
  height: @swiperHeight;
}
.play {
  position: absolute;
  height: 56rpx;
  width: 56rpx;
  bottom: 50rpx;
  right: 30rpx;
  z-index: 3;
  transform: translateZ(3px);
  background: url(https://static.soyoung.com/sy-pre/play-1727165400636.png)
    no-repeat center center transparent;
  background-size: contain;
}
.pause {
  background: url(https://static.soyoung.com/sy-pre/stop-1727165400636.png)
    no-repeat center center transparent;
  background-size: contain;
}
.sound {
  position: absolute;
  height: 56rpx;
  width: 56rpx;
  bottom: 50rpx;
  right: 116rpx;
  z-index: 3;
  transform: translateZ(3px);
  background: url(https://static.soyoung.com/sy-pre/sound-1727165400636.png)
    no-repeat center center transparent;
  background-size: contain;
}
.muted {
  background: url(https://static.soyoung.com/sy-pre/muted-1727165400636.png)
    no-repeat center center transparent;
  background-size: contain;
}
</style>
