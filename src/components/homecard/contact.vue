<template>
  <div
    class="contact"
    v-if="inf"
    @click="handleClick"
    :style="style"
    :class="{ skeleton }"
  >
    <img v-if="icon" :src="icon" />
    {{ text }}
  </div>
</template>
<script>
import { throttle } from 'lodash-es';
import { mapGetters } from 'vuex';
export default {
  props: {
    skeleton: {
      type: Boolean,
      default: true
    },
    inf: {
      type: Object,
      default: () => null
    }
  },
  computed: {
    ...mapGetters(['isLogin']),
    icon() {
      return this.inf?.icon;
    },
    text() {
      return this.inf?.name || '';
    },
    style() {
      const { top } = uni.getMenuButtonBoundingClientRect();
      return this.skeleton
        ? `top: ${top}px`
        : `background-color: ${this.inf?.bg_color};top:${top}px`;
    }
  },
  methods: {
    // 登录
    async keepSession() {
      // 如果没有登录，先去登录，然后判断是否是 新用户
      if (!this.isLogin) {
        this.loading = true;
        // 校验登录
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        this.loading = false;
        if (!isAuth) return false;
      }
      return true;
    },
    handleClick: throttle(
      async function () {
        if (this.skeleton) return;
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:contact_click',
          ext: {
            url: this.inf.jump_url
          }
        });
        if (!(await this.keepSession())) return;
        // this.$toKefuDialog({
        //   source: 'sy_yx_mini_program_private_msg_customer_service_icon',
        //   unJoinFn: () => {
        //     // 未加C时执行
        //     this.$bridge({
        //       url: this.inf.jump_url.startsWith('/')
        //         ? this.inf.jump_url
        //         : `/${this.inf.jump_url}`
        //     });
        //   }
        // });
        this.$bridge({
          url: this.inf.jump_url.startsWith('/')
            ? this.inf.jump_url
            : `/${this.inf.jump_url}`
        });

        // uni.showModal({
        //   title: '温馨提示',
        //   content: '将为您打开新氧诊所小程序进行咨询',
        //   confirmText: '打开',
        //   cancelText: '取消',
        //   success: (resp) => {
        //     if (resp.confirm) {
        //     }
        //   }
        // });
      },
      1000,
      { trailing: false }
    )
  }
};
</script>
<style lang="less" scoped>
.contact {
  box-sizing: border-box;
  position: fixed;
  padding: 0 20rpx;
  left: 30rpx;
  min-width: 160rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
  transform: translateZ(3px);
  font-size: 20rpx;
  color: #030303;
  letter-spacing: 0;
  font-weight: 500;
  img {
    margin-right: 10rpx;
    width: 32rpx;
    height: 32rpx;
  }
}

@skeletonColor: #fff;
.skeleton {
  background-color: @skeletonColor;
  color: @skeletonColor;
  img {
    opacity: 0;
  }
}
</style>
