<template>
  <div class="container" :class="{ skeleton }">
    <div v-if="hasLocation" @click="handleClick">
      <div class="title">{{ cardTitle }}</div>
      <div class="cover">
        <img :src="cover" mode="aspectFill" v-if="cover" />
      </div>
      <div class="bottom">
        <div class="flex">
          <div class="name">{{ name }}</div>
          <div class="distance" :style="juliStyle">{{ juli }}</div>
          <div class="arrow"></div>
        </div>
        <div class="addr">{{ addr }}</div>
      </div>
    </div>
    <view class="location-tip" v-else>
      <image
        class="left"
        src="https://static.soyoung.com/sy-design/2hca5b5fhk6c31727161750110.png"
      ></image>
      <view class="center">授权地理位置查看附近门店</view>
      <view class="right" @click="handleGetLocationScope">立即授权</view>
    </view>
  </div>
</template>
<script>
import { throttle } from 'lodash-es';
export default {
  name: '',
  props: {
    skeleton: {
      type: Boolean,
      default: true
    },
    inf: {
      type: Object,
      default: () => null
    },
    hasLocation: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {
    cardTitle() {
      return this.inf?.card_title;
    },
    cover() {
      return this.inf?.img?.url || '';
    },
    name() {
      return this.inf?.name;
    },
    addr() {
      return this.inf?.address;
    },
    juli() {
      return this.inf?.juli;
    },
    juliStyle() {
      return this.juli ? `width: ${this.measureText(this.juli)}px;` : '';
    }
  },
  methods: {
    handleClick: throttle(
      function () {
        if (this.skeleton) return;
        this.$bridge({
          url: '/packageHospital/hospital-list'
        });
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:hospital_card_click',
          ext: {
            hospital_id: this.inf?.soyoung_hospital_id || '',
            status: this.hasLocation ? 2 : 1
          }
        });
      },
      1000,
      { trailing: false }
    ),
    // 授权地理定位
    handleGetLocationScope: throttle(
      function () {
        if (this.skeleton) return;
        wx.getSetting({
          success: (res) => {
            const setting = res.authSetting['scope.userLocation'];
            if (setting === undefined) {
              this.getLocation();
            } else {
              wx.openSetting();
            }
          }
        });
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:hospital_card_click',
          ext: {
            hospital_id: this.inf?.soyoung_hospital_id || '',
            status: this.hasLocation ? 2 : 1
          }
        });
      },
      1000,
      { trailing: false }
    ),
    measureText(text) {
      if (!this.offscreen) {
        this.offscreen = wx.createOffscreenCanvas({
          type: '2d'
        });
      }
      const ctx = this.offscreen.getContext('2d');
      ctx.font = '14px/1.2 PingFangSC-Regular';
      return ctx.measureText(text).width + 6;
    },
    createReportObserver() {
      if (this.ob) this.ob.disconnect();
      this.ob = this.createIntersectionObserver({
        thresholds: [0, 1],
        observeAll: true
      });
      this.ob.relativeToViewport({
        left: 0,
        top: 0,
        bottom: 0,
        right: 0
      });
      this.ob.observe('.container', (res) => {
        if (res.intersectionRatio === 1) {
          this.$reportData({
            info: 'sy_wxtuan_tuan_home_new:hospital_card_exposure',
            ext: {
              hospital_id: this.inf?.soyoung_hospital_id || '',
              status: this.hasLocation ? 2 : 1
            }
          });
        }
      });
      this.$once('hook:beforeDestroy', () => {
        this.ob.disconnect();
      });
    }
  },
  mounted() {
    this.$watch(
      'hasLocation',
      function () {
        setTimeout(() => {
          this.createReportObserver();
        }, 100);
      },
      { deep: true, immediate: true }
    );
  }
};
</script>
<style lang="less" scoped>
.container {
  padding: 60rpx 0;
  overflow: hidden;
  .title {
    box-sizing: border-box;
    margin: 0 30rpx 20rpx;
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    color: #030303;
    line-height: 50rpx;
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .cover {
    width: 100vw;
    height: 320rpx;
    vertical-align: top;
    overflow: hidden;
    img {
      width: 100%;
      height: 294rpx;
    }
  }
  .bottom {
    box-sizing: border-box;
    padding: 30rpx 30rpx;
    .flex {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10rpx;
      .name {
        flex: 1;
        margin-right: 40rpx;
        font-family: PingFangSC-Medium;
        font-size: 28rpx;
        color: #030303;
        font-weight: 500;
      }
      .distance {
        font-size: 24rpx;
        color: #030303;
        font-weight: 400;
        text-align: right;
        white-space: nowrap;
      }
      .arrow {
        margin-top: 10rpx;
        margin-left: 10rpx;
        content: '';
        display: inline-block;
        height: 18rpx;
        width: 12rpx;
        background: url(https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png)
          no-repeat center center transparent;
        background-size: contain;
      }
    }
    .addr {
      box-sizing: border-box;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #646464;
      font-weight: 400;
    }
  }

  // 未开启定位提示
  .location-tip {
    height: 120rpx;
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx 30rpx 30rpx 30rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #f2f2f2;
    .left {
      display: inline-block;
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }
    .center {
      flex: 1;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #030303;
      font-weight: 500;
    }
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 144rpx;
      height: 60rpx;
      background-color: #333333;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #fff;
      font-weight: 400;
    }
  }
}

@skeletonColor: #ffffff;
.skeleton {
  .title {
    width: 200rpx;
    background-color: @skeletonColor;
    color: @skeletonColor;
  }
  .cover {
    background: @skeletonColor;
    img {
      opacity: 0;
    }
  }
  .bottom {
    background-color: @skeletonColor;

    .flex {
      .name {
        background-color: #f2f2f2;
        color: #f2f2f2 !important;
        width: 200rpx;
      }
      .distance {
        background-color: #f2f2f2;
        color: #f2f2f2 !important;
      }
    }
    .addr {
      background-color: #f2f2f2;
      color: #f2f2f2 !important;
      width: 400rpx;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .location-tip {
    .left {
      opacity: 0;
    }
    .center {
      background: @skeletonColor;
      color: @skeletonColor;
      line-height: 60rpx;
    }
    .right {
      margin-left: 30rpx;
      background: @skeletonColor;
    }
  }
}
</style>
