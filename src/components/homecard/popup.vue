<!-- 首页弹框 start  -->
<template>
  <root-portal>
    <div class="popup" v-if="visible">
      <div class="popup-mask" @click="handleClose"></div>
      <div class="popup-wrap">
        <div class="popup-img-wrap" v-if="popupType === 'img_index'">
          <image
            class="popup-img"
            :src="popupImage"
            alt=""
            mode="aspectFit"
            @click="handleClick"
          />
        </div>
        <div
          class="popup-img-wrap-level"
          v-if="popupType === 'index_new_level'"
        >
          <image
            class="popup-img"
            :src="popupImage"
            alt=""
            mode="aspectFit"
            @load="showText"
          />
          <div class="popup-main" v-show="textVisible">
            <div
              class="popup-main-title"
              :style="{ color: popupData.page_color || '#333333' }"
            >
              {{ popupData.main_title }}
            </div>
            <div class="popup-main-subtitle-area">
              <div
                class="popup-main-subtitle-animation-area"
                :class="popupData.up_sub_title ? 'animate' : 'static'"
              >
                <div
                  class="popup-main-subtitle animate-1"
                  :style="{ color: popupData.page_color || '#333333' }"
                  v-if="popupData.up_sub_title"
                >
                  {{ popupData.up_sub_title || '' }}
                </div>
                <div
                  class="popup-main-subtitle animate-2"
                  :style="{ color: popupData.page_color || '#333333' }"
                >
                  {{ popupData.sub_title }}
                </div>
              </div>
            </div>
            <div
              class="popup-main-desc"
              :style="{ color: popupData.page_color || '#333333' }"
            >
              {{ popupData.desc }}
            </div>
            <div
              class="popup-main-button"
              @click="handleJump(popupData.button_list[0].link)"
            >
              {{ popupData.button_list[0].name }}
            </div>
          </div>
        </div>
        <div class="popup-close" @click="handleClose"></div>
      </div>
    </div>
  </root-portal>
</template>

<script>
export default {
  name: 'propsGroup',
  data() {
    return {
      queue: [],
      popupData: null,
      popupId: '',
      popupType: '',
      popupImage: '',
      popupWidth: 0,
      popupHeight: 0,
      textVisible: false
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    img: {
      type: String
    }
  },
  methods: {
    sleep(timer = 300) {
      return new Promise((r) => setTimeout(r, timer));
    },
    showText() {
      this.textVisible = true;
    },
    async handleClose() {
      this.$emit('update:visible', false);
      this.reportClose();
      this.sleep();
      this.actionPopup();
    },
    async handleClick() {
      this.$emit('update:visible', false);
      const { link_type: type, link: url, wx_app_id: appId } = this.popupData;
      this.$emit('jump', {
        type,
        url,
        appId
      });
      this.reportClick();
      this.sleep();
      this.actionPopup();
    },
    handleJump(link) {
      if (link.indexOf('.com') > -1 || link.indexOf('http') > -1) {
        this.$toH5(link);
      } else {
        this.$bridge({
          url: link
        });
      }
    },
    async actionPopup() {
      if (this.queue.length === 0) return;
      const indexPopup = this.queue.shift();
      const { id, data, on_click, on_close, on_exposure, type } = indexPopup;
      this.popupData = data;
      this.popupId = id;
      this.popupType = type;
      this.reportParams = {
        on_click,
        on_close,
        on_exposure
      };
      if (data?.img?.u) {
        this.$emit('update:visible', true);
        this.popupImage = data.img.u;
        this.popupWidth = data.img.w;
        this.popupHeight = data.img.h;
        this.reportExposure();
      }
    },
    async fetchPopupData() {
      const res = await this.$request({
        url: '/syGroupBuy/chain/index/popupUp'
      })
        .then((res) => res.data)
        .catch((error) => {
          return {
            errorCode: -100,
            errorMsg: error || '网络错误,稍后再试',
            responseData: {}
          };
        });
      const { errorCode, responseData } = res;
      if (+errorCode === 0 && responseData) {
        this.queue = responseData.list.filter(({ type }) =>
          ['img_index', 'index_new_level'].includes(type)
        );
        this.actionPopup();
      }
    },
    // 上报 曝光
    reportClose() {
      const { key, ex } = this.reportParams.on_close;
      this.$reportData({
        info: key,
        ext: ex
      });
    },
    // 上报 曝光
    reportExposure() {
      const { key, ex } = this.reportParams.on_exposure;
      this.$request({
        url: '/syGroupBuy/chain/index/popupExposure',
        data: {
          id: this.popupId,
          ex_str: JSON.stringify(ex || '')
        }
      })
        .then((res) => res.data)
        .catch((error) => {
          return {
            errorCode: -100,
            errorMsg: error || '网络错误,稍后再试',
            responseData: {}
          };
        });
      this.$reportData({
        info: key,
        ex_str: ex ? JSON.stringify(ex) : ''
      });
    },
    // 上报 点击
    reportClick() {
      const { key, ex } = this.reportParams.on_click;
      this.$request({
        url: '/syGroupBuy/chain/index/popupClick',
        data: {
          id: this.popupId,
          ex_str: ex ? JSON.stringify(ex) : ''
        }
      })
        .then((res) => res.data)
        .catch((error) => {
          return {
            errorCode: -100,
            errorMsg: error || '网络错误,稍后再试',
            responseData: {}
          };
        });

      this.$reportData({
        info: key,
        ext: ex
      });
    }
  },
  created() {
    this.reportParams = {};
    this.fetchPopupData();
  }
};
</script>

<style lang="scss" scoped>
.popup {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100vw;
  z-index: 10000;
  overflow: hidden;
  .popup-mask {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .popup-wrap {
    position: absolute;
    top: calc(50% + 60rpx);
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .popup-img {
    display: block;
    width: 300 * 2rpx;
    height: 423 * 2rpx;
  }
  .popup-img-wrap-level {
    width: 590rpx;
    height: 654rpx;
    overflow: hidden;
    .popup-img {
      position: absolute;
      width: 295 * 2rpx;
      height: 327 * 2rpx;
    }
  }
  .popup-img-wrap {
    border-radius: 20rpx;
    overflow: hidden;
  }

  .popup-main {
    padding: 60rpx 60rpx 40rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    justify-content: space-between;
    position: relative;
    z-index: 1;
    .popup-main-title {
      margin-top: 232rpx;
      font-family: DfKing-Regular;
      font-size: 30rpx;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }
    .popup-main-subtitle-area {
      margin-top: 20rpx;
      overflow-x: hidden;
      overflow-y: auto;
      height: 42rpx;
    }

    .popup-main-subtitle.animate-1 {
      opacity: 1;
    }
    .popup-main-subtitle.animate-2 {
      opacity: 0;
    }
    .animate {
      animation: scrollUp 1s linear 1 forwards;
      animation-delay: 0.5s;
      .animate-1 {
        animation: fadeOut 0.5s forwards;
        animation-delay: 0.5s;
      }
      .animate-2 {
        animation: fadeIn 0.5s forwards;
        animation-delay: 1s;
      }
    }
    .static {
      .animate-2 {
        opacity: 1;
      }
    }
    .popup-main-subtitle {
      font-family: DfKing-Regular;
      font-size: 46rpx;
      line-height: 42rpx;
      color: #030303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
    .popup-main-desc {
      margin-top: 20rpx;
      font-family: DfKing-Regular;
      font-size: 24rpx;
      line-height: 32rpx;
      color: #646464;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      flex: 1;
    }
    .popup-main-button {
      width: 470rpx;
      height: 74rpx;
      line-height: 74rpx;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #a9ea6a;
      text-align: center;
      font-weight: 500;
      background: #333333;
    }
    @keyframes scrollUp {
      0% {
        transform: translateY(0);
      }
      100% {
        transform: translateY(-50%);
      }
    }
    @keyframes fadeOut {
      0% {
        opacity: 1;
      }

      100% {
        opacity: 0;
      }
    }
    @keyframes fadeInO {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }
  }
  .popup-close {
    position: absolute;
    bottom: -104rpx;
    left: 50%;
    height: 64rpx;
    width: 64rpx;
    background: url(https://static.soyoung.com/sy-design/2hca7nc2ykif91735023124258.png)
      no-repeat;
    background-size: contain;
    transform: translateX(-50%);
  }
}
</style>
