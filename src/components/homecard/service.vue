<template>
  <div class="container" :class="{ skeleton }" :style="backgroundStyle">
    <div v-if="Array.isArray(list) && list.length > 0" class="swiper-bg">
      <img class="swiper-bg__img" :src="skuImg.url" alt="" />
      <div class="swiper-bg__info">
        <div v-if="cityStoreConfig.city_num" class="store-badge">
          {{ cityStoreConfig.city_num }}城{{ cityStoreConfig.store_num }}店
        </div>
        <SwiperNumber :list="swiperList" :sdata="list" @close="close" />
      </div>
    </div>
    <div
      :class="{
        row: true,
        pdt40: !Array.isArray(list)
      }"
      :style="{
        justifyContent: 'flex-start'
      }"
      @click="handleLogin"
    >
      <div class="big strong ell skeleton-title" :style="tittleStyle">
        {{ welcome }}
      </div>
      <image
        class="level-icon"
        v-if="inf.level_value && inf.level_value > 0"
        :src="inf.level_icon.u"
        mode="heightFix"
        @click="handleGoToLevel"
      />
      <div
        class="qrcode-entry"
        @click="handleGoToQrCode"
        v-if="inf.show_member_qr_code"
      >
        <image
          class="qrcode-icon"
          :src="
            inf.member_qr_code_url ||
            'https://static.soyoung.com/sy-design/2hca5b5fhk6c31746590518696.png'
          "
          mode="widthFix"
        />
        <span class="qrcode-text">{{
          inf.member_qr_code_name || '会员码'
        }}</span>
      </div>
      <!-- <span></span> -->
      <!-- <div class="mid thin arrow">2 张优惠券</div> -->
    </div>
    <ul class="row items" v-if="iconList.length">
      <li
        v-for="(item, index) in iconList"
        :key="index"
        class="center item"
        :data-index="index"
        @click="handleClick"
      >
        <div class="icon" v-if="item.icon">
          <img :src="item.icon" v-if="item.icon !== 'skeleton'" />
        </div>
        <!-- "[1, 2, 3].includes(item.icon_type)" -->
        <div
          class="hug"
          :style="'color: ' + item.title_color || 'inherit'"
          v-else
        >
          {{ item.number }}
        </div>
        <div
          class="sm thin skeleton-sub-title"
          :style="'color: ' + item.title_color || 'inherit'"
        >
          {{ item.title }}
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
import { checkXyToken } from '@/api/user';
import { throttle } from 'lodash-es';
import { mapGetters, mapState } from 'vuex';
import SwiperNumber from './swiper-number.vue';

export default {
  components: {
    SwiperNumber
  },
  props: {
    skuImg: {
      type: Object,
      default: () => {}
    },
    skeleton: {
      type: Boolean,
      default: true
    },
    inf: {
      type: Object,
      default: () => null
    },
    skuClassifyCount: {
      type: Array,
      default: () => []
    },
    cityStoreConfig: {
      type: Object,
      default: () => ({})
    },
    changeDataObj: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      swiperList: {
        classify_id: 0,
        count: 0
      },
      list: []
    };
  },
  watch: {
    changeDataObj: {
      handler(val) {
        console.log(val, '*** 每次间隔5s更新一次 ***');
        this.swiperList = val;
      },
      immediate: true,
      deep: true
    },
    skuClassifyCount: {
      handler(val) {
        this.list = val;
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {},
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    ...mapGetters(['isLogin']),
    welcome() {
      return this.inf?.title || 'Hello,请登录/注册';
    },
    tittleStyle() {
      return `color:${this.inf?.title_color || 'inherit'};`;
    },
    backgroundStyle() {
      if (this.inf?.bg_img?.material_url)
        return `background-image: url(${this.inf.bg_img.material_url}); `;
      return ``;
    },
    iconList() {
      if (this.skeleton) return new Array(4).fill({ icon: 'skeleton' });
      if (this.inf?.icon_list) return this.inf.icon_list;
      return [];
    }
  },
  methods: {
    handleLogin: throttle(
      async function () {
        if (this.skeleton) return;
        // 检查用户是否有xyToken
        if (this.userInfo.uid && this.userInfo.xyToken) {
          checkXyToken().then((res) => {
            if (res.isValid !== 1) {
              this.$setUserInfoToStorage({
                uid: '',
                xyToken: ''
              });
            }
            this.$emit('login');
          });
        } else {
          this.$emit('login');
        }
      },
      1000,
      { trailing: false }
    ),
    handleGoToLevel() {
      this.$bridge({
        url: '/packageVip/index'
      });
    },
    // 显示二维码
    handleGoToQrCode() {
      this.$bridge({
        url: '/packageVip/qrCode'
      });
    },
    handleClick: throttle(
      async function (e) {
        if (this.skeleton) return;
        const { index } = e.currentTarget.dataset;
        const { jump_url, jump_type, need_login, program_id, title } =
          this.iconList[index];
        this.$emit('jump', {
          type: jump_type,
          url: jump_url,
          appId: program_id,
          auth: need_login
        });
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:service_area_click',
          ext: {
            serial_num: index + 1,
            url: jump_url,
            title
          }
        });
      },
      1000,
      { trailing: false }
    ),
    close() {
      this.swiperList = {
        classify_id: 0,
        count: 1
      };
      this.$emit('close');
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  position: relative;
  margin: -30rpx auto 30rpx;
  width: 345 * 2rpx;
  box-sizing: border-box;
  padding: 0rpx 0 50rpx;
  background-color: #fff;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 1;
  transform: translateY(1px);

  .swiper-bg {
    position: relative;
    width: 100%;
    height: 88rpx;
    margin-bottom: 40rpx;
    background: #f0f0f0;
    padding: 26rpx 24rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    &__img {
      width: 230rpx !important;
      height: 100rpx;
      margin-left: 6rpx;
      object-fit: contain; // 防止图片因为宽高比例不一样被拉伸
    }
    &__info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-end;
      .store-badge {
        display: flex;
        align-items: center;
        background: #333333;
        color: #ffffff;
        padding: 0 6rpx;
        font-family: Outfit-Regular;
        font-size: 16rpx;
        font-weight: 400;
      }
    }
  }
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #030303;
    padding: 0 30rpx;
    box-sizing: border-box;
  }

  .items {
    margin-top: 50rpx;
    .item {
      width: 25%;
    }
  }
  .arrow::after {
    content: '';
    margin-left: 10rpx;
    display: inline-block;
    height: 18rpx;
    width: 12rpx;
    background: url(https://static.soyoung.com/sy-design/6eboz32amcrz1726715722203.png)
      no-repeat center center transparent;
    background-size: contain;
  }
  .level-icon {
    height: 30rpx;
    margin-left: 10rpx;
    flex-shrink: 0;
  }
  .qrcode-entry {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;
    top: 118rpx;
    right: 30rpx;
    .qrcode-icon {
      width: 36rpx;
      height: 36rpx;
    }
    .qrcode-text {
      font-family: PingFangSC-Regular;
      font-size: 16rpx;
      color: #030303;
      text-align: center;
      font-weight: 400;
    }
  }
  .center {
    text-align: center;
  }
  .strong {
    font-weight: 500;
  }
  .hug {
    font-family: OutFit-Regular;
    font-size: 48rpx;
    font-weight: 500;
  }
  .big {
    font-size: 36rpx;
  }
  .ell {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .mid {
    font-size: 24rpx;
  }
  .sm {
    font-size: 20rpx;
  }
  .fat {
    font-family: PingFangSC-Medium;
    font-weight: 500;
  }
  .thin {
    font-family: PingFangSC-Regular;
    font-weight: 400;
  }
  .icon {
    display: inline-block;
    width: 60rpx;
    height: 60rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.pdt40 {
  padding-top: 40rpx !important;
}
@skeletonColor: #f2f2f2;
.skeleton {
  .skeleton-title {
    width: 200rpx;
    color: @skeletonColor!important;
    background-color: @skeletonColor;
    overflow: hidden;
  }
  .icon {
    background-color: @skeletonColor;
  }
  .items .item {
    width: 100rpx;
  }
  .skeleton-sub-title {
    color: @skeletonColor;
    background-color: @skeletonColor;
  }
}
</style>
