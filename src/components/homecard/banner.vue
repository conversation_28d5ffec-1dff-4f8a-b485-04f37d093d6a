<template>
  <div class="banner">
    <swiper
      v-if="hasIndicator"
      class="swiper-wrapper"
      circular
      :interval="interval"
      :autoplay="autoPlay"
      @change="handleSwiperChange"
    >
      <swiper-item
        class="swiper-item-wraper"
        v-for="(banner, index) in list"
        :key="index"
      >
        <div
          v-if="banner.material_type === 2"
          class="swiper-item"
          :data-index="index"
        >
          <BannerVideo
            :src="banner.material_url"
            :poster="banner.material_url"
            @play="stopSwiper"
            @pause="startSwiper"
            @ended="startSwiperImediate"
          />
        </div>
        <div
          v-else
          class="swiper-item"
          :data-index="index"
          @click="handleClick"
        >
          <img
            class="image"
            :lazy-load="index == 0"
            :src="banner.material_url"
            mode="aspectFill"
          />
        </div>
      </swiper-item>
    </swiper>
    <!-- 只有一个的时候 -->
    <div class="swiper-wrapper" v-else-if="hasBanner">
      <div
        class="swiper-item-wraper"
        v-for="(banner, index) in list"
        :key="index"
      >
        <div
          v-if="banner.material_type === 2"
          class="swiper-item"
          :data-index="index"
        >
          <BannerVideo
            :src="banner.material_url"
            @play="stopSwiper"
            @pause="startSwiper"
            @ended="startSwiperImediate"
          />
        </div>
        <div
          v-else
          class="swiper-item"
          :data-index="index"
          @click="handleClick"
        >
          <img
            class="image"
            :lazy-load="index == 0"
            :src="banner.material_url"
            mode="aspectFill"
          />
        </div>
      </div>
    </div>
    <!-- indicator -->
    <div class="swiper-slider" v-if="hasIndicator">
      <div class="slider-cur" :style="swiperSliderStyle"></div>
    </div>
  </div>
</template>
<script>
import BannerVideo from './banner-video.vue';
import { throttle } from 'lodash-es';
export default {
  name: 'banner',
  components: {
    BannerVideo
  },
  props: {
    skeleton: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      autoPlay: true,
      currentIndex: 0,
      interval: 3000
    };
  },
  computed: {
    hasBanner() {
      if (this.skeleton) return false;
      return this.list?.length > 0;
    },
    hasIndicator() {
      if (this.skeleton) return false;
      return this.list?.length > 1;
    },
    swiperSliderStyle() {
      const len = this.list?.length;
      return len > 0
        ? `width: ${100 / len}%;transform: translateX(${
            100 * this.currentIndex
          }%);`
        : '';
    }
  },
  methods: {
    handleSwiperChange({ detail }) {
      this.currentIndex = detail.current;
    },
    stopSwiper() {
      this.autoPlay = false;
    },
    startSwiper() {
      this.autoPlay = true;
    },
    startSwiperImediate() {
      this.autoPlay = true;
    },
    handleClick: throttle(
      async function (e) {
        if (this.skeleton) return;
        const { index } = e.currentTarget.dataset;
        const { jump_url, jump_type, program_id } = this.list[index];
        this.$emit('jump', {
          type: jump_type,
          url: jump_url,
          appId: program_id
        });
        this.$reportData({
          info: 'sy_wxtuan_tuan_home_new:banner_click',
          ext: {
            serial_num: index + 1,
            url: jump_url
          }
        });
      },
      1000,
      { trailing: false }
    ),
    createReportObserver() {
      const ob = this.createIntersectionObserver({
        thresholds: [0, 1],
        observeAll: true
      });
      ob.relativeToViewport({
        left: 0,
        top: 0,
        bottom: 0,
        right: 0
      });
      ob.observe('.swiper-item', (res) => {
        if (res.intersectionRatio === 1) {
          const index = res.dataset.index;
          const b = this.list[index];
          this.$reportData({
            info: 'sy_wxtuan_tuan_home_new:banner_exposure',
            ext: {
              serial_num: index + 1,
              url: b.jump_url
            }
          });
        }
      });
      this.$once('hook:beforeDestroy', () => {
        ob.disconnect();
      });
    }
  },
  mounted() {
    const unwatch = this.$watch(
      'list',
      function () {
        setTimeout(() => {
          this.createReportObserver();
          unwatch();
        }, 100);
      },
      { deep: true }
    );
  }
};
</script>
<style lang="less" scoped>
@swiperHeight: 1000rpx;

.banner {
  position: relative;
  height: @swiperHeight;
  background-color: #f2f2f2;
  .swiper-wrapper {
    height: @swiperHeight;
    .image {
      width: 100vw;
      height: @swiperHeight;
    }
  }
  .swiper-slider {
    position: absolute;
    left: 30rpx;
    bottom: 50rpx;
    height: 4rpx;
    width: 120rpx;
    background: #f8f8f8;
    opacity: 0.9;
    overflow: hidden;
    z-index: 1;
    .slider-cur {
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 100%;
      opacity: 0.9;
      background: #030303;
      transition: transform 0.3s;
      z-index: 1;
    }
  }
}
</style>
