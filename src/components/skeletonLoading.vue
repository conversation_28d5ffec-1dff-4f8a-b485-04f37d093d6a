<template>
  <div class="modal-loading" :class="{ 'modal-loading-in': inType == 1 }"></div>
</template>

<script>
export default {
  props: {
    inType: Number
  },
  data() {
    return {};
  }
};
</script>

<style lang="less" scoped>
.modal-loading {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 40%,
      rgba(255, 255, 255, 0.5) 50%,
      rgba(255, 255, 255, 0) 60%
    )
    rgba(255, 255, 255, 0);
  background-size: 200% 100%;
  background-position-x: 180%;
  animation: 1.1s loading ease-in-out infinite;
}
.modal-loading-in {
  position: absolute;
}
@keyframes loading {
  to {
    background-position-x: -20%;
  }
}
</style>
