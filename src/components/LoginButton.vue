<template>
  <div @tap="loginHandler">
    <slot></slot>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      isloading: false
    };
  },
  computed: {
    ...mapGetters(['isLogin', 'hasWxAvatar'])
  },
  methods: {
    async loginHandler() {
      // 未登录
      if (!this.isLogin && !this.isloading) {
        // 没有微信头像调起授权
        if (!this.hasWxAvatar) {
          try {
            const res = await this.wxProfile();
            await this.$uniLogin();
            this.$setUserInfoToStorage({
              nickName: res.userInfo.nickName,
              avatarUrl: res.userInfo.avatarUrl
            });
          } catch (error) {
            console.log(error, '获取用户信息失败');
            uni.showModal({
              content: '获取用户信息失败',
              showCancel: false
            });
            return;
          }
        }
        this.isloading = true;
        const res = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return false;
        });
        this.isloading = false;
        if (res) {
          this.$emit('after');
        }
      } else {
        this.$emit('after');
      }
    },
    wxProfile() {
      return new Promise((resole, reject) => {
        wx.getUserProfile({
          lang: 'zn_CN',
          desc: '用于完善信息',
          success: (res) => {
            console.log(res, 'profile');
            resole(res);
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.login-view {
  // line-height: 0;
}
</style>
