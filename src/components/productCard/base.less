.yuan {
  font-size: 10px;
  color: #61b43e;
  font-weight: 500;
  align-self: baseline;
  margin-right: 1px;
}
.price {
  color: #61b43e;
  font-weight: 500;
  font-size: 17px;
  align-self: baseline;
  margin-right: 2px;
}
.priceText1 {
  font-family: PingFangSC-Semibold;
  font-size: 10px;
  color: #8c8c8c;
  font-weight: 600;
  align-self: baseline;
}
.priceRow {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex: 1;
}
.priceTextDeleteLine {
  font-size: 11px;
  font-weight: 400;
  color: #bababa;
  text-decoration: line-through;
  align-items: baseline;
}
.marginBottom2 {
  margin-bottom: 4rpx;
}
.zixunButton {
  background-color: #61b43e;
  color: #ffffff;
  font-size: 10px;
  height: 20px;
  width: 57px;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
}
