<template>
  <div class="product-card" @click="goProduct">
    <div class="product-card__identify">
      <img
        mode="heightFix"
        v-if="product.sku_label.group_buy_chain_sku_label.op_label.img_url"
        :src="product.sku_label.group_buy_chain_sku_label.op_label.img_url"
        alt=""
      />
    </div>
    <div class="product-card-img">
      <img
        :src="
          product.sku_info.img_url ||
          'https://static.soyoung.com/sy-pre/30408he0fth8c-1727082600620.jpg'
        "
        alt="sku图片"
      />
    </div>
    <div class="product-card-content">
      <div class="product-card-content-title">
        <span
          :style="{
            maxWidth: product.sku_label.group_buy_chain_sku_label.title_label
              .img_url
              ? (215 - 30) * 2 + 'rpx'
              : '450rpx'
          }"
          >{{ product.sku_info.title }}</span
        >
        <img
          mode="aspectFit"
          v-if="product.sku_label.group_buy_chain_sku_label.title_label.img_url"
          :src="product.sku_label.group_buy_chain_sku_label.title_label.img_url"
          alt=""
        />
      </div>
      <div
        class="product-card-content-description"
        :style="{ maxWidth: (maxWidth + 16) * 2 + 'rpx' }"
      >
        {{ product.sku_info.chain_promote_info || '' }}
      </div>
      <div class="lineBox">
        <div
          v-if="product.sku_stat.sold_cnt_desc"
          class="product-card-content-sale"
        >
          {{ product.sku_stat.sold_cnt_desc }}
        </div>
        <div
          v-if="product.sku_label.pain_tag_info"
          class="painInfoBox"
          :style="{
            color: product.sku_label.pain_tag_info.text_color,
            backgroundColor: product.sku_label.pain_tag_info.background_color
          }"
        >
          <image
            class="painIcon"
            :src="product.sku_label.pain_tag_info.icon"
          ></image>
          <div class="text">
            {{ product.sku_label.pain_tag_info.name }}
          </div>
        </div>
      </div>

      <div
        class="product-card-content-price"
        :style="{
          alignItems:
            product.price_info.price_position.chain_price_position
              .best_price_text === '????'
              ? 'center'
              : 'baseline'
        }"
      >
        <img
          class="product-card-content-price__identify"
          v-if="
            product.price_info.price_position !== null &&
            product.price_info.price_position.chain_price_position !== null
          "
          src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1727580418784.png"
          style="width: 16rpx; height: 16rpx"
          alt=""
        />
        <span
          v-if="
            product.price_info.price_position !== null &&
            product.price_info.price_position.chain_price_position !== null
          "
          class="product-card-content-price__money"
          >{{
            product.price_info.price_position.chain_price_position
              .best_price_text
          }}</span
        >
        <span
          v-if="
            product.price_info.price_position !== null &&
            product.price_info.price_position.chain_price_position !== null
          "
          class="product-card-content-price__description"
          >{{
            product.price_info.price_position.chain_price_position
              .best_price_desc
          }}</span
        >
        <span
          v-if="
            product.price_info.price_position !== null &&
            product.price_info.price_position.chain_price_position !== null &&
            product.price_info.price_position.chain_price_position
              .original_price_text !== ''
          "
          class="product-card-content-price__originPrice"
          >￥{{
            product.price_info.price_position.chain_price_position
              .original_price_text
          }}</span
        >
        <img
          mode="heightFix"
          v-if="
            product.price_info.price_position.chain_price_position
              .sku_price_breaking.price_text
          "
          :src="
            product.price_info.price_position.chain_price_position
              .sku_price_breaking.list_price_tag_icon
          "
          alt=""
        />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    product: Object,
    cIndex: Number,
    type: String,
    keyWord: String
  },
  data() {
    return {
      useLeftImage:
        'https://static.soyoung.com/sy-pre/20240906-155023-1725606600646.png',
      maxWidth: 0
    };
  },
  mounted() {
    uni.getSystemInfo({
      success: (res) => {
        this.maxWidth = res.screenWidth - 180;
      }
    });
    this.registerExposure();
  },
  methods: {
    goProduct() {
      this.$reportData({
        info: 'sy_chain_store_s_search_result:feed_result_click',
        ext: {
          search_word: this.keyWord,
          zt_item_type: 3,
          zt_item_id: this.product.sku_id,
          serial_num: this.cIndex + 1,
          exposure_ext: this.product.ext
        }
      });
      uni.navigateTo({
        url: `/pages/product?id=${this.product.sku_info.sku_id}`
      });
    },
    registerExposure() {
      this.$registerExposure(
        '.package-search-result-page-scrollView',
        () => {
          this.$reportData({
            info: 'sy_chain_store_s_search_result:feed_result_exposure',
            ext: {
              search_word: this.keyWord,
              zt_item_type: 3,
              zt_item_id: this.product.sku_id,
              serial_num: this.cIndex + 1,
              exposure_ext: this.product.ext
            }
          });
        },
        this
      );
      this.$registerExposure(
        '.product-card',
        () => {
          this.$reportData({
            info: 'sy_chain_store_s_search_result:feed_result_exposure',
            ext: {
              search_word: this.keyWord,
              zt_item_type: 3,
              zt_item_id: this.product.sku_id,
              serial_num: this.cIndex + 1,
              exposure_ext: this.product.ext
            }
          });
        },
        this
      );
    }
  }
};
</script>
<style lang="less" scoped>
@import './base.less';
.product-card {
  width: 100%;
  display: flex;
  flex-direction: row;
  position: relative;
  margin-bottom: 40rpx;
  &__identify {
    position: absolute;
    left: 0;
    top: 0;
    height: 28rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    img {
      //width: 80rpx;
      height: 28rpx;
    }
  }
  &-img {
    width: 200rpx;
    height: 150rpx;
    img {
      width: 200rpx;
      height: 100%;
      object-fit: contain;
    }
  }
  &-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-left: 20rpx;
    &-title {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      span {
        display: block;
        font-family: PingFangSC-Medium;
        font-size: 28rpx;
        color: #030303;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      img {
        //width: 60rpx;
        max-width: 96rpx;
        height: 28rpx;
        margin-left: 10rpx;
      }
    }
    &-description {
      flex: 1;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #999999;
      font-weight: 400;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 4rpx 0;
      box-sizing: border-box;
    }
    &-sale {
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #646464;
      font-weight: 400;
    }
    &-price {
      width: 100%;
      display: flex;
      align-items: baseline;
      justify-content: flex-start;
      &__identify {
        font-size: 20rpx;
        color: #61b43e;
        font-weight: 500;
        margin-right: 2rpx;
        align-self: baseline;
      }
      &__money {
        font-family: OutFit-Regular;
        font-size: 34rpx;
        color: #61b43e;
        line-height: 42rpx;
        font-weight: 500;
        margin-right: 6rpx;
        align-self: baseline;
      }
      &__description {
        font-family: PingFangSC-Semibold;
        font-size: 20rpx;
        color: #8c8c8c;
        font-weight: 600;
        margin-right: 4rpx;
        align-self: baseline;
      }
      &__originPrice {
        font-size: 20rpx;
        font-weight: 400;
        color: #bababa;
        text-decoration: line-through;
        align-items: baseline;
        margin-left: 4rpx;
        align-self: baseline;
      }
      img {
        height: 40rpx;
      }
    }
  }
}

.lineBox {
  display: flex;
  align-items: center;
  margin-bottom: 3rpx;
}
.painInfoBox {
  height: 28rpx;
  display: flex;
  align-items: center;
  padding: 0 6rpx;
  font-family: PingFangSC-Regular;
  font-size: 20rpx;
  color: #373a36;
  margin-left: 16rpx;
  .painIcon {
    width: 20rpx;
    height: 20rpx;
    margin-right: 4rpx;
  }
}
</style>
