<template>
  <div class="points-card">
    <div class="line line1">
      <div class="left">{{ cardData.remark }}</div>
      <div
        class="right"
        :class="{
          'add': cardData.in_or_out == 1,
          'reduce': cardData.in_or_out == 2
        }"
      >
        {{ cardData.in_or_out == 1 ? '+' : '-' }}{{ cardData.change }}
      </div>
    </div>
    <div class="line line2">
      <div class="left">{{ dayFormat(cardData.create_date) }}</div>
      <div class="right">剩余：{{ cardData.after }}</div>
    </div>
  </div>
</template>
<script>
import dayjs from '@/common/dayjs.min';
export default {
  props: {
    cardData: Object
  },
  methods: {
    dayFormat(time) {
      return dayjs(time).format('YYYY.MM.DD HH:mm');
    }
  }
};
</script>
<style lang="less" scoped>
.points-card {
  width: 100%;
  padding: 30rpx 0;
  .line {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .line1 {
    margin-bottom: 16rpx;
    .left {
      font-family: PingFangSC-Medium;
      font-size: 28rpx;
      color: #222222;
      letter-spacing: 0;
      font-weight: 500;
    }
    .right {
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      letter-spacing: 0;
      text-align: right;
      font-weight: 600;
      &.add {
        color: #be8e4e;
      }
      &.reduce {
        color: #e3585e;
      }
    }
  }
  .line2 {
    .left {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #999999;
      letter-spacing: 0;
      font-weight: 400;
    }
    .right {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #999999;
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
    }
  }
}
</style>
