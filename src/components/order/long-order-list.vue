<template>
  <div class="long-order-list">
    <!-- 列表前自定义插槽 -->
    <slot name="before" />
    <div
      v-for="(listItem, groupIndex) in viewList"
      :id="'wrp_' + groupIndex"
      :key="groupIndex"
    >
      <div v-for="(item, index) in listItem" :key="index">
        <template v-if="item.listViewItemHeight">
          <!-- 不在可视范围内 -->
          <div
            class="out-view"
            :style="{ 'height': item.listViewItemHeight + 'px' }"
          >
            <div class="skeleton">
              <div class="top">
                <div class="left grey-bg"></div>
                <div class="right">
                  <div class="line1">
                    <div class="title grey-bg"></div>
                    <div class="title grey-bg"></div>
                  </div>
                  <div class="line2">
                    <div class="sku grey-bg"></div>
                    <div class="price grey-bg"></div>
                  </div>
                </div>
              </div>
              <div class="bottom grey-bg"></div>
            </div>
          </div>
        </template>
        <template v-else>
          <!-- 在可视范围内 -->
          <OrderCard
            :order="item"
            :key="item.order_id"
            :index="index"
            :loading="loading"
            @noscroll="noscroll"
            @getData="getData"
            @clickButton="clickButton"
            @toDetail="toDetail"
            @setShareInfo="setShareInfo"
          ></OrderCard>
        </template>
      </div>
    </div>
    <!-- 列表后自定义插槽 -->
    <slot name="after" />
  </div>
</template>
<script>
import { mapState } from 'vuex';
import OrderCard from '@/components/order/order-card.vue';
let intersectionObserver;
export default {
  components: {
    OrderCard
  },
  props: {
    list: Array,
    loading: Boolean
  },
  data() {
    return {
      renderPendingQueue: [], // 待渲染任务队列
      isRenderTask: false, // 正在渲染
      wholeList: [], // 所有数据
      viewList: [], // 待渲染的视图列表
      winHeight: 0, // 手机屏幕高度
      groupIndex: 0, // 分组索引
      groupHeightArr: [] // 记录分组高度
    };
  },
  computed: {
    ...mapState({
      systemInfo: (state) => state.global.systemInfo
    })
  },
  watch: {
    // 监听list的变化
    list: {
      handler(val, preVal = []) {
        if (!val.length) return;
        if (val.length > preVal.length) {
          const cloneVal = val.slice();
          // 找出增量传入的 list，删除多余的，只保留新增的
          cloneVal.splice(0, preVal.length);
          // 创建一个渲染任务
          const task = () => {
            // setList 为真正需要执行的渲染列表的方法
            this.setList(cloneVal);
          };
          // 把渲染任务放入待渲染队列中
          this.renderPendingQueue.push(task);
          // 开始执行渲染任务
          this.runRunderTask();
        } else if (val.length < preVal.length) {
          // 列表数量减少，更新list所有数据
          const cloneVal = val.slice();
          this.renderPendingQueue = [];
          this.isRenderTask = false;
          this.wholeList = [];
          this.viewList = [];
          this.groupIndex = 0;
          this.groupHeightArr = [];
          // 创建一个渲染任务
          const task = () => {
            // setList 为真正需要执行的渲染列表的方法
            this.setList(cloneVal);
          };
          // 把渲染任务放入待渲染队列中
          this.renderPendingQueue.push(task);
          // 开始执行渲染任务
          this.runRunderTask();
        } else {
          // 如果列表没更新长度，说明是需要更新现有数据的显示内容
          this.refreshNowList();
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 获取屏幕的高度
    this.winHeight = this.systemInfo.windowHeight;
    // 保存所有数据
    // this.wholeList = [];
    // 记录分组高度
    // this.groupHeightArr = [];
  },
  beforeDestroy() {
    intersectionObserver.disconnect();
  },
  methods: {
    // 装载数据，渲染列表
    setList(val) {
      const groupIndex = this.groupIndex;
      const newList = this.viewList;
      // 把数据保存到 wholeList 中
      this.wholeList[groupIndex] = val;
      // 插入到视图列表中
      newList[groupIndex] = val;
      this.groupIndex++;
      // 直接渲染最新加入的数据
      this.$set(this, 'viewList', newList);
      this.$nextTick(() => {
        // 记录渲染后的视图高度
        console.log('看下此时的viewList', this.viewList);
        this.setHeight(groupIndex);
      });
    },
    // 更新待渲染队列，执行渲染任务
    runRunderTask() {
      // 如果没有渲染任务了，或者渲染任务已开始，则直接返回
      if (this.renderPendingQueue.length === 0 || this.isRenderTask) {
        return;
      }
      // 取出第一个渲染任务
      const current = this.renderPendingQueue.shift();
      // 开始渲染
      this.isRenderTask = true;
      // 执行渲染任务
      typeof current === 'function' && current();
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    // 记录视图高度
    setHeight(groupIndex) {
      const that = this;
      const query = uni.createSelectorQuery().in(this);
      query &&
        query
          .select(`#wrp_${groupIndex}`)
          .boundingClientRect((res) => {
            // 记录分组的高度
            that.groupHeightArr[groupIndex] = (res && res.height) || 100;
          })
          .exec();

      // console.log(`开始监听分组 ${groupIndex} 变化`);
      // 开始监听分组变化
      this.observePage(groupIndex);
    },
    // 观察页面变化
    observePage(groupIndex) {
      intersectionObserver = this.createIntersectionObserver();
      // 当 transition div 块从全部相交，到从视口消失。
      // 这里规定的有效区域为两个屏幕
      intersectionObserver.relativeToViewport({
        top: 2 * this.winHeight,
        bottom: 2 * this.winHeihgt
      });
      intersectionObserver.observe(`#wrp_${groupIndex}`, (res) => {
        const newList = this.viewList;
        const nowWholeList = this.wholeList[groupIndex] || [];
        // 目标元素不可见，切换成虚拟节点占位
        if (res.intersectionRatio === 0) {
          // 如果不在有效的视图范围内，那么不需要渲染真实的数据，只需要计算高度，进行占位就可以了
          const listViewHeightArr = [];
          let listViewItemHeight = 0;
          if (this.groupHeightArr[groupIndex]) {
            listViewItemHeight =
              this.groupHeightArr[groupIndex] / nowWholeList.length;
          } else {
            listViewItemHeight = 100;
          }

          for (let i = 0; i < nowWholeList.length; i++) {
            listViewHeightArr.push({ listViewItemHeight });
          }
          newList[groupIndex] = listViewHeightArr;
        } else {
          // 如果在有效的区域内，那么直接渲染真实的数据
          if (this.wholeList && this.wholeList[groupIndex]) {
            newList[groupIndex] = this.wholeList[groupIndex];
          }
        }
        this.$set(this, 'viewList', newList);
        this.$nextTick(() => {
          this.isRenderTask = false;
          // 渲染下一个更新任务
          this.runRunderTask();
          this.$forceUpdate();
        });
      });
    },
    // list数据更新，长度没有增加，只更新缓存的订单数据
    refreshNowList() {
      const cloneList = this.list.slice();
      for (let i = 0; i < this.wholeList.length; i++) {
        // 找出 list 中对应
        const num = this.wholeList[i].length;
        const list = [];
        for (let j = 0; j < num; j++) {
          const jnum = 10 * i + j;
          list.push(cloneList[jnum]);
        }
        this.wholeList[i].splice(0, num, ...list);
      }
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    setShareInfo(share_info) {
      this.$emit('setShareInfo', share_info);
    },
    getData(data) {
      this.$emit('getData', data);
    },
    noscroll(data) {
      this.$emit('noscroll', data);
    },
    clickButton(data) {
      this.$emit('clickButton', data);
    },
    toDetail(data) {
      this.$emit('toDetail', data);
    }
  }
};
</script>
<style lang="less" scoped>
.long-order-list {
  box-sizing: border-box;
  .out-view {
    overflow: hidden;
    width: 100%;
    .grey-bg {
      background: #e6e6e6;
    }
    .skeleton {
      width: 100%;
      height: 100%;
      padding: 20rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      .top {
        width: 100%;
        height: 200rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        justify-content: flex-start;
        .left {
          width: 180rpx;
          height: 180rpx;
          margin-right: 20rpx;
        }
        .right {
          height: 100%;
          flex: 1;
          .line1 {
            width: 100%;
            .title {
              height: 40rpx;
              width: 100%;
              margin-bottom: 20rpx;
            }
          }
          .line2 {
            margin-top: 40rpx;
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .sku {
              height: 40rpx;
              width: 75%;
            }
            .price {
              height: 40rpx;
              width: 20%;
            }
          }
        }
      }
      .bottom {
        flex: 1;
        width: 100%;
      }
    }
  }
}
</style>
