<template>
  <div class="tab-container-out">
    <div
      class="tab-container"
      :style="{
        top: '-8px',
        position: 'fixed',
        padding: padding || 0,
        'border-radius': borderRadius || ''
      }"
    >
      <!-- position: ceiling ? 'sticky' : '',
			position: ceiling ? '-webkit-sticky' : '', -->
      <div
        v-for="(item, index) in tabList"
        :key="item.value"
        @click="tabChange(item, index)"
        class="tab-item"
        :class="{ 'tab-focus': item.value == tabFocus }"
      >
        <span :style="{ 'font-size': fontSize || '36rpx' }">
          {{ item.label }}
        </span>
        <img v-if="item.icon" class="tab-icon" :src="item.icon" alt="" />
      </div>
      <div
        class="tab-slide"
        v-show="visible"
        :style="{
          transform: `translateX(${left}px)`
        }"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabFocus: 0,
      navStatusBarHeight: 0,
      left: 0,
      visible: false
    };
  },
  props: {
    scrollTop: Number,
    top: Number,
    padding: String,
    borderRadius: String,
    fontSize: String,
    defineNavBar: Boolean,
    tabList: Array,
    ceiling: Boolean,
    noContCeil: Boolean,
    defaultValue: String || Number
  },
  watch: {
    defaultValue: {
      handler() {
        this.tabFocus = this.defaultValue;
        this.setSlideLeft();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    tabChange(item, index) {
      this.tabFocus = item.value;
      this.setSlideLeft();
      this.$emit('tabChange', {
        item,
        index
      });
    },
    // 设置滑块的左侧距离
    setSlideLeft() {
      const that = this;
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll('.tab-item')
        .boundingClientRect((rects) => {
          const index = that.tabList.findIndex(
            (tab) => tab.value === +that.tabFocus
          );
          const { left, width } = rects[index];
          that.left = left + width / 2;
          that.visible = true;
        })
        .exec();
    },
    setTopSim() {
      this.navStatusBarHeight = -8;
    },
    setTop() {
      const systemInfo = wx.getSystemInfoSync();
      // 状态栏高度
      const statusBarHeight = Number(systemInfo.statusBarHeight);
      const menu = wx.getMenuButtonBoundingClientRect();
      // 状态栏加导航栏高度
      let navStatusBarHeight = statusBarHeight + menu.height;
      if (!this.defineNavBar && menu) {
        navStatusBarHeight += (menu.top - statusBarHeight) * 2;
      }
      const plus = 0;
      // if (systemInfo.system.indexOf('iOS') > -1) {
      //   plus = 2
      // }
      console.log('navStatusBarHeight', navStatusBarHeight);
      this.navStatusBarHeight = navStatusBarHeight + plus;
      this.$emit('setNavHeight', this.navStatusBarHeight);
    }
  },
  mounted() {
    console.log(this.fontSize, 'this.fontSize');
    if (this.defaultValue) {
      this.tabFocus = this.defaultValue;
    } else {
      this.tabFocus = this.tabList[0].value;
    }
    this.setSlideLeft();
    if (this.ceiling && !this.noContCeil) {
      this.setTop();
    } else {
      this.setTopSim();
    }
  }
};
</script>

<style lang="less" scoped>
.tab-container-out {
  width: 100%;
  height: 108rpx;
}
.tab-container {
  width: 100%;
  display: flex;
  top: 0px;
  justify-content: space-between;
  align-items: center;
  z-index: 9;
  box-sizing: border-box;
  background: #fff;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #646464;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  height: 116rpx;
  position: relative;
  .tab-item {
    position: relative;
    // margin-left: 30rpx;
    span {
      font-size: 36rpx;
      display: block;
    }
    .tab-icon {
      position: absolute;
      right: -36rpx;
      width: 28rpx;
      height: 28rpx;
      top: 12rpx;
    }
  }
  .tab-slide {
    position: absolute;
    bottom: 0rpx;
    left: -22rpx;
    height: 4rpx;
    width: 40rpx;
    background-color: #333333;
    background-size: contain;
    transition: transform 0.2s; // 滑块移动
  }
  .tab-focus {
    // margin-top: -4rpx;
    font-family: PingFangSC-Medium;
    span {
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      color: #030303;
      font-weight: 600;
    }
  }
}
</style>
