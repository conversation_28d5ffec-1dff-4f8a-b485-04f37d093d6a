<template>
  <div
    class="warn-text"
    :class="{
      'warn-text-warning': type === 'warning'
    }"
    :style="customStyle"
  >
    <img
      v-if="type === 'warning'"
      class="warn-text-icon"
      src="https://static.soyoung.com/sy-design/381uiqreukmbk1726299976579.png"
    />
    <img
      v-else
      class="warn-text-icon"
      src="https://static.soyoung.com/sy-design/381uiqreukmbk1726299976827.png"
    />
    <div class="warn-text-right">
      <div class="strong">{{ title }}</div>
      <div v-if="isShowSubText">
        您的预约专员将在
        <text class="strong">48小时内</text>
        联系您确认，请留意电话/微信
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: { type: String, default: '预约需平台确认后生效' },
    customStyle: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'normal'
    },
    isShowSubText: {
      type: Boolean,
      default: true
    }
  }
};
</script>
<style lang="less" scoped>
.warn-text {
  box-sizing: border-box;
  width: 100%;
  padding: 10rpx 24rpx;
  height: 76rpx;
  padding-right: 15rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ebfbdc;
  &.warn-text-warning {
    padding: 20rpx 24rpx;
    background: #ffe0d6;
    .strong {
      color: #030303;
      font-size: 26rpx;
      font-weight: 400;
    }
  }
  .strong {
    color: @text-color;
    font-weight: 400;
  }
  .warn-text-icon {
    flex-shrink: 0;
    margin-right: 20rpx;
    width: 28rpx;
    height: 30rpx;
  }
  .warn-text-right {
    flex: 1;
    line-height: 36rpx;
    min-height: 36rpx;
    font-size: 24rpx;
    font-weight: 400;
    word-break: break-all;
    margin-right: -10rpx; // 特殊处理一下 ，为了能放的开
  }
}
</style>
