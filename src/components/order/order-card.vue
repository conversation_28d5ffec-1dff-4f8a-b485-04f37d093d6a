<template>
  <!-- 订单列表专属card -->
  <!-- order_status：新氧优享的订单状态 -->
  <!-- 订单状态，0等待付款未付款，未过期，1是过期未付款，已失效。 2未使用已付款，未核销，3已使用，已付款，已核销。4已付款，过期未核销。5已退款 -->
  <div class="order-card item-observer">
    <!-- 订单头部信息 -->

    <div class="order-header">
      <div class="line0" @click="goDetail(item.basic_order_module.order_id)">
        <div class="order-id">
          订单编号:{{ item.basic_order_module.order_id }}
        </div>
        <div
          class="order-status"
          :class="{
            'disabled':
              [
                '已使用',
                '已取消',
                '已退款',
                '拼团失败',
                '分期放款失败'
              ].indexOf(item.status_module.str_status) > -1 ||
              [1, 3, 5, 16, 19].indexOf(item.status_module.orderStatus) > -1
          }"
        >
          {{ item.status_module.str_status }}
        </div>
      </div>
      <div
        class="line1"
        v-if="
          countDownText &&
          ((item.basic_order_module.pay_status === 0 &&
            item.status_module.orderStatus === 0) ||
            item.status_module.orderStatus === 17)
        "
        @click="goDetail(item.basic_order_module.order_id)"
      >
        <div class="left">
          <div
            class="num"
            v-if="countDownText"
            :key="item.basic_order_module.order_id"
          >
            {{ countDownText }} &nbsp;<span class="blue-text">
              {{ countDown }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <!-- 商品card -->
    <div
      class="product-card-wrap"
      @click="goDetail(item.basic_order_module.order_id)"
    >
      <ProductCardList
        :ref="'productCardList' + item.basic_order_module.order_id"
        :list="item.product_module || []"
        :orderId="item.basic_order_module.order_id || ''"
        :status="item.status_module.orderStatus"
        :orderInfo="item"
      ></ProductCardList>
    </div>

    <!-- 操作按钮 -->
    <div class="operate-btn-wrap">
      <div
        class="pay-price"
        v-if="item.button_module.show_button_list.some((i) => i.type === 67)"
        @click="goDetail(item.basic_order_module.order_id)"
      >
        <span class="text">待支付</span>
        <span class="number"
          >￥{{ item.basic_order_module.price_deposit }}</span
        >
      </div>
      <div v-else></div>
      <div class="btn-wrapper">
        <template>
          <span
            v-for="i in item.button_module.show_button_list.filter(
              (item) => item.type !== 70
            )"
            class="btn btn-expo"
            :data-name="i.name"
            :class="[68].includes(i.type) ? 'light' : ''"
            :key="i.type"
            @click="handleClickButton(JSON.parse(JSON.stringify(i)))"
          >
            {{ i.name }}
          </span>
        </template>
        <button
          class="button-pintuan btn-expo"
          v-for="i in item.button_module.show_button_list.filter(
            (item) => item.type == 70
          )"
          :data-name="i.name"
          @click="handleClickButton(JSON.parse(JSON.stringify(i)))"
          :key="i.type"
          open-type="share"
        >
          {{ i.name }}
        </button>
      </div>
    </div>
  </div>
</template>
<script>
import ProductCardList from '@/components/order/product-card-list';
import payMixins from '@/components/confirm-order/pay';
import SubscribeMixins from '@/mixins/subscribe';
import EvaluateMixin from '@/components/order/evaluateMixin';
import pay, { subscribeTmpl } from '@/components/newPay';
import dayjs from 'dayjs';

export default {
  mixins: [payMixins, SubscribeMixins, EvaluateMixin],
  components: { ProductCardList },
  props: {
    order: Object,
    index: Number,
    loading: Boolean
  },
  data() {
    return {
      jumping: false,
      item: this.order,
      backMoney: 0,
      countDownText: '',
      countDown: '00:00:00',
      counter: -1,
      timer: null,
      paying: false,
      reportParams: {},
      showCashBackStop: false // 展示退款挽留弹窗
    };
  },
  created() {},
  mounted() {
    console.log(this.order);
    this.initExposure();
  },
  computed: {
    // 是否是连锁机构
    isPrimeChainHospital() {
      return this.order.reserve_card?.hospital_info?.is_chain_hospital === 1;
    },
    // 是否已经确认
    isApptConfirm() {
      return this.order.reserve_card?.reserve_confirm_yn === 1;
    },
    // 是否需要预约
    needReserve() {
      return this.order.reserve_card?.need_reserve === 1;
    },
    reservationDate() {
      return dayjs(this.item.reservation_module.reservation_date).format(
        'YYYY-MM-DD HH:mm'
      );
    }
  },
  watch: {
    order: {
      handler() {
        this.item = this.order;
        // this.item.backMoney =
        //   this.order.order_list?.[0]?.guide_info?.reserve_info?.back_money;
        if (this.timer) {
          clearInterval(this.timer);
        }
        this.startCountDown();
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getStyle(order_status, yueyueYn) {
      // 订单状态，
      // 0等待付款未付款，未过期，
      // 1是过期未付款，已失效。
      // 2未使用已付款，未核销，
      // 3已使用，已付款，已核销。
      // 4已付款，过期未核销。
      // 5已退款
      // 转换》》》》》
      // style
      // 0等待付款未付款，未过期；
      // 1是过期未付款，已失效。；
      // 2已付款待预约，未核销
      // 3已付款已预约，未核销；
      // 4已付款，已核销；
      // 5已退款
      let style = 0;
      switch (order_status) {
        case 0:
          style = 0;
          break;
        case 1:
          style = 1;
          break;
        case 2:
          if (yueyueYn) {
            // 已预约
            style = 3;
          } else {
            style = 2;
          }
          break;
        case 3:
          style = 4;
          break;
        case 5:
          style = 5;
          break;
      }
      return style;
    },
    initExposure() {
      this.$nextTick(() => {
        // 豆腐块埋点
        this.$registerExposure(
          '.btn-expo',
          (info) => {
            console.log('info', info);
            this.$reportData({
              info: 'sy_chain_store_tuan_order_list:button_exposure',
              ext: {
                content: info.dataset.name
              }
            });
          },
          this
        );
      });
    },
    // 开始倒计时
    startCountDown() {
      if (this.item) {
        let time = 0;
        console.log(this.item.basic_order_module.pay_type);
        // 剩余付款时间
        if (
          this.item.basic_order_module.pay_expire_date &&
          this.item.status_module.orderStatus === 0 &&
          this.item.basic_order_module.pay_type !== 27
        ) {
          this.countDownText = '剩余付款时间';
          time = this.item.basic_order_module.pay_expire_date; // 类型为Date类型
        } else if (
          this.item.basic_order_module.tuan_time_count_down &&
          this.item.status_module.orderStatus === 17
        ) {
          this.countDownText = '剩余拼团时间';
          time = this.item.basic_order_module.tuan_time_count_down; // 类型为数字，剩余秒数
        }
        if (time) {
          if (+this.item.status_module.orderStatus === 17) {
            // 当拼团时
            this.counter = parseInt(
              this.item.basic_order_module.tuan_time_count_down
            );
            console.log(this.counter);
          } else {
            //付款时，需自己计算下剩余时间
            this.counter = parseInt(
              new Date(time).getTime() / 1000 - new Date().getTime() / 1000
            );
          }

          if (this.counter > 0) {
            this.handleTimer(this.counter);
          } else {
            this.countDownText = '';
            this.countDown = '00:00:00';
          }
          this.$forceUpdate();
        } else {
          this.countDownText = '';
          this.countDown = '00:00:00';
        }
      }
    },
    // 定时器
    handleTimer() {
      const that = this;
      this.countDown = this.formatSeconds();
      this.timer = setInterval(() => {
        this.counter--;
        if (this.counter > 0) {
          this.countDown = this.formatSeconds();
        } else {
          clearInterval(that.timer);
          // 倒计时结束，订单变为失效状态
          if (+this.item.status_module.orderStatus === 17) {
            this.item.basic_order_module.orderStatus = 19;
            this.countDown = '00:00:00';
            this.item.basic_order_module.pay_expire_date = 0;
            this.item.status_module.str_status = '拼团失败';
            this.item.button_module.show_button_list = [];
            this.$forceUpdate();
          } else {
            this.item.status_module.orderStatus = 1;
            this.countDown = '00:00:00';
            this.item.basic_order_module.pay_expire_date = 0;
            this.item.status_module.str_status = '已取消';
            this.item.button_module.show_button_list = [
              {
                type: 68,
                name: '再次购买'
              }
            ];
            this.$forceUpdate();
          }
        }
      }, 1000);
    },
    // // 秒转时分秒，求模很重要，数字的下舍入
    formatSeconds() {
      const hh = Math.floor((this.counter / 3600) % 24);
      const mm = Math.floor((this.counter / 60) % 60);
      const ss = Math.floor(this.counter % 60);
      return `${hh < 10 ? '0' : ''}${hh}:${mm < 10 ? '0' : ''}${mm}:${
        ss < 10 ? '0' : ''
      }${ss}`;
    },
    // 针对团的时间显示
    tuanfmt() {
      let time = this.counter;
      const hh = parseInt(time / 3600);
      time = time % 3600;
      const mm = parseInt(time / 60);
      time = time % 60;
      const ss = parseInt(time);
      return `${hh < 10 ? '0' : ''}${hh}:${mm < 10 ? '0' : ''}${mm}:${
        ss < 10 ? '0' : ''
      }${ss}`;
    },
    handleClickButton(item) {
      this.$emit('clickButton', this.item.basic_order_module.order_id);
      switch (item.type) {
        case 67: // 立即付款
          this.payFn(item);
          return;
        case 68: // 再次购买
          this.buyAgain(item);
          return;
        case 69: // 立即申请
          this.proposal(item);
          return;
        case 70: // 邀请好友参团
          this.invite(item);
          return;
        case 10: // 立即预约
          this.appointment(item);
          return;
        case 75: // 查看预约 // TODO孝松
          this.checkAppointment(item);
          return;
      }
    },
    // 再次购买
    buyAgain(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_page:button_click',
        ext: {
          content: item.name,
          url: '/pages/product?id=' + this.item.basic_order_module.product_id
        }
      });
      this.$bridge({
        url: '/pages/product?id=' + this.item.basic_order_module.product_id
      });
    },
    // 跳转支付
    async payFn(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_page:button_click',
        ext: {
          content: item.name,
          url: ''
        }
      });
      try {
        const oid = this.item.basic_order_module.oid;
        const payResult = await pay(
          {
            oid,
            pay_type: this.item.basic_order_module.pay_type
          },
          async () => this.createGroupBySub(subscribeTmpl, [oid])
        );

        if (payResult.status === 0) {
          uni.navigateTo({
            url: `/packageOrder/paySuccess?id=${this.item.basic_order_module.order_id}`
          });
        } else {
          // TODO 跳转订单详情页面
          uni.navigateTo({
            url: `/packageOrder/order-detail?orderId=${this.item.basic_order_module.order_id}`
          });
        }
        console.log('===>', payResult);
        // console.log(response);
        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: error.message,
          icon: 'error'
        });
      }
    },
    proposal(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_page:button_click',
        ext: {
          content: item.name,
          url: item.url
        }
      });
      this.$bridge({
        url: '/pages/h5?url=' + encodeURIComponent(item.url)
      });
    },
    // 跳转订单详情
    goDetail(orderId) {
      if (this.loading || this.jumping) {
        return;
      }
      this.$reportData({
        info: 'sy_wxtuan_tuan_order_list_product:card_click',
        ext: {
          serial_num: this.index + 1,
          product_id: this.item.basic_order_module.product_id,
          url:
            'packageOrder/order-detail?orderId=' +
            this.item.basic_order_module.order_id
        }
      });

      // if (+this.item.order_status === 1) return;
      this.jumping = true;
      this.$emit('toDetail', orderId);
      this.$bridge({
        url: `/packageOrder/order-detail?orderId=${orderId}`,
        callbackSuccess: () => {
          this.jumping = false;
        },
        callbackFail() {
          this.jumping = false;
        }
      });
    },
    appointment(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_page:button_click',
        ext: {
          content: item.name,
          url: `/packageOrder/order-detail?orderId=${this.item.basic_order_module.order_id}&openAppointment=now`
        }
      });
      this.$bridge({
        url: `/packageOrder/order-detail?orderId=${this.item.basic_order_module.order_id}&openAppointment=now`
      });
    },
    checkAppointment(item) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_page:button_click',
        ext: {
          content: item.name,
          url: `/packageOrder/order-detail?orderId=${this.item.basic_order_module.order_id}`
        }
      });
      this.$bridge({
        url: `/packageOrder/order-detail?orderId=${this.item.basic_order_module.order_id}`
      });
    },
    invite(item) {
      // 调阿凯逻辑
      console.log('item', item);
      console.log('order', this.order);
      this.$reportData({
        info: 'sy_wxtuan_tuan_page:button_click',
        ext: {
          content: item.name,
          url: ''
        }
      });
      // this.createGroupBySub(
      //   [
      //     'WVEq1xRbHUtILERdzzBl88FzQJU8wQA0ZdfdCE1dUKs',
      //     'eGnRAuGNgOmj8uFVNQqZbiaMMssCfKSiLeIjNZTTVS4',
      //     'E-3yPJELuSVKOUIkV8q0NY2L9J-LGsu_jEf5ZIVOSDU'
      //   ],
      //   [this.order.basic_order_module.group_id]
      // );
      this.$emit('setShareInfo', this.order);
    },
    // 邀请好友参团
    // onSharePintuanClick() {
    //   this.$reportData({
    //     info: 'sy_wxtuan_tuan_order_list:yaoqing_click',
    //     ext: {
    //       product_id: this.item.pid,
    //       group_id: this.item.button?.group_btn?.group_id || 0,
    //       order_id: this.item.order_id
    //     }
    //   });
    //   this.$bridge({
    //     url: `/packageOrder/order-detail?orderId=${this.item.order_id}`
    //   });
    // },
    // 邀请好友参团
    // setShareInfo(share_info) {
    //   this.$reportData({
    //     info: 'sy_wxtuan_tuan_order_list:yaoqing_click',
    //     ext: {
    //       product_id: this.item.pid,
    //       group_id: this.item.button?.group_btn?.group_id || 0,
    //       order_id: this.item.order_id
    //     }
    //   });
    //   this.$emit('setShareInfo', share_info);
    // },
    // 申请分期
    // applyLoanFn() {
    //   const url =
    //     '/pages/h5?url=' +
    //     encodeURIComponent(this.item.button.loan_btn.apply_url);
    //   this.$bridge({
    //     url: url
    //   });
    // },
    // 显示预约核销码
    // showQrCodeFn() {
    //   this.$bridge({
    //     url: `/packageOrder/order-detail?orderId=${this.item.order_id}&action=showQrCodeModal`
    //   });
    // },
    // viewTitleTip(tip) {
    //   uni.showModal({
    //     title: '提示',
    //     content: tip,
    //     showCancel: false,
    //     success() {}
    //   });
    // },
    // // 写评价
    // toPingjia() {
    //   // 套餐时打开评价列表
    //   this.$emit('openPostList', this.item);
    // },
    toMap(hos_info) {
      uni.openLocation({
        latitude: Number(hos_info.lat),
        longitude: Number(hos_info.lng),
        scale: 18,
        name: hos_info.name_cn,
        address: hos_info.address
      });
    }
  }
};
</script>
<style lang="less">
.order-header {
  .num {
    .blue-text {
      font-family: OutFit-Regular;
      font-size: 26rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
    }
  }
}
</style>
<style lang="less" scoped>
.order-card {
  padding: 30rpx;
  background: #ffffff;
  margin-bottom: 20rpx;
  position: relative;
  .order-header {
    margin-bottom: 30rpx;
    .blue-text {
      font-family: OutFit-Regular;
      font-size: 26rpx;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
      line-height: 40rpx;
      margin-left: 4rpx;
    }
    .num {
      display: flex;
      align-items: center;
    }
    .line0 {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
      .order-id {
        font-family: Outfit-Regular;
        font-size: 26rpx;
        color: #8c8c8c;
        letter-spacing: 0;
        font-weight: 400;
      }
      .order-status {
        width: 240rpx;
        font-family: PingFangSC-Medium;
        font-size: 26rpx;
        color: #61b43e;
        letter-spacing: 0;
        text-align: right;
        font-weight: 500;
        &.disabled {
          color: #999999;
        }
      }
    }
    .line1 {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: space-between;
      margin-top: 20rpx;
      .left {
        font-family: PingFangSC-Medium;
        font-size: 26rpx;
        color: #333333;
        letter-spacing: 0;
        font-weight: 500;
        flex: 1;
        .title-tip {
          display: inline-block;
          width: 24rpx;
          height: 24rpx;
          margin-left: 10rpx;
        }
      }
      .right {
        width: 240rpx;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #f85d2d;
        letter-spacing: 0;
        text-align: right;
        font-weight: 400;
        &.disabled {
          color: #999999;
        }
      }
    }
    .line2 {
      .title-desc {
        margin-top: 12rpx;
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #999999;
        letter-spacing: 0;
        font-weight: 400;
      }
    }
  }

  /* 商品区域 */
  .product-card-wrap {
    .product-card {
      margin-bottom: 50rpx;
      &:last-child {
        margin-bottom: 0rpx;
      }
    }
  }

  /* 待付款金额 */
  .pay-price {
    font-family: PingFangSC-Medium;
    font-size: 13px;
    color: #222222;
    text-align: right;
    font-weight: 500;
    text-align: right;
    margin-top: 30rpx;
    .text {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #8c8c8c;
      font-weight: 400;
    }
    .number {
      font-family: OutFit-Regular;
      font-size: 26rpx;
      color: #222222;
      font-weight: 500;
    }
  }

  /* 返现活动 */
  .back-money-desc {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    background: #fcf5e0;
    padding: 18rpx 10rpx;
    margin-top: 30rpx;
    img {
      display: inline-block;
      width: 28rpx;
      height: 22rpx;
      margin-right: 10rpx;
      margin-top: 6rpx;
    }
    span {
      flex: 1;
      display: inline-block;
      font-family: PingFangSC-Regular;
      font-size: 12 * 2rpx;
      color: #be8e4e;
      letter-spacing: 1rpx;
      font-weight: 400;
    }
  }

  /* // 服务指引 */
  .order-extend-wrap {
    margin-top: 30rpx;
    .order-service-info {
      padding: 30rpx 20rpx;
      box-sizing: border-box;
      background: #f8f8fc;
      border-radius: 14rpx;
      .line {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        margin-bottom: 28rpx;
        &:last-child {
          margin-bottom: 0;
        }
        .left {
          width: 190rpx;
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #aaabb3;
          font-weight: 400;
        }
        .center {
          flex: 1;
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #555555;
          font-weight: 400;
          .tip {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: @text-color;
            font-weight: 400;
            display: inline-block;
            margin-left: 10rpx;
          }
        }
        .right {
          width: 36rpx;
          &.map {
            width: 60rpx;
            text-align: right;
          }
          .point {
            display: inline-block;
            width: 36rpx;
            height: 36rpx;
            vertical-align: baseline;
          }
        }
      }
    }
  }

  /* // 按钮操作区 */
  .operate-btn-wrap {
    text-align: right;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    .btn-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 30rpx;
    }
    .btn {
      display: inline-block;
      background: @border-color;
      padding: 0 30rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      height: 37 * 2rpx;
      line-height: 37 * 2rpx;
      box-sizing: border-box;
      overflow: hidden;
      cursor: pointer;
      margin-left: 30rpx;
      &.light {
        border: 1px solid #333333;
        background: #ffffff;
        color: #222222;
      }
    }
    .button-pintuan {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 82 * 2rpx;
      height: 37 * 2rpx;
      line-height: 37 * 2rpx;
      font-size: 12 * 2rpx;
      color: #ffffff;
      font-weight: 400;
      margin: 0;
      margin-left: 12rpx;
      width: 108 * 2rpx;
      background-color: @border-color;
      border-color: @border-color;
      border-radius: 0;
      margin-left: 30rpx;
      color: #fff;
      &::after {
        border: none;
      }
    }
  }
  /* 拼团状态图 */
  .pituan-status-img {
    position: absolute;
    right: 0;
    top: 0;
    display: inline-block;
    width: 52px;
    height: 52px;
    z-index: 8;
  }

  .beautiful-sisiter-img {
    display: inline-block;
    width: 68 * 2rpx;
    height: 44rpx;
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
