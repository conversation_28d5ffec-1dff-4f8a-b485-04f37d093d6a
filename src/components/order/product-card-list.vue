<template>
  <div class="product-card-list" :class="{ 'has-gift-order': has_gift_order }">
    <div
      class="product-card-wrap"
      :class="{
        'gift-product-card': item.is_gift,
        'sun-gift': index != 0
      }"
      v-for="(item, index) in product_list"
      :key="index"
    >
      <div
        class="product-card"
        :class="{
          disabled: item.status_str == '已退款' || item.status_str == '已失效'
        }"
      >
        <div class="product-img-wrap">
          <image
            v-if="item.img_left && item.img_left.u"
            class="product-img-wrap-corner-marker"
            :src="item.img_left.u"
            :style="{
              width: item.img_left.w * 2 + 'rpx',
              height: item.img_left.h * 2 + 'rpx'
            }"
          ></image>
          <img
            :src="
              item.img_cover ||
              'https://static.soyoung.com/sy-pre/3opscfq735zbu-1717643400628.png'
            "
            mode="aspectFill"
            class="product-img"
          />
        </div>
        <div class="box">
          <div class="line-1">
            <div
              class="title"
              :class="{
                'indentation':
                  !(item.is_gift && item.show_is_gift) &&
                  (item.title || '').indexOf('【') === 0
              }"
            >
              <img
                v-if="item.is_gift && item.show_is_gift"
                src="https://static.soyoung.com/sy-pre/alok43w4257l-1682503800715.png"
                class="zengpin-icon"
                :class="{
                  'mariginRight': !((item.title || '').indexOf('【') === 0)
                }"
              />
              {{ item.title }}
              <!-- <rich-text
                class="rich-tex"
                :nodes="titleFormatFn(item)"
              ></rich-text> -->
            </div>
            <!-- <div class="num">x1</div> -->
          </div>
          <div class="line-2">
            <div class="number">¥{{ item.product_online }}</div>
            <div class="divide"></div>
            <span class="text">数量</span>
            <div class="number">
              {{ `${item.amount}` }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      }
    },
    status: {
      type: Number,
      default: NaN
    },
    orderId: {
      type: String,
      dafault: ''
    },
    orderInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      product_list: this.list,
      orderStatus: +this.status
    };
  },
  watch: {
    list: {
      handler() {
        this.product_list = this.list;
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    has_gift_order() {
      return (
        this.product_list.filter((item) => {
          return item.is_gift;
        }).length > 0
      );
    }
  },
  methods: {
    titleFormatFn(item) {
      let str = item.title;
      if ((str || '').indexOf('【') === 0) {
        str = `<span style="margin-left: -7px;">【</span>${str.substr(1)}`;
      }
      if (item.is_gift && item.show_is_gift) {
        str =
          `<img src="https://static.soyoung.com/sy-pre/alok43w4257l-1682503800715.png" class="zengpin-icon" />` +
          str;
      }
      return str;
    }
  }
};
</script>
<style lang="less">
// .line-1 {
//   .title {
//     .zengpin-icon {
//       width: 28rpx;
//       height: 28rpx;
//       vertical-align: middle;
//       margin-right: 8rpx;
//       margin-bottom: 6rpx;
//     }
//   }
// }
</style>
<style lang="less" scoped>
.product-card-list {
  .product-card-wrap {
    display: flex;
    flex-direction: column;
    margin-bottom: 50rpx;

    .product-card {
      display: flex;
      flex-direction: row;

      .product-img-wrap {
        height: 136rpx;
        width: 182rpx;
        position: relative;
        //border-radius: 4rpx;
        overflow: hidden;
        .product-img {
          display: inline-block;
          height: 136rpx;
          width: 182rpx;
          //border-radius: 4rpx;
        }
        .status {
          height: 16 * 2rpx;
          padding: 0 10rpx;
          background: #222222;
          font-family: PingFangSC-Medium;
          font-size: 11px;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 500;
          position: absolute;
          left: 0;
          top: 0;
        }
        .product-img-wrap-corner-marker {
          position: absolute;
          left: 0;
          right: 0;
          //width: 84rpx;
          //height: 28rpx;
        }
      }

      .box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 20rpx;
        .line-1 {
          display: flex;
          justify-content: space-between;
          // height: 72rpx;
          .title {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis;
            width: 100%;
            // height: 72rpx;
            font-family: PingFangSC-Medium;
            font-size: 26rpx;
            color: #222222;
            line-height: 36rpx;
            font-weight: 500;
            .rich-tex {
              height: 72rpx;
              font-family: PingFangSC-Medium;
              font-size: 26rpx;
              line-height: 36rpx;
            }
            .zengpin-icon {
              width: 28rpx;
              height: 28rpx;
              vertical-align: middle;
              margin-bottom: 6rpx;
              &.mariginRight {
                margin-right: 8rpx;
              }
            }
            // 需要缩进
            &.indentation {
              text-indent: -14rpx;
            }
          }
          .num {
            width: 13 * 2rpx;
            height: 18 * 2rpx;
            font-family: PingFangSC-Regular;
            font-size: 13 * 2rpx;
            color: #555555;
            font-weight: 400;
            text-align: right;
          }
        }

        .line-2 {
          display: flex;
          align-items: center;
          height: 36rpx;
          text-align: right;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #8c8c8c;
          font-weight: 400;
          margin-top: 8rpx;
          margin-left: -6rpx;
          .number {
            font-family: OutFit-Regular;
            font-size: 26rpx;
            color: #8c8c8c;
            line-height: 18 * 2rpx;
            font-weight: 400;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: left;
            margin-left: 3px;
          }
          .divide {
            width: 2rpx;
            height: 16rpx;
            background-color: #8c8c8c;
            margin: 0 16rpx;
          }
        }
      }

      &.disabled {
        filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -o-filter: grayscale(100%);
        -webkit-filter: grayscale(1);
        .product-img-wrap {
          .status {
            background: #aaabb3;
          }
        }
        .box {
          .title,
          .num,
          .sku,
          .price {
            color: #aaabb3;
          }
        }
      }
    }

    &:last-child {
      margin-bottom: 0rpx;
    }
    // 赠品
    &.gift-product-card {
      margin-bottom: 0rpx;
      .product-card {
        &:last-child {
          margin-bottom: 0rpx;
        }
        .line-1 {
          .zengpin {
            border-radius: 2px;
            overflow: hidden;
            display: inline-block;
            width: 56rpx;
            height: 30rpx;
            margin-right: 10rpx;
          }
          .title {
            font-family: PingFangSC-Regular;
            font-size: 13px;
            color: #777777;
            line-height: 20px;
            font-weight: 400;
          }
        }
        .line-2 {
          display: flex;
          align-items: center;
          height: 36rpx;
          text-align: right;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #8c8c8c;
          font-weight: 400;
          margin-top: 8rpx;
          margin-left: -6rpx;
          .number {
            font-family: OutFit-Regular;
            font-size: 26rpx;
            color: #8c8c8c;
            line-height: 18 * 2rpx;
            font-weight: 400;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: left;
            margin-left: 3px;
          }
          .divide {
            width: 2rpx;
            height: 16rpx;
            background-color: #8c8c8c;
            margin: 0 16rpx;
          }
        }
      }

      //作为子商品时的订单
      &.sun-gift {
        .product-card {
          padding: 30rpx 20rpx;
          background: #f8f8f8;

          .product-img-wrap {
            height: 136rpx;
            width: 182rpx;
            position: relative;
            .product-img {
              display: inline-block;
              height: 136rpx;
              width: 182rpx;
            }
            .status {
              display: none;
            }
          }
          .box {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-left: 20rpx;
            .line-1 {
              display: flex;
              justify-content: space-between;
              height: 72rpx;
              .title {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                word-break: break-all;
                text-overflow: ellipsis;
                width: 192 * 2rpx;
                height: 80rpx;
                font-family: PingFangSC-Regular;
                font-size: 13px;
                color: #777777;
                line-height: 20px;
              }
              .num {
                width: 13 * 2rpx;
                height: 18 * 2rpx;
                font-family: PingFangSC-Regular;
                font-size: 13px;
                color: #777777;
                font-weight: 400;
                text-align: right;
              }
            }

            .line-2 {
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 36rpx;
              text-align: right;
              font-family: PingFangSC-Regular;
              color: #808080;
              margin-top: 10rpx;
              .zengpin {
                border-radius: 2px;
                overflow: hidden;
                display: inline-block;
                width: 56rpx;
                height: 30rpx;
                margin-right: 10rpx;
              }
              .price {
                text-align: right;
                font-family: PingFangSC-Regular;
                font-size: 13px;
                color: #777777;
                text-align: right;
                font-weight: 400;
              }
            }
          }
        }
      }
    }
  }

  .order-extend-wrap {
    margin-top: 50rpx;
  }

  // 包含赠品
  &.has-gift-order {
    .product-card-wrap {
      margin-bottom: 30rpx;
      &.gift-product-card {
        margin-bottom: 0rpx;
      }
    }
  }
}
</style>
