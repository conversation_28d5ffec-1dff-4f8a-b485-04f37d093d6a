import { createNamespacedHelpers, mapGetters } from 'vuex';
const { mapState } = createNamespacedHelpers('global');
export default {
  computed: {
    ...mapState(['userInfo']),
    ...mapGetters(['isLogin'])
  },
  methods: {
    async toWritePost(info) {
      if (!this.isLogin) {
        // 校验登录
        const isAuth = await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
        if (!isAuth) return;
      }
      requirePlugin('sy-evaluate').setUserInfo({
        open_id: this.userInfo.openId,
        union_id: this.userInfo.unionId,
        xy_token: this.userInfo.xyToken,
        uid: this.userInfo.uid
      });
      this.$bridge({
        url: info.write_post_wxapi_url
      });
    }
  }
};
