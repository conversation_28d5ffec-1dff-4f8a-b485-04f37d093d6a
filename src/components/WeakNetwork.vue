<template>
  <root-portal>
    <div class="error-page" v-if="visible">
      <img class="cover" src="/static/image/weak.png" />
      <div class="tip">无法连接网络，请检查您的网络设置</div>
      <div class="button" @click.stop="() => $emit('refresh')">重新加载</div>
    </div>
  </root-portal>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    path: {
      type: Array,
      default: () => []
    }
  },
  created() {
    const cb = ({ isConnected = true, weakNet = false, url }) => {
      console.log(isConnected, weakNet, this.path, url);
      if (!isConnected) {
        this.$emit('update:visible', true);
        return;
      }
      if (url && this.path.includes(url) && weakNet) {
        this.$emit('update:visible', true);
      }
    };
    uni.$on('networkWeak', cb);
    this.$once('hook:beforeDestroy', () => {
      uni.$off('networkWeak', cb);
    });
  }
};
</script>
<style lang="less" scoped>
.error-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1001;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: #fff;
  .cover {
    width: 100%;
    margin: 0 auto 40rpx;
    width: 70rpx;
    height: 70rpx;
  }
  .tip {
    margin-bottom: 60rpx;
    font-size: 28rpx;
    color: #030303;
  }
  .button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 74rpx;
    width: 300rpx;
    font-size: 26rpx;
    color: #fff;
    background-color: #333333;
  }
}
</style>
