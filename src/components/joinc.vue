<template>
  <root-portal>
    <div class="joinc-model" v-if="value">
      <div class="mask" @click="onClose"></div>
      <div class="hospital-joinc">
        <image
          src="https://static.soyoung.com/sy-design/bzsokyai5osd1726228765172.png"
          mode="widthFix"
          class="close"
          @click="onClose"
        ></image>
        <div class="joinc-box">
          <image
            src="https://static.soyoung.com/sy-design/2m067e29fe33r1726228765465.png"
            mode="widthFix"
            class="logo"
          ></image>
          <p class="logo-title">THANK YOU FOR MAKING AN APPOINTMENT</p>
          <label class="welcome">感谢您预约</label>
          <label class="hospital-name" v-if="info.hospital_name">{{
            info.hospital_name
          }}</label>
          <label class="hospital-serve">添加客服享专属服务</label>
          <div class="hospital-tips">
            <div class="tips">
              <image
                src="https://static.soyoung.com/sy-design/2hca7nc2ykif91726199186116.png"
                mode="widthFix"
              ></image>
              <span>快速响应</span>
            </div>
            <div class="tips">
              <image
                src="https://static.soyoung.com/sy-design/2hca7nc2ykif91726199186116.png"
                mode="widthFix"
              ></image>
              <span>最新优惠</span>
            </div>
            <div class="tips">
              <image
                src="https://static.soyoung.com/sy-design/2hca7nc2ykif91726199186116.png"
                mode="widthFix"
              ></image>
              <span>术后保障</span>
            </div>
            <div class="tips">
              <image
                src="https://static.soyoung.com/sy-design/2hca7nc2ykif91726199186116.png"
                mode="widthFix"
              ></image>
              <span>预约到院</span>
            </div>
          </div>
          <image
            src="https://static.soyoung.com/sy-design/2hca9lhmiqshw1726199186119.png"
            mode="widthFix"
            class="arrow"
          ></image>
          <div
            :class="[
              'qrcode',
              { 'has-code': info.consultant_qr_code ? true : false }
            ]"
          >
            <image
              :src="info.consultant_qr_code"
              show-menu-by-longpress
              mode="widthFix"
              v-if="info.consultant_qr_code"
            ></image>
          </div>
          <image
            :src="
              info.is_flagship
                ? 'https://static.soyoung.com/sy-design/2m067e29fe33r1726199186132.png'
                : 'https://static.soyoung.com/sy-design/1lxvc5dfx1fet1729152696384.png'
            "
            mode="widthFix"
            class="btn"
          ></image>
          <div class="call" @click="handleClickHospitalTel" v-if="info.mobile">
            <image
              src="https://static.soyoung.com/sy-design/amhiohkx1oen1726199186125.png"
              mode="widthFix"
            ></image>
            <span>联系客服</span>
          </div>
        </div>
      </div>
    </div>
  </root-portal>
</template>
<script>
import { getConsultantInfo } from '@/api/hospital';
export default {
  props: {
    hospital_id: {
      type: String,
      default: ''
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  pageTrackConfig() {
    return {
      info: 'sy-yx_strict_join_group_add_customer_page',
      ext: {}
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.getInfo();
        }
      },
      deep: true
    }
  },
  data() {
    return {
      info: {}
    };
  },
  methods: {
    handleClickHospitalTel() {
      const { is_work_time, mobile, mobile_msg } = this.info;
      if (+is_work_time === 1) {
        uni.makePhoneCall({
          phoneNumber: mobile
        });
      } else {
        return uni.showModal({
          content: mobile_msg,
          showCancel: false
        });
      }
    },
    async getInfo() {
      const { errorCode, errorMsg, responseData } = await getConsultantInfo({
        hospital_id: this.hospital_id
      });
      if (errorCode !== 0) {
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        return;
      }
      this.info = responseData;
    },
    onClose() {
      this.$emit('input', false);
    }
  }
};
</script>
<style lang="less" scoped>
.joinc-model {
  .mask {
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10001;
    animation: opacityami 0.3s ease-in-out;
  }
}
.hospital-joinc {
  width: 100%;
  height: 1300rpx;
  position: fixed;
  z-index: 10002;
  bottom: 0;
  left: 0;
  animation: opacityami 0.3s ease-in-out;
  .close {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    right: 26rpx;
    top: 26rpx;
  }
  .joinc-box {
    width: 100%;
    height: 100%;
    background-image: url('https://static.soyoung.com/sy-design/wwhxfx4sgcq21726199186308.png');
    background-size: 100% 100%;
    padding-top: 64rpx;
  }
  .logo {
    width: 150rpx;
    height: 0;
    display: block;
    margin-left: 60rpx;
  }
  .logo-title {
    font-family: OutFit-Regular;
    font-size: 20rpx;
    color: #bababa;
    letter-spacing: 0;
    font-weight: 200;
    margin-top: 32rpx;
    margin-left: 60rpx;
  }
  .welcome {
    font-family: PingFangSC-Light;
    font-size: 30rpx;
    color: #030303;
    letter-spacing: 0.5rpx;
    text-align: center;
    line-height: 46rpx;
    font-weight: 200;
    margin-top: 10rpx;
    margin-left: 60rpx;
    display: block;
    text-align: left;
  }
  .hospital-name {
    font-family: PingFangSC-Regular;
    font-size: 30rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    margin-top: 4rpx;
    margin-left: 60rpx;
    display: block;
    text-align: left;
    word-wrap: break-word;
    word-break: break-all;
  }
  .hospital-serve {
    font-family: PingFangSC-Medium;
    font-size: 60rpx;
    color: #030303;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    margin-top: 56rpx;
    margin-left: 60rpx;
    display: block;
    text-align: left;
  }
  .hospital-tips {
    margin-top: 12rpx;
    padding: 0 50rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    span {
      font-family: PingFangSC-Light;
      font-size: 26rpx;
      color: #030303;
      text-align: center;
      font-weight: 200;
      margin-left: 6rpx;
    }
    image {
      width: 24rpx;
      height: 28rpx;
      margin-top: -1px;
    }
    .tips {
      display: inline-flex;
      align-items: center;
      justify-content: flex-start;
      flex: 1;
    }
  }
  .arrow {
    width: 62rpx;
    height: 96rpx;
    display: block;
    margin: auto;
    margin-top: 76rpx;
  }
  .qrcode {
    margin: auto;
    margin-top: 44rpx;
    width: 264rpx;
    height: 264rpx;
    background-color: #f2f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    &.has-code {
      background-color: #a9ea6a;
    }
    image {
      width: 240rpx;
      height: 240rpx;
      margin: auto;
    }
  }
  .btn {
    width: 660rpx;
    margin: auto;
    margin-top: 28rpx;
    display: flex;
  }
  .call {
    text-align: center;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 36rpx;
    image {
      width: 32rpx;
      height: 32rpx;
      display: inline-block;
    }
    span {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #030303;
      font-weight: 400;
      display: inline-block;
      margin-left: 10rpx;
    }
  }
}

@keyframes opacityami {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
