<template>
  <div @click="swClick" class="switch">
    <div class="switch-slide" :class="{ 'switch-slide-on': swc }">
      <div class="switch-button"></div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    defaultValue: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      swc: false
    };
  },
  created() {
    this.defaultValue && (this.swc = this.defaultValue);
  },
  methods: {
    swClick() {
      this.swc = !this.swc;
      this.$emit('change', this.swc);
    }
  }
};
</script>
<style lang="less" scoped>
.switch {
  display: flex;
  align-items: center;
  width: 82rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background: #cdcdcd;
  box-sizing: border-box;
  .switch-slide {
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 44rpx;
    width: 44rpx;
    border-radius: 22rpx;
    transition: all 0.2s;
    .switch-button {
      margin: 0 4rpx;
      width: 36rpx;
      height: 36rpx;
      background-color: #fff;
      border-radius: 18rpx;
    }
  }
  .switch-slide-on {
    width: 82rpx;
    background: @border-color;
  }
}
</style>
