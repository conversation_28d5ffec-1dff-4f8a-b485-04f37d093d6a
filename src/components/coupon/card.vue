<template>
  <div
    class="card"
    :class="invalidCouponClass"
    :style="{
      'margin-bottom': marginBottom + 'rpx'
    }"
  >
    <!-- 卡片实体 -->
    <div class="inner">
      <div class="left">
        <div
          class="price"
          :class="{
            discount: Number(cardData.is_discount) === 1
          }"
        >
          {{ discountPrice }}
        </div>
        <div class="min">
          {{ discountMinLimit }}
        </div>
      </div>
      <div class="center"></div>
      <div class="right">
        <div class="name">
          <div class="tag" v-if="showTypeTag">
            {{ isAllowance ? '津贴' : '红包' }}
          </div>
          {{ cardData.name }}
        </div>
        <rich-text class="time" :nodes="timeRangeString"></rich-text>
        <!-- 津贴的话 -->
        <div v-if="isAllowance" class="rule no-arrow">可与红包叠加使用</div>
        <!-- 红包的话 -->
        <div
          v-else
          class="rule"
          :class="{
            fold
          }"
          @click="onRuleClick"
        >
          使用规则
        </div>
      </div>
      <!-- 按钮、各种状态 -->
      <!-- 场景:0-权益中心使用红包 -->
      <div
        v-if="scene === 0 && !isAllowance"
        class="primary-btn"
        @click="on2Sku"
      >
        去使用
      </div>
      <!-- 场景:1-权益中心查看历史红包 -->
      <block v-else-if="scene === 1">
        <div
          class="status"
          :class="{
            expired: cardData.expire_yn === 1,
            used: cardData.use_yn === 1
          }"
        ></div>
      </block>
      <!-- 场景:2-商品详情页页领取红包 -->
      <block v-else-if="scene === 2">
        <div
          v-if="cardData.receive_yn === 0"
          class="primary-btn"
          @click="onReceive"
        >
          领取
        </div>
        <div v-else class="status received"></div>
      </block>
      <!-- 场景:3-确认订单页选择红包-->
      <div
        class="check-btn"
        :class="{
          checked
        }"
        v-else-if="scene === 3"
        @click="onChoose"
      ></div>
    </div>
    <!-- 下面的规则折叠层 -->
    <div class="rule-box" v-if="fold">
      <div class="rule-text">
        {{ cardData.desc }}
      </div>
    </div>
    <!-- 是否是24小时内刚获得的红包 -->
    <div class="new" v-if="cardData.is_new === 1 && scene === 0">新获得</div>
  </div>
</template>
<script>
import dayjs from '@/common/dayjs.min';
import { cloneDeep } from 'lodash-es';
/**
 * @typedef Coupon 红包
 * @property {number} code_id 领取之后记录的ID
 * @property {number} coupon_id 红包ID
 * @property {number} days 领取之后多少天有限
 * @property {string} desc 描述
 * @property {number} discount_rate 折扣百分比
 * @property {string} end_date 结束时间
 * @property {number} expire_yn 是否过期
 * @property {number} is_discount 是否折扣券
 * @property {number} limit 是否可以重复领取 0: 无限领取
 * @property {number} money_max 最高门槛
 * @property {number} money_min 最低门槛
 * @property {string} name 红包名称
 * @property {number} price_deposit_cutdown 红包优惠金额
 * @property {number} remain_time 剩余有效期单位:S
 * @property {number} time_type 1:相对时间; 0:固定时间
 * @property {number} use_yn 是否使用
 * @property {string} start_date 开始时间
 * @property {number} is_new 是否是新的,左上角角标
 * @property {number} fe_type coupon 红包
 */

/**
 * @typedef Allowance 津贴
 * @property {number} allowance_id 津贴领取后的ID
 * @property {string} desc 描述
 * @property {string} end_date 截止时间
 * @property {number} expire_yn 是否过期
 * @property {number} is_new 是否是新的,左上角角标
 * @property {number} money_min 最小值
 * @property {string} name 红包名称
 * @property {number} price_deposit_cutdown 折扣金额
 * @property {number} remain_time 剩余有效期单位:S
 * @property {string} start_date 开始时间
 * @property {number} stock_id 津贴ID
 * @property {number} type 无意义
 * @property {number} use_yn 是否使用
 * @property {number} fe_type  allowance 津贴
 */

function covertTimeToHm(times) {
  const h = Math.floor(times / 3600);
  const m = Math.floor((times % 3600) / 60);
  return `<span style="color:#BE8E4E">有效期仅剩${h}小时${m}分钟</span>`;
}
export default {
  components: {},
  props: {
    /** @type {import('vue').PropType<Coupon|Allowance>}*/
    cardData: {
      type: Object,
      default: () => {}
    },
    scene: {
      type: Number,
      default: 0
    },
    showTypeTag: {
      type: Boolean,
      default: true
    },
    checked: {
      type: Boolean,
      default: false
    },
    marginBottom: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      fold: false
    };
  },
  computed: {
    discountPrice() {
      if (!this.cardData) return 0;
      const { is_discount, discount_rate, price_deposit_cutdown } =
        this.cardData;
      return is_discount === 1 ? `${discount_rate}折` : price_deposit_cutdown;
    },
    discountMinLimit() {
      if (!this.cardData) return '无门槛';
      return this.cardData.money_min > 0
        ? '满' + this.cardData.money_min + '可用'
        : '无门槛';
    },
    lt24hours() {
      if (!this.cardData) return false;
      return (
        this.cardData.remain_time && this.cardData.remain_time <= 24 * 3600
      );
    },
    // 红包时间展示逻辑
    couponTimeString() {
      if (!this.cardData) return '-';
      const {
        receive_yn,
        remain_time,
        start_date,
        end_date,
        time_type,
        days,
        expire_yn,
        use_yn
      } = this.cardData;
      // 过期、已经使用的红包
      if (expire_yn === 1 || use_yn === 1) return `${start_date}至${end_date}`;
      switch (time_type) {
        // 1如果是领取后多少天的模式
        case 1: {
          return receive_yn === 1
            ? this.lt24hours
              ? covertTimeToHm(remain_time)
              : `有效期至${end_date}`
            : `领取后${days}天有效`;
        }
        // 固定时间
        default:
          // 还未到红包有效期
          if (dayjs().isBefore(dayjs(start_date))) {
            return `${start_date}至${end_date}`;
          } else {
            return receive_yn === 1
              ? this.lt24hours
                ? covertTimeToHm(remain_time)
                : `有效期至${end_date}`
              : `${start_date}至${end_date}`;
          }
      }
    },
    // 津贴的时间展示逻辑
    allowanceTimeString() {
      if (!this.cardData) return '-';
      const {
        receive_yn,
        remain_time,
        start_date,
        end_date,
        expire_yn,
        use_yn
      } = this.cardData;
      // 过期、已经使用的红包
      if (expire_yn === 1 || use_yn === 1) return `${start_date}至${end_date}`;
      return receive_yn === 1
        ? this.lt24hours
          ? covertTimeToHm(remain_time)
          : `有效期至${end_date}`
        : `${start_date}至${end_date}`;
    },
    timeRangeString() {
      return `<p class="prime-coupon-time-range">${
        this.isAllowance ? this.allowanceTimeString : this.couponTimeString
      }</p>`;
    },
    isAllowance() {
      return this.cardData?.fe_type === 'allowance';
    },
    invalidCouponClass() {
      if (!this.cardData) return '';
      const { expire_yn, use_yn } = this.cardData;
      return expire_yn === 1 || use_yn === 1 ? 'invalid' : '';
    }
  },
  methods: {
    async on2Sku() {
      this.$emit('toSpu', this.cardData);
    },
    onChoose() {
      this.$emit('afterChoose', this.cardData);
    },
    async onReceive() {
      if (this.cardData.receive_yn === 1) return;
      this.$emit('receive', cloneDeep(this.cardData));
    },
    onRuleClick() {
      this.fold = !this.fold;
    }
  }
};
</script>
<style lang="less" scoped>
@theme-color: #ff4556;
.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.card {
  position: relative;
  margin: 0 auto;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  min-height: 113 * 2rpx;
  &.invalid {
    .left,
    .right,
    .rule-box .rule-text {
      opacity: 0.7;
    }
  }
  .inner {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 113 * 2rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      box-sizing: border-box;
      flex-shrink: 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-content: center;
      height: 113 * 2rpx;
      width: 94 * 2rpx;
      border: 1px solid #ffddde;
      border-right: none;
      background: #fff2f2;
      border-top-left-radius: 16rpx;
      border-bottom-left-radius: 16rpx;
      .price {
        .ellipsis();
        width: 100%;
        margin-bottom: -10rpx;
        font-style: normal;
        font-size: 50rpx;
        color: @theme-color;
        text-align: center;
        font-weight: 600;
        &::before {
          vertical-align: 4%;
          margin-right: -4rpx;
          content: '￥';
          font-size: 26rpx;
          color: @theme-color;
        }
        &.discount::before {
          display: none;
        }
      }
      .min {
        font-size: 24rpx;
        color: #999999;
        text-align: center;
        font-weight: 400;
      }
    }
    .center {
      flex-shrink: 0;
      margin: -1px 0;
      height: 113 * 2rpx;
      width: 28rpx;
      background: url(https://static.soyoung.com/sy-pre/line-1690524600769.png)
        no-repeat center center transparent;
      background-size: 28rpx 113 * 2rpx;
    }
    .right {
      flex: 1;
      height: 113 * 2rpx;
      box-sizing: border-box;
      padding-left: 14rpx;
      border: 1px solid #ffddde;
      border-left: none;
      background: #fff2f2;
      border-top-right-radius: 16rpx;
      border-bottom-right-radius: 16rpx;
      .name {
        .ellipsis();
        margin-top: 44rpx;
        color: #222222;
        line-height: 42rpx;
        height: 42rpx;
        font-weight: 600;
        // background: rgba(0, 0, 0, 0.1);
        .tag {
          position: relative;
          display: inline-block;
          margin-right: 10rpx;
          vertical-align: 14%;
          text-align: center;
          font-size: 20rpx;
          width: 60rpx;
          line-height: 32rpx;
          color: @theme-color;
          background: #fbe7e8;
          border-radius: 4rpx;
          font-weight: 500;
        }
      }
      .rule {
        position: relative;
        padding-top: 18rpx;
        width: 110rpx;
        font-size: 20rpx;
        color: #999999;
        font-weight: 400;
        &.no-arrow {
          width: 180rpx;
          &::after {
            display: none;
          }
        }
        &::before {
          position: absolute;
          content: '';
          top: 0;
          left: 0;
          height: 80rpx;
          width: 110rpx;
          // background: rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
        &::after {
          vertical-align: 2rpx;
          margin-left: 4rpx;
          display: inline-block;
          content: '';
          background: url(https://static.soyoung.com/sy-pre/arrow-1685340600733.png)
            no-repeat center center transparent;
          background-size: contain;
          height: 8rpx;
          width: 14rpx;
          transition: transform 0.3s;
        }
        &.fold::after {
          transform: rotate(180deg);
        }
      }
    }
  }
  .status {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 84rpx;
    width: 108rpx;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    z-index: 1;
    &.expired {
      background-image: url(https://static.soyoung.com/sy-pre/expired-1685362200714.png);
    }
    &.used {
      background-image: url(https://static.soyoung.com/sy-pre/used-1690722600719.png);
    }
    &.received {
      background-image: url(https://static.soyoung.com/sy-pre/received-1690722600719.png);
    }
  }
  .new {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100rpx;
    height: 40rpx;
    background: #f8eee2;
    border-radius: 6rpx 0 6rpx 0;
    font-size: 22rpx;
    color: #be8e4e;
    font-weight: 500;
    z-index: 2;
  }
  .rule-box {
    box-sizing: border-box;
    width: calc(100% - 2px);
    height: auto;
    margin: -14rpx auto 0;
    padding: 34rpx 24rpx 20rpx;
    border: 1px solid #ffddde;
    border-radius: 0 0 16rpx 16rpx;
    background: #fff2f2;
    overflow: hidden;
    z-index: 0;
    .rule-text {
      margin: 0 auto;
      width: 100%;
      font-size: 20rpx;
      color: #999999;
      line-height: 32rpx;
      font-weight: 400;
      overflow: hidden;
    }
  }
  .primary-btn {
    position: absolute;
    top: 130rpx;
    right: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68 * 2rpx;
    height: 28 * 2rpx;
    background: @theme-color;
    border-radius: 28rpx;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 400;
    z-index: 6;
  }
  .check-btn {
    position: absolute;
    top: 120rpx;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50 * 2rpx;
    height: 50 * 2rpx;
    z-index: 6;
    // background: rgba(0, 0, 0, 0.1);
    &::after {
      content: '';
      height: 34rpx;
      width: 34rpx;
      background: url(https://static.soyoung.com/sy-pre/check-1685351400720.png)
        center center transparent;
      background-size: contain;
    }
    &.checked::after {
      background: url(https://static.soyoung.com/sy-design/2cqx5i3pk5wiz1717640379655.png)
        center center transparent;
      background-size: contain;
    }
  }
}
</style>
<style lang="less">
.prime-coupon-time-range {
  padding: 10rpx 0;
  line-height: 32rpx;
  font-size: 21rpx;
  font-weight: 400;
  color: #666666;
  white-space: nowrap;
}
</style>
