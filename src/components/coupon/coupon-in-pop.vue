<template>
  <div class="popup-coupon" v-if="visible && filterList.length">
    <div class="coupon">
      <div
        class="header"
        :style="{
          backgroundImage: 'url(' + popupImages.top_img + ')'
        }"
      ></div>
      <ul
        class="center-list"
        :style="{
          backgroundImage: 'url(' + popupImages.mid_img + ')'
        }"
      >
        <li class="center" v-for="(item, index) in filterList" :key="index">
          <div class="item">
            <div class="money">
              <div class="money-discount">
                <template v-if="Number(item.is_discount) === 1">
                  <em>{{ item.discount_rate + '折' }}</em>
                </template>
                <template v-else>
                  <span>￥</span><em>{{ item.price_deposit_cutdown }}</em>
                </template>
              </div>
              <p class="money-min">满{{ item.money_min }}可用</p>
            </div>
            <div class="text">
              <p>{{ item.name }}</p>
              <p>
                {{ item.time_range_str }}
              </p>
            </div>
            <div class="btn">
              <button
                @click="receiveSingle(item)"
                :class="{
                  had: item.receive_yn === 1
                }"
              >
                {{ item.receive_yn === 1 ? '已领' : '领取' }}
              </button>
            </div>
          </div>
        </li>
      </ul>
      <div
        class="footer"
        :style="{
          backgroundImage: 'url(' + popupImages.bottom_img + ')'
        }"
      >
        <div class="get" @click="receiveAll"></div>
        <div class="close" @click="close"></div>
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash-es';
// import mixins from '@/components/spu/mixins';
import { apiGetReceiveAllowance, apiGetReceiveCoupon } from '@/api/coupon.js';
// import dayjs from '@/common/dayjs.min';

function getApi(type) {
  return type === 'coupon' ? apiGetReceiveCoupon : apiGetReceiveAllowance;
}
// 领取红包逻辑
async function _fmt2receive(type, ids) {
  if (Array.isArray(ids)) {
    ids = ids.join(',');
  }
  if (!ids) return [];
  const responseData = await getApi(type)(ids);
  if (Array.isArray(responseData) && responseData.length) return responseData;
  return [];
}
export default {
  components: {},
  props: {
    list: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    },
    popupImages: {
      type: Object,
      default: () => ({
        top_img: 'https://static.soyoung.com/sy-pre/top-1661778600681.png',
        mid_img: 'https://static.soyoung.com/sy-pre/center-1661778600681.png',
        bottom_img: 'https://static.soyoung.com/sy-pre/bottom-1661778600681.png'
      })
    }
  },
  data() {
    return {
      filterList: []
    };
  },
  watch: {
    list: {
      handler(value) {
        if (!value.length) return;
        this.filterList = [];
        const tempList = cloneDeep(value);
        while (tempList.length) {
          const cur = tempList.shift();
          if (+cur.receive_yn === 1) continue;
          if (!('coupon_id' in cur)) continue;
          cur.fe_type = 'coupon';
          cur.time_range_str =
            cur.time_type === 1
              ? `领取后${cur.days}天有效`
              : `${cur.start_date}至${cur.end_date}`;
          this.filterList.push(cur);
        }
      },
      deep: true
    }
  },
  computed: {
    // 存在未领取的红包
    hasAllReceived() {
      return this.filterList.every((item) => Number(item.receive_yn) === 1);
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false);
    },
    async receiveSingle(item) {
      if (!(await this.keepSession())) return;
      if (this.hasAllReceived) return;
      await _fmt2receive('coupon', item.coupon_id);
      item.receive_yn = 1;
      if (this.hasAllReceived) {
        this.close();
      }
      this.reportStat('sy_wxtuan_tuan_product:red_pop_get_click');
    },
    async receiveAll() {
      if (!(await this.keepSession())) return;
      if (this.hasAllReceived) return;
      const couponIds = this.filterList
        .filter((item) => Number(item.receive_yn) !== 1)
        .map((item) => item.coupon_id);
      await _fmt2receive('coupon', couponIds);
      this.close();
      this.reportStat('sy_wxtuan_tuan_product:red_pop_get_click');
    }
  }
};
</script>
<style lang="less" scoped>
.popup-coupon {
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100001;
  transform: translateZ(11px);
  .coupon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .header {
    height: 146 * 2rpx;
    width: 348 * 2rpx;
    background: url(https://static.soyoung.com/sy-pre/top-1661778600681.png)
      no-repeat center center transparent;
    background-size: 348 * 2rpx 146 * 2rpx;
  }
  .center-list {
    margin-top: -1px;
    box-sizing: border-box;
    padding: 5rpx;
    max-height: 300rpx;
    overflow-y: auto;
    overflow-x: hidden;
    background: url(https://static.soyoung.com/sy-pre/center-1661778600681.png)
      repeat-y transparent;
    background-size: 348 * 2rpx 18 * 2rpx;
    z-index: 1;
  }
  .center {
    margin-top: -2rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 136rpx;
    width: 348 * 2rpx;
    z-index: 4;
  }
  .footer {
    margin-top: -95rpx;
    position: relative;
    height: 155 * 2rpx;
    width: 348 * 2rpx;
    background: url(https://static.soyoung.com/sy-pre/bottom-1661778600681.png)
      no-repeat center center transparent;
    background-size: contain;
    z-index: -1;
  }
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    height: 63 * 2rpx;
    width: 256 * 2rpx;
    background: url(https://static.soyoung.com/sy-pre/coupon-1661778600681.png)
      no-repeat center center transparent;
    background-size: contain;
    .money {
      display: flex;
      justify-content: center;
      align-items: stretch;
      flex-wrap: wrap;
      box-sizing: border-box;
      flex-basis: 150rpx;
      color: #ff4040;
      .money-discount {
        margin-bottom: 10rpx;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        em {
          font-size: 48rpx;
          font-weight: 500;
        }
        span {
          margin-right: -2rpx;
          font-size: 30rpx;
        }
      }

      .money-min {
        width: 100%;
        text-align: center;
        font-size: 18rpx;
      }
    }
    .text {
      flex-basis: 230rpx;
      font-size: 25rpx;
      font-weight: 500;
      color: #222222;
      p:first-child {
        width: 230rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      p:last-child {
        font-size: 18rpx;
        color: #999999;
        font-weight: 400;
      }
    }
    .flex-center() {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .btn {
      position: relative;
      .flex-center;
      flex-basis: 136rpx;
      button {
        .flex-center;
        width: 44 * 2rpx;
        height: 22 * 2rpx;
        background: #ff4040;
        color: #fff;
        border-radius: 13 * 2rpx;
        font-size: 12px;
        padding: 0;
        &::after {
          content: '';
          border: none;
        }
        &.had {
          color: #ff4040;
          background: #fff;
        }
      }
    }
  }
  .get {
    position: absolute;
    top: 120rpx;
    left: 50%;
    transform: translateX(-50%);
    height: 55 * 2rpx;
    width: 55 * 2rpx;
    z-index: 1;
  }
  .close {
    position: absolute;
    bottom: -104rpx;
    left: 50%;
    height: 96 / 3 * 2rpx;
    width: 96 / 3 * 2rpx;
    background: url(https://static.soyoung.com/sy-pre/close-1656987000707.png)
      no-repeat center center transparent;
    background-size: contain;
    transform: translateX(-50%);
  }
}
</style>
