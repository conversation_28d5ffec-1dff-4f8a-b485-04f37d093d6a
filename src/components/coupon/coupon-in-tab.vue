<template>
  <div class="max-box">
    <div class="nav">
      <div class="left">
        <div
          v-for="item in typeItems"
          :key="item.type"
          class="btn"
          :class="{
            selected: item.type === type
          }"
          @click="onTypeChange(item.type)"
        >
          {{ item.text }}
        </div>
      </div>
      <div class="right" @click="onRule">
        使用说明
        <i></i>
      </div>
    </div>
    <div class="coupon-list" v-if="fetching || list.length">
      <CouponCard
        :marginBottom="20"
        v-for="item in list"
        :cardData="item"
        :key="item.allowance_id || item.code_id"
        :showTypeTag="false"
        :scene="0"
        @toSpu="on2Sku"
      />
      <div
        class="coupon-history"
        v-if="!fetching && hasHistory"
        @click="on2history"
      >
        查看历史优惠券
      </div>
    </div>
    <div class="coupon-empty" v-else>
      <div class="center">
        <img src="https://static.soyoung.com/sy-pre/coupon-1689066600715.png" />
        <p>暂无可用{{ type === 'coupon' ? '红包' : '津贴' }}</p>
      </div>
      <div
        class="coupon-history"
        v-if="!fetching && hasHistory"
        @click="on2history"
      >
        查看历史优惠券
      </div>
    </div>
    <PageLoading :visible="waiting || jumping" />
    <root-portal>
      <page-container :show="skuListVisible" @afterleave="afterleave">
        <div class="sku-container">
          <div class="close" @click="skuListVisible = false"></div>
          <div class="header">适用商品</div>
          <scroll-view
            scroll-y="true"
            @scrolltolower="onScrollBottom"
            class="body"
          >
            <block v-if="skuTotal">
              <div
                v-for="item in skuList"
                @click="
                  go2SkuPage(
                    item.pid,
                    item.material_id,
                    item.material_type,
                    item.times
                  )
                "
                :key="item.pid"
                class="reduce-p-item"
              >
                <div class="item-left">
                  <img :src="item.cover_img" />
                </div>
                <div class="item-right">
                  <div class="item-right-title">
                    {{ item.title }}
                  </div>
                  <div class="item-right-cont">
                    <img
                      v-if="item.floor_price > 0"
                      src="https://static.soyoung.com/sy-pre/3skcgkrkjl6wt-1672301400661.png"
                    />
                    <div
                      class="item-right-cont-o"
                      :class="{ 'item-right-cont-o-b': item.floor_price <= 0 }"
                    >
                      <span>￥</span>
                      {{ item.price_online }}
                    </div>
                    <div class="item-right-cont-n">
                      <div
                        v-if="item.floor_price > 0"
                        class="item-right-cont-n-c"
                      >
                        <span class="item-right-cont-m">￥</span
                        >{{ item.floor_price }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <LoadMore :opts="loadMoreOpts" />
            </block>
            <div class="sku-empty" v-else>
              <img
                src="https://static.soyoung.com/sy-pre/coupon-1689066600715.png"
              />
              <div>暂无商品支持此红包的使用</div>
            </div>
          </scroll-view>
        </div>
      </page-container>
    </root-portal>
  </div>
</template>
<script>
import CouponCard from '@/components/coupon/card.vue';
import PageLoading from '@/components/pageLoading.vue';
import LoadMore from '@/components/LoadMore.vue';
import { coupon_utils_formatCouponList } from '@/components/coupon/coupon-utils';
import {
  apiGetCoupon4CouponCenter,
  apiGetAllowance4CouponCenter,
  apiGetSkuByCoupon
} from '@/api/coupon';

import {
  getCancelToken,
  aboutRequestTask
} from '@/utils/common-package/request';

import { mapGetters } from 'vuex';
export default {
  components: { PageLoading, CouponCard, LoadMore },
  props: ['coupon_num', 'allowance_num'],
  data() {
    return {
      skuListVisible: false,
      fetching: true,
      waiting: false,
      jumping: false,
      type: 'coupon',
      list: [],
      skuFetching: false,
      skuList: [],
      skuTotal: 0,
      skuPage: 1,
      skuCouponId: 0,
      historyCouponList: [],
      historyAllowanceList: []
    };
  },
  watch: {
    isLogin(val) {
      if (val) {
        this.fetchData();
        this.fetchHistoryData();
      }
    },
    skuListVisible(val) {
      this.$setTabBar({
        showMenu: !val
      });
    }
  },
  computed: {
    ...mapGetters(['isLogin']),
    typeItems() {
      return [
        {
          type: 'coupon',
          text: `红包(${this.coupon_num})`
        },
        {
          type: 'allowance',
          text: `津贴(${this.allowance_num})`
        }
      ];
    },
    api() {
      return this.type === 'coupon'
        ? apiGetCoupon4CouponCenter
        : apiGetAllowance4CouponCenter;
    },
    loadMoreOpts() {
      return {
        more: this.skuTotal > this.skuList.length,
        total: this.skuTotal
      };
    },
    hasHistory() {
      return (
        [].concat(this.historyAllowanceList, this.historyCouponList).length > 0
      );
    }
  },
  onPageShow() {
    if (this.isLogin) {
      this.fetchData();
      this.fetchHistoryData();
    }
  },
  methods: {
    onTypeChange(type) {
      this.type = type;
      this.fetchData();
    },
    async fetchHistoryData() {
      [this.historyCouponList, this.historyAllowanceList] = await Promise.all([
        apiGetCoupon4CouponCenter(2),
        apiGetAllowance4CouponCenter(2)
      ]);
    },
    async fetchData() {
      this.fetching = true;
      const timer = setTimeout(() => {
        this.waiting = true;
      }, 500);
      aboutRequestTask(this.cancelToken);
      const list = await this.api(1, (this.cancelToken = getCancelToken()));
      if (list) {
        this.list = coupon_utils_formatCouponList(this.type)(list);
      }
      this.waiting = false;
      clearTimeout(timer);
      this.fetching = false;
    },
    onScrollBottom() {
      if (this.skuFetching) return;
      if (!this.loadMoreOpts.more) return;
      const callback = async () => {
        this.skuPage++;
        this.skuFetching = true;
        const responseData = await apiGetSkuByCoupon(
          this.skuCouponId,
          this.skuPage
        );
        this.skuFetching = false;
        if (!responseData) return;
        const { list } = responseData;
        this.skuList.push(...list);
      };
      clearTimeout(this.skuTimer);
      this.skuTimer = setTimeout(callback, 500);
    },
    async on2Sku(cardData) {
      this.$reportData({
        info: 'sy_wxtuan_tuan_yhq_info:use_bt_click',
        ext: {
          red_id: cardData.coupon_id
        }
      });
      if (this.jumping) return;
      this.jumping = true;
      this.skuCouponId = cardData.coupon_id;
      this.skuList = [];
      this.skuTotal = -1;
      this.skuPage = 1;
      const responseData = await apiGetSkuByCoupon(
        this.skuCouponId,
        this.skuPage
      );
      if (!responseData) {
        this.jumping = false;
        return;
      }
      const { total, list } = responseData;
      if (total > 1) {
        // await uni.hideTabBar({ animation: true });
        this.skuTotal = total;
        this.skuList = list;
        this.skuListVisible = true;
      } else if (total === 1) {
        const [{ pid, material_id, material_type, times }] = list || [];
        this.go2SkuPage(pid, material_id, material_type, times);
      } else {
        uni.$log('暂无可用商品，拼命上架中~' + cardData.coupon_id, 'error');
        uni.showToast({
          title: '暂无可用商品，拼命上架中~',
          icon: 'none'
        });
      }
      this.jumping = false;
    },
    afterleave() {
      this.skuListVisible = false;
      this.jumping = false;
      // uni.showTabBar({ animation: true });
      // this.$setTabBar({
      //   showMenu: true,
      //   animation: true
      // });
    },
    go2SkuPage(pid, material_id, material_type, times = 0) {
      this.$bridge({
        url: `/pages/product?material_id=${material_id}&material_type=${material_type}&sku_id=${pid}&times=${times}`
      });
    },
    on2history() {
      this.$reportData({
        info: 'sy_wxtuan_tuan_yhq_info:his_bt_click',
        ext: {}
      });
      this.$bridge({
        url: `/packageCoupon/history`,
        callbackSuccess: (res) => {
          res.eventChannel.emit('coupon_center_history_coupon_data_pass', {
            type: this.type,
            couponList: this.historyCouponList,
            allowanceList: this.historyAllowanceList
          });
        }
      });
    },
    onRule() {
      this.$emit('rule');
    }
  }
};
</script>
<style lang="less" scoped>
.max-box {
  display: static;
  padding-bottom: 180rpx;
  // border: 0.5px solid red;
}
@filter_height: 47 * 2rpx;
@btn_height: 27 * 2rpx;

.nav {
  position: sticky;
  top: 0;
  left: 0;
  box-sizing: border-box;
  padding: 0 30rpx;
  height: @filter_height;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  z-index: 10;
  .left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .btn {
      margin-right: 30rpx;
      height: @btn_height;
      display: flex;
      justify-content: center;
      align-items: center;
      height: btn_height;
      box-sizing: border-box;
      padding: 0 30rpx;
      font-size: 24rpx;
      color: #555;
      font-weight: 400;
      background: #f3f3f3;
      border-radius: @btn_height / 2;
      &.selected {
        color: @text-color;
        background: #ebe9f7;
        font-weight: 600;
      }
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .right {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 26rpx;
    color: #666666;
    font-weight: 400;
    i {
      margin-left: 8rpx;
      height: 26rpx;
      width: 26rpx;
      background: url(https://static.soyoung.com/sy-pre/info-1685081400731.png)
        no-repeat center center transparent;
      background-size: contain;
    }
  }
}
.coupon-list {
  box-sizing: border-box;
  padding: 20rpx 30rpx 20rpx;
  min-height: 300rpx;
  background: #ffffff;
  margin-bottom: 14rpx;
}
.coupon-history {
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999999;
  letter-spacing: 0;
  font-weight: 400;
  text-align: center;
  &::after {
    content: '';
    display: inline-block;
    vertical-align: -2%;
    width: 22rpx;
    height: 20rpx;
    background: url(https://static.soyoung.com/sy-pre/to-1685412600723.png)
      center center transparent;
    background-size: contain;
  }
}
.coupon-empty {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    display: block;
    margin: calc(50vh - 94rpx - 128rpx) auto 15rpx;
    width: 35 * 2rpx;
    height: 35 * 2rpx;
  }
  p {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
  .coupon-history {
    position: fixed;
    left: 50%;
    bottom: 220rpx;
    transform: translateX(-50%);
    z-index: 1;
  }
}
</style>
// sku-container
<style scoped lang="less">
@header_height:50 * 2rpx;
.sku-container {
  height: 90vh;
  .header {
    height: @header_height;
    line-height: @header_height;
    text-align: center;
    font-size: 32rpx;
    color: #222222;
    font-weight: 500;
  }
  .close {
    width: 80rpx;
    height: 80rpx;
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    background: url(https://static.soyoung.com/sy-pre/close-1661847000665.png)
      no-repeat center / 100%;
    background-size: 40rpx 40rpx;
    z-index: 10;
    // background-color: rgba(0, 0, 0, 0.1);
  }
  .body {
    height: calc(90vh - @header_height);
    box-sizing: border-box;
    padding: 0 30rpx;
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
  .sku-empty {
    img {
      display: block;
      margin: 200rpx auto 24rpx;
      width: 64 * 2rpx;
      height: 50 * 2rpx;
    }
    div {
      font-size: 28rpx;
      color: #999999;
      text-align: center;
      font-weight: 400;
    }
  }
}
</style>
// sku-card
<style lang="less" scoped>
.reduce-p-item {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  padding-top: 30rpx;
  padding-bottom: 24rpx;
  .item-left {
    img {
      width: 228rpx;
      height: 184rpx;
      border-radius: 8rpx;
    }
  }
  .item-right {
    font-family: PingFangSC-Medium;
    font-size: 30rpx;
    color: #222222;
    line-height: 48rpx;
    font-weight: 500;
    box-sizing: border-box;
    padding-left: 20rpx;
    .item-right-title {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      overflow: hidden;
      /*! autoprefixer: off */
      -webkit-box-orient: vertical;
      min-height: 2em;
    }
    .item-right-cont {
      position: relative;
      img {
        position: absolute;
        width: 280rpx;
        height: 40rpx;
        top: -15rpx;
        left: 4rpx;
      }
      .item-right-cont-o {
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #999999;
        text-align: center;
        line-height: 32rpx;
        font-weight: 400;
        text-align: left;
        margin-top: 16rpx;
      }
      .item-right-cont-o-b {
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color: #fb444f;
        font-weight: 600;
        span {
          margin-right: -4rpx;
        }
      }
      .item-right-cont-n {
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        height: 50rpx;
        color: #fb444f;
        font-weight: 600;
        text-align: left;
        // padding-left: 80rpx;
        box-sizing: border-box;
        position: relative;
        .item-right-cont-n-c {
          position: absolute;
          left: 140rpx;
          transform: translate(-50%);
          .item-right-cont-m {
            font-family: PingFangSC-Semibold;
            font-size: 22rpx;
            color: #fb444f;
            font-weight: 600;
          }
        }
      }
    }
  }
}
.reduce-p-item:last-child {
  border-bottom: 0;
}
</style>
