import { apiGetReceiveAllowance, apiGetReceiveCoupon } from '@/api/coupon.js';
// import dayjs from '@/common/dayjs.min';

function getApi(type) {
  return type === 'coupon' ? apiGetReceiveCoupon : apiGetReceiveAllowance;
}
// 领取红包逻辑
async function _fmt2receive(type, ids) {
  if (Array.isArray(ids)) {
    ids = ids.join(',');
  }
  if (!ids) return [];
  const responseData = await getApi(type)(ids);
  if (Array.isArray(responseData) && responseData.length) return responseData;
  return [];
}

// 处理前端数据
export function coupon_utils_formatCouponList(type) {
  return (list) =>
    list?.map((item) => ({
      ...item,
      fe_type: type
      // start_date: dayjs(item.start_date).format('YYYY-MM-DD'),
      // end_date: dayjs(item.end_date).format('YYYY-MM-DD')
    })) || [];
}

/**
 * 领取单个红包
 * @param {*} item
 * @param {*} targetList
 * @returns
 */
export async function coupon_utils_receiveSingleHandler(item) {
  if (+item.receive_yn === 1) return () => {};
  const receivedIdList = await _fmt2receive(
    item.fe_type,
    item.fe_type === 'coupon' ? item.coupon_id : item.stock_id
  );
  return function updateStatus(targetList = []) {
    while (receivedIdList.length > 0) {
      const receivedId = receivedIdList.shift();
      const target = targetList.find(
        (item) =>
          Number(item.fe_type === 'coupon' ? item.coupon_id : item.stock_id) ===
          Number(receivedId)
      );
      if (!target) return;
      target.receive_yn = 1;
    }
  };
}

/**
 * 领取所有红包
 * @param {Array} couponList
 * @param {Array} allowanceList
 * @returns
 */
export async function coupon_utils_receiveAllHandler(
  couponList,
  allowanceList
) {
  const couponIds = couponList
    .filter((item) => Number(item.receive_yn) !== 1)
    .map((item) => item.coupon_id);
  const allowanceIds = allowanceList
    .filter((item) => Number(item.receive_yn) !== 1)
    .map((item) => item.stock_id);
  const [receivedCouponIdList, receivedAllowanceIdList] = await Promise.all([
    couponIds ? _fmt2receive('coupon', couponIds) : Promise.resolve([]),
    allowanceIds ? _fmt2receive('allowance', allowanceIds) : Promise.resolve([])
  ]);
  return function updateStatus(
    targetCouponList = [],
    targetAllowanceList = []
  ) {
    // 红包状态
    while (receivedCouponIdList.length > 0) {
      const receivedId = receivedCouponIdList.shift();
      const target = targetCouponList.find(
        (item) => Number(item.coupon_id) === Number(receivedId)
      );
      if (!target) return;
      target.receive_yn = 1;
    }
    // 津贴状态
    while (receivedAllowanceIdList.length > 0) {
      const receivedId = receivedAllowanceIdList.shift();
      const target = targetAllowanceList.find(
        (item) => Number(item.stock_id) === Number(receivedId)
      );
      if (!target) return;
      target.receive_yn = 1;
    }
  };
}
