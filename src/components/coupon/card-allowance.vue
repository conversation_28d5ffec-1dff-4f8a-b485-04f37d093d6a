<template>
  <div
    class="card popup-allowance-scroll-item"
    :class="{
      invalid: item.status === 5,
      default: item.status === 4,
      used: item.status === 3
    }"
    :style="{
      'margin-bottom': marginBottom + 'rpx'
    }"
  >
    <view class="popup-allowance-scroll-item-1">
      <view class="popup-allowance-scroll-item-1-left">
        <text class="title">{{ item.title }}</text>
        <text class="label" v-if="item.desc">{{ item.desc }}</text>
      </view>
      <view class="popup-allowance-scroll-item-1-right">
        <div class="line-1">
          <text>￥</text>
          <text>{{ item.amount }}</text>
        </div>
        <div class="line-2">
          {{ item.min_money ? `满${item.min_money}元可用` : '无门槛' }}
        </div>
      </view>
    </view>
    <view class="popup-allowance-scroll-item-2">
      <text class="popup-allowance-scroll-item-2-left">{{
        item.use_time_notice
      }}</text>
      <text class="popup-allowance-scroll-item-2-right">{{
        item.subtitle
      }}</text>
    </view>
    <view class="popup-allowance-scroll-item-3">
      <text class="popup-allowance-scroll-item-3-left">{{ item.notice }}</text>
      <!--<image-->
      <!--  class="popup-allowance-scroll-item-3-right"-->
      <!--  :src="-->
      <!--    Number(item.is_selected) === 1-->
      <!--      ? 'https://static.soyoung.com/sy-pre/snipaste_2024-06-02_10-48-37-1717294200633.png'-->
      <!--      : 'https://static.soyoung.com/sy-pre/snipaste_2024-06-02_10-47-15-1717294200633.png'-->
      <!--  "-->
      <!--&gt;</image>-->
    </view>
    <!-- 是否是24小时内刚获得的红包 -->
    <!--<div class="new" v-if="item.is_new === 1 && scene === 0">新获得</div>-->
    <!--已使用-->
    <img
      v-if="item.status === 5"
      src="https://static.soyoung.com/sy-design/3nod68bwn53zw1717578467568.png"
      alt=""
      class="status-icon"
    />
    <!--已过期-->
    <img
      v-if="item.status === 3"
      src="https://static.soyoung.com/sy-design/3nod6md0jz60f1717578467576.png"
      alt=""
      class="status-icon"
    />
    <div v-if="showGoUseBtn" class="go-use-btn" @click="onGoUseBtnClick">
      去使用
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    valid: {
      type: Boolean,
      default: () => {}
    },
    showGoUseBtn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fold: false
    };
  },
  computed: {},
  methods: {
    onGoUseBtnClick() {
      const jumpLink = this.item.link_url;
      this.$reportData({
        info: 'sy_wxtuan_tuan_yhq_info:use_bt_click',
        ext: {
          red_id: this.item.allowance_id
        }
      });
      if (jumpLink) {
        if (jumpLink.startsWith('http')) {
          uni.navigateTo({
            url: '/pages/h5?url=' + encodeURIComponent(jumpLink)
          });
        } else if (jumpLink.startsWith('app.soyoung')) {
          uni.showToast({
            title: '请在APP中访问',
            icon: 'none'
          });
        } else if (jumpLink === '/pages/item') {
          uni.switchTab({
            url: '/pages/item'
          });
        } else {
          uni.navigateTo({
            url: jumpLink
          });
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
@px: 2rpx;

.popup-allowance-scroll-item {
  width: 100%;
  box-sizing: border-box;
  padding: 15 * @px;
  //background-image: linear-gradient(90deg, #fff2e2 0%, #ffeae2 100%);
  background: url('https://static.soyoung.com/sy-design/2oz4gvql823qq1717578467962.png')
    center/100% 100% no-repeat;
  border-radius: 8 * @px;
  margin-bottom: 10 * @px;
  position: relative;
  font-family: PingFangSC-Regular;
  height: 226rpx;
  &.invalid,
  &.used {
    background-image: linear-gradient(90deg, #fff2e277 0%, #ffeae277 100%);
    .popup-allowance-scroll-item-1,
    .popup-allowance-scroll-item-2,
    .popup-allowance-scroll-item-3 {
      opacity: 0.45;
    }
  }
  .popup-allowance-scroll-item-1,
  .popup-allowance-scroll-item-2,
  .popup-allowance-scroll-item-3 {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .popup-allowance-scroll-item-1 {
    height: 28 * @px;
    padding-bottom: 1 * @px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .popup-allowance-scroll-item-1-left {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding-right: 20 * @px;
      & > text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .title {
        font-family: PingFangSC-Medium;
        font-size: 15 * @px;
        color: #222222;
        font-weight: 500;
        padding-right: 6 * @px;
      }
      .label {
        font-family: PingFangSC-Regular;
        font-size: 10 * @px;
        color: #b68155;
        font-weight: 400;
        box-sizing: border-box;
        padding: 2 * @px 5 * @px;
        background-color: rgba(#d0842e, 0.15);
        border-radius: 2 * @px;
      }
    }
    .popup-allowance-scroll-item-1-right {
      .line-1 {
        display: flex;
        align-items: flex-start;
        & > text {
          overflow: hidden;
          white-space: nowrap;
        }
        text:first-child {
          font-family: PingFangSC-Medium;
          font-size: 13 * @px;
          color: #b68155;
          font-weight: 500;
          position: relative;
          top: 8rpx;
        }
        text:last-child {
          font-family: PingFangSC-Medium;
          font-size: 24 * @px;
          color: #b68155;
          font-weight: 500;
          position: relative;
          top: -10rpx;
        }
      }
      .line-2 {
        font-size: 12 * @px;
        color: #777777;
        text-align: right;
        position: relative;
        top: -16rpx;
      }
    }
  }
  .popup-allowance-scroll-item-2 {
    .popup-allowance-scroll-item-2-left {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #222222;
      font-weight: 400;
      padding-right: 20 * @px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      position: relative;
      top: -6rpx;
    }
    .popup-allowance-scroll-item-2-right {
      font-family: PingFangSC-Regular;
      font-size: 12 * @px;
      color: #777777;
      font-weight: 400;
    }
  }
  .popup-allowance-scroll-item-3 {
    padding-top: 36rpx;
    margin-top: 3 * @px;
    border-top: 1rpx dashed #e7b280;
    .popup-allowance-scroll-item-3-left {
      font-family: PingFangSC-Regular;
      font-size: 11 * @px;
      color: #777777;
      font-weight: 400;
      line-height: 16 * @px;
      min-height: 16 * @px;
    }
    .popup-allowance-scroll-item-3-right {
      width: 18 * @px;
      height: 18 * @px;
      min-width: 18 * @px;
    }
  }
}
.status-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 54 * @px;
  height: 42 * @px;
}
.go-use-btn {
  position: absolute;
  top: 147rpx;
  right: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 68 * 2rpx;
  height: 28 * 2rpx;
  background: #ff4556;
  border-radius: 28rpx;
  font-size: 26rpx;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
  z-index: 6;
}
</style>
