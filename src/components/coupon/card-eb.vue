<template>
  <div
    class="card"
    :class="{
      invalid: !valid
    }"
  >
    <!-- 卡片实体 -->
    <div class="inner">
      <div class="left">
        <div class="left-wrap">
          <div
            class="price"
            :class="{
              discount: Number(cardData.is_discount) === 1,
              'is-price': Number(cardData.is_discount) === 1
            }"
          >
            <span v-if="discountPrefix" class="discount-prefix">{{
              discountPrefix
            }}</span>
            {{ discountPrice }}
            <span v-if="discountAppend" class="discount-append">{{
              discountAppend
            }}</span>
          </div>
          <div class="min">
            {{ discountMinLimit }}
          </div>
        </div>
      </div>
      <div class="center"></div>
      <div class="right">
        <div class="right-wrap">
          <div class="name">
            <div class="tag" v-if="cardData.basic_desc.coupon_tag_name">
              {{ cardData.basic_desc.coupon_tag_name }}
            </div>
            {{ cardData.coupon_info.name }}
          </div>
          <div
            v-if="isWillExpire24h"
            class="time-string"
            style="color: #fe6631"
          >
            {{ countdownText }}
          </div>
          <div
            v-else
            class="time-string"
            :class="{
              small:
                cardData.get_info.use_time_notice &&
                cardData.get_info.use_time_notice.length > 26
            }"
          >
            {{ cardData.get_info.use_time_notice }}
          </div>
          <!-- 红包的话 -->
          <div
            class="rule"
            :class="{
              fold
            }"
            @click="onRuleClick"
          >
            使用规则
          </div>
        </div>
      </div>
      <!--下单 选择红包-->
      <div
        class="check-btn"
        :class="{
          checked
        }"
        v-if="scene === 3"
        @click="onChoose"
      ></div>
      <div v-if="isWillExpire48h" class="new-got-icon">即将过期</div>
      <div v-else-if="isNewGot" class="new-got-icon">新获得</div>
      <!-- <div class="new-got-icon">新获得</div> -->
      <!--已使用-->
      <div v-if="cardData.extend_info.use_type === 3" class="status-icon">
        已使用
      </div>
      <!--已过期-->
      <div v-if="cardData.extend_info.use_type === 4" class="status-icon">
        已失效
      </div>
      <div v-if="showGoUseBtn" class="go-use-btn" @click="onGoUseBtnClick">
        去使用
      </div>
    </div>
    <!-- 下面的规则折叠层 -->
    <div class="rule-box" v-if="fold">
      <div class="rule-text">
        {{ cardData.extend_info.desc }}
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash-es';
import SubscribeMixins from '@/mixins/subscribe';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
dayjs.extend(duration);

export default {
  components: {},
  mixins: [SubscribeMixins],
  props: {
    /** @type {import('vue').PropType<Coupon|Allowance>}*/
    cardData: {
      type: Object,
      default: () => {}
    },
    valid: {
      type: Boolean,
      default: true
    },
    scene: {
      type: Number,
      default: 0
    },
    showGoUseBtn: {
      type: Boolean,
      default: false
    },
    showTypeTag: {
      type: Boolean,
      default: true
    },
    checked: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fold: false,
      countdownText: ''
    };
  },
  computed: {
    isWillExpire24h() {
      // TODO 红包红点上线的时候 关掉
      if (this.scene === 1) return false;
      if (!this.cardData?.code_info?.end_date) return false;
      return (
        dayjs(this.cardData.code_info.end_date).subtract(1, 'day').valueOf() <
        dayjs().valueOf()
      );
    },
    isWillExpire48h() {
      // TODO 红包红点上线的时候 关掉

      if (this.scene === 1) return false;
      if (!this.cardData?.code_info?.end_date) return false;

      return (
        dayjs(this.cardData.code_info.end_date).subtract(2, 'day').valueOf() <
        dayjs().valueOf()
      );
    },
    isNewGot() {
      if (this.scene === 1) return false;
      const { create_date } = this.cardData.code_info;
      const now = new Date().getTime();
      const createTime = new Date(create_date).getTime();
      return now - createTime <= 24 * 60 * 60 * 1000;
    },
    discountPrice() {
      if (!this.cardData || !this.cardData.coupon_info) return 0;
      // type 1.满减券 2.折扣
      const { type, discount_value, discount_rate } = this.cardData.coupon_info;
      return type === 2 ? discount_rate : discount_value;
    },
    discountPrefix() {
      const { type } = this.cardData.coupon_info;
      return type === 1 ? '￥' : '';
    },
    discountAppend() {
      const { type } = this.cardData.coupon_info;
      return type === 2 ? `折` : '';
    },
    discountMinLimit() {
      if (!this.cardData || !this.cardData.coupon_info) return '无门槛';
      return this.cardData.coupon_info.min_amount > 0
        ? '满' + this.cardData.coupon_info.min_amount + '可用'
        : '无门槛';
    }
  },
  mounted() {
    if (this.isWillExpire24h) {
      this.countdown = this.cardData?.code_info?.end_date
        ? dayjs(this.cardData.code_info.end_date).valueOf() - dayjs().valueOf()
        : 0;
      // this.countdown = 10000
      if (this.countdown > 0) this.doCountDown();
    }
  },
  methods: {
    doCountDown() {
      this.countdownText = `有效期仅剩：${dayjs
        .duration(this.countdown)
        .format('HH:mm:ss')}`;
      if (this.countdown <= 0) {
        this.$emit('countdownEnd');
        return;
      }
      this.timer = setTimeout(() => {
        this.countdown -= 1000;
        this.doCountDown();
      }, 1000);
    },
    async onGoUseBtnClick() {
      this.createGroupBySub(
        [
          'F9tgwtgH228AvSbaXVGVAQ2dIrOR6LrapCWApcvv1Ts',
          'du6O-P5ekgS7sgunEZ7jZRxeXnc47hhYC0IKWcz_P_w',
          'oOKsRs0YI3ciiC42YX2JxEEZvzAzJXmVBzE6JqR04zE'
          // '9Z6qT2uSf1JM5UiiSY4KZjh_a8ozTolN_sS9jc-pr8w'
        ],
        [this.cardData.coupon_info.id]
      );
      const jumpLink = this.cardData.extend_info.jump_link;
      // const jumpType = this.cardData.extend_info.jump_type;
      // if (jumpType === 1)
      this.$reportData({
        info: 'sy_wxtuan_tuan_yhq_info:use_bt_click',
        ext: {
          red_id: this.cardData.code_info && this.cardData.code_info.coupon_id
        }
      });
      if (jumpLink) {
        if (jumpLink.includes('pages/index')) {
          uni.switchTab({
            url: '/pages/index'
          });
        } else if (jumpLink.includes('pages/item')) {
          uni.switchTab({
            url: '/pages/item'
          });
        } else if (jumpLink.startsWith('http')) {
          uni.navigateTo({
            url: '/pages/h5?url=' + encodeURIComponent(jumpLink)
          });
        } else if (jumpLink.startsWith('app.soyoung')) {
          uni.showToast({
            title: '请在APP中访问',
            icon: 'none'
          });
        } else {
          uni.navigateTo({
            url: jumpLink
          });
        }
      }
    },
    async on2Sku() {
      this.$emit('toSpu', this.cardData);
    },
    onChoose() {
      this.$emit('afterChoose', this.cardData);
    },
    async onReceive() {
      if (this.cardData.receive_yn === 1) return;
      this.$emit('receive', cloneDeep(this.cardData));
    },
    onRuleClick() {
      this.fold = !this.fold;
    }
  },
  beforeDestroy() {
    clearTimeout(this.timer);
  }
};
</script>
<style lang="less" scoped>
@theme-color: #61b43e;
.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.card {
  position: relative;
  margin: 0 auto 20rpx;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  min-height: 226rpx;

  overflow: hidden;
  &.invalid {
    .left-wrap,
    .right-wrap,
    .center,
    .rule-text {
      opacity: 0.7;
    }
    .rule-box {
      background: #f8f8f8;
    }
    .inner {
      background-image: url('https://static.soyoung.com/sy-pre/20240923-152049-1727075400629.png');
    }
  }
  .inner {
    background: url('https://static.soyoung.com/sy-pre/20240923-152049-1727075400629.png')
      center/100% 100% no-repeat;
    box-sizing: border-box;
    position: relative;
    width: 100%;
    height: 113 * 2rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      box-sizing: border-box;
      flex-shrink: 0;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-content: center;
      height: 113 * 2rpx;
      width: 104 * 2rpx;
      border-right: none;
      border-top-left-radius: 16rpx;
      border-bottom-left-radius: 16rpx;
      .price {
        .ellipsis();
        width: 100%;
        margin-bottom: -10rpx;
        font-style: normal;
        font-size: 52rpx;
        color: @theme-color;
        text-align: center;
        font-weight: 600;
        margin-bottom: 6rpx;
        &.is-price {
          &::before {
            vertical-align: 4%;
            margin-right: -4rpx;
            content: '￥';
            font-size: 26rpx;
            color: @theme-color;
          }
        }
        &.discount::before {
          display: none;
        }
        .discount-prefix {
          font-size: 28rpx;
        }
        .discount-append {
          font-size: 28rpx;
        }
      }
      .min {
        font-size: 24rpx;
        color: #999999;
        text-align: center;
        font-weight: 400;
      }
    }
    .center {
      flex-shrink: 0;
      margin: -1px 0;
      height: 113 * 2rpx;
      width: 28rpx;
      background-size: 28rpx 113 * 2rpx;
    }
    .right {
      flex: 1;
      height: 113 * 2rpx;
      box-sizing: border-box;
      padding-left: 14rpx;
      border-left: none;
      overflow: hidden;
      .right-wrap {
        width: calc(100% - 14rpx);
      }
      .name {
        .ellipsis();
        margin-top: 24rpx;
        color: #222222;
        height: 44rpx;
        font-weight: 600;
        font-size: 32rpx;

        // background: rgba(0, 0, 0, 0.1);
        .tag {
          position: relative;
          display: inline-block;
          margin-right: 10rpx;
          text-align: center;
          width: 60rpx;
          line-height: 36rpx;
          padding: 0 10rpx;
          background: #a9ea6a;
          font-family: PingFangSC-Medium;
          font-size: 22rpx;
          color: #030303;
          letter-spacing: 0;
          font-weight: 500;
          margin-top: 4rpx;
        }
      }
      .rule {
        position: relative;
        padding-top: 30rpx;
        font-size: 26rpx;
        color: #8c8c8c;
        font-weight: 400;
        line-height: 36rpx;
        display: flex;
        flex-direction: row;
        white-space: nowrap;
        height: 40rpx;
        align-items: center;
        gap: 12rpx;
        &.no-arrow {
          width: 180rpx;
          &::after {
            display: none;
          }
        }
        &::before {
          position: absolute;
          content: '';
          top: 0;
          left: 0;
          height: 80rpx;
          width: 110rpx;
          // background: rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
        &::after {
          vertical-align: 2rpx;
          margin-left: 4rpx;
          display: inline-block;
          content: '';
          background: url(https://static.soyoung.com/sy-pre/arrow-1685340600733.png)
            no-repeat center center transparent;
          background-size: contain;
          height: 14rpx;
          width: 20rpx;
          transition: transform 0.3s;
        }
        &.fold::after {
          transform: rotate(180deg);
        }
      }
    }
  }
  .status {
    position: absolute;
    right: 0;
    bottom: 0;
    height: 84rpx;
    width: 108rpx;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    z-index: 1;
    &.expired {
      background-image: url(https://static.soyoung.com/sy-pre/expired-1685362200714.png);
    }
    &.used {
      background-image: url(https://static.soyoung.com/sy-pre/used-1690722600719.png);
    }
    &.received {
      background-image: url(https://static.soyoung.com/sy-pre/received-1690722600719.png);
    }
  }
  .new {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100rpx;
    height: 40rpx;
    background: #f8eee2;
    border-radius: 6rpx 0 6rpx 0;
    font-size: 22rpx;
    color: #be8e4e;
    font-weight: 500;
    z-index: 2;
  }
  .rule-box {
    box-sizing: border-box;
    width: calc(100% - 2px);
    height: auto;
    margin: -14rpx auto 0;
    padding: 34rpx 24rpx 20rpx;
    border: 1px solid #f2f2f2;
    border-radius: 0 0 16rpx 16rpx;
    background: #f8f8f7;
    overflow: hidden;
    z-index: 0;
    .rule-text {
      margin: 0 auto;
      width: 100%;
      font-size: 20rpx;
      color: #999999;
      line-height: 32rpx;
      font-weight: 400;
      overflow: hidden;
    }
  }
  .primary-btn {
    position: absolute;
    top: 130rpx;
    right: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 68 * 2rpx;
    height: 28 * 2rpx;
    background: @theme-color;
    border-radius: 28rpx;
    font-size: 26rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 400;
    z-index: 6;
  }
  .check-btn {
    position: absolute;
    top: 120rpx;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50 * 2rpx;
    height: 50 * 2rpx;
    z-index: 6;
    // background: rgba(0, 0, 0, 0.1);
    &::after {
      content: '';
      height: 34rpx;
      width: 34rpx;
      background: url(https://static.soyoung.com/sy-pre/check-1685351400720.png)
        center center transparent;
      background-size: contain;
    }
    &.checked::after {
      background: url(https://static.soyoung.com/sy-design/3o6q6zpv0xqzv1717661039326.png)
        center center transparent;
      background-size: contain;
    }
  }
}
.time-string {
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  margin-top: 10rpx;
  line-height: 40rpx;
  color: #8c8c8c;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  &.small {
    font-size: 19rpx;
  }
}
.status-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 92rpx;
  height: 36rpx;
  font-size: 24rpx;
  color: #fff;
  background: #bababa;
  display: flex;
  align-items: center;
  justify-content: center;
}
.new-got-icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 36rpx;
  padding: 0 10rpx;
  font-size: 22rpx;
  line-height: 36rpx;
  color: #fe6631;
  background: #ffe8e0;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style lang="less">
.prime-coupon-time-range {
  padding: 10rpx 0;
  line-height: 32rpx;
  font-size: 21rpx;
  font-weight: 400;
  color: #666666;
  white-space: nowrap;
}
.go-use-btn {
  position: absolute;
  top: 130rpx;
  right: 34rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 66 * 2rpx;
  height: 30 * 2rpx;
  background: #333333;
  font-size: 26rpx;
  color: #ffffff;
  text-align: center;
  font-weight: 400;
  z-index: 6;
}
</style>
