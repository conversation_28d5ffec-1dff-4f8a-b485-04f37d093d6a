<!-- 接口文档 https://soyoung.feishu.cn/wiki/GubcwXxl6ixIC6kBdBBcFHygnFb#share-PyG5dOZCaoVkdAxZICPc3XZ2nae -->
<script>
export default {
  name: 'new-card',
  props: {
    source: {
      type: String,
      default: 'product'
    },
    cardData: {
      type: Object,
      default() {
        return {};
      }
    },
    rightButtonText: {
      type: String,
      default: '去使用'
    },
    rightButtonStatus: {
      type: Boolean,
      default: false
    },
    selectCouponId: {
      type: Number || String,
      default: 0
    }
  },
  data() {
    return {
      ruleVisible: false
    };
  },
  methods: {
    cardTap() {
      this.$emit('card-tap', this.cardData);
    },
    cardSelect() {
      this.$emit('card-select', this.cardData);
    }
  }
};
</script>

<template>
  <view class="new-card">
    <view v-if="cardData.basic_desc.coupon_status_name" class="new-card-tag">
      {{ cardData.basic_desc.coupon_status_name }}
    </view>
    <view
      v-else-if="
        cardData.extend_info &&
        cardData.extend_info.ext_desc_info &&
        cardData.extend_info.ext_desc_info.title
      "
      class="new-card-tag"
    >
      {{ cardData.extend_info.ext_desc_info.title }}
    </view>
    <view class="new-card__content">
      <view class="new-card__content__left">
        <view class="new-card__content__left-price">
          <block v-if="Number(cardData.coupon_info.type) === 1">
            <text class="new-card__content__left-price-min"> ￥ </text>
            <text class="new-card__content__left-price-max">
              {{ cardData.coupon_info.discount_value }}
            </text>
          </block>
          <block v-else-if="Number(cardData.coupon_info.type) === 2">
            <text class="new-card__content__left-price-max">
              {{ cardData.coupon_info.discount_rate }}
            </text>
            <text class="new-card__content__left-price-min"> 折 </text>
          </block>
        </view>
        <view class="new-card__content__left-tips">
          <block v-if="Number(cardData.coupon_info.min_amount)">
            满￥{{ cardData.coupon_info.min_amount }}可用
          </block>
          <block v-else> 无门槛 </block>
        </view>
      </view>
      <view class="new-card__content__right">
        <view class="new-card__content__right-title">
          <div class="tag" v-if="cardData.basic_desc.coupon_tag_name">
            {{ cardData.basic_desc.coupon_tag_name }}
          </div>
          {{ cardData.coupon_info.name }}
        </view>
        <view class="new-card__content__right-time">
          {{ cardData.get_info.use_time_notice }}
        </view>
        <view class="new-card__content__right-interactive">
          <view class="new-card__content__right-interactive-rule">
            <view
              v-if="cardData.extend_info.desc"
              @click="ruleVisible = !ruleVisible"
            >
              <text>使用规则</text>
              <image
                src="https://static.soyoung.com/sy-design/1orloiz8wps3a1726282046854.png"
                :style="{
                  transform: `rotate(${!ruleVisible ? '180deg' : '0deg'})`
                }"
              ></image>
            </view>
          </view>
          <image
            v-if="source === 'confirmOrder'"
            :src="
              cardData.code_info.code_id === selectCouponId
                ? 'https://static.soyoung.com/sy-design/1i3fb7pl05wlg1726282046849.png'
                : 'https://static.soyoung.com/sy-design/8k1ijrc526id1726282046701.png'
            "
            @click="cardSelect"
          ></image>
          <button
            v-else
            class="new-card__content__right-interactive-button"
            :class="{ disabled: rightButtonStatus }"
            :disabled="rightButtonStatus"
            @click="cardTap"
          >
            {{ rightButtonText }}
          </button>
        </view>
      </view>
    </view>
    <view v-if="ruleVisible" class="new-card__footer">
      {{ cardData.extend_info.desc }}
    </view>
  </view>
</template>

<style scoped lang="less">
@px: 2rpx;

.new-card {
  width: 100%;
  box-sizing: border-box;
  padding-top: 12 * @px;
  background-color: #f8f8f8;
  border: 1 * @px solid #f2f2f2;
  position: relative;

  &-tag {
    position: absolute;
    top: 0;
    left: 0;
    background: #ffe8e0;
    font-family: PingFangSC-Medium;
    font-size: 22rpx;
    color: #fe6631;
    letter-spacing: 0;
    line-height: 36rpx;
    padding: 0 10rpx;
    font-weight: 500;
  }
  &:before,
  &:after {
    content: '';
    width: 18 * @px;
    height: 9 * @px;
    border: 1 * @px solid #f2f2f2;
    background-color: #ffffff;
    position: absolute;
    z-index: 1;
    left: 98 * @px;
    box-sizing: border-box;
  }

  &:before {
    top: -1 * @px;
    border-top: none;
    border-radius: 0 0 100 * @px 100 * @px;
  }

  &:after {
    bottom: -1 * @px;
    border-bottom: none;
    border-radius: 100 * @px 100 * @px 0 0;
  }

  &__content {
    display: flex;
    padding-bottom: 14 * @px;

    &__left {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 107 * @px;
      min-width: 107 * @px;
      padding: 0 4 * @px;
      box-sizing: border-box;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        right: 0;
        height: 100%;
        width: 1 * @px;
        background-image: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0.05) 60%,
          rgba(0, 0, 0, 0.05) 0%,
          1px,
          transparent 1px,
          transparent
        );
        background-repeat: repeat-y;
        background-size: 100% 7 * @px;
      }

      &-price {
        display: flex;
        align-items: baseline;

        &-min {
          font-family: PingFangSC-Semibold;
          font-size: 14 * @px;
          color: #61b43e;
          letter-spacing: 0;
          font-weight: 600;
        }

        &-max {
          font-family: OutFit-Regular;
          font-size: 26 * @px;
          color: #61b43e;
          letter-spacing: 0;
          font-weight: 500;
        }

        text:last-child {
          padding-left: 1 * @px;
        }
      }

      &-tips {
        padding-top: 9 * @px;
        font-family: Outfit-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;
        letter-spacing: 0;
        font-weight: 400;
      }
    }

    &__right {
      flex: 1;
      padding-left: 15 * @px;
      padding-right: 15 * @px;

      &-title {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Medium;
        font-size: 16 * @px;
        color: #030303;
        letter-spacing: 0;
        font-weight: 500;
        .tag {
          font-family: PingFangSC-Medium;
          font-size: 22rpx;
          color: #030303;
          letter-spacing: 0;
          font-weight: 500;
          line-height: 36rpx;
          background: #a9ea6a;
          padding: 0 10rpx;
          margin-right: 10rpx;
        }
      }

      &-time {
        font-family: PingFangSC-Regular;
        font-size: 13 * @px;
        color: #8c8c8c;
        letter-spacing: 0;
        font-weight: 400;
        padding-top: 3 * @px;
      }

      &-interactive {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 9 * @px;

        > image {
          width: 17 * @px;
          height: 17 * @px;
          min-width: 17 * @px;
        }

        &-rule {
          display: flex;
          align-items: center;

          > view {
            display: flex;
            align-items: center;
          }

          text {
            font-family: PingFangSC-Regular;
            font-size: 13 * @px;
            color: #8c8c8c;
            letter-spacing: 0;
            font-weight: 400;
          }

          image {
            width: 8 * @px;
            height: 6.5 * @px;
            min-width: 8 * @px;
            margin-left: 6 * @px;
          }
        }

        &-button {
          height: 30 * @px;
          margin: 0;
          padding: 0;
          width: 66 * @px;
          min-width: 66 * @px;
          border: none;
          background-color: #333333;
          border-radius: 0;
          font-family: PingFangSC-Medium;
          font-size: 13 * @px;
          color: #ffffff;
          letter-spacing: 0;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;

          &:after {
            border: none;
          }
        }

        .disabled {
          background-color: #bababa;
          color: #ffffff;
        }
      }
    }
  }

  &__footer {
    font-family: PingFangSC-Regular;
    font-size: 13 * @px;
    color: #8c8c8c;
    letter-spacing: 0;
    font-weight: 400;
    border-top: 1 * @px solid #f2f2f2;
    padding: 10 * @px 15 * @px;
    position: relative;

    &:before {
      content: '';
      width: 18 * @px;
      height: 9 * @px;
      border: 1 * @px solid #f2f2f2;
      background-color: #f8f8f8;
      position: absolute;
      z-index: 1;
      left: 98 * @px;
      box-sizing: border-box;
      top: -9 * @px;
      border-bottom: none;
      border-radius: 100 * @px 100 * @px 0 0;
    }
  }
}
</style>
