<template>
  <view>
    <view v-if="showDialog" class="warp-dialog">
      <view class="dialog">
        <view :class="[title ? 'title' : '']">{{ title }}</view>
        <view class="com-sty">{{ infoData }}</view>
        <view class="buttons">
          <view @click="onCancel" v-if="cancel">取消</view>
          <view class="ok" @click="onOk">确定</view>
        </view>
      </view>
    </view>
    <view v-if="showDialog" class="uni-mask"></view>
  </view>
</template>
<script>
export default {
  name: 'modal',
  props: {
    title: {
      type: String,
      default: ''
    },
    infoData: {
      type: String,
      default: ''
    },
    showDialog: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    /**
     * 关闭
     * @returns null
     */
    onCancel() {
      this.showDialog = false;
    },
    /**
     * 确认
     * @returns null
     */
    onOk() {
      this.$emit('ok');
    }
  }
};
</script>
<style lang="less" scoped>
.warp-dialog {
  display: flex;
  align-items: center;
  flex-direction: column;
  min-height: 100vh;
}
.dialog {
  position: fixed;
  background: #ffffff;
  border-radius: 16rpx;
  height: 250rpx;
  width: 80%;
  // inset: 0;
  z-index: 11;
  font-family: PingFangSC-Regular;
  font-size: 24rpx;
  line-height: 24rpx;
  transition: opacity 300ms ease 0ms, -webkit-transform 300ms ease 0ms,
    transform 300ms ease 0ms;
  transform-origin: 50% 50%;
  text-align: center;
  margin: 0 auto;
  top: 33%;
  .title {
    font-size: 30rpx;
    color: #333333;
    text-align: center;
    line-height: 42rpx;
    margin-top: 70rpx;
    margin-bottom: 35rpx;
  }
  .com-sty {
    padding: 20px;
    height: 80rpx;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #333;
    font-weight: 400;
    text-align: left;
    letter-spacing: 0;
    overflow: hidden;
  }
  .buttons {
    display: flex;
    width: 100%;
    font-family: PingFangSC-Regular;
    .ok {
      width: 256rpx;
      height: 60rpx;
      background: #ff6161;
      border-radius: 42rpx;
      text-align: center;
      margin: 0 auto;
      line-height: 60rpx;
      font-family: PingFangSC-Medium;
      font-size: 28rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
    }
  }
  .red-sty {
    color: #ff6161;
  }
}
.uni-mask {
  z-index: 10;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: fixed;
  opacity: 0.4;
  background: #000000;
}
</style>
