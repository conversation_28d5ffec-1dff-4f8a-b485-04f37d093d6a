<template>
  <div
    class="modal-mask"
    @click="close"
    :style="{ bottom: '-1px' }"
    @animationend="animationend"
    :class="{
      'fade-in': transform
    }"
    v-if="visible"
  >
    <div class="mask"></div>
    <div
      class="pannel"
      :class="{
        safe: false
      }"
      @click.stop
    >
      <div class="title">选择城市</div>
      <div class="close" @click="close"></div>
      <div class="city-bottom-modal-content">
        <div class="city-filter">
          <div class="icon"></div>
          <input
            type="text"
            placeholder="搜城市"
            placeholder-class="hospital-choose-input-placeholder"
            confirm-type="search"
            v-model.trim="searchCityInput"
          />
        </div>
        <scroll-view scroll-y :scroll-x="false" class="city-list">
          <div class="js-wraper" v-if="cityListWithFilter.length">
            <div
              class="city-item"
              :key="city.city_id"
              v-for="city in cityListWithFilter"
              :data-id="city.city_id"
              :class="{
                active: Number(defaultCityId) === Number(city.city_id)
              }"
              @click="handleCitySelect"
            >
              <div class="name">{{ city.city_name }}</div>
              <div class="duihao"></div>
            </div>
          </div>
          <div v-else class="city-empty">非常抱歉没找到结果</div>
        </scroll-view>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    defaultCityId: {
      type: Number || String,
      default: ''
    },
    cityList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      transform: false,
      // cityList: [],
      searchCityInput: ''
    };
  },
  computed: {
    cityListWithFilter() {
      if (!this.searchCityInput) return this.cityList;
      return (
        this.cityList?.filter(({ city_name }) =>
          city_name.includes(this.searchCityInput)
        ) || []
      );
    }
  },
  // created() {
  //   this.fetchCityList();
  // },
  watch: {
    visible(visible) {
      if (visible) {
        setTimeout(() => {
          this.transform = true;
        }, 50);
      } else {
        this.transform = false;
      }
    }
  },
  methods: {
    async animationend() {
      setTimeout(() => {
        this.$emit('open');
      }, 100);
    },
    close() {
      this.$emit('visible', false);
    },
    // async fetchCityList() {
    //   const list = await this.getHospitalListWithLetter();
    //   if (list) this.cityList = list;
    // },
    handleCitySelect(event) {
      const cityId = Number(event.currentTarget.dataset.id);
      this.$emit('city-change', cityId);
    }
    // async getHospitalListWithLetter() {
    //   const res = await this.$request({
    //     url: '/'
    //   })
    //     .then((res) => res.data)
    //     .catch((error) => {
    //       return {
    //         errorCode: -100,
    //         errorMsg: error || '网络错误,稍后再试',
    //         responseData: null
    //       };
    //     });
    //   const { errorCode, responseData } = res;
    //   if (+errorCode === 0 && Array.isArray(responseData?.list)) {
    //     return responseData.list;
    //   } else {
    //     return [];
    //   }
    // }
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(900px);
  &.fade-in {
    .mask {
      opacity: 1;
    }
    .pannel {
      transform: translateY(0);
    }
  }
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 2px);
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.7);
  transition: opacity 0.2s;
}
.pannel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.2s;
  background-color: #ffffff;
  &.safe {
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
}
.title {
  .flex-align-center;
  font-family: PingFangSC-Medium;
  justify-content: center;
  height: 52 * 2rpx;
  font-size: 32rpx;
  color: #030303;
  font-weight: 500;
}
.close {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 28rpx;
  background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1726026826488.png)
    no-repeat center / 100%;
  z-index: 9;
}

.city-bottom-modal-content {
  position: relative;
  display: block;
  // 70 UI 要求占屏幕高
  // 104 modal 的 title bar 高度
  height: calc(60vh - 104rpx);
  box-sizing: border-box;
  padding: 0 30rpx;
  .city-filter {
    box-sizing: border-box;
    padding: 0 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64rpx;
    width: 100%;
    background: #f2f2f2;
    border-radius: 2px;
    .icon {
      margin-right: 10rpx;
      width: 28rpx;
      height: 28rpx;
      background: url(https://static.soyoung.com/sy-design/1zuveb3ngazob1725359158395.png)
        no-repeat center center transparent;
      background-size: contain;
    }
    input {
      // 38 图标 width
      // 10 右内边距
      width: calc(100% - 38rpx - 10rpx);
    }
  }
  .city-list {
    box-sizing: border-box;
    margin: 20rpx 0;
    // 70 UI 要求占屏幕高
    // 64 input检索框高度
    // 104 modal 标题bar 高度
    // 40 城市列表的 上下外边距 之和
    max-height: calc(70vh - 64rpx - 104rpx - 40rpx);
    overflow-y: auto;
    .city-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 80rpx;
      &.active {
        .name {
          color: #61b43e;
          font-weight: 500;
        }
        .duihao {
          transform: opacity, 0.3s;
          opacity: 1;
        }
      }
      .name {
        font-family: PingFangSC-Regular;
        font-size: 30rpx;
        color: #030303;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
      .duihao {
        opacity: 0;
        height: 32rpx;
        width: 32rpx;
        background: url(https://static.soyoung.com/sy-design/b0ptomak0liw1726828968901.png)
          no-repeat center center transparent;
        background-size: 32rpx 32rpx;
      }
    }
  }
  .city-empty {
    box-sizing: border-box;
    padding-top: 30rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
}
</style>
<style lang="less">
.hospital-choose-input-placeholder {
  font-family: PingFangSC-Regular;
  font-size: 26rpx;
  color: #bababa;
  font-weight: 400;
}
</style>
