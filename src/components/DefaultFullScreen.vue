<template>
  <div class="full-page">
    <div class="center">
      <div class="cover">
        <img :src="currentPage.url" />
      </div>
      <p class="text">{{ customText || currentPage.text }}</p>
      <div
        v-if="(customAction || currentPage.action) === 'home'"
        class="btn"
        @click="on2home"
      >
        去首页逛逛
      </div>
      <div v-else class="btn" @click="on2login">去登录</div>
    </div>
    <PageLoading :visible="mixin_login_doing" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import PageLoading from '@/components/pageLoading.vue';
const map = {
  sku: {
    url: 'https://static.soyoung.com/sy-pre/3b98ybomv9usz-1717661400634.png',
    text: '您浏览的商品已下架',
    action: 'home'
  },
  coupon: {
    url: 'https://static.soyoung.com/sy-pre/15btn671l88lv-1717661400634.png',
    text: '登录后可查看权益',
    action: 'login'
  },
  order: {
    url: 'https://static.soyoung.com/sy-design/2hca637nb8ad51717666542243.png',
    text: '登录后可查看订单',
    action: 'login'
  }
};
export default {
  props: {
    pageKey: {
      type: String,
      default: ''
    },
    customText: {
      type: String,
      default: ''
    },
    customAction: {
      type: String,
      default: ''
    }
  },
  components: {
    PageLoading
  },
  data() {
    return {
      mixin_login_doing: false
    };
  },
  computed: {
    currentPage() {
      return map[this.pageKey] || {};
    },
    ...mapGetters(['isLogin'])
  },
  methods: {
    on2home() {
      uni.switchTab({
        url: '/pages/index'
      });
    },
    async on2login() {
      this.mixin_login_doing = true;
      if (!this.isLogin) {
        await this.$login().catch((msg) => {
          if (msg !== '页面退回') {
            uni.showModal({
              title: '登录异常',
              content: msg,
              showCancel: false
            });
          }
          return null;
        });
      }
      this.mixin_login_doing = false;
    }
  }
};
</script>
<style lang="less" scoped>
.full-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  transform: translateZ(99998px);
  background-color: #f6f6f6;
  .center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .cover {
    display: block;
    height: 180rpx;
    width: 180rpx;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #999999;
    text-align: center;
    font-weight: 400;
  }
  .btn {
    margin: 48rpx auto 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 420rpx;
    height: 76rpx;
    line-height: 76px;
    border-radius: 90rpx;
    border: 4rpx solid @border-color;
    font-size: 26rpx;
    color: @text-color;
    text-align: center;
    font-weight: 500;
  }
}
</style>
