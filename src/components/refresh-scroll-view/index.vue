<template>
  <view
    class="refresh-scroll-view"
    ref="refresh-scroll-view"
    :class="{ 'show': showPullDown }"
    :style="[scrollContainerStyle]"
    @touchstart="touchstart"
    @touchmove="touchmove"
    @touchend="touchend"
    @touchcancel="touchcancel"
  >
    <!-- 顶部loading动画，下拉和更新整个列表时显示 -->
    <view
      class="pull-down-refresh-loading"
      :style="{ 'top': fixedTopHeight + 116 + 'rpx' }"
      v-show="showPullDown"
    >
      <image
        class="loading-icon"
        src="https://static.soyoung.com/sy-pre/9lklkdzpl9nx-1727251800646.gif"
      />
    </view>

    <view class="scroll-Y" :style="[scrollContentStyle]">
      <slot></slot>
    </view>

    <!-- 底部loading动画，加载下一页时显示 -->
    <template v-if="!showPullDown && showHasMore">
      <template v-if="hasMore == 1">
        <view class="pull-up-refresh-loading" v-if="loading">
          <image
            v-show="loading"
            class="loading-icon"
            src="https://static.soyoung.com/sy-pre/9lklkdzpl9nx-1727251800646.gif"
          />
        </view>
        <div class="to-get-more" @click="loadNextPage" v-else-if="isLogin">
          点击加载更多
        </div>
      </template>
      <template v-else>
        <div class="not-has-more">没有更多了</div>
      </template>
    </template>
  </view>
</template>

<script>
/**
 * 封装 下拉刷新，上拉加载，左右滑动
 */

export default {
  name: 'refresh-scroll-view',
  components: {},
  props: {
    touchHeight: {
      // 上下滑动触发超出距离
      type: Number,
      default: 150
    },
    touchWidth: {
      // 左右滑动触发超出距离
      type: Number,
      default: 60
    },
    height: {
      type: Number,
      default: 0
    },
    bottom: {
      // 和底部距离多远，执行触底方法（用来预加载）
      type: Number,
      default: 50
    },
    // 列表刷新，或第一页数据获取中
    refreshing: {
      type: [String, Boolean],
      default: true
    },
    // 获取下一页数据中
    loading: {
      type: [String, Boolean],
      default: true
    },
    // 是否在顶部
    isInTop: {
      type: Boolean,
      default: true
    },
    // 是否在底部
    isInBottom: {
      type: Boolean,
      default: false
    },
    // 父级页面滚动的距离（距离顶部）
    pageScrollTop: {
      type: Number,
      default: 0
    },
    hasMore: {
      type: [String, Number],
      default: 1
    },
    isLogin: {
      type: Boolean,
      default: false
    },
    // fixed定位时顶部预留的高度，px
    fixedTopHeight: {
      type: Number,
      default: 0
    },
    showHasMore: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      scrollTop: 0,
      old: {
        scrollTop: 0
      },

      touch_start: {
        x: '',
        y: ''
      }, // 手指起始位置
      touch_end: {
        x: '',
        y: ''
      }, // 手指位置
      touch_direction: '', // 手指移动方向

      showPullDown: false, // 是否显示下拉刷新

      pullDownCanDo: false, // 是否可以调用刷新

      touchLeftCanDo: false, // 是否可以调用左侧滑动
      touchRightCanDo: false, // 是否可以调用右侧滑动

      moveX: 0,
      moveY: 0,
      doLoadmore: false, // 开始上拉加载

      isCloseTip: true, // 是否关闭了提示

      hasMove: false
    };
  },
  computed: {
    scrollContainerStyle() {
      return {
        height: `${this.height || this.height_}rpx`
      };
    },
    scrollContentStyle() {
      const top = this.showPullDown ? Math.max(this.moveY, 130) : this.moveY;
      return {
        height: `${this.height || this.height_}rpx`,
        'margin-top': `${top}rpx`
      };
    }
  },
  watch: {
    refreshing: {
      handler(v) {
        if (+this.pageScrollTop === 0) {
          this.showPullDown = v;
        }
      },
      deep: true,
      immediate: true
    },
    // loading: {
    //   handler(v) {
    //     if (+this.pageScrollTop === 0) {
    //       this.showPullDown = v;
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // },
    // 值有变更即
    pageScrollTop: {
      handler(v) {
        this.scroll(v);
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    const that = this;
    uni.getSystemInfo({
      success: function (e) {
        that.height_ = (750 / e.windowWidth) * e.windowHeight;
      }
    });
  },
  mounted() {},
  destroyed() {},
  methods: {
    scroll(v) {
      this.touch_direction = '';
      this.pullDownCanDo = false;
      this.hasMove = true;

      this.old.scrollTop = v;
    },
    upper() {
      // console.log("到达顶部")
    },
    lower() {
      // console.log("到达底部")
    },
    goTop() {
      const that = this;
      // 回到顶部
      this.scrollTop = this.old.scrollTop;
      this.$nextTick(function () {
        that.scrollTop = 0;
        // 解决设置 scrollTop 失效
        wx.pageScrollTo({
          scrollTop: that.scrollTop
        });
      });
    },
    touchstart(event) {
      // 手指按下
      // console.log('start');
      this.touch_start.x = event.changedTouches[0].pageX;
      this.touch_start.y = event.changedTouches[0].pageY;

      this.hasMove = false;
    },
    touchmove(event) {
      // console.log('move');
      const end_move_page_x = event.changedTouches[0].pageX;
      const end_move_page_y = event.changedTouches[0].pageY;

      const moveX = this.touch_start.x - end_move_page_x;
      const moveY = this.touch_start.y - end_move_page_y;

      if (moveX === 0) {
        // console.log('回到原位置');
        this.stopTips();
      }
      if (moveX > 0 && Math.abs(moveY) < 10) {
        // console.log('向左滑动');
        if (Math.abs(moveX) > Math.abs(moveY)) {
          this.touch_direction = 'left';
        }
      }

      if (moveX < 0 && Math.abs(moveY) < 10) {
        // console.log('向右滑动');
        if (Math.abs(moveX) > Math.abs(moveY)) {
          this.touch_direction = 'right';
        }
      }

      if (moveY === 0) {
        // console.log('回到原位置');
        this.stopTips();
      }
      if (moveY > 0) {
        // console.log('向上滑动');
        if (Math.abs(moveY) > Math.abs(moveX)) {
          this.touch_direction = 'top';
        }
      }
      if (moveY < 0) {
        // console.log('向下滑动');
        if (Math.abs(moveY) > Math.abs(moveX)) {
          this.touch_direction = 'bottom';
        }
      }

      this.hasMove = true;

      this.checkTouchY(moveX, moveY);
    },

    checkTouchY(moveX, moveY) {
      // console.log('move_check');

      this.pullDownCanDo = false;

      this.touchLeftCanDo = false;
      this.touchRightCanDo = false;

      this.showPullDown = false;

      this.moveX = Math.abs(moveX);
      this.moveY = Math.abs(moveY);

      // 下拉 并且 在顶部
      if (this.touch_direction === 'bottom' && this.isInTop) {
        // console.log('下拉 并且在 顶部')
        // console.log(this.moveY)
        // console.log(this.touchHeight)
        if (this.moveY > this.touchHeight) {
          // 下拉 并且在 顶部
          // console.log("我在顶部下拉")
          this.showPullDown = true; // 显示下拉刷新
          this.pullDownCanDo = true; // 执行刷新
          this.moveY = Math.abs(this.touchHeight);
        }
      } else if (
        this.touch_direction === 'left' &&
        this.moveX > this.touchWidth &&
        this.moveY < 50
      ) {
        this.touchLeftCanDo = true;
      } else if (
        this.touch_direction === 'right' &&
        this.moveX > this.touchWidth &&
        this.moveY < 50
      ) {
        this.touchRightCanDo = true;
      }
    },
    touchend(event) {
      // console.log('end');
      // console.log(this.moveY);
      // console.log(event);
      this.doLoadmore = false;

      this.touch_end.x = event.changedTouches[0].pageX;
      this.touch_end.y = event.changedTouches[0].pageY;

      if (!this.hasMove) {
        // const moveX = this.touch_start.x - this.touch_end.x;
        // const moveY = this.touch_start.y - this.touch_end.y;

        this.touch_direction = 'top';

        this.checkTouchY(0, this.touchHeight + 10);
      }

      // console.log("this.touch_direction", this.touch_direction)
      // console.log("this.isInBottom", this.isInBottom)
      // console.log("this.isInTop", this.isInTop)
      // console.log("this.pullDownCanDo", this.pullDownCanDo)

      if (this.pullDownCanDo) {
        // 调用刷新
        this.onPullDown();
      } else if (this.touchLeftCanDo) {
        // 触发左滑
        this.onTransverseTouch('left');
      } else if (this.touchRightCanDo) {
        // 触发右滑
        this.onTransverseTouch('right');
      } else {
        this.stopTips();
      }
    },
    touchcancel() {
      // console.log('cancel');
    },
    // 触发左右滑动
    onTransverseTouch() {
      const that = this;
      // 隐藏刷新文字提示
      this.clearTimer = setTimeout(function () {
        that.stopTips();
      }, 3000);
    },
    // 触发下拉
    onPullDown() {
      const that = this;
      // 刷新
      this.goTop(); // 回到顶部

      // console.log('下拉刷新');
      this.$emit('onPullDown', this.stopTips);

      // 隐藏刷新文字提示
      this.clearTimer = setTimeout(function () {
        that.stopTips();
      }, 3000);
    },
    stopTips() {
      const that = this;
      this.isCloseTip = false;

      // 如果提示没关闭，则关闭
      if (!this.isCloseTip) {
        if (this.clearTimer) {
          clearTimeout(this.clearTimer);
        }

        this.clearTimer = setTimeout(function () {
          that.showPullDown = false;
          that.pullDownCanDo = false;

          that.touchLeftCanDo = false;
          that.touchRighttCanDo = false;

          that.isCloseTip = true;

          if (that.clearTimer) {
            clearTimeout(that.clearTimer);
          }
        }, 500);
      }
    },
    loadNextPage() {
      this.$emit('loadNextPage');
    }
  }
};
</script>

<style lang="scss" scoped>
@keyframes fade-in {
  0% {
    opacity: 0;
    height: 0;
  }
  100% {
    opacity: 1;
    height: 130rpx;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
    height: 130rpx;
  }
  100% {
    opacity: 0;
    height: 0;
  }
}

@keyframes icon-fade-in {
  0% {
    opacity: 0;
    height: 0rpx;
    width: 0rpx;
  }
  100% {
    opacity: 1;
    height: 40rpx;
    width: 40rpx;
  }
}

@keyframes icon-fade-out {
  0% {
    opacity: 1;
    height: 40rpx;
    width: 40rpx;
  }
  100% {
    opacity: 0;
    height: 0rpx;
    width: 0rpx;
  }
}

@keyframes margin-in {
  0% {
    margin-top: 0rpx;
  }
  100% {
    margin-top: 130;
  }
}

@keyframes margin-out {
  0% {
    margin-top: 130rpx;
  }
  100% {
    margin-top: 0;
  }
}

.image {
  width: 100%;
  height: 100%;
}

.refresh-scroll-view {
  width: 100%;
  position: relative;
  overflow: hidden;

  .pull-down-refresh-loading {
    position: fixed;
    top: 116rpx;
    left: 0;
    text-align: center;
    overflow: hidden;
    animation: fade-out 0.2s linear forwards;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    z-index: 1000;
    .loading-icon {
      display: inline-block;
      animation: icon-fade-out 0.2s linear forwards;
      margin: 0 auto;
    }
    // &.show {
    //   animation: fade-in 0.2s linear forwards;
    //   .loading-icon {
    //     animation: icon-fade-in 0.2s linear forwards;
    //   }
    // }
  }

  .pull-up-refresh-loading {
    text-align: center;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    z-index: 1000;
    .loading-icon {
      display: inline-block;
      opacity: 1;
      height: 60rpx;
      width: 60rpx;
      margin: 20rpx auto 0;
    }
  }

  .to-get-more,
  .not-has-more {
    margin: 30rpx 0;
    text-align: center;
    color: #aaabb3;
    font-size: 28rpx;
  }

  .scroll-Y {
    width: 100%;
    animation: margin-out 0.2s linear forwards;
  }

  &.show {
    .pull-down-refresh-loading {
      position: fixed;
      top: 116rpx;
      left: 0;
      animation: fade-in 0.2s linear forwards;
      .loading-icon {
        animation: icon-fade-in 0.2s linear forwards;
      }
    }
    .scroll-Y {
      animation: margin-in 0.2s linear forwards;
    }
  }

  .scroll-load-refresh,
  .scroll-load-more {
    text-align: center;
    color: #000;
    font-size: 25rpx;
    position: absolute;
    line-height: 60rpx;
    z-index: 1;
    background-color: #eee;
    width: 100%;
  }

  .scroll-load-refresh {
    top: 0;

    display: flex;
    justify-content: center;
    align-items: center;
  }

  .scroll-load-more {
    bottom: 0;

    display: flex;
    justify-content: center;
    align-items: center;

    .translate-line::v-deep {
      height: 0;
    }
  }

  .go-to-top-icon {
    position: absolute;
    right: 20rpx;
    bottom: calc(20rpx + 60rpx + 10rpx);
    width: 60rpx;
    height: 60rpx;
    background-color: #eee;
    border-radius: 50%;

    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .native-refresh-icon {
    position: fixed;
    top: 0;
    left: calc(50% - 30rpx);
    width: 60rpx;
    height: 60rpx;
    z-index: 999;

    display: flex;
    justify-content: center;
    align-items: center;
  }

  .empty-tips {
    width: 100%;
    line-height: 50rpx;
    text-align: center;
    color: #657575;
    font-size: 30rpx;
  }
}
</style>
