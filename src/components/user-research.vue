<template>
  <div class="user-research-entry" v-if="info">
    <img
      class="entry-btn"
      :class="{
        'index': key == 'youxiang_info',
        'orderDetail': key == 'youxiang_order_info'
      }"
      :src="info.icon.url"
      @click="goToSurvey"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex';
export default {
  props: {
    delay: {
      type: Number,
      default: 300
    },
    is_join_c: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: null,
      loading: false,
      info: null,
      path: '',
      key: '',
      keyMap: {
        'pages/index': 'youxiang_info',
        'pages/product': 'youxiang_product_info',
        'packageOrder/order-detail': 'youxiang_order_info'
      }
    };
  },
  watch: {
    is_join_c() {
      if (this.path && this.key) {
        this.init();
      }
    }
  },
  onPageShow() {
    const pages = getCurrentPages();
    this.path = pages[pages.length - 1].route;
    this.key = this.keyMap[this.path];
    if (this.path && this.key) {
      this.init();
    }
  },
  onPageHide() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    })
  },
  methods: {
    init() {
      // 首页未加C不显示入口
      // if (this.path.indexOf('pages/index') > -1 && !this.is_join_c) {
      //   return;
      // }
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.getData();
      }, this.delay);
    },
    async getData() {
      if (this.loading) return;
      this.loading = true;
      const res = await this.$request({
        url: '/csq/getSurveyInfo',
        method: 'GET',
        data: {
          scene_key: this.key
        }
      })
        .then((res) => res.data)
        .catch((err) => ({
          errorCode: -100,
          errorMsg: err,
          responseData: {}
        }));
      this.loading = false;
      const { responseData, errorCode } = res || {};
      if (+errorCode === 0) {
        this.info = responseData;
        // 组件曝光
        this.$reportData({
          info: 'sy_wxtuan_other_csq:suspension_window_exposure',
          ext: {
            questionnaire_id: this.info.qid
          }
        });
      } else {
        this.info = null;
      }
    },
    goToSurvey() {
      this.$reportData({
        info: 'sy_wxtuan_other_csq_page',
        ext: {
          questionnaire_id: this.info.qid
        }
      });
      this.$toH5(this.info.survey_link);
    }
  }
};
</script>
<style lang="less">
page {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
</style>
<style lang="less" scoped>
.user-research-entry {
  .entry-btn {
    display: inline-block;
    width: 100rpx;
    height: 100rpx;
    position: fixed;
    right: 30rpx;
    bottom: 480rpx;
    z-index: 996;
    cursor: pointer;
    &.index {
      bottom: 380rpx;
    }
    &.orderDetail {
      bottom: 360rpx;
    }
  }
}
</style>
