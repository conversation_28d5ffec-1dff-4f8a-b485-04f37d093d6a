<template>
  <div
    class="modal-mask"
    @click="close"
    :style="{ bottom: bottom - 1 + 'px' }"
    @animationend="animationend"
    :class="{
      'fade-in': transform
    }"
    v-if="visible"
  >
    <div class="mask"></div>
    <div
      class="pannel"
      :class="{
        safe: useSafearea
      }"
      :style="{
        background,
        borderRadius
      }"
      @click.stop
    >
      <div class="title" v-if="showTitle">{{ title }}</div>
      <div class="close" v-if="showCancel" @click="close"></div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    radius: {
      type: Number,
      default: 0
    },
    bottom: {
      type: Number,
      default: 0
    },
    visible: {
      type: Boolean,
      default: false
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    background: {
      type: String,
      default: '#fff'
    },
    useSafearea: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  computed: {
    borderRadius() {
      return `${this.radius}rpx ${this.radius}rpx 0 0`;
    }
  },
  data() {
    return {
      transform: false
    };
  },
  watch: {
    visible(visible) {
      if (visible) {
        setTimeout(() => {
          this.transform = true;
        }, 50);
      } else {
        this.transform = false;
      }
    }
  },
  methods: {
    async animationend() {
      setTimeout(() => {
        this.$emit('open');
      }, 100);
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$emit('onClose');
    }
  }
};
</script>

<style lang="less" scoped>
.flex-align-center() {
  display: flex;
  align-items: center;
}
.modal-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  overflow: hidden;
  color: #222222;
  font-weight: 500;
  font-size: 32rpx;
  transform: translateZ(900px);
  &.fade-in {
    .mask {
      opacity: 1;
    }
    .pannel {
      transform: translateY(0);
    }
  }
}
.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 2px);
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.5);
  transition: opacity 0.2s;
}

.pannel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  //border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.2s;
  &.safe {
    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2
    padding-bottom: env(safe-area-inset-bottom);
  }
}
.title {
  .flex-align-center;
  font-family: PingFangSC-Medium;
  justify-content: center;
  height: 52 * 2rpx;
  font-size: 32rpx;
  color: #030303;
  font-weight: 500;
}
.close {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: 20rpx;
  top: 28rpx;
  background: url(https://static.soyoung.com/sy-design/bzsokyai5osd1726026826488.png)
    no-repeat center / 100%;
  z-index: 9;
}
</style>
