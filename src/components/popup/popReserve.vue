<template>
  <!-- 订单状态，0等待付款未付款，未过期，1是过期未付款，已失效。 2未使用已付款，未核销，3已使用，已付款，已核销。4已付款，过期未核销。5已退款 -->
  <div>
    <customPopup
      v-model="popupStatus1"
      backgroundImg="linear-gradient(138deg, #FFF6F7 0%, #FFE3D8 100%)"
      width="300px"
    >
      <template #body>
        <div class="dia-title">
          <div v-if="cancelAppointment">
            <div>是否确认取消预约</div>
          </div>
          <div v-else-if="titleStatus === 'mul'">
            <div>改约提醒</div>
          </div>
          <div v-else-if="titleStatus != 1">
            <div>需要消耗修改次数</div>
            <div>是否确认{{ cancelAppointment ? '取消' : '修改' }}</div>
          </div>
          <div v-else>
            <div>修改预约会影响您的返现资格</div>
            <div>是否确认{{ cancelAppointment ? '取消' : '修改' }}</div>
          </div>
        </div>
        <div class="dia-body">
          <div v-if="titleStatus !== 'mul'" class="dia-middle">
            <div v-if="titleStatus != 1">
              <div class="dia-middle-title">需要消耗1次修改机会</div>
              <div class="dia-middle-text">
                <span>共3次/</span>
                <span class="dia-middle-text-red">剩余{{ times }}次</span>
              </div>
            </div>
            <div v-else>
              <div v-if="newFan != 0">
                <div class="dia-middle-title2">本次返现金额</div>
                <div class="dia-middle-money">
                  <div class="dia-middle-money-l dia-middle-money-n">
                    <span class="money-icon">￥</span>
                    {{ oldFan }}
                  </div>
                  <div class="dia-middle-money-img">
                    <span>降低为</span>
                    <img
                      src="https://static.soyoung.com/sy-pre/1451prul7kj48-1671523800681.png"
                    />
                  </div>
                  <div class="dia-middle-money-r dia-middle-money-n">
                    <span class="money-icon">￥</span>
                    {{ newFan }}
                  </div>
                </div>
              </div>
              <div v-else>
                <!-- <div class="dia-middle-title2 dia-middle-title3">
                  本次返现金额
                  <span class="dia-middle-oldfan"
                    ><span class="money-icon">￥</span>{{ oldFan }}</span
                  >
                </div>
                <div class="dia-m-shixiao">
                  <div>修改后失效</div>
                </div> -->
                <div class="dia-middle-title2 dia-middle-title3">
                  本次核销返现机会失效
                </div>
                <div class="dia-middle-text margin-top-20">
                  <span class="dia-middle-text-red-regular">失效金额</span>
                  <span class="dia-middle-text-red"
                    >￥
                    <span class="dia-middle-text-large">{{
                      oldFan || ''
                    }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="
              shouyue &&
              (shouyueChangeNum - shouyueInfo.edit_num <= 0 || !expirationDate)
            "
            class="dia-middle dia-middle-bg margin-20"
          >
            <div>
              <div class="dia-middle-title2 dia-middle-title3">
                守约之星奖励失效
              </div>
              <div class="dia-middle-text margin-top-20">
                <span class="dia-middle-text-red-regular">失效金额</span>
                <span class="dia-middle-text-red"
                  >￥
                  <span class="dia-middle-text-large">{{
                    shouyueInfo.money || ''
                  }}</span>
                </span>
              </div>
            </div>
          </div>
          <div
            v-if="
              shouyue &&
              !(shouyueChangeNum - shouyueInfo.edit_num <= 0 || !expirationDate)
            "
            class="dia-middle dia-middle-bg margin-20"
          >
            <div>
              <div
                class="dia-middle-title2 dia-middle-title3 dia-middle-title-2line"
              >
                <div>守约之星奖励，仅限修改一次</div>
                <div>预约时间，是否使用？</div>
              </div>
              <div class="dia-middle-title2 dia-middle-title3"></div>
            </div>
          </div>
          <div class="dia-change-info">
            <div>
              到店时间：{{
                cancelAppointment
                  ? oldData.month + '月' + oldData.day + '日 ' + oldData.from
                  : changeTime
              }}
            </div>
            <div>
              服务门店：{{
                cancelAppointment ? oldData.hospital_name : changeHospital
              }}
            </div>
            <div v-if="titleStatus == 1">
              修改次数：<span v-if="times > 0">需消耗一次,</span>剩余<span
                class="dia-change-info-s"
                >{{ times }}次</span
              ><span class="dia-change-info-t">/共3次</span>
            </div>
            <div v-if="shouyue && expirationDate">
              守约之星修改次数：<span
                v-if="shouyueChangeNum - shouyueInfo.edit_num > 0"
                >需消耗一次,</span
              >剩余<span class="dia-change-info-s"
                >{{ shouyueChangeNum - shouyueInfo.edit_num || 0 }}次</span
              ><span class="dia-change-info-t">/共1次</span
              ><span class="dia-change-info-t"
                >（截止{{ expirationDate }}）</span
              >
            </div>
          </div>
          <!-- <span
            >由于涉及到资源的提前调配，单个订单最多支持修改3次预约，当前订单剩余可修改次数为{{
              times
            }}次，</span
          >
          <span v-if="times !== 0">您确定将时间改为{{ changeTime }}么？</span>
          <span v-else
            >如仍需修改，请联系<span @click="toPhone" class="to-service"
              >专属客服</span
            ></span
          > -->
        </div>
      </template>
      <template #footer>
        <div class="dia-btns">
          <div class="btn" @click="confirm">
            {{
              times === 0
                ? '联系客服'
                : !cancelAppointment
                ? '确认修改'
                : '确认取消'
            }}
          </div>
          <div class="btn confrim" @click="cancel">再考虑下</div>
        </div>
      </template>
    </customPopup>
  </div>
</template>

<script>
import customPopup from '@/components/popup';
export default {
  props: {
    shouyue: Number, // 是否触发首约
    isMul: Number, // 是否多约
    popupStatus: Boolean,
    cancelAppointment: Boolean, // 是否为取消预约
    title: String,
    times: Number,
    newFan: Number,
    backMoney: Number, // 旧的返现数据，从baseinfo里返回了
    reserveId: Number,
    changeTime: String,
    oldTime: String,
    oldData: Object,
    changeHospital: String
  },
  components: { customPopup },
  data() {
    return {
      shouyueChangeNum: 1, // 守约之星允许的修改次数
      shouyueInfo: {},
      expirationDate: '',
      popupStatus1: false,
      titleStatus: 2,
      oldFan: 0
    };
  },
  watch: {
    popupStatus: {
      handler() {
        this.popupStatus1 = this.popupStatus;
      },
      immediate: true
    }
  },
  methods: {
    confirm() {
      this.$emit('confirm');
    },
    async toPhone() {
      this.$bridge({
        url: `/packageAccount/consult?qzychannel=50`
      });
    },
    async getShouyueInfo() {
      const res = await this.$request({
        url: '/syGroupBuy/reservation/getAgreementInfo',
        data: { reserve_id: this.reserveId }
      });
      console.log(res?.data?.responseData);
      if (res?.data?.responseData) {
        this.shouyueInfo = res.data.responseData;
        // 计算截止日期
        this.countTime();
      }
    },
    formatDate(str) {
      const date = new Date(str);
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      let day = date.getDate();
      day = day < 10 ? '0' + day : day;
      let h = date.getHours();
      h = h < 10 ? '0' + h : h;
      let m = date.getMinutes();
      m = m < 10 ? '0' + m : m;
      let s = date.getSeconds();
      s = s < 10 ? '0' + s : s;
      return year + '-' + month + '-' + day + ' ' + h + ':' + m + ':' + s;
    },
    countTime() {
      const oldTimeS = this.oldTime.replace(/-/g, '/');
      const timeTemp = new Date(oldTimeS).valueOf();
      const now = new Date().valueOf();
      const les = timeTemp - 48 * 60 * 60 * 1000;
      // 没过期，计算时间
      if (les > now) {
        this.expirationDate = this.formatDate(les);
      } else {
        this.expirationDate = '';
      }
    },
    cancel() {
      this.$emit('cancel');
    }
  },
  created() {
    if (this.isMul) {
      this.titleStatus = 'mul';
    }
  },
  async mounted() {
    this.countTime();
    if (this.shouyue) {
      this.getShouyueInfo();
    }
    this.oldFan = this.backMoney;
    if (!this.isMul) {
      if (this.newFan < this.oldFan) {
        this.titleStatus = 1;
      } else if (this.newFan >= this.oldFan) {
        this.titleStatus = 2;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.margin-top-20 {
  margin-top: 20rpx;
}
.dia-title {
  font-family: PingFangSC-Medium;
  font-size: 34rpx;
  color: #222222;
  letter-spacing: 0;
  text-align: center;
  font-weight: 500;
}
.dia-body {
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
  text-align: left;
  .dia-middle {
    width: 100%;
    height: 170rpx;
    background-image: url('https://static.soyoung.com/sy-pre/3gy1lv657dr7x-1671437400694.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-top: 40rpx;
    text-align: center;
    .dia-middle-title {
      font-family: PingFangSC-Medium;
      font-size: 40rpx;
      line-height: 56rpx;
      color: #964c33;
      letter-spacing: 0;
      font-weight: 500;
      padding-top: 36rpx;
      padding-bottom: 6rpx;
    }
    .dia-middle-title2 {
      padding-top: 26rpx;
      font-family: PingFangSC-Medium;
      font-size: 30rpx;
      line-height: 41rpx;
      color: #964c33;
      letter-spacing: 0;
      font-weight: 500;
    }
    .dia-middle-title3 {
      padding-top: 36rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .money-icon {
        font-size: 24rpx;
        margin-right: -2rpx;
      }
      .dia-middle-oldfan {
        font-family: PingFangSC-Semibold;
        font-size: 44rpx;
        color: #ff3535;
        letter-spacing: 0;
        line-height: 36rpx;
        font-weight: 600;
      }
    }
    .dia-middle-title-2line {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding-top: 42rpx;
    }
    .dia-m-shixiao {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 14rpx;
      div {
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        color: #ff3535;
        letter-spacing: 0;
        font-weight: 500;
        width: 204rpx;
        height: 42rpx;
        background: #fff4ec;
        border-radius: 20rpx 0 20rpx 0;
      }
    }
    .dia-middle-money {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 12rpx;
      .dia-middle-money-img {
        display: flex;
        flex-direction: column;
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #94492f;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        margin: 0 30rpx;
        img {
          width: 62rpx;
          height: 12rpx;
        }
      }
      .dia-middle-money-n {
        width: 144rpx;
        height: 70rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url('https://static.soyoung.com/sy-pre/ddn0f92qb5y7-1671527400680.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        font-family: PingFangSC-Semibold;
        font-size: 44rpx;
        color: #ff3535;
        letter-spacing: 0;
        line-height: 50rpx;
        font-weight: 600;
        .money-icon {
          font-size: 32rpx;
          margin-right: -4rpx;
          margin-bottom: -6rpx;
        }
      }
      .dia-middle-money-l {
        color: #ff3535;
      }
      .dia-middle-money-r {
        color: #a8654e;
      }
    }
    .dia-middle-text {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #94492f;
      letter-spacing: 0;
      text-align: center;
      line-height: 36rpx;
      font-weight: 400;
    }
    .dia-middle-text-red {
      font-family: PingFangSC-Semibold;
      color: #ff3535;
      font-size: 24rpx;
      .dia-middle-text-large {
        font-size: 44rpx;
      }
    }
    .dia-middle-text-red-regular {
      font-family: PingFangSC-Regular;
      color: #ff3535;
      font-size: 28rpx;
    }
  }
  .dia-middle-bg {
    background-image: url(https://static.soyoung.com/sy-pre/mczondm5j47t-1688526600741.png);
    .dia-middle-title2 {
      color: #7e3821;
    }
  }
  .margin-20 {
    margin-top: 20rpx;
  }
  .dia-change-info {
    width: 100%;
    background: #fff;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #666666;
    letter-spacing: 0;
    font-weight: 400;
    box-sizing: border-box;
    padding: 20rpx;
    border-radius: 16rpx;
    margin-top: 20rpx;
    opacity: 0.8;
    div {
      margin-bottom: 10rpx;
    }
    div:last-child {
      margin-bottom: 0rpx;
    }
    .dia-change-info-t {
      color: #333333;
    }
    .dia-change-info-s {
      color: @text-color;
    }
  }
}
.to-service {
  color: @text-color;
  margin-left: 10rpx;
}
.dia-btns {
  display: flex;
  justify-content: center;
  margin-top: 50rpx;
  margin-bottom: 20rpx;
  font-size: 14px;
  letter-spacing: 0;
  font-weight: 400;
  .btn {
    width: 208rpx;
    height: 68rpx;
    border: 1rpx solid #aaabb3;
    border-radius: 20px;
    color: #222;
    line-height: 68rpx;
  }
  .confrim {
    margin-left: 36rpx;
    color: #fff;
    background: @border-color;
  }
}
</style>
