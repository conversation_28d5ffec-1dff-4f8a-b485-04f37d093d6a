<template>
  <root-portal>
    <div class="custom-popup" v-show="value" @click="onMaskClick">
      <div
        class="warp"
        :style="{ width: n_width, backgroundImage: backgroundImg }"
        @click.stop
      >
        <div class="header">
          <div class="close" v-if="showClose" @click="cancel"></div>
          {{ title }}
        </div>
        <div class="body">
          <slot name="body"></slot>
        </div>
        <div class="footer">
          <slot v-if="$slots.footer" name="footer"></slot>
          <div class="btns" v-else>
            <div class="btn" v-if="showCancel" @click="cancel">取消</div>
            <div class="btn confrim" @click="confirm">{{ confirmBtnText }}</div>
          </div>
        </div>
      </div>
    </div>
  </root-portal>
</template>

<script>
export default {
  mounted() {},
  props: {
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '80'
    },
    backgroundImg: {
      type: String,
      default: ''
    },
    value: {
      type: Boolean,
      default: false
    },
    showCancel: {
      type: Boolean,
      default: true
    },
    confirmBtnText: {
      type: String,
      default: '确定'
    },
    showClose: {
      type: Boolean,
      default: false
    },
    closeIfMaskClick: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    n_width() {
      let n_width = 0;
      n_width =
        (this.width + '').indexOf('px') > -1 ? this.width : this.width + '%';
      return n_width;
    }
  },
  methods: {
    cancel() {
      this.$emit('cancel');
      this.$emit('input', false);
    },
    confirm() {
      this.$emit('confrim');
      this.$emit('confirm');
      this.$emit('input', false);
    },
    onMaskClick() {
      if (this.closeIfMaskClick) this.cancel();
    }
  }
};
</script>

<style lang="less" scoped>
.custom-popup {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 9999999;
  top: 0px;
  left: 0px;
  background-color: rgba(0, 0, 0, 0.6);
  transform: translateZ(999px);
  text-align: center;
  .warp {
    background-color: #fff;
    width: 200 * 2rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 15 * 2rpx 20 * 2rpx;
    border-radius: 15px;
    .header {
      position: relative;
      font-size: 18 * 2rpx;
      color: #333333;
      text-align: center;
      font-weight: 500;
      margin-bottom: 10 * 2rpx;
      .close {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        right: 0rpx;
        top: 4rpx;
        background: url(https://static.soyoung.com/sy-pre/close-1661847000665.png)
          no-repeat center / 100%;
        z-index: 2;
      }
    }
    .body {
      font-size: 15 * 2rpx;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }
    .btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 25 * 2rpx;
      .btn {
        flex: 1;
        box-sizing: border-box;
        display: inline-block;
        padding: 10rpx;
        border: 0.5px solid @border-color;
        border-radius: 20px;
        color: @text-color;
        margin-right: 18 * 2rpx;
        &:last-child {
          margin-right: 0px;
        }
        &.confrim {
          background-color: @border-color;
          color: #fff;
        }
      }
    }
  }
}
</style>
