<template>
  <div class="location-box-fixed">
    <!-- 当前定位城市 -->
    <div class="location-text" @click="open2selectHospital">
      <div class="text">
        {{ hospitalName }}
        <div v-if="!hasYourCityChain" class="tip">{{ tipText }}</div>
      </div>
      <img
        class="switch"
        src="https://static.soyoung.com/sy-design/jthum6gttthj1725518950478.png"
      />
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { throttle } from 'lodash-es';

export default {
  name: 'LocationForItem',
  components: {},
  data() {
    return {
      loading: true,
      rect: { top: 0, left: 10 },
      cityList: [],
      scopeUserLocation: false
    };
  },
  props: {
    hospitalName: {
      type: String,
      default: ''
    },
    tipText: {
      type: String,
      default: ''
    }
  },
  watch: {},
  computed: {
    ...mapState({
      userInfo: (state) => state.global.userInfo
    }),
    hospital() {
      return this.userInfo.hospital;
    },
    // 所在城市是否有连锁机构
    hasYourCityChain() {
      const { cityId } = this.userInfo;
      return (
        this.cityList.findIndex(
          ({ city_id }) => Number(city_id) === Number(cityId)
        ) > -1
      );
    },
    // 是否存在城市列表
    hasCityList() {
      return this.cityList.length > 0;
    }
  },
  methods: {
    calcMenuPostion() {
      const menuRect = uni.getMenuButtonBoundingClientRect();
      const { windowWidth } = uni.getSystemInfoSync();
      const { top, right } = menuRect;
      this.rect.top = top;
      this.rect.left = windowWidth - right;
    },
    // 校验是否有获取地理位置的权限
    checkScopeOfUserLocation() {
      return new Promise((resolve, reject) => {
        wx.getSetting({
          success(res) {
            resolve(res.authSetting['scope.userLocation']);
          },
          fail(arg) {
            reject(arg);
          }
        });
      });
    }
  },
  created() {
    // 点击节流
    this.open2selectHospital = throttle(
      () => {
        this.$emit('select-click');
      },
      1500,
      { trailing: false }
    );
    this.calcMenuPostion();
  }
};
</script>
<style lang="less" scoped>
.location-box-fixed {
  //position: absolute;
  //z-index: 9999;
  //transform: translateZ(10px);
  .text {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 480rpx;
  }
}
.location-text {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  color: #000000;
  font-weight: 400;
  .text {
    position: relative;
    font-family: PingFangSC-Medium;
  }
  .tip {
    position: absolute;
    width: 440rpx;
    top: 40rpx;
    left: 0;
    font-size: 22rpx;
    color: #7e8ba0;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  > img {
    width: 40rpx;
    height: 40rpx;
    margin-right: 6rpx;
  }
  .switch {
    margin-left: 10rpx;
    width: 11rpx;
    height: 17rpx;
  }
}
</style>
