<template>
  <div class="item-card" v-if="data" @click="goBtnClick">
    <div class="left">
      <img
        v-if="data.head_img && data.head_img.img_url"
        class="product-img"
        :src="data.head_img.img_url"
        alt=""
      />
      <img
        v-if="
          data.op_label_info &&
          data.op_label_info.img &&
          data.op_label_info.img.img_url
        "
        :src="data.op_label_info.img.img_url"
        class="product-op-label-img"
        mode="heightFix"
        alt=""
      />
    </div>
    <div class="right">
      <div class="title">
        <div
          :class="[
            'title-text',
            {
              'title-width':
                data.title_label_info &&
                data.title_label_info.img &&
                data.title_label_info.img.img_url
            }
          ]"
        >
          {{ f(data.title) }}
        </div>
        <image
          mode="heightFix"
          v-if="
            data.title_label_info &&
            data.title_label_info.img &&
            data.title_label_info.img.img_url
          "
          :src="data.title_label_info.img.img_url"
          class="title-label"
        />
      </div>
      <div class="sub-title">{{ data.sub_title }}</div>
      <div class="lineBox">
        <div class="order-count">{{ data.show_order_cnt }}</div>
        <div
          v-if="data.pain_tag_info"
          class="painInfoBox"
          :style="{
            color: data.pain_tag_info.text_color,
            backgroundColor: data.pain_tag_info.background_color
          }"
        >
          <image class="painIcon" :src="data.pain_tag_info.icon"></image>
          <div class="text">
            {{ data.pain_tag_info.name }}
          </div>
        </div>
      </div>

      <div class="price-line">
        <!--  原价区域 -->
        <div class="price colored">
          <span class="price-icon">￥</span>
          <!--  价格后端返回，show_price 是多种价格选择的，后端有判断逻辑；可能是价格或者文案-->
          <span class="price-value">{{ data.show_price }}</span>
          <!-- 价格后面的文案，后端返回 -->
          <span class="price-last">{{ data.price_last }}</span>
          <!-- 破价品 icon  -->
          <img
            v-if="data.price_breaking_icon"
            class="price-add-c-icon"
            mode="heightFix"
            :src="data.price_breaking_icon"
            alt="咨询有惊喜"
          />
        </div>
        <!--如果.show_price 展示的是优惠价，这里再展示一个原价-->
        <!--破价品不展示原价-->
        <div
          class="price-origin"
          v-if="data.show_price_type !== 0 && !data.price_breaking_icon"
        >
          <span class="price-icon">￥</span
          ><span class="price-value delete-line">{{ data.price_online }}</span>
        </div>
        <!-- <img
          class="go-btn"
          src="https://static.soyoung.com/sy-design/36glvo20sf3c51716895628403.png"
          alt=""
        /> -->
      </div>
    </div>
  </div>
</template>
<script>
import SubscribeMixins from '@/mixins/subscribe';

const subTemplateIds = [
  'cWqU-ViIiMqghwa1oNYJbK6qrArgr0iEY5aCGAfIXlA',
  'xHGxtKz_okVCgkvY6CEV8HH9HtiW1NPoSAKeE-8VKnE'
];
export default {
  name: 'ItemCard',
  props: ['data', 'reportExt'],
  mixins: [SubscribeMixins],
  data() {
    return {};
  },
  methods: {
    f(title) {
      const max = 18;
      if (
        title &&
        this.data.title_label_info &&
        this.data.title_label_info.img &&
        this.data.title_label_info.img.img_url
      ) {
        if (title.length > max) {
          return title.slice(0, max) + '...';
        } else {
          return title;
        }
      } else {
        return title;
      }
    },

    async getSetting() {
      return new Promise((resolve) => {
        wx.getSetting({
          withSubscriptions: true,
          success(res) {
            const { itemSettings = {}, mainSwitch } = res.subscriptionsSetting;
            // 总开关关闭
            if (mainSwitch) {
              const cacheTempIds = [];
              subTemplateIds.forEach((id) => {
                const status = itemSettings[id];
                if (status) {
                  cacheTempIds.push(id);
                }
              });
              // 如果所有模板都勾选了总是选择，则不展示
              if (subTemplateIds.length === cacheTempIds.length) {
                resolve(false);
                return;
              }
              // 走到这里，说明有模板没有勾选总是选择
              // 获取缓存中的订阅消息发送时间
              const storageTempInfos = uni.getStorageSync('tempInfos');
              let allowSend = true;
              // 如果缓存中保存了模版相关信息
              if (storageTempInfos) {
                subTemplateIds.forEach((id) => {
                  const prevSendTime = storageTempInfos[id];
                  //  如果缓存中保存了模版相关信息，并且模版相关信息没有超过一天，则不展示
                  if (
                    prevSendTime &&
                    prevSendTime + 24 * 60 * 60 * 1000 > Date.now()
                  ) {
                    allowSend = false;
                  }
                });
              }
              resolve(allowSend);
            } else {
              // 总开关关闭
              resolve(false);
            }
          }
        });
      });
    },
    async goBtnClick() {
      const allowSend = await this.getSetting();
      if (allowSend) {
        await this.createGroupBySub(subTemplateIds, []);
        uni.setStorageSync(
          'tempInfos',
          subTemplateIds.reduce((acc, cur) => {
            acc[cur] = Date.now();
            return acc;
          }, {})
        );
      }
      // const url = `/pages/product?material_id=${
      //   this.data.type === 1 ? this.data.spu_id : this.data.pid
      // }&material_type=${this.data.type === 1 ? 1 : 3}&sku_id=${
      //   this.data.type === 1 ? this.data.pid : ''
      // }`;

      const url = `/pages/product?id=${this.data.pid}`;
      this.$reportData({
        info: 'sy_chain_store_tuan_product:feed_click',
        ext: {
          url,
          product_id: this.data.pid,
          ...this.reportExt
        }
      });
      uni.navigateTo({
        url
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.item-card {
  display: flex;
  align-items: stretch;
  position: relative;
  .left {
    padding-right: 20rpx;
    .product-img {
      height: 150rpx;
      width: 200rpx;
      vertical-align: top;
    }
    .product-op-label-img {
      position: absolute;
      height: 28rpx;
      width: 0;
      left: 0;
      top: 0;
    }
  }
  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    align-items: flex-start;
    .title {
      display: flex;
      align-items: center;
      .title-text {
        font-family: PingFangSC-Medium;
        line-height: 40rpx;
        height: 40rpx;
        font-size: 28rpx;
        color: #232321;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .title-width {
        max-width: 240rpx;
      }
      .title-label {
        height: 28rpx;
        display: inline-block;
        margin-left: 8rpx;
        flex-shrink: 0;
      }
    }
    .sub-title {
      line-height: 28rpx;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #345f22;
      margin-top: 4rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 330rpx;
    }
    .order-count {
      display: inline-block;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #999999;
      padding: 2rpx 6rpx;
      background: #f5f6f7;
      border-radius: 2rpx;
    }
    .price-line {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      position: relative;
      width: 100%;
      .price {
        margin-top: 4rpx;
        position: relative;
        color: #232321;
        font-family: PingFangSC-Medium;
        display: flex;
        align-items: center;
        &.colored {
          color: #61b43e;
        }
        .price-icon,
        .price-last {
          font-size: 18rpx;
          position: relative;
          top: 4rpx;
          margin-left: 4rpx;
        }
        .price-value {
          font-size: 30rpx;
        }
        .price-add-c-icon {
          width: 0;
          height: 32rpx;
          margin-left: 6rpx;
        }
      }
      .price-origin {
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        color: #999999;
        margin-top: 16rpx;
        margin-left: 10rpx;
      }
      .delete-line {
        text-decoration: line-through;
      }
      .go-btn {
        z-index: 5;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 44rpx;
        height: 44rpx;
      }
    }
  }
}
.lineBox {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
}
.painInfoBox {
  height: 28rpx;
  display: flex;
  align-items: center;
  padding: 0 6rpx;
  font-family: PingFangSC-Regular;
  font-size: 20rpx;
  color: #373a36;
  margin-left: 16rpx;
  .painIcon {
    width: 20rpx;
    height: 20rpx;
    margin-right: 4rpx;
  }
}
</style>
