<template>
  <div
    class="home-search-page"
    :style="{
      marginTop: (maxHeight + 6) * 2 + 'rpx',
      height: `calc(100vh - ${(maxHeight + 6) * 2}rpx)`
    }"
    @touchstart="touchStart"
    @touchend="touchEnd"
  >
    <!-- 导航栏 start -->
    <Navbar :background="`rgba(255, 255, 255, 1)`" :hasBack="false">
      <div class="home-search-page-header">
        <block>
          <div class="back" @click="handleBack"></div>
          <div class="title">搜索</div>
        </block>
      </div>
    </Navbar>
    <!-- 导航栏 end -->

    <div class="home-search-page-content">
      <!-- 搜索栏 start -->
      <div
        class="home-search-page-content-wrapper"
        :style="{ top: maxHeight - 6 + 'px' }"
      >
        <div class="home-search-page-content-wrapper-input">
          <input
            v-model="searchValue"
            type="text"
            :focus="isTextFocus"
            @confirm="handleSearch"
            @focus="handleFocus"
            @input="handleInput"
          />
          <img
            v-if="!isResult && searchValue !== ''"
            @click="handleClickClear"
            src="https://static.soyoung.com/sy-design/bzsokyai5osd1725437086589.png"
            alt=""
          />
          <span
            v-if="!isResult"
            class="home-search-page-content-wrapper-input__btn"
            @click="handleSearch"
            >搜索</span
          >
        </div>
      </div>
      <!-- 搜索栏 start -->

      <!-- 最近、热门、当季推荐 start -->
      <template>
        <RecommendCollectionPage
          :style="{ display: !isFocus && !isResult ? 'block' : 'none' }"
          :historyList="historyList"
          :cityId="cityId"
          @handleHistoryChange="handleHistoryChange"
          @clear="handleClearHistory"
        ></RecommendCollectionPage>
      </template>
      <!-- 热门推荐 end -->

      <!-- sug页 start -->
      <template>
        <PackageSearchSugPage
          :style="{
            display:
              isFocus && searchValue !== '' && !isResult ? 'block' : 'none'
          }"
          :iptTxt="searchValue"
          :sugList="sugList"
          @selectKeyWordItem="selectKeyWordItem"
          @handleAllChange="handleAllChange"
        ></PackageSearchSugPage>
      </template>
      <!-- sug页 end -->

      <!-- 搜索结果页 start -->
      <template v-if="isResult">
        <PackageSearchResultPage
          :product="productList"
          :keyWord="searchValue"
          :loading="loading"
          :cityId="cityId"
        ></PackageSearchResultPage>
      </template>
      <!-- 搜索结果页 end -->
    </div>
  </div>
</template>

<script>
import { getSearchResultApi, getSearchSugListApi } from '@/api/packageSearch';
import Navbar from '@/components/NavBar.vue';
import { debounce } from 'lodash-es';
import PackageSearchResultPage from './component/packageSearchResultPage.vue';
import PackageSearchSugPage from './component/packageSearchSugPage.vue';
import RecommendCollectionPage from './component/recommendCollectionPage.vue';

const user_info = uni.getStorageSync('user_info');
let touchDotX = 0; //X按下时坐标
let touchDotY = 0; //y按下时坐标
let interval; //计时器
let time = 0; //从按下到松开共多少时间*100
export default {
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_s_search_result_page',
      ext: {
        search_word: this.searchValue
      }
    };
  },
  components: {
    Navbar,
    RecommendCollectionPage,
    PackageSearchSugPage,
    PackageSearchResultPage
  },
  data() {
    return {
      isTextFocus: false,
      historyList: [], // 历史搜索
      maxHeight: 0,
      searchValue: '',
      sugList: [], // sug页数据
      productList: [], // 搜索结果页数据
      isFocus: false, // 是否聚焦  false: 推荐  true: sug页
      isResult: true, // 是否有搜索结果
      page: 1,
      page_size: 10,
      hasMore: false,
      is_recommend: null,
      loading: true,
      cityId: 1
    };
  },
  mounted() {
    this.maxHeight = uni.getMenuButtonBoundingClientRect().bottom;
    // 先从本地获取历史搜索
    uni.getStorage({
      key: `history_${user_info.unionId}`,
      success: (res) => {
        this.historyList = JSON.parse(res.data) || [];
      }
    });
  },
  watch: {
    // searchValue: {
    //   handler(val) {
    //     console.log(!!val, '!!!!!!!!!!!!!!!')
    //     this.isFocus = !!val;
    //   },
    //   immediate: true
    // }
  },
  async onLoad(options) {
    this.searchValue = options.keyword || '';
    this.cityId = options.cityId || 1;
    if (options.keywords !== '') {
      this.isResult = true;
      await this._getSearchResultApi();
    }
  },
  methods: {
    async _getSugList() {
      const data = await getSearchSugListApi({
        source: 1,
        keyword: this.searchValue,
        city_id: this.cityId
      });
      if (data) {
        const { result_list } = data;
        this.sugList = result_list;
        if (result_list.length === 0) {
          this.isFocus = false;
          this.isResult = false;
        } else {
          this.isFocus = true;
        }
      }
    },
    async _getSearchResultApi() {
      const data = await getSearchResultApi({
        source: 1,
        keyword: this.searchValue,
        page: this.page,
        page_size: this.page_size,
        is_recommend: this.is_recommend,
        city_id: this.cityId,
        cityId: this.cityId
      });
      if (data) {
        let price_position = {
          'chain_price_position': {
            'best_price_text': '',
            'best_price_desc': '',
            'original_price_text': '',
            'sku_price_breaking': {
              'list_price_tag_icon': '',
              'price_text': ''
            }
          }
        };
        let feed_area = [];
        let feed_recommend_area = [];
        let feed_recommend_opt = {};
        let typeArray = [];
        if (this.page === 1) {
          this.productList =
            data.data.length > 0
              ? data.data.map((item) => {
                  typeArray.push(item.type);
                  let price_position = {
                    chain_price_position: {
                      best_price_text: '',
                      best_price_desc: '',
                      original_price_text: '',
                      sku_price_breaking: {
                        list_price_tag_icon: '',
                        price_text: ''
                      }
                    }
                  };
                  this.hasMore = item.items.has_more;
                  if (item.type === 'feed_recommend_area') {
                    this.is_recommend = 1;
                  } else {
                    this.is_recommend = 0;
                  }
                  if (item.type !== 'feed_recommend_opt') {
                    item.items?.feed_list.map((child) => {
                      if (
                        child.data?.price_info &&
                        (!child.data.price_info?.price_position ||
                          !!child.data.price_info?.price_position
                            ?.chain_price_position)
                      ) {
                        return {
                          ...child,
                          data: {
                            ...child.data,
                            price_position
                          }
                        };
                      }
                      return child;
                    });
                    return item;
                  }
                  return item;
                })
              : [];
          if (!typeArray.includes('feed_area')) {
            this.$reportData({
              info: 'sy_chain_store_s_search_result:noresult_exposure',
              ext: {
                search_word: this.searchValue || ''
              }
            });
          }
        } else {
          if (data.data.length > 0) {
            let curData = JSON.parse(JSON.stringify(this.productList));
            data.data.forEach((item) => {
              this.hasMore = item.items.has_more;
              item.items.feed_list.forEach((child) => {
                if (item.type === 'feed_area') {
                  feed_area.push({
                    ...child,
                    data: {
                      ...child.data,
                      price_info: {
                        ...child.data.price_info,
                        price_position: child.data.price_info?.price_position
                          ? child.data.price_info.price_position
                          : price_position
                      }
                    }
                  });
                } else if (item.type === 'feed_recommend_area') {
                  feed_recommend_area.push({
                    ...child,
                    data: {
                      ...child.data,
                      price_info: {
                        ...child.data.price_info,
                        price_position: child.data.price_info?.price_position
                          ? child.data.price_info.price_position
                          : price_position
                      }
                    }
                  });
                } else {
                  feed_recommend_opt = {
                    has_more: 0,
                    is_recommend: 0,
                    title: '',
                    items: {
                      feed_list: [child],
                      has_more: 0,
                      is_recommend: 0,
                      title: ''
                    },
                    style: 'single_column',
                    type: 'feed_recommend_opt'
                  };
                }
              });
            });
            curData.forEach((item) => {
              if (item.type === 'feed_area') {
                item.items.feed_list = item.items.feed_list.concat(feed_area);
              } else if (item.type === 'feed_recommend_area') {
                item.items.feed_list =
                  item.items.feed_list.concat(feed_recommend_area);
              }
            });
            this.productList = curData.concat(feed_recommend_opt);
          }
        }
        this.page += 1;
      }
      console.log(this.productList, 'productlist');
      this.loading = false;
    },
    handleBack() {
      this.$reportData({
        info: 'sy_chain_store_s_search_result:return_click',
        ext: {}
      });
      uni.navigateBack({
        delta: 1
      });
    },
    handleClickClear() {
      this.isTextFocus = true;
      this.sugList = [];
      this.searchValue = '';
      this.productList = [];
      this.isResult = false;
      this.isFocus = false;
    },
    handleFocus(item) {
      this.productList = [];
      if (item.detail.value !== '') {
        this.isFocus = true;
        this.isResult = false; // 聚焦唤起sug页
        this._getSugList();
      } else {
        this.isFocus = false;
        this.isResult = false; // 聚焦唤起sug页
      }
    },
    handleInput() {
      debounce(() => {
        this._getSugList();
      }, 500)();
    },
    // handleBluer(event) {
    //   if (event.detail.value === '') {
    //     this.isFocus = false;
    //     this.isResult = false;
    //   } else {
    //     this.isFocus = true;
    //   }
    // },
    selectKeyWordItem(item) {
      this.isResult = true;
      this.searchValue = item.search;
      this.page = 1;
      this.productList = [];
      this._getSearchResultApi();
      this.$_history_save(item.search);
    },
    handleAllChange() {
      this.productList = [];
      this.sugList = [];
      if (this.searchValue !== '') {
        this.page = 1;
        this.isResult = true;
        this._getSearchResultApi();
        this.$_history_save(this.searchValue);
      } else {
        this.isResult = false;
        this.isFocus = false;
      }
    },
    handleHistoryChange(item) {
      this.searchValue = item;
      this.isResult = true;
      this.isFocus = false;
      this.page = 1;
      this._getSearchResultApi();
      this.$_history_save(item);
    },
    handleClearHistory() {
      uni.removeStorage({
        key: `history_${user_info.unionId}`
      });
      this.historyList = [];
    },
    handleSearch() {
      this.sugList = [];
      this.productList = [];
      if (this.searchValue !== '') {
        this.page = 1;
        this.isResult = true;
        this._getSearchResultApi();
        this.$_history_save(this.searchValue);
        this.$reportData({
          info: 'sy_chain_store_s_search_index:active_words_click',
          ext: {
            zt_item_type: 7,
            zt_item_id: this.searchValue
          }
        });
      }
    },
    $_history_save(keyword) {
      // 如果存在，则提到首位，否则添加
      var index = this.historyList.indexOf(keyword);
      if (index !== -1) {
        if (index === 0) {
          return;
        } else {
          this.historyList.splice(index, 1);
        }
      }
      this.historyList.unshift(keyword);
      uni.setStorage({
        key: `history_${user_info.unionId}`,
        data: JSON.stringify(this.historyList)
      });
    },
    touchStart: function (e) {
      touchDotX = e.touches[0].pageX; // 获取触摸时的原点
      touchDotY = e.touches[0].pageY;
      // 使用js计时器记录时间
      interval = setInterval(function () {
        time++;
      }, 100);
    },
    // 触摸结束事件
    touchEnd: function (e) {
      let touchMoveX = e.changedTouches[0].pageX;
      let touchMoveY = e.changedTouches[0].pageY;
      let tmX = touchMoveX - touchDotX;
      let tmY = touchMoveY - touchDotY;
      if (time < 20) {
        let absX = Math.abs(tmX);
        let absY = Math.abs(tmY);
        if (absX > 2 * absY) {
          if (tmX < 0) {
            console.log('左滑=====');
          } else {
            console.log('右滑=====');
            this.$reportData({
              info: 'sy_chain_store_s_search_result:return_click',
              ext: {}
            });
          }
        }
        if (absY > absX * 2 && tmY < 0) {
          console.log('上滑动=====');
        }
      }
      clearInterval(interval); // 清除setInterval
      time = 0;
    }
  },
  onReachBottom() {
    if (this.hasMore) {
      this._getSearchResultApi();
    }
  }
};
</script>

<style lang="less">
page {
  background: #f8f8f8;
}
.home-search-page {
  background: #f8f8f8;
  position: relative;
  &-header {
    position: relative;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    .back {
      position: absolute;
      left: 6px;
      top: 50%;
      height: 88rpx;
      width: 88rpx;
      z-index: 1;
      background: url(https://static.soyoung.com/sy-design/3cj8rc3ipek931725437086652.png)
        no-repeat center center transparent;
      background-size: 22rpx 36rpx;
      transform: translateY(-50%);
    }
    .title {
      font-family: PingFangSC-Medium;
      font-size: 34rpx;
      color: #030303;
      font-weight: 500;
    }
  }
  &-content {
    width: 100%;
    padding: 20rpx 0 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    background: #f8f8f8;
    &-wrapper {
      width: 100%;
      position: fixed;
      left: 0;
      padding: 30rpx 30rpx 20rpx;
      box-sizing: border-box;
      background: #ffffff;
      z-index: 99;
      &-input {
        width: 100%;
        height: 84rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        background: #f2f2f2;
        padding: 10rpx 10rpx 10rpx 20rpx;
        box-sizing: border-box;
        img {
          width: 40rpx;
          height: 40rpx;
          margin: 0 10px;
          transition: all 0.3s;
        }
        input {
          flex: 1;
        }
        &__btn {
          width: 130rpx;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #333333;
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #ffffff;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
