<template>
  <div class="recommendation-collection-page" id="collection">
    <template v-if="loading">
      <img
        src="https://static.soyoung.com/sy-design/kgmpik5wigpe1727067724336.png"
        alt=""
        style="height: 420px; width: 100%"
      />
    </template>

    <template v-else>
      <!-- 最近搜索 start -->
      <div
        v-if="historyList.length > 0"
        class="recommendation-collection-page-hotRecommend"
      >
        <div class="hot-recommend-title">
          <span>最近搜索</span>
          <img
            @click="handleClearChange"
            src="https://static.soyoung.com/sy-design/9jb9ppka8hk11725449309773.png"
            alt=""
          />
        </div>
        <div class="hot-recommend-list exposure">
          <div
            :id="`history-recommend-list-item__${index}`"
            v-for="(item, index) in historyList.slice(0, 10)"
            :key="`history-list${index}`"
            :data-title="item"
            :data-serial_num="index + 1"
            :class="{
              'hot-recommend-list-item exposure-item': true
            }"
            @click="handleHotChange(index, 'history')"
          >
            {{ item }}
          </div>
          <!--          <div-->
          <!--            v-if="rows >= 3 && isToggle"-->
          <!--            class="hot-recommend-list-item hot-recommend-list-more"-->
          <!--            @click="handleToggle"-->
          <!--          >-->
          <!--            <img-->
          <!--              src="https://static.soyoung.com/sy-design/kgmpik5wigpe1726111990339.png"-->
          <!--              style="width: 10.8px; height: 8.61px"-->
          <!--              alt=""-->
          <!--            />-->
          <!--          </div>-->
        </div>
      </div>
      <!-- 最近搜索 end -->

      <!-- 热门推荐 start -->
      <div
        v-if="hotList.length >= 4"
        class="recommendation-collection-page-hotRecommend"
      >
        <div class="hot-recommend-title">
          <span>{{ historyTitle }}</span>
        </div>
        <div class="hot-recommend-list exposure-hot">
          <div
            v-for="(item, index) in hotList"
            :key="`hot-list__${index}`"
            :data-title="item.title"
            :data-serial_num="index + 1"
            :class="[
              'hot-recommend-list-item exposure-hot-item',
              `hot-recommend-list-item__${index}`
            ]"
            ref="listItem"
            @click="handleHotChange(index, 'hot')"
          >
            {{ item.title }}
          </div>
          <!--          <div-->
          <!--            v-if="hotRows >= 3 && isHotToggle"-->
          <!--            class="hot-recommend-list-item hot-recommend-list-more"-->
          <!--            @click="handleHotToggle"-->
          <!--          >-->
          <!--            <img-->
          <!--              src="https://static.soyoung.com/sy-design/kgmpik5wigpe1726111990339.png"-->
          <!--              style="width: 10.8px; height: 8.61px"-->
          <!--              alt=""-->
          <!--            />-->
          <!--          </div>-->
        </div>
      </div>
      <!-- 热门推荐 end -->

      <!-- 当季推荐 start -->
      <div
        v-if="recommendList.length > 0"
        class="recommendation-collection-page-inSeason pt40"
      >
        <div class="recommendation-collection-page-inSeason__title">
          {{ recommendTitle }}
        </div>
        <scroll-view
          class="recommendation-collection-page-inSeason-scrollView"
          enable-flex
          :scroll-x="true"
          :loop="false"
          :scroll-y="false"
          :scroll-with-animation="true"
        >
          <div
            class="recommendation-collection-page-inSeason-scrollView-item scrollViewItem"
            :id="`recommend-scroll__${item.sku_id}`"
            v-for="(item, index) in recommendList"
            :key="item.sku_id"
            @click="goProduct(item.sku_id, item.sku_info.product_id, index)"
            :data-id="item.sku_id"
            :data-serial_num="index + 1"
          >
            <img
              :src="
                item.sku_info.img_url ||
                'https://static.soyoung.com/sy-pre/30408he0fth8c-1727082600620.jpg'
              "
              alt=""
              style="background: #f2f2f2"
            />
            <div class="scroll-view-item-content">
              <div class="scroll-view-item-content-title">
                <span class="scroll-view-item-content-title__sp1">{{
                  item.sku_info.title
                }}</span>
              </div>
              <div
                class="scroll-view-item-content-price"
                v-if="
                  item.price_info.price_position !== null &&
                  item.price_info.price_position.chain_price_position !== null
                "
              >
                <template>
                  <img
                    class="scroll-view-item-content-price__sp1"
                    src="https://static.soyoung.com/sy-design/3gcimprsb1n2m1727580418784.png"
                    style="width: 16rpx; height: 16rpx"
                    alt=""
                  />
                  <span class="scroll-view-item-content-price__sp2">{{
                    item.price_info.price_position.chain_price_position
                      .best_price_text
                  }}</span>
                  <div
                    v-if="
                      item.price_info.price_position.chain_price_position
                        .sku_price_breaking === null ||
                      item.price_info.price_position.chain_price_position
                        .sku_price_breaking.list_price_tag_icon === ''
                    "
                  >
                    <span class="scroll-view-item-content-price__sp3">{{
                      item.price_info.price_position.chain_price_position
                        .best_price_desc
                    }}</span>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div
            class="recommendation-collection-page-inSeason-scrollView-item z-image"
          >
            <img
              src="https://static.soyoung.com/sy-pre/20240913-113653-1726197000634.jpeg"
              alt="占位图"
            />
          </div>
        </scroll-view>
      </div>
      <!-- 当季推荐 end -->
    </template>

    <!-- weak -->
    <WeakNetwork
      @refresh="refresh"
      :path="['/apiconnecthub/zy/search/start']"
      :visible.sync="isNetwork"
    />
  </div>
</template>

<script>
import { getSearchStartApi } from '@/api/packageSearch';
import WeakNetwork from '@/components/WeakNetwork.vue';
export default {
  props: {
    historyList: Array,
    cityId: {}
  },
  components: {
    WeakNetwork
  },
  data() {
    return {
      loading: true,
      neighborSearchData: [],
      recommendList: [],
      hotList: [],
      originWidth: 430,
      originHotList: [],
      historySearchList: [],
      his_hot_width: {
        history: [],
        hot: []
      },
      isToggle: true,
      isHotToggle: true,
      totalLabels: 0,
      rows: 0,
      maxTotalLabels: 0,
      currentRowWidth: 0,
      initData: {},
      totalHotLabels: 0,
      hotRows: 0,
      maxHotTotalLabels: 0,
      currentRowHotWidth: 0,
      historyTitle: '',
      recommendTitle: '',
      isNetwork: false
    };
  },
  watch: {
    cityId: {
      handler: function () {
        this._getSearchStartApi({ source: 1 });
      },
      deep: true
    }
  },
  created() {
    this.$nextTick(() => {
      this._getSearchStartApi({ source: 1 });
    });
  },
  methods: {
    async _getSearchStartApi() {
      console.log('this.cityId', this.cityId);
      const data = await getSearchStartApi({ city_id: this.cityId });
      if (data) {
        const { recommend_list, hot_list, hot_title, recommend_title } = data;
        this.recommendList =
          recommend_list.length > 0
            ? recommend_list.map((item) => {
                let price_position = {
                  'chain_price_position': {
                    'best_price_text': '',
                    'best_price_desc': '',
                    'original_price_text': '',
                    'sku_price_breaking': {
                      'list_price_tag_icon': '',
                      'price_text': ''
                    }
                  }
                };
                if (
                  item.type !== 'feed_recommend_opt' &&
                  !item?.price_info?.price_position
                ) {
                  return {
                    ...item,
                    price_info: {
                      ...item.price_info,
                      price_position
                    }
                  };
                }
                return item;
              })
            : [];
        this.hotList = hot_list;
        this.originHotList = hot_list;
        this.initData = data;
        this.historyTitle = hot_title;
        this.recommendTitle = recommend_title;
        // this.getAllWidth();
        this.$nextTick(() => {
          this.registerExposure();
        });
        this.isNetwork = false;
      }
      this.loading = false;
    },
    async refresh() {
      this.loading = true;
      await this._getSearchStartApi();
      this.loading = false;
    },
    getAllWidth() {
      uni.getSystemInfo({
        success: (res) => {
          console.log(res, 'res img');
          this.rows = 0;
          this.totalLabels = 0;
          this.maxTotalLabels = 0;
          this.totalHotLabels = 0;
          this.hotRows = 0;
          this.maxHotTotalLabels = 0;
          this.currentRowHotWidth = 0;
          this.currentRowWidth = 0;
          this.isToggle = false;
          this.isHotToggle = false;
          this.originWidth = Number(res.screenWidth - 30);
          this.his_hot_width = {
            history: [],
            hot: []
          };
          this.his_hot_width.history = this.historyList.map((item, index) => {
            var canvas = uni.createCanvasContext(`firstCanvas${index}`);
            // 设置字体样式
            canvas.font = '13px PingFangSC-Regular';
            // 测量文本的宽度
            var width = canvas.measureText(item).width;
            return width + 30 > this.originWidth
              ? this.originWidth
              : width + 30;
          });
          this.his_hot_width.hot = this.hotList.map((item, index) => {
            var canvas = uni.createCanvasContext(`secondCanvas${index}`);
            // 设置字体样式
            canvas.font = '13px PingFangSC-Regular';
            // 测量文本的宽度
            var width = canvas.measureText(item.title).width;
            return width + 30;
          });

          console.log(this.his_hot_width, 'his_hot_width', this.originWidth);
          // let hotWidth = {}
          // this.his_hot_width.hot.forEach(hotItem => {
          //
          // })
          this.his_hot_width.history.forEach((labelWidth) => {
            if (this.currentRowWidth + labelWidth > this.originWidth) {
              this.rows++;
              this.currentRowWidth = labelWidth;
            } else {
              this.currentRowWidth += labelWidth;
            }

            if (
              this.rows === 3 &&
              36 + this.currentRowWidth < this.originWidth &&
              !this.isToggle
            ) {
              this.currentRowWidth += 36;
              this.isToggle = true;
            }

            if (this.rows < 3) {
              this.totalLabels++;
            }

            if (this.rows === 3 && this.isToggle) {
              if (this.currentRowWidth + 36 > this.originWidth) {
                this.totalLabels--;
              }
            }

            if (this.rows < 5) {
              this.maxTotalLabels++;
            }
          });
          this.his_hot_width.hot.forEach((labelWidth) => {
            if (this.currentRowHotWidth + labelWidth > this.originWidth) {
              this.hotRows++;
              this.currentRowHotWidth = labelWidth;
            } else {
              this.currentRowHotWidth += labelWidth;
            }

            if (
              this.hotRows === 3 &&
              36 + this.currentRowHotWidth < this.originWidth &&
              !this.isHotToggle
            ) {
              this.currentRowWidth += 36;
              this.isHotToggle = true;
            }

            if (this.hotRows < 3) {
              this.totalHotLabels++;
            }

            if (this.hotRows === 3 && this.isHotToggle) {
              if (this.currentRowHotWidth + 36 > this.originWidth) {
                this.totalHotLabels--;
              }
            }

            if (this.hotRows < 5) {
              this.maxHotTotalLabels++;
            }
          });
          console.log(`最近搜索当前行:${this.rows}`);
          console.log(
            `原始宽度:${this.originWidth}, 3行的总宽度：${this.originWidth * 3}`
          );
          console.log(`热门搜索当前最少标签:${this.totalLabels}`);
          console.log(`热门搜索最大展示标签:${this.maxTotalLabels}`);
          this.hotList = this.originHotList.slice(0, this.totalHotLabels);
          this.historySearchList = this.historyList.slice(0, this.totalLabels);
        }
      });
    },
    handleToggle() {
      this.$reportData({
        info: 'sy_chain_store_s_search_index:recent_expand_click',
        ext: {}
      });
      this.isToggle = false;
      this.historySearchList = this.historyList.slice(0, this.maxTotalLabels);
    },
    handleHotToggle() {
      this.$reportData({
        info: 'sy_chain_store_s_search_index:hot_expand_click',
        ext: {}
      });
      this.isHotToggle = false;
      this.hotList = this.originHotList.slice(0, this.maxHotTotalLabels);
    },
    goProduct(id, pid, index) {
      this.$reportData({
        info: 'sy_chain_store_s_search_index:recomment_products_click',
        ext: {
          zt_item_type: 3,
          zt_item_id: id,
          serial_num: index + 1
        }
      });
      uni.navigateTo({
        url: `/pages/product?id=${id}`
      });
    },
    handleHotChange(index, identify) {
      if (identify === 'hot') {
        this.$reportData({
          info: 'sy_chain_store_s_search_index:hotwords_click',
          ext: {
            ext: this.hotList[index].ext,
            zt_item_type: 7,
            zt_item_id: this.hotList[index].title,
            serial_num: index + 1
          }
        });
        this.$emit('handleHistoryChange', this.hotList[index].title);
      } else {
        this.$reportData({
          info: 'sy_chain_store_s_search_index:recentwords_click',
          ext: {
            zt_item_type: 7,
            zt_item_id: this.historyList[index],
            serial_num: index + 1
          }
        });
        this.$emit('handleHistoryChange', this.historyList[index]);
      }
    },
    handleClearChange() {
      this.$reportData({
        info: 'sy_chain_store_s_search_index:recent_trash_click',
        ext: {}
      });
      this.$emit('clear');
    },
    registerExposure() {
      this.$registerExposure(
        '.scrollViewItem',
        (res) => {
          this.$reportData({
            info: 'sy_chain_store_s_search_index:recomment_products_exposure',
            ext: {
              zt_item_type: 3,
              zt_item_id: res.dataset.id,
              serial_num: res.dataset.serial_num
            }
          });
        },
        this
      );
      // 热门搜索词
      this.$registerExposure(
        '.exposure-hot-item',
        (res) => {
          this.$reportData({
            info: 'sy_chain_store_s_search_index:hotwords_exposure',
            ext: {
              zt_item_type: 7,
              zt_item_id: res.dataset.title,
              serial_num: res.dataset.serial_num
            }
          });
        },
        this
      );
      // 历史搜索词
      this.$registerExposure(
        '.exposure-item',
        (res) => {
          this.$reportData({
            info: 'sy_chain_store_s_search_index:recentwords_exposure',
            ext: {
              zt_item_type: 7,
              zt_item_id: res.dataset.title,
              serial_num: res.dataset.serial_num
            }
          });
        },
        this
      );
    }
  }
};
</script>

<style lang="less" scoped>
.recommendation-collection-page {
  width: 100%;
  margin-top: 80rpx;
  padding-top: 10rpx;
  background: #ffffff;
  &-hotRecommend {
    width: 100%;
    margin-top: 30rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    .hot-recommend-title {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 30rpx;
      span {
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        color: #030303;
        font-weight: 500;
      }
      img {
        width: 40rpx;
        height: 40rpx;
      }
    }
    .hot-recommend-list {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      overflow: hidden;
      &-item {
        min-width: 36px;
        border: 2rpx solid #f2f2f2;
        padding: 18rpx;
        box-sizing: border-box;
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #030303;
        font-weight: 400;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &-more {
        width: 36px;
        height: 38px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 0 20rpx;
      }
    }
  }
  &-inSeason {
    width: 100%;
    display: flex;
    flex-direction: column;
    &__title {
      font-family: PingFangSC-Medium;
      font-size: 32rpx;
      color: #030303;
      font-weight: 500;
      padding: 0 0 30rpx 30rpx;
      box-sizing: border-box;
    }
    &-scrollView {
      width: 100%;
      height: 376rpx;
      display: flex;
      flex-direction: row;
      padding-bottom: 50rpx;
      &-item {
        max-width: 304rpx;
        height: 376rpx;
        background: #f8f8f8;
        border-radius: 4rpx;
        margin-right: 18rpx;
        display: flex;
        flex-direction: column;
        &:first-child {
          margin-left: 30rpx;
        }
        img {
          width: 304rpx;
          height: 228rpx;
          &.question__img {
            width: 150rpx;
            height: 40rpx;
            object-fit: contain;
            margin-left: 10rpx;
          }
        }
        .scroll-view-item-content {
          width: 100%;
          display: flex;
          flex-direction: column;
          padding: 20rpx;
          box-sizing: border-box;
          &-title {
            width: 100%;
            display: flex;
            align-items: center;
            &__sp1 {
              font-family: PingFangSC-Regular;
              font-size: 14px;
              color: #030303;
              letter-spacing: 0;
              font-weight: 400;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            &__sp2 {
              background: #ffe8e1;
              font-family: PingFangSC-Medium;
              font-size: 10px;
              color: #fe6631;
              text-align: center;
              font-weight: 500;
              padding: 1rpx 6rpx;
              box-sizing: border-box;
              margin-left: 10rpx;
            }
            img {
              width: 60rpx;
              height: 28rpx;
            }
          }
          &-price {
            width: 100%;
            display: flex;
            align-items: baseline;
            justify-content: flex-start;
            padding-top: 10rpx;
            box-sizing: border-box;
            &__sp1 {
              color: #61b43e;
              font-size: 20rpx;
            }
            &__sp2 {
              font-family: OutFit-Regular;
              font-size: 38rpx;
              color: #61b43e;
              font-weight: 500;
              margin-left: 2rpx;
            }
            &__sp3 {
              font-family: PingFangSC-Semibold;
              font-size: 20rpx;
              color: #8c8c8c;
              font-weight: 600;
              margin-left: 4rpx;
            }
            .priceTextDeleteLine {
              font-size: 11px;
              font-weight: 400;
              color: #bababa;
              text-decoration: line-through;
              align-items: baseline;
            }
            .zixunButton {
              background-color: #61b43e;
              color: #ffffff;
              font-size: 20rpx;
              height: 40rpx;
              width: 57px;
              font-weight: 400;
              display: flex;
              justify-content: center;
              align-items: center;
              text-align: center;
              line-height: 40rpx;
              margin-left: 10rpx;
            }
          }
        }
      }
      .z-image {
        width: 18rpx;
        height: 376rpx;
        background: #ffffff;
        img {
          width: 18rpx;
          height: 100%;
        }
      }
    }
  }
  .pt40 {
    padding-top: 30rpx;
  }
}
</style>
