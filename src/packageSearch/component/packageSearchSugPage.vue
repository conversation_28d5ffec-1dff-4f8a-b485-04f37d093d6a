<template>
  <div class="package-search-sug-page">
    <div class="package-search-sug-page-list">
      <div
        class="package-search-sug-page-list-item"
        v-for="(item, index) in sugListData"
        :key="item.id"
        @click="handleSugChange(index)"
        :data-type="item.type"
        :data-name="item.search"
        :data-serial_num="index + 1"
      >
        <img :src="item.icon" alt="" />
        <!--        <span class="name" v-if="!item.isHtml" v-html="item.searchHtml"></span>-->
        <!--        <span class="name" v-if="item.isHtml" v-html="item.search"></span>-->
        <div class="sug-list">
          <div
            :class="{
              'sug-list-item': true,
              'sug-list-item__active': sugWord.highlight,
              'sug-list-item__overflow': index === item.rich.length - 1
            }"
            v-for="(sugWord, index) in item.rich"
            :key="index"
          >
            {{ sugWord.name }}
          </div>
        </div>
      </div>
      <div
        class="package-search-sug-page-list-item"
        v-if="sugListData.length > 0"
        @click="$emit('handleAllChange')"
      >
        <img
          src="https://static.soyoung.com/sy-design/1zuveb3ngazob1725949458799.png"
          alt=""
        />
        <span class="package-search-sug-page-list-item__result"
          >查看全部相关结果</span
        >
        <img
          class="arrow-right__img"
          src="https://static.soyoung.com/sy-design/2stqqz9og4hfc1725949458797.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    iptTxt: {
      type: String,
      default: ''
    },
    sugList: {
      type: Array,
      default: () => []
    },
    cityId: {}
  },
  watch: {
    sugList: {
      handler: function (list) {
        this.toHighligth(list);
      },
      deep: true
    }
  },
  data() {
    return {
      sugListData: []
    };
  },
  created() {},
  methods: {
    handleSugChange(index) {
      // SUG词点击埋点
      this.$reportData({
        info: 'sy_chain_store_s_search_index:sug_words_click',
        ext: {
          zt_item_type: 7,
          zt_item_id: `${this.sugListData[index].search}`,
          serial_num: index + 1,
          search_word: this.iptTxt
        }
      });
      this.$emit('selectKeyWordItem', this.sugListData[index]);
    },
    toHighligth: function (list) {
      this.sugListData = [];
      for (let i = 0; i < list.length; i++) {
        let resultRichArray = [];
        let richArray =
          list[i].search != null ? list[i].search.split(this.iptTxt) : [];
        if (list[i].search.includes(this.iptTxt)) {
          if (richArray.length > 0) {
            let num = 0;
            let isCenter = false;
            richArray.forEach((item, index) => {
              if (item === '') {
                num++;
              }
              if (index === 0 && item === '') {
                resultRichArray.unshift({
                  highlight: true,
                  name: this.iptTxt
                });
                isCenter = false;
              } else if (
                index === richArray.length - 1 &&
                item === '' &&
                num !== richArray.length
              ) {
                resultRichArray.push({
                  highlight: true,
                  name: this.iptTxt
                });
                isCenter = false;
              } else {
                resultRichArray.push({
                  highlight: false,
                  name: item
                });
                isCenter = false;
              }
            });
            isCenter = num === 0;
            if (isCenter) {
              // 在resultRichArray中间插入一个高亮词
              resultRichArray.splice(1, 0, {
                highlight: true,
                name: this.iptTxt
              });
            }
          }
        } else {
          resultRichArray.push({
            highlight: false,
            name: list[i].search
          });
        }
        list[i].rich = resultRichArray;
        this.sugListData.push(list[i]);
      }
      this.$nextTick(() => {
        this.registerExposure();
      });
    },
    checkHtml(htmlStr) {
      var reg = /<\/?[a-z][\s\S]*>/i;
      return reg.test(htmlStr);
    },
    registerExposure() {
      // SUG曝光埋点
      this.$registerExposure(
        '.package-search-sug-page-list',
        (res) => {
          this.$reportData({
            info: 'sy_chain_store_s_search_index:sug_words_exposure',
            ext: {
              zt_item_type: 7,
              zt_item_id: res.dataset.name,
              serial_num: res.dataset.serial_num,
              search_word: this.iptTxt
            }
          });
        },
        this
      );
      this.$registerExposure(
        '.package-search-sug-page-list-item',
        (res) => {
          this.$reportData({
            info: 'sy_chain_store_s_search_index:sug_words_exposure',
            ext: {
              zt_item_type: 7,
              zt_item_id: res.dataset.name,
              serial_num: res.dataset.serial_num,
              search_word: this.iptTxt
            }
          });
        },
        this
      );
    }
  }
};
</script>

<style lang="less" scoped>
.package-search-sug-page {
  width: 100%;
  margin-top: 80rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  background: #ffffff;
  &-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    &-item {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      padding: 46rpx 0;
      box-sizing: border-box;
      border-bottom: 1px solid #f2f3f4;
      &:last-child {
        border-bottom: 0;
      }
      img {
        width: 50rpx;
        height: 50rpx;
        margin-right: 30rpx;
        &.arrow-right__img {
          width: 20rpx;
          height: 30rpx;
        }
      }
      span {
        flex: 1;
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #030303;
        font-weight: 400;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &.package-search-sug-page-list-item__result {
          font-family: PingFangSC-Regular;
          font-size: 13px;
          color: #646464;
          font-weight: 400;
        }
      }
      .sug-list {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: PingFangSC-Regular;
        font-size: 13px;
        color: #030303;
        font-weight: 400;
        &-item__active {
          color: #61b43e;
        }
        &-item__overflow {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
