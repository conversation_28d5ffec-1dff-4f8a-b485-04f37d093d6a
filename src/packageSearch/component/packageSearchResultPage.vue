<template>
  <div class="package-search-result-page">
    <div
      v-if="loading"
      class="package-search-result-page__loading"
      :style="{ height: `calc(100vh - ${(height + 46) * 2}rpx)` }"
    >
      <img
        src="https://static.soyoung.com/sy-pre/9lklkdzpl9nx-1727251800646.gif"
        alt=""
      />
    </div>
    <template v-else>
      <template v-for="(item, index) in productList">
        <div
          :key="index"
          v-if="
            item.type === 'feed_recommend_opt' &&
            item.items.feed_list.length > 0
          "
          :class="{
            'package-search-result-page__empty pd50': true,
            'nothing-empty':
              item.items.feed_list[0].data.desc &&
              item.items.feed_list[0].data.desc === '没有更多'
          }"
        >
          <div class="page-empty-description">
            {{ item.items.feed_list[0].data.desc }}
          </div>
        </div>
        <div
          :key="index"
          v-if="item.type === 'feed_recommend_area'"
          class="page-empty-title"
        >
          {{ item.items.title }}
        </div>

        <template
          v-if="
            item.type === 'feed_area' || item.type === 'feed_recommend_area'
          "
        >
          <scroll-view
            class="package-search-result-page-scrollView pd50"
            enable-flex
            :key="`scrollView-${index}`"
            :scroll-x="false"
            :loop="false"
            :scroll-y="true"
            :scroll-with-animation="true"
          >
            <template v-if="item.items.feed_list.length > 0">
              <ProductCard
                v-for="(child, cIndex) in item.items.feed_list"
                :key="child.item_id"
                :product="child.data"
                :cIndex="cIndex"
                :type="item.type"
                :keyWord="keyWord"
                :style="{ paddingTop: cIndex === 0 ? '20rpx' : '0' }"
              ></ProductCard>
            </template>
          </scroll-view>
        </template>
      </template>
    </template>
  </div>
</template>

<script>
import ProductCard from '@/components/productCard/productCard.vue';
export default {
  components: {
    ProductCard
  },
  props: {
    product: {
      type: Array,
      default: () => []
    },
    keyWord: {
      type: String,
      default: '玻尿酸'
    },
    loading: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    product: {
      handler: function (list) {
        this.productList = list || [];
      },
      deep: true
    }
  },
  data() {
    return {
      productList: [],
      height: 0
    };
  },
  created() {
    this.height = uni.getMenuButtonBoundingClientRect().bottom;
  },
  methods: {}
};
</script>

<style lang="less" scoped>
.package-search-result-page {
  width: 100%;
  margin-top: 68rpx;
  background: #ffffff;
  &-scrollView {
    display: flex;
    flex-direction: column;
    background: #ffffff;
  }
  &__empty {
    width: 100%;
    background: #ffffff;
    .page-empty-description {
      padding: 20rpx 0 40rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #8c8c8c;
      text-align: center;
      font-weight: 400;
      margin-bottom: 40rpx;
      border-bottom: 2rpx solid #f2f3f4;
    }
  }
  .page-empty-title {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #333333;
    font-weight: 500;
    padding: 0 30rpx 0 30rpx;
    box-sizing: border-box;
  }

  &__loading {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 100rpx;
      height: 100rpx;
    }
  }
}
.nothing-empty {
  width: 100%;
  background: #f8f8f8;
  padding-top: 20rpx;
  div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #8c8c8c;
    font-weight: 400;
    border-bottom: 0 !important;
    margin: 0;
  }
}
.pd50 {
  padding: 20rpx 30rpx 0;
  box-sizing: border-box;
}
</style>
