<template>
  <div
    class="home-search-page"
    :style="{
      marginTop: (maxHeight + 6) * 2 + 'rpx',
      height: `calc(100vh - ${(maxHeight + 6) * 2}rpx)`
    }"
    @touchstart="touchStart"
    @touchend="touchEnd"
  >
    <!-- 导航栏 start -->
    <Navbar :background="`rgba(255, 255, 255, 1)`" :hasBack="false">
      <div class="home-search-page-header">
        <block>
          <div class="back" @click="handleBack"></div>
          <div class="title">搜索</div>
        </block>
      </div>
    </Navbar>
    <!-- 导航栏 end -->

    <div class="home-search-page-content">
      <!-- 搜索栏 start -->
      <div
        class="home-search-page-content-wrapper"
        :style="{ top: maxHeight + 'px' }"
      >
        <div class="home-search-page-content-wrapper-input">
          <input
            ref="searchInput"
            v-model="searchValue"
            type="text"
            :focus="isTextFocus"
            @confirm="handleSearch"
            @focus="handleFocus"
            @input="handleInput"
          />
          <img
            v-if="searchValue !== ''"
            @click="handleClickClear"
            src="https://static.soyoung.com/sy-design/bzsokyai5osd1725437086589.png"
            alt=""
          />
          <span
            class="home-search-page-content-wrapper-input__btn"
            @click="handleSearch"
            >搜索</span
          >
        </div>
      </div>
      <!-- 搜索栏 start -->

      <!-- 最近、热门、当季推荐 start -->
      <RecommendCollectionPage
        :style="{ display: !isFocus ? 'flex' : 'none' }"
        :historyList="historyList"
        :cityId="cityId"
        @handleHistoryChange="handleHistoryChange"
        @clear="handleClearHistory"
      ></RecommendCollectionPage>
      <!-- 热门推荐 end -->

      <!-- sug页 start -->
      <PackageSearchSugPage
        :style="{ display: isFocus && searchValue ? 'flex' : 'none' }"
        :iptTxt="searchValue"
        :sugList="sugList"
        :cityId="cityId"
        @selectKeyWordItem="selectKeyWordItem"
        @handleAllChange="handleAllChange"
      ></PackageSearchSugPage>
      <!-- sug页 end -->
    </div>
  </div>
</template>

<script>
import { getSearchSugListApi } from '@/api/packageSearch';
import Navbar from '@/components/NavBar.vue';
import { debounce } from 'lodash-es';
import PackageSearchSugPage from './component/packageSearchSugPage.vue';
import RecommendCollectionPage from './component/recommendCollectionPage.vue';

const user_info = uni.getStorageSync('user_info');
let touchDotX = 0; //X按下时坐标
let touchDotY = 0; //y按下时坐标
let interval; //计时器
let time = 0; //从按下到松开共多少时间*100
export default {
  components: {
    Navbar,
    RecommendCollectionPage,
    PackageSearchSugPage
  },
  data() {
    return {
      historyList: [], // 历史搜索
      maxHeight: 0,
      searchValue: '',
      sugList: [], // sug页数据
      productList: [], // 搜索结果页数据
      isFocus: false, // 是否聚焦  false: 推荐  true: sug页
      isResult: false, // 是否有搜索结果
      page: 1,
      page_size: 20,
      isTextFocus: true,
      cityId: 1
    };
  },
  mounted() {
    this.maxHeight = uni.getMenuButtonBoundingClientRect().bottom;
  },
  onLoad(query) {
    // 先从本地获取历史搜索
    this.cityId = query.cityId || 1;
    uni.getStorage({
      key: `history_${user_info.unionId}`,
      success: (res) => {
        this.historyList = JSON.parse(res.data) || [];
      }
    });
    this.isFocus = false;
    this.isResult = false;
  },
  // watch: {
  //   searchValue: {
  //     handler(val) {
  //       this.isFocus = !!val;
  //     },
  //     immediate: true
  //   }
  // },
  pageTrackConfig() {
    return {
      info: 'sy_chain_store_s_search_index_page',
      ext: {
        search_word: this.searchValue
      }
    };
  },
  methods: {
    async _getSugList() {
      const data = await getSearchSugListApi({
        source: 1,
        keyword: this.searchValue,
        city_id: this.cityId,
        cityId: this.cityId
      });
      if (data) {
        const { result_list } = data;
        this.sugList = result_list;
        if (result_list.length === 0) {
          this.isFocus = false;
          this.isResult = false;
        } else {
          this.isFocus = true;
        }
      }
    },
    handleBack() {
      this.$reportData({
        info: 'sy_chain_store_s_search_index:return_click',
        ext: {}
      });
      uni.navigateBack({
        delta: 1
      });
    },
    handleClickClear() {
      this.searchValue = '';
      this.isResult = false;
      this.isFocus = false;
    },
    handleFocus(item) {
      if (item.detail.value !== '') {
        this.isFocus = true;
        this.isResult = false; // 聚焦唤起sug页
        this._getSugList();
      } else {
        this.isFocus = false;
        this.isResult = false; // 聚焦唤起sug页
      }
    },
    // handleBluer(event) {
    //   if (event.detail.value === '') {
    //     this.isFocus = false;
    //     this.isResult = false;
    //   } else {
    //     this.isFocus = true;
    //   }
    // },
    handleInput() {
      debounce(() => {
        this._getSugList();
      }, 500)();
    },
    selectKeyWordItem(item) {
      setTimeout(() => {
        this.$_history_save(item.search);
        this.searchValue = '';
        this.isFocus = false;
        this.isTextFocus = false;
      }, 1000);
      uni.navigateTo({
        url: `/packageSearch/searchResult?keyword=${item.search}&cityId=${this.cityId}`
      });
    },
    handleAllChange() {
      if (this.searchValue !== '') {
        this.$_history_save(this.searchValue);
        this.isFocus = false;
        this.isTextFocus = false;
        uni.navigateTo({
          url: `/packageSearch/searchResult?keyword=${this.searchValue}&cityId=${this.cityId}`
        });
        setTimeout(() => {
          this.searchValue = '';
        }, 200);
      }
    },
    handleHistoryChange(item) {
      setTimeout(() => {
        this.searchValue = '';
        this.isFocus = false;
        this.$_history_save(item);
        this.isTextFocus = false;
      }, 1000);
      uni.navigateTo({
        url: `/packageSearch/searchResult?keyword=${item}&cityId=${this.cityId}`
      });
    },
    handleClearHistory() {
      uni.removeStorage({
        key: `history_${user_info.unionId}`
      });
      this.historyList = [];
    },
    handleSearch() {
      if (this.searchValue !== '') {
        this.isFocus = false;
        this.$reportData({
          info: 'sy_chain_store_s_search_index:active_words_click',
          ext: {
            zt_item_type: 7,
            zt_item_id: this.searchValue
          }
        });
        this.$_history_save(this.searchValue);
        this.isTextFocus = false;
        uni.navigateTo({
          url: `/packageSearch/searchResult?keyword=${this.searchValue}&cityId=${this.cityId}`
        });
        this.searchValue = '';
      }
    },
    $_history_save(keyword) {
      // 如果存在，则提到首位，否则添加
      var index = this.historyList.indexOf(keyword);
      if (index !== -1) {
        if (index === 0) {
          return;
        } else {
          this.historyList.splice(index, 1);
        }
      }
      this.historyList.unshift(keyword);
      uni.setStorage({
        key: `history_${user_info.unionId}`,
        data: JSON.stringify(this.historyList)
      });
    },
    touchStart: function (e) {
      touchDotX = e.touches[0].pageX; // 获取触摸时的原点
      touchDotY = e.touches[0].pageY;
      // 使用js计时器记录时间
      interval = setInterval(function () {
        time++;
      }, 100);
    },
    // 触摸结束事件
    touchEnd: function (e) {
      let touchMoveX = e.changedTouches[0].pageX;
      let touchMoveY = e.changedTouches[0].pageY;
      let tmX = touchMoveX - touchDotX;
      let tmY = touchMoveY - touchDotY;
      if (time < 20) {
        let absX = Math.abs(tmX);
        let absY = Math.abs(tmY);
        if (absX > 2 * absY) {
          if (tmX < 0) {
            console.log('左滑=====');
          } else {
            console.log('右滑=====');
            this.$reportData({
              info: 'sy_chain_store_s_search_index:return_click',
              ext: {}
            });
          }
        }
        if (absY > absX * 2 && tmY < 0) {
          console.log('上滑动=====');
        }
      }
      clearInterval(interval); // 清除setInterval
      time = 0;
    }
  }
};
</script>

<style lang="less">
.home-search-page {
  background: #ffffff;
  &-header {
    position: relative;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    .back {
      position: absolute;
      left: 6px;
      top: 50%;
      height: 88rpx;
      width: 88rpx;
      z-index: 1;
      background: url(https://static.soyoung.com/sy-design/3cj8rc3ipek931725437086652.png)
        no-repeat center center transparent;
      background-size: 22rpx 36rpx;
      transform: translateY(-50%);
    }
    .title {
      font-family: PingFangSC-Medium;
      font-size: 34rpx;
      color: #030303;
      font-weight: 500;
    }
  }
  &-content {
    width: 100%;
    padding: 20rpx 0 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    &-wrapper {
      width: 100%;
      position: fixed;
      left: 0;
      padding: 30rpx 30rpx 20rpx;
      box-sizing: border-box;
      background: #ffffff;
      z-index: 99;
      &-input {
        width: 100%;
        height: 84rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        background: #f2f2f2;
        padding: 10rpx 10rpx 10rpx 20rpx;
        box-sizing: border-box;
        img {
          width: 40rpx;
          height: 40rpx;
          margin: 0 10px;
          transition: all 0.3s;
        }
        input {
          flex: 1;
        }
        &__btn {
          width: 130rpx;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #333333;
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #ffffff;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
