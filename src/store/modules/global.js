const state = () => {
  return {
    productId: '',
    userInfo: uni.getStorageSync('user_info') || {},
    liveFloatClose: false,
    channelsliveWindowClose: false,
    systemInfo: {},
    experienceCard: {
      payFinishFrom: '',
      uid: '',
      type: ''
    }
  };
};

const mutations = {
  setUserFn(state, obj) {
    state.userInfo = obj;
  },
  setSystemInfo(state, obj) {
    state.systemInfo = obj;
  },
  setPayFinishFrom(state, obj) {
    console.log('支付成功弹窗修改啦', obj);
    state.experienceCard = obj;
  },
  setLiveFloatClose(state, bool) {
    state.liveFloatClose = bool;
  },
  SET_PRODUCT_ID(state, id) {
    state.productId = id;
  },
  setchannelsliveWindowCloseM(state, bool) {
    state.channelsliveWindowClose = bool;
  }
};

const actions = {
  setUser(context, obj) {
    context.commit('setUserFn', obj);
  },
  actSetSystemInfo(context, obj) {
    context.commit('setSystemInfo', obj);
  },
  payFinishFrom(context, obj) {
    context.commit('setPayFinishFrom', obj);
  },
  setLiveFloatClose(context, bool) {
    context.commit('setLiveFloatClose', bool);
  },
  setProductId(context, id) {
    context.commit('SET_PRODUCT_ID', id);
  },
  setchannelsliveWindowCloseAction(context, bool) {
    context.commit('setchannelsliveWindowCloseM', bool);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
