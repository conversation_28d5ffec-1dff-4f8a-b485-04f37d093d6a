const state = () => {
  return {
    hospitalInfo: null,
    productInfo: null,
    hospitalCity: null,
    hospitalProduct: null
  };
};

const mutations = {
  setHospitalCity(state, obj) {
    state.hospitalCity = obj;
  },
  // 机构品
  setHospitalProduct(state, info) {
    state.hospitalProduct = info;
  }
};

const actions = {
  setHospitalCity(context, obj) {
    context.commit('setHospitalCity', obj);
  }
};

export default {
  namespaced: true,
  actions,
  state,
  mutations
};
