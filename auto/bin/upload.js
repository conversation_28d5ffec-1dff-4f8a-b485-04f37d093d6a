// 上传小程序代码
const ci = require('miniprogram-ci');
const configMap = require('../config.js');

/**
 * 构建 字典
 * __prime_main__ 优享主包
 * __prime_chain_store__ 优享连锁
 */
const primePlatform = process.argv[2] || '__prime_main__';
const desc = process.argv[3] || 'upload';

const { appid, wxPath, keyPath, version, robot } = configMap[primePlatform];

const project = new ci.Project({
  appid,
  type: 'miniProgram',
  projectPath: wxPath,
  privateKeyPath: keyPath,
  ignores: ['node_modules/**/*']
});

const upload = async () => {
  const uploadResult = await ci.upload({
    project,
    version,
    desc,
    setting: {
      es6: true,
      minifyJS: true,
      minifyWXML: true,
      minifyWXSS: true,
      minify: true
    },
    robot
    // onProgressUpdate: console.log
  });
  console.log(uploadResult);
};

upload();
