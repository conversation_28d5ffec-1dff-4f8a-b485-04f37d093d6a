// 上传小程序代码
const ci = require('miniprogram-ci')
const path = require('path')
const { appid, wxPath, keyPath } = require('../config.js')
const { notifyFeishu } = require('./robot.js')
const { spawn } = require('child_process')

const ROBOT = process.argv[2]
const BRANCH = process.argv[3] || 'release-v6'
const API_ENV = process.argv[4] || 'prod'
const DESC = process.argv[5] || 'upload'
const API_ENV_DEV_NUM = process.argv[6] || 0
console.log('[机器人]', ROBOT)
console.log('[分支]', BRANCH)
console.log('[环境]', API_ENV)
if(API_ENV === 'dev') {
  console.log('[开发编号]', API_ENV_DEV_NUM)
}
console.log('[描述]', DESC)

const ub = spawn('./node_modules/.bin/vue-cli-service', ['uni-build'], {
  cwd: process.cwd(),
  env: {
    ...process.env,
    NODE_ENV: 'production',
    UNI_PLATFORM: 'mp-weixin',
    API_ENV_DEV_NUM,
    API_ENV
  }
})
ub.stdout.on('data', (data) => {
  const string = data + ''
  console.log(string.replace('\n', ''));
});
ub.stderr.on('data', (data) => {
  console.error(data + '');
});
ub.on('close', (code) => {
  console.log(`child process exited with code ${code}`);
  if(code === 0) setTimeout(preview, 500)
});

async function preview () {
  const qrcodeOutputDest = path.resolve(process.cwd(), './destination.jpg')
  const robot = Number(ROBOT || 30)
  console.log('机器人' + robot + '准备好了')
  const project = new ci.Project({
    appid,
    type: 'miniProgram',
    projectPath: wxPath,
    privateKeyPath: keyPath,
    ignores: ['node_modules/**/*']
  })
  const stat = await new Promise(resolve => {
    require('fs').stat(wxPath, function(err, res) {
      if(err) {
        process.exit(1)
      }
      resolve(res)
    })
  })
  console.log('[stat]', stat)
  await ci.preview({
    project,
    desc: DESC,
    setting: {
      es6: true,
      minifyJS: true,
      minifyWXML: true,
      minifyWXSS: true,
      minify: true
    },
    qrcodeFormat: 'image',
    qrcodeOutputDest,
    robot
    // pagePath: 'pages/index/index', // 预览页面
    // searchQuery: 'a=1&b=2',  // 预览参数 [注意!]这里的`&`字符在命令行中应写成转义字符`\&`
  })
  notifyFeishu(qrcodeOutputDest, {
    DESC,
    BRANCH,
    API_ENV,
    API_ENV_DEV_NUM
  })
}
