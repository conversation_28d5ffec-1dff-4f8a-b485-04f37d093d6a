const fs = require('fs')
const request = require('request')
const path = require('path')
const qiniu = require('qiniu')

async function notifyFeishu(filePath, {
  DESC,
  BRANCH,
  API_ENV,
  API_ENV_DEV_NUM
}) {
  const url = await _qiniuUpload(filePath, BRANCH, API_ENV)
  // 小程序构建群 https://open.feishu.cn/open-apis/bot/v2/hook/fc790eb6-eaf0-4017-baae-56836aeab03e
  // α海豹土鸡队 https://open.feishu.cn/open-apis/bot/v2/hook/77f02e77-ea17-41c8-bc84-06987273fea5
  // 测试群  https://open.feishu.cn/open-apis/bot/v2/hook/93bed829-ed3c-467a-a43e-fe5757712731
  _post('https://open.feishu.cn/open-apis/bot/v2/hook/93bed829-ed3c-467a-a43e-fe5757712731', {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title: 'prime-test构建完成',
          content: [
            [
              {
                tag: 'text',
                text: `分支:${BRANCH};环境:${API_ENV};${API_ENV === 'dev' ? '编号' + API_ENV_DEV_NUM : ''}`
              }
            ],
            [
              {
                tag: 'text',
                text: DESC
              },
              {
                tag: 'a',
                href: url,
                text: '点击扫码'
              }
            ]
          ]
        }
      }
    }
  }).then(res => {
    console.log(res)
  })
}


function _post (url, data) {
  // https:// open.feishu.cn/open-apis/im/v1/images
  return new Promise((resolve, reject) => {
    request.post(url,
      {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        body: JSON.stringify(data)
      }, function (error, response, body) {
        if (error) {
          return reject(error)
        }
        resolve(JSON.parse(body))
      })
  })
}

// eslint-disable-next-line no-unused-vars
async function _upload () {
  const filePath = path.resolve(process.cwd(), './destination.jpg')
  return getAccessToken().then(token => {
    return new Promise((resolve, reject) => {
      request.post('https://open.feishu.cn/open-apis/im/v1/images',
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': 'Bearer ' + token
          },
          formData: {
            image_type: 'message',
            image: fs.readFileSync(filePath)
          }
        }, function (error, response, body) {
          if (error) {
            return reject(error)
          }
          resolve(body)
        })
    })
  })
}

function getAccessToken() {
  return _post('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', {
    app_id: 'cli_a4c0ff59fe7a500e',
    app_secret: '3W9rDEUfPpqfxjA1Tr12PfpIFz6rwcey'
  }).then(res => {
    const { code, expire, msg, tenant_access_token } = res
    console.log(res)
    if(code === 0) {
      console.log(expire)
      return tenant_access_token
    } else {
      console.error(msg)
      process.exit(1)
    }
  })
}

// 文件上传七牛
async function _qiniuUpload(filePath, branch, env) {
  const accessKey = 'FIHBP_mIg-6hf40fHyJXuxkIKjSPeAD28DIj4ITv';
  const secretKey = 'UW4JD1n_u_xzGNEDD7OlOk1Ot0i4AQDhomwV1ZX3';
  const mac = new qiniu.auth.digest.Mac(accessKey, secretKey);
  const putPolicy = new qiniu.rs.PutPolicy({
    scope: 'static'
  });
  const uploadToken = putPolicy.uploadToken(mac);
  const config = new qiniu.conf.Config();
  // 空间对应的机房
  config.zone = qiniu.zone.Zone_z0;
  const formUploader = new qiniu.form_up.FormUploader(config);
  const putExtra = new qiniu.form_up.PutExtra();
  const key = `prime/dev_qrcode_${branch}_${env}${new Date().getTime()}.jpg`;
  return new Promise((resolve) => {
    formUploader.putFile(uploadToken, key, filePath, putExtra, function(respErr,
      respBody, respInfo) {
      if (respErr) {
        throw respErr;
      }
      if (respInfo.statusCode === 200) {
        resolve(`https://static.soyoung.com/${respBody.key}`)
      } else {
        console.error(respErr)
        process.exit(1)
      }
    });
  })
}

module.exports.notifyFeishu = notifyFeishu

