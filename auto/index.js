const prompts = require('prompts');
const { spawn } = require('child_process');
const configMap = require('./config');

async function start() {
  let API_ENV_DEV_NUM = 0;
  let PRIME_PLATFORM = '__prime_main__';
  let UNI_OUTPUT_DIR = '';
  const { API_ENV } = await prompts({
    type: 'select',
    name: 'API_ENV',
    message: '选择环境',
    choices: [
      { title: "联调环境（devwxapi{x}.sy）", value: "dev" },
      { title: "提测环境（wxapi{x}.sy）", value: "test" },
      { title: "预发环境 ", value: "pre" },
      { title: "线上环境", value: "prod" },
    ]
  });
  if (API_ENV === 'dev' || API_ENV === 'test') {
    const devNum = await prompts({
      type: 'number',
      name: 'API_ENV_DEV_NUM',
      message: '开发环境编号(go_rpc_groupbuy无开发环境编号)',
      validate: (value) => (value > 200 ? `环境编号小于200` : true),
      initial: 0
    });
    API_ENV_DEV_NUM = devNum.API_ENV_DEV_NUM;
  }

  // const platform = await prompts({
  //   type: 'select',
  //   name: 'PRIME_PLATFORM',
  //   message: '选择要编译的优享平台',
  //   choices: [
  //     { title: '优享主包', value: '__prime_main__' },
  //     { title: '优享连锁', value: '__prime_chain_store__' }
  //   ]
  // });
  PRIME_PLATFORM = '__prime_chain_store__';
  const { wxDevPath } = configMap[PRIME_PLATFORM];
  UNI_OUTPUT_DIR = wxDevPath;

  const ub = spawn('vue-cli-service', ['uni-build', '--watch'], {
    env: {
      ...process.env,
      NODE_ENV: 'development',
      UNI_PLATFORM: 'mp-weixin',
      API_ENV,
      API_ENV_DEV_NUM,
      PRIME_PLATFORM,
      UNI_OUTPUT_DIR
    }
  });
  ub.stdout.on('data', (data) => {
    const string = data + '';
    console.log(string.replace('\n', ''));
  });
  ub.stderr.on('data', (data) => {
    console.error(data + '');
  });
  ub.on('close', (code) => {
    console.log(`child process exited with code ${code}`);
  });
}

start();
