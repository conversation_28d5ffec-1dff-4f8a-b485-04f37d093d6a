const ci = require('miniprogram-ci')
const path = require('path')
const { version } = require('../package.json')

const CONFIG = {
  appid: 'wx4c984b5d0eb25e91',
  wxPath: path.resolve(__dirname, '../dist/build/mp-weixin'),
  keyPath: path.resolve(__dirname, './key/private.wx4c984b5d0eb25e91.key'),
  robot: 30,
}

const { appid, wxPath, keyPath, robot } = CONFIG
const desc = process.argv[2] || 'upload'
const project = new ci.Project({
  appid,
  type: 'miniProgram',
  projectPath: wxPath,
  privateKeyPath: keyPath,
  ignores: ['node_modules/**/*']
})

const upload = async () => {
  const uploadResult = await ci.upload({
    project,
    version,
    desc,
    setting: {
      es6: true,
      minifyJS: true,
      minifyWXML: true,
      minifyWXSS: true,
      minify: true
    },
    robot
    // onProgressUpdate: console.log
  });
  console.log(uploadResult);
};

upload()
