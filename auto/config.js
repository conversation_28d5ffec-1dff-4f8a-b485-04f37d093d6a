const path = require('path');
const { version } = require('../package.json');
const config = {
  // 优享主包
  '__prime_main__': {
    appid: 'wx83a3c7bd850395ed',
    syAppid: '107',
    wxPath: path.resolve(process.cwd(), 'dist/build/mp-weixin'),
    wxDevPath: path.resolve(process.cwd(), 'dist/dev/mp-weixin-main'),
    keyPath: path.resolve(
      process.cwd(),
      'auto/key/private.wx83a3c7bd850395ed.key'
    ),
    version,
    robot: 30
  },
  // 优享连锁
  '__prime_chain_store__': {
    appid: 'wx4c984b5d0eb25e91',
    syAppid: '123',
    wxPath: path.resolve(process.cwd(), 'dist/build/mp-weixin'),
    wxDevPath: path.resolve(process.cwd(), 'dist/dev/mp-weixin-chain'),
    keyPath: path.resolve(
      process.cwd(),
      'auto/key/private.wx4c984b5d0eb25e91.key'
    ),
    version,
    robot: 30
  }
};
module.exports = config;
