{"name": "mini-programs-uni-app", "version": "10.4.22", "private": true, "scripts": {"build-main": "cross-env NODE_ENV=production PRIME_PLATFORM=__prime_main__ UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build-chain": "cross-env NODE_ENV=production PRIME_PLATFORM=__prime_chain_store__ UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev": "node auto/index", "qa-auto": "cross-env NODE_ENV=development-67 SY_HOST=$npm_config_host UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "lint:staged": "lint-staged", "qa-online": "cross-env NODE_ENV=online UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize", "qa-preview": "cross-env NODE_ENV=online8 UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize", "lint": "eslint .  --ext .js", "prettier": "prettier --write '**/*.{js,json,vue,less,scss}'"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-28320200727001", "@dcloudio/uni-h5": "^2.0.0-28320200727001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-360": "^2.0.0-28320200727001", "@dcloudio/uni-mp-alipay": "^2.0.0-28320200727001", "@dcloudio/uni-mp-baidu": "^2.0.0-28320200727001", "@dcloudio/uni-mp-qq": "^2.0.0-28320200727001", "@dcloudio/uni-mp-toutiao": "^2.0.0-28320200727001", "@dcloudio/uni-mp-weixin": "^2.0.0-28320200727001", "@dcloudio/uni-quickapp-native": "^2.0.0-28320200727001", "@dcloudio/uni-quickapp-webview": "^2.0.0-28320200727001", "@dcloudio/uni-stat": "^2.0.0-28320200727001", "@soyoung/SyEncrypt": "^1.0.13", "@vue/runtime-dom": "^3.5.13", "core-js": "^3.6.5", "dayjs": "^1.11.11", "flyio": "^0.6.2", "less": "^3.12.2", "less-loader": "^6.2.0", "lodash": "^4.17.20", "lodash-es": "^4.17.21", "lottie-miniprogram": "^1.0.11", "moment": "^2.30.1", "number-precision": "^1.6.0", "queue": "^6.0.2", "regenerator-runtime": "^0.12.1", "sass": "^1.26.10", "sass-loader": "^10.0.1", "tki-qrcode": "^0.1.6", "vue": "^2.6.11", "vuex": "^3.2.0"}, "devDependencies": {"@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.0-28320200727001", "@dcloudio/uni-cli-shared": "^2.0.0-28320200727001", "@dcloudio/uni-migration": "^2.0.0-28320200727001", "@dcloudio/uni-template-compiler": "^2.0.0-28320200727001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-28320200727001", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-28320200727001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-28320200727001", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-28320200727001", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-28320200727001", "@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-service": "~4.4.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "qs": "^5", "eslint": "^7.32.0", "eslint-plugin-vue": "^6.2.2", "husky": "^4.3.8", "jest": "^25.4.0", "lint-staged": "^10.5.4", "mini-types": "*", "miniprogram-api-typings": "*", "miniprogram-ci": "^1.1.6", "postcss-comment": "^2.0.0", "prettier": "^2.7.1", "prompts": "^2.4.2", "qiniu": "^7.8.0", "request": "^2.88.2", "vue-template-compiler": "^2.6.11"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,json,vue,less,scss}": ["prettier --write"], "src/**/*.{js,vue}": ["eslint --fix"]}, "pre-commit": "lint:staged", "browserslist": ["Android >= 8", "ios >= 10"], "uni-app": {"scripts": {}}, "engines": {"node": ">=12 <=16"}}