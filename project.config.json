{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": false, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "2.20.0", "appid": "wx83a3c7bd850395ed", "projectname": "%E7%BE%8E%E6%AC%A1%E5%8D%A1", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}