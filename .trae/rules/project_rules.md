项目概览

- 项目名称: mini-programs-uni-app
- 技术栈: 这是一个基于 uni-app 框架的项目，主要使用 Vue.js (版本 2.6.11) 进行开发。uni-app 是一个跨平台的开发框架，可以一套代码发布到多个平台，例如微信小程序、支付宝小程序、H5 等。
- 状态管理: 项目使用了 Vuex (版本 3.2.0) 进行集中的状态管理。
- 代码规范: 项目配置了 ESLint 和 Prettier 来保证代码风格的统一，并通过 Husky 和 lint-staged 在代码提交前自动进行格式化和校验。
项目结构

- src : 核心代码目录。
  - main.js : 应用的入口文件。
  - App.vue : 应用的根组件。
  - pages.json : 全局配置文件，用于配置页面路由、窗口表现、tabBar 等。
  - pages : 存放主包页面。
  - package* : 多个以 package 开头的目录是应用的分包，用于优化小程序启动性能。例如：
    - packageActivity : 活动相关的分包。
    - packageAppointment : 预约相关的分包。
    - packageCoupon : 优惠券相关的分包。
    - packageOrder : 订单相关的分包。
  - components : 存放可复用的 Vue 组件。
  - api : 存放与后端接口交互的模块。
  - store : 存放 Vuex 的状态管理模块。
  - static : 存放静态资源，如图片等。
  - utils : 存放工具函数。
- project.config.json : 微信小程序的项目配置文件。
主要依赖库

- @dcloudio/uni-app : uni-app 框架核心。
- vue : Vue.js 框架。
- vuex : Vue.js 的状态管理库。
- flyio : 一个支持多端的 HTTP 请求库。
- dayjs : 一个轻量级的日期时间处理库。
- lodash : 提供了很多实用的工具函数。