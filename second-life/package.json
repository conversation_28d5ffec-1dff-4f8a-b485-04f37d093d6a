{"name": "second-life", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "lint": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "prettier": "prettier --write 'src/**/*.{ts,tsx,js,jsx,scss,css,json}'", "lint:staged": "lint-staged", "prepare": "husky install"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@tarojs/components": "4.1.2", "@tarojs/helper": "4.1.2", "@tarojs/plugin-platform-weapp": "4.1.2", "@tarojs/plugin-platform-alipay": "4.1.2", "@tarojs/plugin-platform-tt": "4.1.2", "@tarojs/plugin-platform-swan": "4.1.2", "@tarojs/plugin-platform-jd": "4.1.2", "@tarojs/plugin-platform-qq": "4.1.2", "@tarojs/plugin-platform-h5": "4.1.2", "@tarojs/plugin-platform-harmony-hybrid": "4.1.2", "@tarojs/runtime": "4.1.2", "@tarojs/shared": "4.1.2", "@tarojs/taro": "4.1.2", "@tarojs/plugin-framework-react": "4.1.2", "@tarojs/react": "4.1.2", "react-dom": "^18.0.0", "react": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-transform-class-properties": "7.25.9", "@tarojs/cli": "4.1.2", "@tarojs/vite-runner": "4.1.2", "babel-preset-taro": "4.1.2", "eslint-config-taro": "4.1.2", "eslint": "^8.57.0", "stylelint": "^16.4.0", "terser": "^5.30.4", "vite": "^4.2.0", "@babel/preset-react": "^7.24.1", "@types/react": "^18.0.0", "@vitejs/plugin-react": "^4.3.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "react-refresh": "^0.14.0", "sass": "^1.75.0", "typescript": "^5.4.5", "postcss": "^8.4.38"}, "engines": {"node": ">=18.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx,scss,css,json}": ["prettier --write"], "src/**/*.{ts,tsx,js,jsx}": ["eslint --fix"]}}