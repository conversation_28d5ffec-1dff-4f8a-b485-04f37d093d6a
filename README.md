# Second Life

基于 Taro + React + TypeScript 的跨平台小程序项目

## 技术栈

- **框架**: Taro 4.1.2
- **前端**: React 18 + TypeScript
- **构建工具**: Vite
- **样式**: Sass
- **包管理**: pnpm
- **代码质量**: ESLint + <PERSON><PERSON><PERSON> + <PERSON><PERSON> + lint-staged

## 项目结构

```
src/
├── api/                    # API 接口层
│   └── request.ts         # 请求封装
├── components/            # 通用组件
│   └── LoadingButton/     # 加载按钮组件
├── pages/                 # 页面
│   └── index/            # 首页
├── styles/               # 样式文件
│   ├── variables.scss    # 样式变量
│   └── common.scss       # 通用样式
├── utils/                # 工具函数
│   └── common.ts         # 通用工具
├── store/                # 状态管理
├── assets/               # 静态资源
├── app.config.ts         # 应用配置
├── app.scss             # 全局样式
└── app.ts               # 应用入口
```

## 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
pnpm install
```

### 开发命令

```bash
# 微信小程序
pnpm dev:weapp

# H5
pnpm dev:h5

# 支付宝小程序
pnpm dev:alipay

# 字节跳动小程序
pnpm dev:tt

# QQ小程序
pnpm dev:qq

# 京东小程序
pnpm dev:jd
```

### 构建命令

```bash
# 微信小程序
pnpm build:weapp

# H5
pnpm build:h5

# 支付宝小程序
pnpm build:alipay

# 字节跳动小程序
pnpm build:tt

# QQ小程序
pnpm build:qq

# 京东小程序
pnpm build:jd
```

### 代码质量

```bash
# ESLint 检查
pnpm lint

# Prettier 格式化
pnpm prettier

# lint-staged (Git hooks)
pnpm lint:staged
```

## 架构特点

### 1. 模块化架构
- API 层统一管理接口请求
- 组件库提供可复用的 UI 组件
- 工具函数库提供通用功能
- 样式系统提供设计规范

### 2. 类型安全
- 全面使用 TypeScript
- 严格的类型检查
- 完善的类型定义

### 3. 代码质量保障
- ESLint 代码规范检查
- Prettier 代码格式化
- Husky Git hooks
- lint-staged 提交前检查

### 4. 开发体验
- Vite 快速构建
- 热重载开发
- 完善的开发工具链

## 开发规范

### 1. 文件命名
- 组件文件使用 PascalCase
- 工具文件使用 camelCase
- 样式文件使用 kebab-case

### 2. 代码风格
- 使用 2 空格缩进
- 使用单引号
- 不使用分号
- 行尾逗号

### 3. 组件开发
- 使用函数式组件
- 使用 TypeScript 接口定义 Props
- 组件目录包含 index.tsx 和 index.scss

### 4. 样式开发
- 使用 Sass 变量
- 遵循 BEM 命名规范
- 使用通用样式类

## 部署

项目支持多平台部署：

- 微信小程序：上传到微信开发者工具
- H5：部署到 Web 服务器
- 其他小程序平台：按各平台要求部署

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License
