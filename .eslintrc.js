module.exports = {
  'env': {
    'browser': true,
    'es2020': true,
    'node': true
  },
  'extends': ['eslint:recommended', 'plugin:vue/essential'],
  'parserOptions': {
    'ecmaVersion': 11,
    'sourceType': 'module'
  },
  'plugins': ['vue'],
  'rules': {
    'vue/no-parsing-error': [
      'error',
      {
        'noncharacter-in-input-stream': false
      }
    ]
  },
  'globals': {
    'uni': true,
    'wx': true,
    'getCurrentPages': true,
    'throw': true,
    'requirePlugin': true,
    '__API_ENV__': true,
    '__PRIME_APPID__': true,
    '__API_ENV_DEV_NUM__': true,
    '__MINI_PRIME_CHAIN_STORE__': true,
    'define': true,
    'getApp': true,
    '$': true
  }
};
